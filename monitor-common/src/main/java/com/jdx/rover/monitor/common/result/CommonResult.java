/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.common.result;

import com.jdx.rover.monitor.common.enums.HttpCodeEnum;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 调用服务返回结果信息
 *
 * <AUTHOR>
 */
@Data
public class CommonResult<T> implements Serializable {
    private static final long serialVersionUID = 2519938312161575983L;
    /**
     * 成功代码
     */
    public static final Integer STATUS_SUCCESS = HttpCodeEnum.OK.getValue();
    /**
     * 失败代码
     */
    public static final Integer STATUS_ERROR = HttpCodeEnum.INNER_SERVER_ERROR.getValue();
    /**
     * 返回编码 默认通信成功  0001系统异常
     */
    private Integer code = HttpCodeEnum.OK.getValue();

    /**
     * 返回信息
     */
    private String message;

    /**
     * 返回业务数据
     */
    private T data;

    /**
     * @param code 返回编码
     * @return
     */
    public CommonResult(Integer code) {
        this.setCode(code);
        this.setData(null);
        this.setMessage(HttpCodeEnum.findTitle(code));
    }

    /**
     * @param httpCodeEnum 返回编码
     * @param message   错误信息
     * @return
     */
    public CommonResult(HttpCodeEnum httpCodeEnum, String message) {
        this.setCode(httpCodeEnum.getValue());
        this.setData(null);
        this.setMessage(message);
    }

    /**
     * @param code 返回编码
     * @param data   返回数据
     * @return
     */
    public CommonResult(Integer code, T data) {
        this.setCode(code);
        this.setData(data);
        this.setMessage(HttpCodeEnum.findTitle(code));
    }

    /**
     * @param httpCode 返回编码
     * @param data       返回数据
     * @return
     */
    public CommonResult(HttpCodeEnum httpCode, T data) {
        this.setCode(httpCode.getValue());
        this.setData(data);
        this.setMessage(HttpCodeEnum.findTitle(this.code));
    }

    /**
     * @param code   创建结果码 0000 创建成功
     * @param message 描述信息
     * @param data     返回数据
     * @return
     */
    public CommonResult(Integer code, String message, T data) {
        this.setCode(code);
        this.setMessage(message);
        this.setData(data);
    }


    /**
     * @param code 创建结果码 0000 创建成功
     * @return
     */
    public CommonResult<T> buildResult(Integer code) {
        String msg = null;
        if (StringUtils.isBlank(this.message)) {
            msg = HttpCodeEnum.findTitle(code);
        }
        return buildResult(code, msg, null);
    }


    /**
     * @param code 创建结果码 0000 创建成功
     * @param data   返回数据
     * @return
     */
    public CommonResult<T> buildResult(Integer code, T data) {
        return buildResult(code, null, data);
    }

    /**
     * @param code   创建结果码 0000 创建成功
     * @param message 描述信息
     * @return
     */
    public CommonResult<T> buildResult(Integer code, String message) {
        return buildResult(code, message, null);
    }

    /**
     * @param code   创建结果码 0000 创建成功
     * @param message 描述信息
     * @param data     返回数据
     * @return
     */
    public CommonResult<T> buildResult(Integer code, String message, T data) {
        this.setCode(code);
        this.setMessage(message);
        this.setData(data);
        return this;
    }
}
