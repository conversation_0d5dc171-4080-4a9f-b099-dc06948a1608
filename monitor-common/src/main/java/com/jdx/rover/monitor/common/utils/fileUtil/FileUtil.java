package com.jdx.rover.monitor.common.utils.fileUtil;

/**
 * @description: FileUtil
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-07-15 09:35
 **/
public class FileUtil {

    /**
     * jdPathCheck
     *
     * @param path path
     * @return boolean
     */
    public static boolean jdPathCheck(String path) {
        String[] badList = new String[]{".\\", "..\\", "./", "../"};
        for (String bad : badList) {
            if (path.contains(bad)) {
                return false;
            }
        }
        return true;
    }
}