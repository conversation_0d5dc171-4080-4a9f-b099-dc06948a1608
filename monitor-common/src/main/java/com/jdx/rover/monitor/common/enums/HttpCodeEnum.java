/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum HttpCodeEnum {
    //http返回状态(200:成功,400:参数错误等,401:未登录,403:权限不够,404:未找到页面,500:服务器异常)
    OK(200, "ok"),
    BAD_REQUEST(400, "bad request"),
    UNAUTHORIZED(401, "unauthorized"),
    FORBIDDEN(403, "forbidden"),
    NOT_FUND(404, "not fund"),
    INNER_SERVER_ERROR(500, "server error"),
    USER_PASSWORD_ERROR(1210, "用户密码错误"),
    USER_PASSWORD_LIMIT(1211, "用户输入密码次数超限"),
    USER_FORCE_CHANGE_PASSWORD(1220, "首次登录,请修改密码后使用!"),
    ;

    private final int value;
    private final String title;

    public int value() {
        return value;
    }

    @Override
    public String toString() {
        return this.title;
    }

    public static HttpCodeEnum of(final int value) {
        for (HttpCodeEnum em : HttpCodeEnum.values()) {
            if (em.value == value) {
                return em;
            }
        }
        return null;
    }

    public static String findTitle(final int value) {
        for (HttpCodeEnum em : HttpCodeEnum.values()) {
            if (em.value == value) {
                return em.title;
            }
        }
        return null;
    }
}
