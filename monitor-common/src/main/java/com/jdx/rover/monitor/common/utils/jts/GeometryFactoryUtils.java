package com.jdx.rover.monitor.common.utils.jts;

import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.PrecisionModel;

/**
 * 几何工厂类util
 *
 * <AUTHOR>
 * @date 2024/01/11
 */
@Slf4j
public class GeometryFactoryUtils {
    /**
     * 单例内部类
     * <a href="https://www.cnblogs.com/zhaosq/p/10135362.html">...</a>
     */
    private static class InnerClassInstance {
        /**
         * 静态初始化器，由JVM来保证线程安全
         * 10_000_000表示保留小数点后7位小数,精度为厘米,
         * <a href="https://blog.csdn.net/lang_niu/article/details/123550453">...</a>
         */
        private static final GeometryFactory INSTANCE = new GeometryFactory(new PrecisionModel(10_000_000));
    }

    /**
     * 获取GeometryFactory实例
     */
    public static GeometryFactory getInstance() {
        return InnerClassInstance.INSTANCE;
    }
}
