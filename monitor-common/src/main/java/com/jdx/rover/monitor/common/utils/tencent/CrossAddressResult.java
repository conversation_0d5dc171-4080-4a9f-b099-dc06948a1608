/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.common.utils.tencent;

import lombok.Data;

/**
 * 坐标转地址结果对象,参考文档 https://lbs.qq.com/service/webService/webServiceGuide/webServiceGcoder
 *
 * <AUTHOR>
 * @date 2023/03/23
 */
@Data
public class CrossAddressResult {
    /**
     * 状态码，0为正常，其它为异常
     */
    private Integer status;

    /**
     * 状态说明
     */
    private String message;

    /**
     * 结果
     */
    private AddressResult result;

    /**
     * 坐标相对位置参考
     */
    @Data
    public static class AddressResult {
        /**
         * 以行政区划+道路+门牌号等信息组成的标准格式化地址
         */
        private String address;
        /**
         * 坐标相对位置参考
         */
        private AddressReference address_reference;
    }

    /**
     * 坐标相对位置参考
     */
    @Data
    public static class AddressReference {
        /**
         * 交叉路口
         */
        private Area crossroad;
    }

    /**
     * 区域对象,含交叉路口和二级地标
     */
    @Data
    public static class Area {
        /**
         * 地点唯一标识
         */
        private String id;
        /**
         * 名称/标题
         */
        private String title;
        /**
         * 此参考位置到输入坐标的直线距离
         */
        private double _distance;
        /**
         * 此参考位置到输入坐标的方位关系，如：北、南、内
         */
        private String _dir_desc;
    }
}
