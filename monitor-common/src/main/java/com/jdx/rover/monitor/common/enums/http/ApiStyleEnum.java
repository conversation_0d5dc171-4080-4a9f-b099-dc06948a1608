/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.common.enums.http;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 接口返回值风格样式枚举类
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum ApiStyleEnum {
    NONE("none", "无格式"),
    HTTP("http", "HttpResult格式"),
    ;

    private final String value;

    private final String title;

    public String value() {
        return value;
    }

    @Override
    public String toString() {
        return this.title;
    }

    public static ApiStyleEnum of(final String value) {
        for (ApiStyleEnum rpt : ApiStyleEnum.values()) {
            if (rpt.value.equals(value)) {
                return rpt;
            }
        }
        return null;
    }

    public static boolean isValid(String name) {
        for (ApiStyleEnum callSource : ApiStyleEnum.values()) {
            if (callSource.name().equals(name)) {
                return true;
            }
        }
        return false;
    }
}

