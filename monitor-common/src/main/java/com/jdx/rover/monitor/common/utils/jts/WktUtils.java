package com.jdx.rover.monitor.common.utils.jts;

import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;

/**
 * wkt读取工具类
 *
 * <AUTHOR>
 * @date 2024/01/11
 */
@Slf4j
public class WktUtils {
    /**
     * 多个点构建多边形
     */
    public static Geometry readWkt(String wkt) {
        WKTReader wktReader = new WKTReader();
        Geometry geometry = null;
        try {
            geometry = wktReader.read(wkt);
        } catch (ParseException e) {
            log.error("读取wkt字符串失败!{}", wkt, e);
        }
        return geometry;
    }
}
