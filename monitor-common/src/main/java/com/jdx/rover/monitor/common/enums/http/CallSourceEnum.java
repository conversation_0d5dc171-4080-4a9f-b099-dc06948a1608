/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.common.enums.http;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 调用来源枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CallSourceEnum {
    WEB("web", "WEB网站"),
    PC("pc", "PC客户端"),
    WECHAT("wechat", "微信公众号"),
    IOS("ios", "IOS平台"),
    ANDROID("android", "安卓平台"),
    ROBOT("robot", "机器人"),
    DEVICE("device", "呼车设备"),
    ;

    private final String value;

    private final String title;

    public String value() {
        return value;
    }

    @Override
    public String toString() {
        return this.title;
    }

    public static CallSourceEnum of(final String value) {
        for (CallSourceEnum rpt : CallSourceEnum.values()) {
            if (rpt.value.equals(value)) {
                return rpt;
            }
        }
        return null;
    }

    public static boolean isValid(String name) {
        for (CallSourceEnum callSource : CallSourceEnum.values()) {
            if (callSource.name().equals(name)) {
                return true;
            }
        }
        return false;
    }
}
