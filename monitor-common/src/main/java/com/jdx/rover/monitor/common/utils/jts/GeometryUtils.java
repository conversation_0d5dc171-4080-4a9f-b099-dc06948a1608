package com.jdx.rover.monitor.common.utils.jts;

import org.geotools.feature.simple.SimpleFeatureBuilder;
import org.geotools.feature.simple.SimpleFeatureTypeBuilder;
import org.geotools.geojson.feature.FeatureJSON;
import org.geotools.geojson.geom.GeometryJSON;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.simplify.DouglasPeuckerSimplifier;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

/**
 * 几何工具类util
 *
 * <AUTHOR>
 * @date 2024/01/11
 */
public class GeometryUtils {
    /**
     * 多个点构建多边形
     */
    public static Geometry createPolygon(List<Coordinate> coordinates) {
        return GeometryFactoryUtils.getInstance().createPolygon(coordinates.toArray(new Coordinate[]{}));
    }

    /**
     * 构建点
     */
    public static Point createPoint(double x, double y) {
        Coordinate coordinate = new Coordinate(x, y);
        return GeometryFactoryUtils.getInstance().createPoint(coordinate);
    }

    /**
     * 构建路线
     */
    public static LineString createLineString(List<Coordinate> coordinates) {
        return GeometryFactoryUtils.getInstance().createLineString(coordinates.toArray(new Coordinate[0]));
    }

    /**
     * 构建矩形
     *
     * @param x1 最小x经度
     * @param y1 最小y纬度
     * @param x2 最大x经度
     * @param y2 最大y纬度
     */
    public static Polygon createEnvelopePolygon(double x1, double y1, double x2, double y2) {
        Coordinate[] shapeEnvelopes = new Coordinate[5];
        shapeEnvelopes[0] = new Coordinate(x1, y1);
        shapeEnvelopes[1] = new Coordinate(x1, y2);
        shapeEnvelopes[2] = new Coordinate(x2, y2);
        shapeEnvelopes[3] = new Coordinate(x2, y1);
        shapeEnvelopes[4] = new Coordinate(x1, y1);
        return GeometryFactoryUtils.getInstance().createPolygon(shapeEnvelopes);
    }

    /**
     * 将给定的几何体和属性转换为 GeoJSON 字符串
     */
    public static String getGeoJsonStr(String id, Geometry geometry, HashMap<String, Object> properties) throws Exception {
        SimpleFeatureTypeBuilder featureTypeBuilder = new SimpleFeatureTypeBuilder();
        // set crs first
        featureTypeBuilder.setCRS(DefaultGeographicCRS.WGS84);
        //set the name
        featureTypeBuilder.setName("simpleFeatureType");
        //add a geometry property
        featureTypeBuilder.add("the_geom", geometry.getClass());
        //add some properties
        List<Object> values = new LinkedList<>();
        properties.keySet().stream().forEach(key -> {
            featureTypeBuilder.add(key, properties.get(key).getClass());
            values.add(properties.get(key));
        });
        //build the type
        final SimpleFeatureType simpleFeatureType = featureTypeBuilder.buildFeatureType();
        SimpleFeatureBuilder featureBuilder = new SimpleFeatureBuilder(simpleFeatureType);
        //添加geometry属性
        featureBuilder.add(simplify(geometry));
        //添加name属性, 添加的数据一定按照SimpleFeatureType给的字段顺序进行赋值
        values.stream().forEach(value -> {
            featureBuilder.add(value);
        });
        //构建要素
        SimpleFeature feature = featureBuilder.buildFeature(id);
        FeatureJSON featureJSON = new FeatureJSON(new GeometryJSON(8));
        return featureJSON.toString(feature);
    }

    /**
     * 使用Douglas-Peucker算法简化几何图形。
     * @param geom 要简化的几何图形。
     * @return 简化后的几何图形。
     */
    public static Geometry simplify(Geometry geom) {
        return DouglasPeuckerSimplifier.simplify(geom, getSmartTolerance(geom));
    }

    /**
     * 智能计算容差（根据路径动态调整）
     * @param path 原始路径（需带坐标系信息）
     */
    public static double getSmartTolerance(Geometry path) {
        double totalLength = path.getLength();          // 路径总长度（米）
        int pointCount = path.getNumPoints();          // 原始点数
        double avgSpacing = totalLength / pointCount;  // 实际平均点距
        // 动态容差 = 平均点距 × 1.2 + 5米（经验公式）
        return avgSpacing * 1.2 + 5;
    }

}