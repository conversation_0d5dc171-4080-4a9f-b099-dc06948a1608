/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.common.exception;

/**
 * RedissionLockException
 *
 * <AUTHOR>
 */
public class RedissionLockException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    public RedissionLockException() {
        super();
    }

    public RedissionLockException(String message) {
        super(message);
    }

    public RedissionLockException(String message, Throwable cause) {
        super(message, cause);
    }

    public RedissionLockException(Throwable cause) {
        super(cause);
    }
}
