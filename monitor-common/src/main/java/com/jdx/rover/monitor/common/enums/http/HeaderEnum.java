/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.common.enums.http;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否有效状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum HeaderEnum {
    X_TOKEN("X-Token", "用户的登录token"),
    API_VERSION("Api-Version", "api的版本号"),
    APP_VERSION("App-Version", "app版本号"),
    /**
     * 调用来源 {@link CallSourceEnum}
     */
    CALL_SOURCE("Call-Source", "调用来源"),
    /**
     * API的返回格式 {@link ApiStyleEnum}
     */
    API_STYLE("Api-Style", "API的返回格式"),
    ;

    private final String value;

    private final String title;

    public String value() {
        return value;
    }

    @Override
    public String toString() {
        return this.title;
    }

    public static HeaderEnum of(final String value) {
        for (HeaderEnum rpt : HeaderEnum.values()) {
            if (rpt.value.equals(value)) {
                return rpt;
            }
        }
        return null;
    }
}
