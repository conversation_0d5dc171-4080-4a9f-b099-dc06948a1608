package com.jdx.rover.monitor.common.utils.jts;

/**
 * @description: GeoUtils
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-07-15 18:02
 **/
public class GeoUtils {

    private static final double EARTH_RADIUS = 6371.0; // 地球平均半径, 单位: 千米

    private GeoUtils() {
    }

    /**
     * 计算两个地理坐标之间的距离
     *
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 两点之间的距离, 单位: 千米
     */
    public static double haversineDistance(double lat1, double lon1, double lat2, double lon2) {
        // 将角度转换为弧度
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        // 应用 Haversine 公式
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2) + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return EARTH_RADIUS * c;
    }
}