/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.common.utils.proto;

import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;

/**
 * protobuf转json
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Slf4j
public class ProtoUtils {
    /**
     * proto转换为json
     */
    public static String protoToJson(MessageOrBuilder message) {
        String result;
        try {
            result = JsonFormat.printer().omittingInsignificantWhitespace().print(message);
        } catch (Exception e) {
            log.error("proto转换成json错误,{}", message, e);
            result = "";
        }
        return result;
    }
}
