/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.common.utils.param;

import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 一个简陋的 lambda 操作map对象
 * </p>
 *
 * <AUTHOR>
 * @date 2023/05/23
 */
public class ParamMap<T> {

    private final Map<String, Object> paramMap;

    /**
     * 构造函数
     */
    public ParamMap() {
        //构造函数初始化Map
        paramMap = new HashMap<>();
    }

    /**
     * 设置Map key-value
     *
     * @param func  方法引用
     * @param value 值
     * @return 返回当前对象进行链式调用
     */
    public ParamMap<T> addProperty(Func1<T, ?> func, Object value) {
        String fieldName = LambdaUtil.getFieldName(func);
        paramMap.put(fieldName, value);
        return this;
    }

    /**
     * 设置Map key-value
     * value 为 null 时不进行赋值
     *
     * @param func  方法引用
     * @param value 值
     * @return 返回当前对象进行链式调用
     */
    public ParamMap<T> addNonNullProperty(Func1<T, ?> func, Object value) {
        String fieldName = LambdaUtil.getFieldName(func);
        if (Objects.nonNull(value)) {
            paramMap.put(fieldName, value);
        }
        return this;
    }

    /**
     * 参数封装完毕,返回相关Map
     */
    public Map<String, Object> toMap() {
        return paramMap;
    }
}
