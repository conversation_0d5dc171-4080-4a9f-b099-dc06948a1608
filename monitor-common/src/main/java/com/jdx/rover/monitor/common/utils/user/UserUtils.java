package com.jdx.rover.monitor.common.utils.user;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: UserUtils
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-06-11 11:09
 **/
public class UserUtils {

    private UserUtils() {

    }

    /**
     * 1、获取并校验当前登录用户
     *
     * @return username
     */
    public static String getAndCheckLoginUser() {
        String username = LoginUtils.getUsername();
        if (StringUtils.isBlank(username)) {
            username = JsfLoginUtil.getUsername();
        }

        if (StringUtils.isBlank(username)) {
            throw new AppException("获取当前登录用户失败");
        }
        return username;
    }

    /**
     * 2、获取当前登录用户
     *
     * @return username
     */
    public static String getLoginUser() {
        String username = LoginUtils.getUsername();
        if (StringUtils.isBlank(username)) {
            username = JsfLoginUtil.getUsername();
        }
        return username;
    }
}