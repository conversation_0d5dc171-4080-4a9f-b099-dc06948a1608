/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.common.utils.alarm;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Optional;

/**
 * 告警工具类
 *
 * <AUTHOR>
 * @date 2024/08/13
 */
@Slf4j
public class AlarmUtil {

    /**
     * 构造全局告警编号
     *
     * @param vehicleName  车号
     * @param reportTime 发生时间
     * @return alarmNumber
     */
    public static String buildAlarmNumber(String vehicleName, Date reportTime) {
        reportTime = Optional.ofNullable(reportTime).orElse(new Date());
        return new StringBuilder(vehicleName).append(reportTime.getTime()).toString();
    }

}
