/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.common.exception;

import lombok.Data;

/**
 * <p>
 * This is a rover runtime exception to be used in direct application.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is not thread safe. And this is not expected to be
 * thread safe when using concurrently
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class RoverRuntimeException extends RuntimeException {
  private String code;

  /**
   * <p>
   * The serialVersionUID to be serialized.
   * </p>
   */
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * The rover runtime excepiton constructor.
   * </p>
   *
   * @param message exception message
   */
  public RoverRuntimeException(String message) {
    super(message);
  }

  /**
   * <p>
   * The rover runtime excepiton constructor.
   * </p>
   *
   * @param message exception message
   */
  public RoverRuntimeException(String code, String message) {
    super(message);
    this.code = code;
  }

  /**
   * <p>
   * The rover runtime excepiton constructor.
   * </p>
   *
   * @param message exception message
   * @param cause   the throwable cause for the exception
   */
  public RoverRuntimeException(String message, Throwable cause) {
    super(message, cause);
  }

}
