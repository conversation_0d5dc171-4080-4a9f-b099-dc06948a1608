package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 车辆调度基本信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleScheduleRepository {
  /**
   * 通过车辆名称获取调度信息
   *
   * @param vehicleName
   * @return
   */
  @Cacheable(value = LocalCacheConstant.SCHEDULE_VEHICLE, key = "#vehicleName")
  public MonitorScheduleEntity get(String vehicleName) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_PREFIX.getValue() + vehicleName;
    MonitorScheduleEntity result = RedissonUtils.getObject(redisKey);
    log.info("VehicleScheduleRepository本地缓存数据={}", result);
    return result;
  }

  /**
   * 通过车辆名称获取redis调度信息
   * @param vehicleName
   * @return
   */
  public MonitorScheduleEntity getFromRedis(String vehicleName) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_PREFIX.getValue() + vehicleName;
    MonitorScheduleEntity result = RedissonUtils.getObject(redisKey);
    return result;
  }

  /**
   * 通过车辆名称获取基本信息
   *
   * @param vehicleNameList
   * @return
   */
  public Map<String, MonitorScheduleEntity> listMap(List<String> vehicleNameList) {
    List<String> redisKeyList = vehicleNameList.stream().map(tmp ->
        RedisKeyEnum.SCHEDULE_VEHICLE_PREFIX.getValue() + tmp).collect(Collectors.toList());
    Map<String, MonitorScheduleEntity> map = RedissonUtils.getRedissonClient()
        .getBuckets().get(redisKeyList.toArray(new String[0]));
    Map<String, MonitorScheduleEntity> result = map.entrySet().stream().collect(Collectors.toMap(
        entry -> StringUtils.substringAfter(entry.getKey(), RedisKeyEnum.SCHEDULE_VEHICLE_PREFIX.getValue()),
        entry -> entry.getValue()));
    return result;
  }

  /**
   * 通过车辆名称获取调度信息列表
   *
   * @param vehicleNameList
   * @return
   */
  public List<MonitorScheduleEntity> list(List<String> vehicleNameList) {
    Map<String, MonitorScheduleEntity> map = listMap(vehicleNameList);
    return vehicleNameList.stream().map(name -> map.get(name)).filter(data -> data != null).collect(Collectors.toList());
  }

  /**
   * 通过车辆名称设置调度信息
   *
   * @param dto
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.SCHEDULE_VEHICLE, key = "#dto.vehicleName")
  public void set(MonitorScheduleEntity dto) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_PREFIX.getValue() + dto.getVehicleName();
    RedissonUtils.setObject(redisKey, dto);
  }

  /**
   * 通过车辆名称删除调度信息
   *
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.SCHEDULE_VEHICLE, key = "#vehicleName")
  public boolean delete(String vehicleName) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_PREFIX.getValue() + vehicleName;
    boolean result = RedissonUtils.deleteObject(redisKey);
    return result;
  }

  /**
   * 获取调度状态,没有本地缓存,高频请勿使用
   */
  public String getScheduleState(String vehicleName) {
    String scheduleState;
    MonitorScheduleEntity monitorScheduleEntity = this.get(vehicleName);
    if (monitorScheduleEntity != null && monitorScheduleEntity.getScheduleState() != null) {
      scheduleState = monitorScheduleEntity.getScheduleState();
    } else {
      scheduleState = VehicleScheduleState.WAITING.getVehicleScheduleState();
    }
    return scheduleState;
  }

}
