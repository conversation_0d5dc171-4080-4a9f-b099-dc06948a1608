/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.IssueOperateHistory;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * This is an issue history repository which contains operations on data
 * base.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Mapper
public interface IssueOperateHistoryMapper extends BaseMapper<IssueOperateHistory> {

  /**
   * <p>
   * Find issue history by issueNo.
   * </p>
   *
   * @param id The no of the issue history.
   * @return The corresponding issue history.
   */
  @Select("SELECT id, issue_no, timestamp, user_name, title, message, vehicle_alarm_id FROM issue_operate_history WHERE issue_no=#{issueNo}")
  public List<IssueOperateHistory> getByIssueNo(@Param("issueNo") String issueNo);

}
