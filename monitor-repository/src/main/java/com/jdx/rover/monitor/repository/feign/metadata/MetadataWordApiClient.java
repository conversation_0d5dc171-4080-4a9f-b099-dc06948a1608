/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.metadata.api.service.word.WordMonitorApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 远程喊话服务客户端
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "metadata-word",value = "metadata-web")
public interface MetadataWordApiClient extends WordMonitorApi {
}
