/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.redis.robot;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.entity.device.RobotMapCongestionInfoDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

/**
 * 机器人拥堵信息
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Repository
public class RobotCongestionInfoRepository {

  /**
   * 获取地图下所有拥堵区域
   *
   * @param mapId
   */
  @Cacheable(value = LocalCacheConstant.ROBOT_CONGESTION_INFO, unless = "#result == null", key = "#mapId")
  public RobotMapCongestionInfoDO get(String mapId) {
    String redisKey = getKey(mapId);
    RobotMapCongestionInfoDO issueDO = RedissonUtils.getObject(redisKey);
    return issueDO;
  }

  /**
   * 添加
   *
   * @param mapId
   * @param congestionInfoDo
   */
  @LocalCacheEvict(value = LocalCacheConstant.ROBOT_CONGESTION_INFO, key = "#mapId")
  public void set(String mapId, RobotMapCongestionInfoDO congestionInfoDo) {
    String redisKey = getKey(mapId);
    RedissonUtils.setObject(redisKey, congestionInfoDo);
  }

  /**
   * 删除
   *
   * @param mapId
   */
  @LocalCacheEvict(value = LocalCacheConstant.ROBOT_CONGESTION_INFO, key = "#mapId")
  public void remove(String mapId) {
    String redisKey = getKey(mapId);
    RedissonUtils.deleteObject(redisKey);
  }

  /**
   * 获取redis key
   *
   * @param mapId
   * @return
   */
  public static String getKey(String mapId) {
    String redisKey = RedisKeyEnum.ROBOT_CONGESTION_PREFIX.getValue() + mapId;
    return redisKey;
  }

}
