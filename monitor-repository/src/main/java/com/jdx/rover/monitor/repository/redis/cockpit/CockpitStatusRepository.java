/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.cockpit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 坐席状态信息
 *
 * <AUTHOR>
 */
@Service
public class CockpitStatusRepository {
    /**
     * 缓存名称
     */
    public static final String CACHE_NAME = "cockpit:status:map:";
    /**
     * 记录时间字段
     */
    private static final String FIELD_RECORD_TIME = "recordTime";
    /**
     * 获取redis key
     *
     * @param cockpitNumber 坐席编号
     */
    public static String getKey(String cockpitNumber) {
        return CACHE_NAME + cockpitNumber;
    }

    /**
     * 通过坐席编号获取对象
     *
     * @param cockpitNumber 坐席编号
     */
    @Cacheable(value = CACHE_NAME, key = "#cockpitNumber")
    public CockpitStatusDO get(String cockpitNumber) {
        String redisKey = getKey(cockpitNumber);
        RMap<String, Object> rMap = RedissonUtils.getMap(redisKey);
        Map<String, Object> mapValue = rMap.readAllMap();
        CockpitStatusDO result = BeanUtil.toBean(mapValue, CockpitStatusDO.class);
        result.setCockpitNumber(cockpitNumber);
        return result;
    }

    /**
     * 设置
     *
     * @param cockpitNumber 坐席编号
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#cockpitNumber")
    public void putMapValue(String cockpitNumber, Func1<CockpitStatusDO, ?> func, Object value) {
        Map<String, Object> paramMap = new ParamMap<CockpitStatusDO>().addProperty(func, value).toMap();
        putAllMapObject(cockpitNumber, paramMap);
    }

    /**
     * 移除
     *
     * @param cockpitNumber 坐席编号
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#cockpitNumber")
    public long fastRemoveMapKey(String cockpitNumber, Func1<CockpitStatusDO, ?>... func) {
        String redisKey = getKey(cockpitNumber);
        List<String> fieldNameList = Arrays.stream(func).map(LambdaUtil::getFieldName).toList();
        RMap<String, Object> rMap = RedissonUtils.getMap(redisKey);
        return rMap.fastRemove(fieldNameList.toArray(new String[0]));
    }

    /**
     * 保存map数据
     *
     * @param cockpitNumber 坐席编号
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#cockpitNumber")
    public void putAllMapObject(String cockpitNumber, Map<String, Object> mapObject) {
        mapObject.values().removeIf(Objects::isNull);
        if (Objects.isNull(mapObject.get(FIELD_RECORD_TIME))) {
            mapObject.put(FIELD_RECORD_TIME, new Date());
        }
        String redisKey = getKey(cockpitNumber);
        RedissonUtils.putAllMapObject(redisKey, mapObject);
    }

    /**
     * 通过坐席编号删除
     *
     * @param cockpitNumber 坐席编号
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#cockpitNumber")
    public boolean remove(String cockpitNumber) {
        String redisKey = getKey(cockpitNumber);
        return RedissonUtils.deleteObject(redisKey);
    }

    /**
     * 通过坐席编号获取
     *
     * @param cockpitNumberList 坐席编号列表
     */
    public Map<String, CockpitStatusDO> listMap(List<String> cockpitNumberList) {
        Map<String, CockpitStatusDO> result = new HashMap<>(cockpitNumberList.size());
        // 自调用,事务不生效,导致使用不了缓存,改成通过spring再次获取
        CockpitStatusRepository self = SpringUtil.getBean(CockpitStatusRepository.class);
        for (String cockpitNumber : cockpitNumberList) {
            CockpitStatusDO dto = self.get(cockpitNumber);
            result.put(cockpitNumber, dto);
        }
        return result;
    }

    /**
     * 通过坐席编号获取
     *
     * @param cockpitNumberList 坐席编号列表
     */
    public List<CockpitStatusDO> list(List<String> cockpitNumberList) {
        Map<String, CockpitStatusDO> map = listMap(cockpitNumberList);
        return new ArrayList<>(map.values());
    }

}
