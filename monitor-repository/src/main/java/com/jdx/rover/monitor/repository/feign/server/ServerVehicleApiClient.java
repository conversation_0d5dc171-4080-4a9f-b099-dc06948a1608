/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.server;

import com.jdx.rover.server.api.service.vehicle.ServerVehicleApi;

import org.springframework.cloud.openfeign.FeignClient;

/**
 * 车辆实时信息客户端
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "server-vehicle",value = "rover-server-business")
public interface ServerVehicleApiClient extends ServerVehicleApi {
}
