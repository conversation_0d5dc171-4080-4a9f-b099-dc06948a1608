package com.jdx.rover.monitor.repository.redis.metadata;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.metadata.api.domain.dto.technical.ErrorCodeTranslateInfoDTO;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RListMultimap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 错误码映射缓存
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ErrorCodeTranslateRepository {

    /**
     * 获取
     *
     * @return
     */
    @Cacheable(value = LocalCacheConstant.ERROR_CODE_TRANSLATE, unless = "#result?.size() == 0", key = "#errorCode")
    public List<ErrorCodeTranslateInfoDTO> get(String errorCode) {
        RListMultimap<String,ErrorCodeTranslateInfoDTO> listMultimap = RedissonUtils.getRedissonClient().getListMultimap(RedisKeyEnum.ERROR_CODE_TRANSLATE_LIST.getValue());
        List<ErrorCodeTranslateInfoDTO> translateInfoList = listMultimap.getAll(errorCode);
        log.info("ErrorCodeTranslateRepository本地缓存数据={}", translateInfoList.size());
        return translateInfoList;
    }

    /**
     * 设置
     *
     * @param errorCode 错误码
     * @param translateInfoList 映射列表
     */
    @LocalCacheEvict(value = LocalCacheConstant.ERROR_CODE_TRANSLATE, key = "#errorCode")
    public void setErrorCode(String errorCode, List<ErrorCodeTranslateInfoDTO> translateInfoList) {
        RListMultimap<String, ErrorCodeTranslateInfoDTO> listMultimap = RedissonUtils.getRedissonClient().getListMultimap(RedisKeyEnum.ERROR_CODE_TRANSLATE_LIST.getValue());
        List<ErrorCodeTranslateInfoDTO> oldValues = listMultimap.replaceValues(errorCode, translateInfoList);
        log.info("ErrorCodeTranslateRepository原始缓存数据={}", JsonUtils.writeValueAsString(oldValues));
    }
}
