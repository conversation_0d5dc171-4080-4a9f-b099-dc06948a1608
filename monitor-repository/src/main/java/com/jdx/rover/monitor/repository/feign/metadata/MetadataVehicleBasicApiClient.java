/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.metadata.api.service.v2.vehicle.VehicleBasicApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 注
 *
 * <AUTHOR>
 * @date 2023/6/2
 */
@FeignClient(contextId = "metadata-vehicle-basic",value = "metadata-web")
public interface MetadataVehicleBasicApiClient extends VehicleBasicApi {
}
