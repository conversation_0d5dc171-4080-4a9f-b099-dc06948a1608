/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.alarm;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.entity.alarm.EarlyWarningDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 坐席状态信息
 *
 * <AUTHOR>
 */
@Service
public class EarlyWarningRepository {
    /**
     * 缓存名称
     */
    public static final String CACHE_NAME = "earlyWarning:object:";

    /**
     * 获取redis key
     */
    public static String getKey(String vehicleName) {
        return CACHE_NAME + vehicleName;
    }

    /**
     * 获取对象
     */
    @Cacheable(value = CACHE_NAME, key = "#vehicleName")
    public EarlyWarningDO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.getObject(redisKey);
    }

    /**
     * 设置对象
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void set(String vehicleName, EarlyWarningDO dto) {
        String redisKey = getKey(vehicleName);
        RedissonUtils.setObject(redisKey, dto);
    }

    /**
     * 通过用户名称删除对象
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public boolean remove(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.deleteObject(redisKey);
    }

    /**
     * 获取对象map集合
     */
    public Map<String, EarlyWarningDO> listMap(List<String> vehicleNameList) {
        List<String> redisKeyList = vehicleNameList.stream().map(EarlyWarningRepository::getKey).toList();
        Map<String, EarlyWarningDO> map = RedissonUtils.getRedissonClient().getBuckets()
                .get(redisKeyList.toArray(new String[0]));
        return map.entrySet().stream().collect(Collectors.toMap(
                entry -> StringUtils.substringAfter(entry.getKey(), CACHE_NAME),
                Map.Entry::getValue));
    }

    /**
     * 获取对象list集合
     */
    public List<EarlyWarningDO> list(List<String> vehicleNameList) {
        Map<String, EarlyWarningDO> map = listMap(vehicleNameList);
        return new ArrayList<>(map.values());
    }
}