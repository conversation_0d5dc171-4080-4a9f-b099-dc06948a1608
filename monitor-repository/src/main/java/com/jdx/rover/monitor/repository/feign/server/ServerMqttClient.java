/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.repository.feign.server;

import com.jdx.rover.server.api.service.mqtt.MqttSendApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * MQTT消息发送服务
 *
 * <AUTHOR>
 * @date 2024/1/24
 */

@FeignClient(value = "rover-server-mqtt", contextId = "MqttSendApi")
public interface ServerMqttClient extends MqttSendApi {
}