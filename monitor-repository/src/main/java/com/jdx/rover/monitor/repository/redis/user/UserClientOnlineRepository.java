/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.user;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.entity.user.UserClientOnlineDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户状态信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class UserClientOnlineRepository {
    private final UserOnlineSetRepository userOnlineSetRepository;
    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "user:clientOnline:map:";

    /**
     * 获取redis key
     *
     * @param userName 用户名称
     * @return
     */
    public static String getKey(String userName) {
        return CACHE_NAME + userName;
    }

    /**
     * 获取
     *
     * @param userName 用户名称
     */
    @Cacheable(value = CACHE_NAME, key = "#userName")
    public Map<String, UserClientOnlineDO> get(String userName) {
        String redisKey = getKey(userName);
        RMap<String, UserClientOnlineDO> rMap = RedissonUtils.getMap(redisKey);
        return rMap.readAllMap();
    }

    /**
     * 设置对象
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public void putByMapKey(String userName, String mapKey, UserClientOnlineDO userClientOnlineDO) {
        String redisKey = getKey(userName);
        RMap<String, UserClientOnlineDO> rMap = RedissonUtils.getMap(redisKey);
        rMap.fastPut(mapKey, userClientOnlineDO);
        userOnlineSetRepository.add(userName);
    }

    /**
     * 通过用户名称删除对象
     *
     * @param userName 用户名称
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public void remove(String userName) {
        String redisKey = getKey(userName);
        RedissonUtils.deleteObject(redisKey);
        userOnlineSetRepository.remove(userName);
    }


    /**
     * 移除状态值
     *
     * @param userName 用户名称
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public void fastRemoveByMapKey(String userName, String mapKey) {
        String redisKey = getKey(userName);
        RMap<String, String> rMap = RedissonUtils.getMap(redisKey);
        rMap.fastRemove(mapKey);
        int size = rMap.size();
        if (size == 0) {
            userOnlineSetRepository.remove(userName);
        }
    }

    /**
     * 通过用户名称获取对象
     *
     * @param userNameList 用户名称列表
     */
    public Map<String, Map<String, UserClientOnlineDO>> listMap(List<String> userNameList) {
        Map<String, Map<String, UserClientOnlineDO>> result = new HashMap<>(userNameList.size());
        for (String userName : userNameList) {
            Map<String, UserClientOnlineDO> dto = get(userName);
            result.put(userName, dto);
        }
        return result;
    }

    /**
     * 通过用户名称获取对象
     *
     * @param userNameList 用户名称列表
     */
    public List<Map<String, UserClientOnlineDO>> list(List<String> userNameList) {
        Map<String, Map<String, UserClientOnlineDO>> map = listMap(userNameList);
        return new ArrayList<>(map.values());
    }
}