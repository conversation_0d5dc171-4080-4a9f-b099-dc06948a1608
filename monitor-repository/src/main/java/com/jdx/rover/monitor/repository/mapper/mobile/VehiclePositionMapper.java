package com.jdx.rover.monitor.repository.mapper.mobile;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.constant.DataSourceConstant;
import com.jdx.rover.monitor.po.mobile.VehiclePosition;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description: VehiclePositionMapper
 * @author: wangguotai
 * @create: 2024-05-24 14:24
 **/
@DS(value = DataSourceConstant.POSTGRESQL)
@Mapper
public interface VehiclePositionMapper extends BaseMapper<VehiclePosition> {

    /**
     * 获取指定范围内车辆
     *
     * @param longitude longitude
     * @param latitude  latitude
     * @param distance  distance
     * @return List<String>
     */
    @Select({"""
            SELECT
            	vehicle_name
            FROM
            	vehicle_position
            WHERE
            	ST_DWithin(point::geography, ST_SetSRID(ST_MakePoint(#{longitude}, #{latitude}), 4326)::geography, #{distance})
            """})
    List<String> getVehicleByDistance(@Param(value = "longitude") Double longitude, @Param(value = "latitude") Double latitude, @Param(value = "distance") Integer distance);

    /**
     * 批量插入或更新车辆位置
     *
     * @param vehiclePositionList vehiclePositionList
     */
    @Insert({"""
            <script>
            INSERT INTO vehicle_position(vehicle_name, point) VALUES
            <foreach collection='vehiclePositionList' item='item' separator=','>
                (#{item.vehicleName}, #{item.point})
            </foreach>
            ON CONFLICT (vehicle_name) DO UPDATE SET point=EXCLUDED.point
            </script>
            """})
    void insertOrUpdateBatch(@Param(value = "vehiclePositionList") List<VehiclePosition> vehiclePositionList);
}