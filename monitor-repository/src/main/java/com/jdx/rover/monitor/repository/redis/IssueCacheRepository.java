/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.entity.IssueCacheEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * This is issue cache Repository.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Slf4j
public class IssueCacheRepository {
  /**
   * <p>
   * Represents the redis template repository.
   * </p>
   */
  @Autowired
  private RedisTemplate<String, Object> clientRedisTemplate;

  /**
   * <p>
   * the redis cache key.
   * </p>
   */
  static final String ISSUE_CACHE_KEY = "issue_%s";

  /**
   * <p>
   * Set issue cache.
   * </p>
   *
   * @param issueCacheEntity the values of issue.
   * @throws IllegalArgumentException if the argument is invalid.
   */
  public void save(String vehicleName, IssueCacheEntity issueCacheEntity) {
    ParameterCheckUtility.checkNotNull(issueCacheEntity, "issueCache");
    ParameterCheckUtility.checkNotNull(vehicleName, "vehicleName");
    String key = String.format(ISSUE_CACHE_KEY, vehicleName);
    clientRedisTemplate.opsForValue().set(key, issueCacheEntity);
  }

  /**
   * <p>
   * Get issue cache.
   * </p>
   *
   * @param vehicleName the key of vehicle name
   * @throws IllegalArgumentException   if the argument is invalid.
   * @throws RoverProxyRuntimeException if faild to convert object to client.
   */
  public IssueCacheEntity getByKey(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    try {
      String key = String.format(ISSUE_CACHE_KEY, vehicleName);
      Object result = clientRedisTemplate.opsForValue().get(key);
      if (result == null) {
        return null;
      }
      return IssueCacheEntity.class.cast(result);
    } catch (ClassCastException e) {
      throw new AppException("Faild to convert object to client.", e);
    }
  }

  /**
   * <p>
   * Delete issue cache.
   * </p>
   *
   * @param vehicleName the key of vehicle name
   * @throws IllegalArgumentException if the argument is invalid.
   */
  public void remove(String vehicleName) {
    log.info(String.format("IssueCache handler clear : %s", vehicleName));
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    String key = String.format(ISSUE_CACHE_KEY, vehicleName);
    Boolean deleteResult = clientRedisTemplate.delete(key);
    if (!deleteResult) {
      log.error("delete IssueCache error,{}", vehicleName);
    }
  }

  /**
   * <p>
   * Th cache has key.
   * </p>
   *
   * @param vehicleName the vehicle name of vehicle
   * @throws IllegalArgumentException if the argument is invalid.
   */
  public Boolean hasKey(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    String key = String.format(ISSUE_CACHE_KEY, vehicleName);
    return clientRedisTemplate.hasKey(key);
  }

  /**
   * 批量获取监控车辆工单缓存
   *
   * @param vehicleNameList 车辆名称列表
   */
  public Map<String, IssueCacheEntity> getBatch(List<String> vehicleNameList) {
    ParameterCheckUtility.checkNotNull(vehicleNameList, "vehicleNameList");
    List<String> redisKeyList = vehicleNameList.stream()
        .map(tmp -> String.format(ISSUE_CACHE_KEY, tmp)).collect(Collectors.toList());
    List<Object> redisObjectList = clientRedisTemplate.opsForValue().multiGet(redisKeyList);
    Map<String, IssueCacheEntity> result = new HashMap<>(vehicleNameList.size());
    for (int i = 0; i < vehicleNameList.size(); i++) {
      IssueCacheEntity value = IssueCacheEntity.class.cast(redisObjectList.get(i));
      result.put(vehicleNameList.get(i), value);
    }
    return result;
  }
}
