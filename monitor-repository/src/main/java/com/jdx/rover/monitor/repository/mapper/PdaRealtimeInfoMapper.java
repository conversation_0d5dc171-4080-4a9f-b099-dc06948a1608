/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.PdaStatusStatisticInfoDTO;
import com.jdx.rover.monitor.po.pda.PdaRealtimeInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Pda设备实时信息表
 */
@Mapper
public interface PdaRealtimeInfoMapper extends BaseMapper<PdaRealtimeInfo> {

    @Select("SELECT realtime_status as param, count(*) as count FROM pda_realtime_info ${ew.customSqlSegment} GROUP BY realtime_status")
    public List<PdaStatusStatisticInfoDTO> getGroupCountByStatus(@Param(Constants.WRAPPER) LambdaQueryWrapper<PdaRealtimeInfo> pdaRealtimeInfoQueryWrapper);

    @Select("SELECT product_model_no as param, count(*) as count FROM pda_realtime_info ${ew.customSqlSegment} GROUP BY product_model_no")
    public List<PdaStatusStatisticInfoDTO> getGroupCountByModuleNo(@Param(Constants.WRAPPER) LambdaQueryWrapper<PdaRealtimeInfo> pdaRealtimeInfoQueryWrapper);

    @Update({ "<script>", "update rover_monitor.pda_realtime_info" +
            "<trim prefix=\"set\" suffixOverrides=\",\">",
            "<trim prefix=\"group_one =case\" suffix=\"end,\">",
            "<foreach collection=\"pdaList\" item=\"item\" index=\"index\">",
            "when device_name=#{item.deviceName} then #{item.groupOne}",
            "</foreach>",
            "</trim>",
            "<trim prefix=\"group_two =case\" suffix=\"end,\">",
            "<foreach collection=\"pdaList\" item=\"item\" index=\"index\">",
            "when device_name=#{item.deviceName} then #{item.groupTwo}",
            "</foreach>",
            "</trim>",
            "<trim prefix=\"group_three =case\" suffix=\"end,\">",
            "<foreach collection=\"pdaList\" item=\"item\" index=\"index\">",
            "when device_name=#{item.deviceName} then #{item.groupThree}",
            "</foreach>",
            "</trim>",
            "<trim prefix=\"group_name =case\" suffix=\"end,\">",
            "<foreach collection=\"pdaList\" item=\"item\" index=\"index\">",
            "when device_name=#{item.deviceName} then #{item.groupName}",
            "</foreach>",
            "</trim>",
            "</trim>",
            "where device_name in",
            "<foreach collection=\"pdaList\" index=\"index\" item=\"item\" separator=\",\" open=\"(\" close=\")\">",
            "#{item.deviceName}",
            "</foreach>", "</script>" })
    public void batchUpdateGroup(List<PdaRealtimeInfo> pdaList);


    @Update({ "<script>", "update rover_monitor.pda_realtime_info" +
            "<trim prefix=\"set\" suffixOverrides=\",\">",
            "<trim prefix=\"realtime_status =case\" suffix=\"end,\">",
            "<foreach collection=\"pdaList\" item=\"item\" index=\"index\">",
            "when device_name=#{item.deviceName} then #{item.realtimeStatus}",
            "</foreach>",
            "</trim>",
            "<trim prefix=\"network_type =case\" suffix=\"end,\">",
            "<foreach collection=\"pdaList\" item=\"item\" index=\"index\">",
            "when device_name=#{item.deviceName} then #{item.networkType}",
            "</foreach>",
            "</trim>",
            "<trim prefix=\"burial_status =case\" suffix=\"end,\">",
            "<foreach collection=\"pdaList\" item=\"item\" index=\"index\">",
            "when device_name=#{item.deviceName} then #{item.burialStatus}",
            "</foreach>",
            "</trim>",
            "<trim prefix=\"position_status =case\" suffix=\"end,\">",
            "<foreach collection=\"pdaList\" item=\"item\" index=\"index\">",
            "when device_name=#{item.deviceName} then #{item.positionStatus}",
            "</foreach>",
            "</trim>",
            "</trim>",
            "where device_name in",
            "<foreach collection=\"pdaList\" index=\"index\" item=\"item\" separator=\",\" open=\"(\" close=\")\">",
            "#{item.deviceName}",
            "</foreach>", "</script>" })
    public void batchUpdateState(List<PdaRealtimeInfo> pdaList);

    @Delete({"<script>", "delete from rover_monitor.pda_realtime_info where device_name in",
            "<foreach collection=\"pdaList\" index=\"index\" item=\"item\" separator=\",\" open=\"(\" close=\")\">",
            "#{item.deviceName}",
            "</foreach>","</script>"})
    public void batchDelete(List<PdaRealtimeInfo> pdaList);
}
