///***************************************************************************
// *
// * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
// *
// **************************************************************************/
//
//package com.jdx.rover.monitor.repository.mapper;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.jdx.rover.monitor.po.AccidentInfo;
//import org.apache.ibatis.annotations.Mapper;
//
///**
// * <p>
// * 事故信息 Mapper
// * </p>
// *
// * <AUTHOR>
// * @version 1.0
// */
////@Mapper
////public interface AccidentInfoMapper extends BaseMapper<AccidentInfo> {
////
////}
