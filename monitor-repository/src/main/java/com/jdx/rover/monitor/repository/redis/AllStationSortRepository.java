package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 所有车辆名称信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AllStationSortRepository {
  /**
   * 获取
   *
   * @return
   */
  @Cacheable(value = LocalCacheConstant.ALL_STATION_SORT_LIST, unless = "#result?.size() == 0",
      key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).ALL_STATION_SORT_LIST")
  public List<String> get() {
    List<String> stationSortList = RedissonUtils.getObject(RedisKeyEnum.ALL_STATION_SORT_LIST.getValue());
    if (CollectionUtils.isEmpty(stationSortList)) {
      log.info("AllStationSortRepository本地缓存数据未获取到!");
      return new ArrayList<>();
    }
    log.info("AllStationSortRepository本地缓存数据={}", stationSortList.size());
    return stationSortList;
  }

  /**
   * 设置
   *
   * @param stationSortList
   */
  @LocalCacheEvict(value = LocalCacheConstant.ALL_STATION_SORT_LIST)
  public void set(List<String> stationSortList) {
    RedissonUtils.setObject(RedisKeyEnum.ALL_STATION_SORT_LIST.getValue(), stationSortList);
  }

  /**
   * 通过车辆名称删除
   *
   * @param station
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.ALL_STATION_SORT_LIST)
  public void remove(String station) {
    List<String> stationSortList = RedissonUtils.getObject(RedisKeyEnum.ALL_STATION_SORT_LIST.getValue());
    stationSortList.remove(station);
    set(stationSortList);
  }
}
