package com.jdx.rover.monitor.repository.redis;

import com.google.common.collect.Lists;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 实时告警信息
 *
 * <AUTHOR>
 */
@Repository
public class VehicleAlarmRepository {

  /**
   * 通过车辆名称获取实时告警信息
   *
   * @param vehicleName
   * @return
   */
  public List<VehicleAlarmDO.VehicleAlarmEventDO> get(String vehicleName) {
    String redisKey = getKey(vehicleName);
    VehicleAlarmDO alarm = RedissonUtils.getObject(redisKey);
    return Optional.ofNullable(alarm).map(VehicleAlarmDO::getAlarmEventList).orElse(Lists.newArrayList());
  }

  /**
   * 通过车辆名称获取实时告警信息
   *
   * @param vehicleName
   * @return
   */
  public List<String> getAlarmTypeList(String vehicleName) {
    List<VehicleAlarmDO.VehicleAlarmEventDO> vehicleAlarmEventDTOList = this.get(vehicleName);
    List<String> alarmTypeList = vehicleAlarmEventDTOList.stream().map(VehicleAlarmDO.VehicleAlarmEventDO::getType).collect(Collectors.toList());
    return alarmTypeList;
  }

  /**
   * 通过车辆名称获取实时告警信息
   *
   * @param vehicleName
   * @return
   */
  public VehicleAlarmDO getVehicleAlarmDTO(String vehicleName) {
    String redisKey = getKey(vehicleName);
    VehicleAlarmDO alarm = RedissonUtils.getObject(redisKey);
    return alarm;
  }

  /**
   * 添加
   *
   * @param vehicleName
   * @param vehicleAlarmDo
   */
  public void add(String vehicleName, VehicleAlarmDO vehicleAlarmDo) {
    String redisKey = getKey(vehicleName);
    RedissonUtils.setObject(redisKey, vehicleAlarmDo);
  }

  /**
   * 删除
   *
   * @param vehicleName
   */
  public void remove(String vehicleName) {
    String redisKey = getKey(vehicleName);
    RedissonUtils.deleteObject(redisKey);
  }

  /**
   * 通过车辆名称获取告警
   *
   * @param vehicleNameList
   * @return
   */
  public Map<String, VehicleAlarmDO> listMap(List<String> vehicleNameList) {
    List<String> redisKeyList = vehicleNameList.stream().map(VehicleAlarmRepository::getKey)
        .collect(Collectors.toList());
    Map<String, VehicleAlarmDO> map = RedissonUtils.getRedissonClient()
        .getBuckets().get(redisKeyList.toArray(new String[0]));
    Map<String, VehicleAlarmDO> result = map.entrySet().stream().collect(Collectors.toMap(
        entry -> StringUtils.substringAfter(entry.getKey(), RedisKeyEnum.ALARM_VEHICLE_PREFIX.getValue()),
        entry -> entry.getValue()));
    return result;
  }

  /**
   * 通过车辆名称获取告警
   *
   * @param vehicleNameList
   * @return
   */
  public List<VehicleAlarmDO> list(List<String> vehicleNameList) {
    Map<String, VehicleAlarmDO> map = listMap(vehicleNameList);
    List<VehicleAlarmDO> list = new ArrayList(map.values());
    return list;
  }

  /**
   * 获取redis key
   *
   * @param vehicleName
   * @return
   */
  public static String getKey(String vehicleName) {
    String redisKey = RedisKeyEnum.ALARM_VEHICLE_PREFIX.getValue() + vehicleName;
    return redisKey;
  }

  /**
   * 获取最高优先级告警
   *
   * @param vehicleName
   * @return
   */
  public String getHighPriorityAlarm(String vehicleName) {
    List<VehicleAlarmDO.VehicleAlarmEventDO> vehicleAlarmEventList = this.get(vehicleName);
    if (CollectionUtils.isEmpty(vehicleAlarmEventList)) {
      return null;
    }
    return vehicleAlarmEventList.get(0).getType();
  }
}
