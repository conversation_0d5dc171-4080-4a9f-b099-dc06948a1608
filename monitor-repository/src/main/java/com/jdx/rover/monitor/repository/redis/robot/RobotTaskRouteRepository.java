/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.redis.robot;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.entity.device.RobotTaskRouteInfoDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

/**
 * 机器人任务规划信息
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Repository
public class RobotTaskRouteRepository {

  /**
   * 通过设备名称获取路径规划信息
   *
   * @param deviceName
   * @return
   */
  @Cacheable(value = LocalCacheConstant.ROBOT_TASK_ROUTE_INFO, unless = "#result == null", key = "#deviceName")
  public RobotTaskRouteInfoDO get(String deviceName) {
    String redisKey = getKey(deviceName);
    RobotTaskRouteInfoDO taskRouteInfoDo = RedissonUtils.getObject(redisKey);
    return taskRouteInfoDo;
  }

  /**
   * 添加
   *
   * @param deviceName
   * @param taskRouteInfoDo
   */
  @LocalCacheEvict(value = LocalCacheConstant.ROBOT_TASK_ROUTE_INFO, key = "#deviceName")
  public void add(String deviceName, RobotTaskRouteInfoDO taskRouteInfoDo) {
    String redisKey = getKey(deviceName);
    RedissonUtils.setObject(redisKey, taskRouteInfoDo);
  }

  /**
   * 删除
   *
   * @param deviceName
   */
  @LocalCacheEvict(value = LocalCacheConstant.ROBOT_TASK_ROUTE_INFO, key = "#deviceName")
  public void remove(String deviceName) {
    String redisKey = getKey(deviceName);
    RedissonUtils.deleteObject(redisKey);
  }

  /**
   * 获取redis key
   *
   * @param deviceName
   * @return
   */
  public static String getKey(String deviceName) {
    String redisKey = RedisKeyEnum.ROBOT_TASK_ROUTE_PREFIX.getValue() + deviceName;
    return redisKey;
  }

}
