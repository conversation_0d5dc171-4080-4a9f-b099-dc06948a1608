/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.repository.redis.mapcollection;

import com.google.common.collect.Lists;
import com.jdx.rover.monitor.vo.mapcollection.MarkCreateVO;
import com.jdx.rover.monitor.enums.mapcollection.MarkTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import java.util.List;
import java.util.Objects;
import org.redisson.api.GeoUnit;
import org.redisson.api.RGeo;
import org.redisson.api.geo.GeoSearchArgs;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/12/10 16:59
 * @description 地图勘查标记Redis
 */
@Repository
public class MapCollectionMarkRedis {

    /**
     * 勘查标记GeoHash
     * key: #0 markType, value: markId
     * @see com.jdx.rover.monitor.enums.mapcollection.MarkTypeEnum
     */
    public static final String MAP_COLLECTION_MARK_GEO = "collection:geo:mark:%s";

    /**
     * 默认查询半径
     */
    public static final Integer DEFAULT_RADIUS = 5000;

    /**
     * GeoHash新增member
     *
     * @param markCreateVO markCreateVO
     * @param markId markId
     */
    public void addGeoMember(MarkCreateVO markCreateVO, Integer markId) {
        String key = String.format(MAP_COLLECTION_MARK_GEO, MarkTypeEnum.of(markCreateVO.getMarkType()).getCode());
        RGeo<Integer> geo = RedissonUtils.getGeo(key);
        geo.add(markCreateVO.getLongitude(), markCreateVO.getLatitude(), markId);
    }

    /**
     * GeoHash删除member
     *
     * @param markId markId
     * @param markTypeEnum  markTypeEnum
     */
    public void removeGeoMember(Integer markId, MarkTypeEnum markTypeEnum) {
        String key = String.format(MAP_COLLECTION_MARK_GEO, markTypeEnum.getCode());
        RGeo<Integer> geo = RedissonUtils.getGeo(key);
        geo.remove(markId);
    }

    /**
     * 获取全部类型半径范围内的标记
     *
     * @param latitude latitude
     * @param longitude longitude
     * @param radius radius
     * @return markIdList
     */
    public List<Integer> searchAllTypeMember(Double latitude, Double longitude, Integer radius) {
        List<Integer> markIdList = Lists.newArrayList();
        markIdList.addAll(searchWithRadiusAndType(latitude, longitude, MarkTypeEnum.INTERSECTION, radius));
        markIdList.addAll(searchWithRadiusAndType(latitude, longitude, MarkTypeEnum.ROAD_GAP, radius));
        return markIdList;
    }

    /**
     * 根据位置、半径和类型获取标记
     * 查询半径可配置
     *
     * @param latitude latitude
     * @param longitude longitude
     * @param markTypeEnum markTypeEnum
     * @param radius radius
     * @return markIdList
     */
    private List<Integer> searchWithRadiusAndType(Double latitude, Double longitude, MarkTypeEnum markTypeEnum, Integer radius) {
        if (Objects.isNull(radius)) {
            radius = DEFAULT_RADIUS;
        }
        String key = String.format(MAP_COLLECTION_MARK_GEO, markTypeEnum.getCode());
        RGeo<Integer> geo = RedissonUtils.getGeo(key);
        return geo.search(GeoSearchArgs. from(longitude, latitude).radius(radius, GeoUnit.METERS));
    }
}
