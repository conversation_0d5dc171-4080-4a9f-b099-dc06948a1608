package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.entity.MonitorScheduleStopOrderEntity;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 车辆调度停靠点订单基本信息
 *
 * <AUTHOR>
 */
@Service
public class VehicleScheduleStopOrderRepository {
  /**
   * 通过车辆名称获取调度停靠点订单信息
   *
   * @param vehicleName
   * @return
   */
  public MonitorScheduleStopOrderEntity get(String vehicleName, Integer stopId, String stopAction) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_STOP_PREFIX.getValue() + vehicleName;
    String hashKey = new StringBuilder(stopId + "_").append(stopAction).toString();
    MonitorScheduleStopOrderEntity result = RedissonUtils.getMapObject(redisKey, hashKey);
    return Optional.ofNullable(result).orElse(new MonitorScheduleStopOrderEntity());
  }

  /**
   * 通过车辆名称获取调度停靠点订单信息
   *
   * @param vehicleName
   * @return
   */
  public Map<String, MonitorScheduleStopOrderEntity> getMap(String vehicleName) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_STOP_PREFIX.getValue() + vehicleName;
    Map<String, MonitorScheduleStopOrderEntity> result = RedissonUtils.getMap(redisKey);
    return Optional.ofNullable(result).orElse(new HashMap<>());
  }

  /**
   * 通过车辆名称设置调度停靠点订单信息
   *
   * @param dto
   * @return
   */
  public void set(String vehicleName, MonitorScheduleStopOrderEntity dto) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_STOP_PREFIX.getValue() + vehicleName;
    String hashKey = new StringBuilder(dto.getId() + "_").append(dto.getStopAction()).toString();
    RedissonUtils.setMapObject(redisKey, hashKey, dto);
  }

  /**
   * 通过车辆名称设置调度停靠点订单信息
   *
   * @param hashData
   * @return
   */
  public void batchSet(String vehicleName, Map<String, MonitorScheduleStopOrderEntity> hashData) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_STOP_PREFIX.getValue() + vehicleName;
    RedissonUtils.setMapObject(redisKey, hashData);
  }

  /**
   * 通过车辆名称删除调度停靠点订单信息
   *
   * @return
   */
  public void delete(String vehicleName) {
    String redisKey = RedisKeyEnum.SCHEDULE_VEHICLE_STOP_PREFIX.getValue() + vehicleName;
    RedissonUtils.deleteObject(redisKey);
  }
}
