/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.IssueAlarmRecord;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * This is an issue alarm repository which contains operations on data
 * base.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Mapper
public interface IssueAlarmRecordMapper extends BaseMapper<IssueAlarmRecord> {

  @Insert(" INSERT INTO issue_alarm_record(vehicle_name, alarm_type, alarm_code, alarm_no, timestamp, issue_no, source, selected) SELECT #{vehicleName}, #{alarmType}, #{alarmCode}, #{alarmNo}, #{timestamp}, #{issueNo}, #{source}, #{selected} FROM DUAL WHERE NOT EXISTS(SELECT id FROM issue_alarm_record WHERE issue_no = #{issueNo} and vehicle_name = #{vehicleName} and alarm_type = #{alarmType})")
  public void insertIfAbsent(IssueAlarmRecord record);
}
