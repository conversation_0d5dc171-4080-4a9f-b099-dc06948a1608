package com.jdx.rover.monitor.repository.feign.ticket;

import com.jdx.rover.ticket.api.api.IssueQueryApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @description: IssueQueryApiClient
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-06-11 18:26
 **/
@FeignClient(contextId = "ticket-query", value = "rover-ticket-web")
public interface IssueQueryApiClient extends IssueQueryApi {
}