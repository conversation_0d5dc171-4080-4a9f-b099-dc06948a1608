/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.datacenter;

import com.jdx.rover.datacenter.api.service.order.OrderAppletApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 数据中心订单服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "datacenter-order",value = "datacenter-web")
public interface DataCenterOrderApiClient extends OrderAppletApi {
}
