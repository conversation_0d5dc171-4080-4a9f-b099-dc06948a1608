/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.redis.robot;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * 机器人长时间停车信息
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Repository
public class RobotStopHistoryRepository {

  /**
   * 通过设备名称获取实时告警信息
   *
   * @param deviceName
   * @return
   */
  @Cacheable(value = LocalCacheConstant.ROBOT_STOP_INFO, unless = "#result == null", key = "#deviceName")
  public Date get(String deviceName) {
    String redisKey = getKey();
    Date alarmDate = RedissonUtils.getMapObject(redisKey, deviceName);
    return alarmDate;
  }

  /**
   * 添加15秒过期
   *
   * @param deviceName
   * @param currentDate
   */
  @LocalCacheEvict(value = LocalCacheConstant.ROBOT_STOP_INFO, key = "#deviceName")
  public void add(String deviceName, Date currentDate) {
    String redisKey = getKey();
    RedissonUtils.setMapObject(redisKey, deviceName, currentDate);
  }

  /**
   * 删除
   *
   * @param deviceName
   */
  @LocalCacheEvict(value = LocalCacheConstant.ROBOT_STOP_INFO, key = "#deviceName")
  public void remove(String deviceName) {
    String redisKey = getKey();
    RedissonUtils.getMap(redisKey).remove(deviceName);
  }

  /**
   * 获取redis key
   *
   * @return
   */
  public static String getKey() {
    String redisKey = RedisKeyEnum.ALARM_ROBOT_PREFIX.getValue();
    return redisKey;
  }

}
