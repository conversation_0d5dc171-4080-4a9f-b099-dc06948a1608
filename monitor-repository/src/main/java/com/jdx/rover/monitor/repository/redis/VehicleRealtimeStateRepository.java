package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.enums.VehicleRealtimeStateEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleGroupsSortEnum;
import com.jdx.rover.monitor.repository.redis.sort.VehicleNameScoreSortedSetRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 车辆实时状态信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleRealtimeStateRepository {
  @Autowired
  private VehicleNameScoreSortedSetRepository vehicleNameScoreSortedSetRepository;

  /**
   * 通过车辆名称获取实时状态
   *
   * @param vehicleNameList
   * @return
   */
  public Map<String, String> listMap(List<String> vehicleNameList) {
    List<Double> sortedValueSet = vehicleNameScoreSortedSetRepository.getScore(vehicleNameList);
    Map<String, String> result = new HashMap<>();
    for (int i = 0; i < vehicleNameList.size(); i++) {
      Double value = sortedValueSet.get(i);
      if (value == null) {
        continue;
      }
      if (VehicleGroupsSortEnum.CONNECTION_LOST.getValue().compareTo(value) <= 0 &&
              VehicleGroupsSortEnum.VEHICLE_CRASH.getValue().compareTo(value) > 0) {
        result.put(vehicleNameList.get(i), VehicleRealtimeStateEnum.CONNECTION_LOST.getValue());
      } else if (VehicleGroupsSortEnum.VEHICLE_CRASH.getValue().compareTo(value) <= 0 &&
              VehicleGroupsSortEnum.OFFLINE.getValue().compareTo(value) > 0) {
        result.put(vehicleNameList.get(i), VehicleRealtimeStateEnum.NORMAL.getValue());
      } else if (VehicleGroupsSortEnum.OFFLINE.getValue().compareTo(value) <= 0) {
        result.put(vehicleNameList.get(i), VehicleRealtimeStateEnum.OFFLINE.getValue());
      }
    }
    return result;
  }
}
