/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.s3;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * <p>
 * This is static class that provides configuration properties for rover service.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "s3")
@Data
@Validated
public class S3Properties {
  /**
   * <p>
   * The accsess key.
   * </p>
   */
  @NotNull
  @NotBlank
  private String accessKey = "iamrootone";

  /**
   * <p>
   * The secret key.
   * </p>
   */
  @NotNull
  @NotBlank
  private String secretKey = "iamroottwo";

  /**
   * <p>
   * The end point.
   * </p>
   */
  @NotNull
  @NotBlank
  private String endpoint = "http://localhost";

  /**
   * <p>
   * The out end point.
   * </p>
   */
  @NotNull
  @NotBlank
  private String outEndpoint = "http://localhost";

  /**
   * <p>
   * The region.
   * </p>
   */
  @NotNull
  @NotBlank
  private String region = "beijing";

  /**
   * <p>
   * The bucket signer override.
   * </p>
   */
  @NotNull
  @NotBlank
  private String signerOverride = "AWSS3V4SignerType";
  }