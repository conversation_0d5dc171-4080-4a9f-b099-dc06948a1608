package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.entity.UserAttentionEntity;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户关注站点记录
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserAttentionStationRepository {
  /**
   * 读取
   *
   * @param stationId
   * @return
   */
  public List<UserAttentionEntity> get(Integer stationId, String event) {
    String redisKey = getRedisKey(stationId);
    List<UserAttentionEntity> userNameList = RedissonUtils.getMapObject(redisKey,event);
    log.info("UserAttentionStationRepository本地缓存数据stationId={},数据={}", stationId, userNameList);
    if (CollectionUtils.isEmpty(userNameList)) {
      return new ArrayList<>();
    }
    return userNameList;
  }

  /**
   * 设置
   *
   * @param stationId
   * @return
   */
  public void set(Integer stationId, String event, UserAttentionEntity userInfo) {
    String redisKey = getRedisKey(stationId);
    List<UserAttentionEntity> userNameList = RedissonUtils.getMapObject(redisKey, event);
    if (CollectionUtils.isEmpty(userNameList)) {
      userNameList = new ArrayList<>();
    }
    if (userNameList.contains(userInfo)) {
      return;
    }
    userNameList.add(userInfo);
    RedissonUtils.setMapObject(redisKey, event, userNameList);
  }

  /**
   * 删除
   *
   * @return
   */
  public void delete(Integer stationId, String event, List<String> removeUserNameList) {
    String redisKey = getRedisKey(stationId);
    List<UserAttentionEntity> userNameList = RedissonUtils.getMapObject(redisKey, event);
    if (CollectionUtils.isEmpty(userNameList)) {
      return;
    }
    List<UserAttentionEntity> filterUserList =
            userNameList.stream().filter(user -> !removeUserNameList.contains(user.getUserName())).collect(Collectors.toList());
    RedissonUtils.setMapObject(redisKey, event, filterUserList);
  }

  /**
   * 判定是否存在
   *
   * @param stationId
   * @param userName
   * @return
   */
  public boolean isContainUser(Integer stationId, String event, String userName) {
    String redisKey = getRedisKey(stationId);
    List<UserAttentionEntity> attentionUserList = RedissonUtils.getMapObject(redisKey, event);
    if (CollectionUtils.isNotEmpty(attentionUserList)) {
      return attentionUserList.stream().anyMatch(user -> StringUtils.equals(userName, user.getUserName()));
    }
    return false;
  }

  /**
   * 通过站点id获取key
   *
   * @param stationId
   * @return
   */
  public static String getRedisKey(Integer stationId) {
    String redisKey = new StringBuilder(RedisKeyEnum.USER_ATTENTION_STATION_SET_PREFIX.getValue())
            .append(stationId).toString();
    return redisKey;
  }
}
