/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.cockpit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.cockpit.CockpitTeamStatusDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 坐席团队状态信息
 *
 * <AUTHOR>
 */
@Service
public class CockpitTeamStatusRepository {
    /**
     * 缓存名称
     */
    public static final String CACHE_NAME = "cockpitTeam:status:map:";
    /**
     * 记录时间字段
     */
    private static final String FIELD_RECORD_TIME = "recordTime";

    /**
     * 获取redis key
     *
     * @param cockpitTeamNumber 坐席编号
     */
    public static String getKey(String cockpitTeamNumber) {
        return CACHE_NAME + cockpitTeamNumber;
    }

    /**
     * 获取对象
     */
    @Cacheable(value = CACHE_NAME, key = "#cockpitTeamNumber")
    public CockpitTeamStatusDO get(String cockpitTeamNumber) {
        String redisKey = getKey(cockpitTeamNumber);
        RMap<String, Object> rMap = RedissonUtils.getMap(redisKey);
        Map<String, Object> mapValue = rMap.readAllMap();
        CockpitTeamStatusDO result = BeanUtil.toBean(mapValue, CockpitTeamStatusDO.class);
        result.setCockpitTeamNumber(cockpitTeamNumber);
        return result;
    }

    /**
     * 设置
     *
     * @param cockpitTeamNumber 坐席编号
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#cockpitTeamNumber")
    public void putMapValue(String cockpitTeamNumber, Func1<CockpitTeamStatusDO, ?> func, Object value) {
        Map<String, Object> paramMap = new ParamMap<CockpitTeamStatusDO>().addProperty(func, value).toMap();
        putAllMapObject(cockpitTeamNumber, paramMap);
    }

    /**
     * 移除
     *
     * @param cockpitTeamNumber 坐席编号
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#cockpitTeamNumber")
    public long fastRemoveMapKey(String cockpitTeamNumber, Func1<CockpitTeamStatusDO, ?>... func) {
        String redisKey = getKey(cockpitTeamNumber);
        List<String> fieldNameList = Arrays.stream(func).map(LambdaUtil::getFieldName).toList();
        RMap<String, Object> rMap = RedissonUtils.getMap(redisKey);
        return rMap.fastRemove(fieldNameList.toArray(new String[0]));
    }

    /**
     * 保存map数据
     *
     * @param cockpitTeamNumber 坐席编号
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#cockpitTeamNumber")
    public void putAllMapObject(String cockpitTeamNumber, Map<String, Object> mapObject) {
        mapObject.values().removeIf(Objects::isNull);
        if (Objects.isNull(mapObject.get(FIELD_RECORD_TIME))) {
            mapObject.put(FIELD_RECORD_TIME, new Date());
        }
        String redisKey = getKey(cockpitTeamNumber);
        RedissonUtils.putAllMapObject(redisKey, mapObject);
    }

    /**
     * 删除
     *
     * @param cockpitTeamNumber 坐席编号
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#cockpitTeamNumber")
    public boolean remove(String cockpitTeamNumber) {
        String redisKey = getKey(cockpitTeamNumber);
        return RedissonUtils.deleteObject(redisKey);
    }

    /**
     * 通过列表获取
     *
     * @param cockpitTeamNumberList 坐席编号列表
     */
    public Map<String, CockpitTeamStatusDO> listMap(List<String> cockpitTeamNumberList) {
        Map<String, CockpitTeamStatusDO> result = new HashMap<>(cockpitTeamNumberList.size());
        for (String cockpitTeamNumber : cockpitTeamNumberList) {
            CockpitTeamStatusDO dto = get(cockpitTeamNumber);
            result.put(cockpitTeamNumber, dto);
        }
        return result;
    }

    /**
     * 通过列表获取
     *
     * @param cockpitTeamNumberList 坐席编号列表
     */
    public List<CockpitTeamStatusDO> list(List<String> cockpitTeamNumberList) {
        Map<String, CockpitTeamStatusDO> map = listMap(cockpitTeamNumberList);
        return new ArrayList<>(map.values());
    }
}
