/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.schedule;

import com.jdx.rover.schedule.api.ql.dongdong.DDMessageNoticeApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 咚咚消息服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "schedule-ql",value = "rover-schedule-ql")
public interface DDMessageNoticeApiClient extends DDMessageNoticeApi {
}
