/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.repository.feign.map;


import com.jdx.rover.map.api.service.MapVariableApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 动态地图Feign接口
 *
 * <AUTHOR>
 * @date 2024/06/21
 */
@FeignClient(contextId = "map-variable",value = "rover-map-web")
public interface MapVariableApiClient  extends MapVariableApi {
}
