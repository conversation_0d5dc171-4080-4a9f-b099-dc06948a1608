/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.repository.redis.metadata;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.entity.device.RobotGroupInfoDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 机器人分组信息缓存
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@Slf4j
public class RobotGroupInfoRepository {

    /**
     * 获取
     *
     * @return
     */
    @Cacheable(value = LocalCacheConstant.ROBOT_GROUP_INFO, key = "#groupNo")
    public RobotGroupInfoDO get(String groupNo) {
        RobotGroupInfoDO robotGroupInfoDo = RedissonUtils.getObject(RedisKeyEnum.ROBOT_GROUP_INFO.getValue() + groupNo);
        log.info("RobotGroupInfoRepository本地缓存数据={}", robotGroupInfoDo);
        return robotGroupInfoDo;
    }

    /**
     * 设置
     *
     */
    @LocalCacheEvict(value = LocalCacheConstant.ROBOT_GROUP_INFO, key = "#groupInfoDo.groupNo")
    public void set(RobotGroupInfoDO groupInfoDo) {
        RedissonUtils.setObject(RedisKeyEnum.ROBOT_GROUP_INFO.getValue() + groupInfoDo.getGroupNo(), groupInfoDo);
    }
}
