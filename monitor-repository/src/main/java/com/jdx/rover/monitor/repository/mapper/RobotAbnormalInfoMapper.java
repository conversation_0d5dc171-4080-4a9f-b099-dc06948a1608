/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.robot.RobotAbnormalInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 机器人(多合一/巡检) 异常错误映射
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Mapper
public interface RobotAbnormalInfoMapper extends BaseMapper<RobotAbnormalInfo> {
}
