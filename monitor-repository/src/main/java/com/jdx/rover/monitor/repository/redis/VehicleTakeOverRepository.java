/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.redis;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监控车辆接管状态
 */
@Repository
public class VehicleTakeOverRepository {

  /**
   * <p>
   * the redis cache key.
   * </p>
   */
  static final String VEHICLE_TAKE_OVER_SOURCE = "vehicle_take_over_%s";

  static final String VEHICLE_TAKE_OVER_SOURCE_PREFIX = "vehicle_take_over_";


  @Cacheable(value = VEHICLE_TAKE_OVER_SOURCE_PREFIX, key = "#vehicleName")
  public VehicleTakeOverEntity get(String vehicleName) {
    String key = String.format(VEHICLE_TAKE_OVER_SOURCE, vehicleName);
    return RedissonUtils.getObject(key);
  }

  /**
   * <p>
   * Set vehicle take over info cache.
   * </p>
   *
   * @throws IllegalArgumentException if the argument is invalid.
   */
  @LocalCacheEvict(value = VEHICLE_TAKE_OVER_SOURCE_PREFIX, key = "#vehicleName")
  public void save(String vehicleName, String userName, String commandSource, String operationStatus) {
    ParameterCheckUtility.checkNotNullNorEmpty(userName, "userName");
    ParameterCheckUtility.checkNotNull(vehicleName, "vehicleName");
    ParameterCheckUtility.checkNotNullNorEmpty(commandSource, "commandSource");
    VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
    vehicleTakeOverEntity.setOperationStatus(operationStatus);
    vehicleTakeOverEntity.setUserName(userName);
    vehicleTakeOverEntity.setCommandSource(commandSource);
    String key = String.format(VEHICLE_TAKE_OVER_SOURCE, vehicleName);
    RedissonUtils.setObject(key, vehicleTakeOverEntity);
  }

  @LocalCacheEvict(value = VEHICLE_TAKE_OVER_SOURCE_PREFIX, key = "#vehicleName")
  public void save(String vehicleName, VehicleTakeOverEntity vehicleTakeOverEntity) {
    String key = String.format(VEHICLE_TAKE_OVER_SOURCE, vehicleName);
    RedissonUtils.setObject(key, vehicleTakeOverEntity);
  }

  /**
   * <p>
   * Get vehicle take over info cache.
   * </p>
   *
   * @param vehicleName the key of vehicle name
   * @throws IllegalArgumentException   if the argument is invalid.
   */
  public Optional<VehicleTakeOverEntity> getByKey(String vehicleName) {
    String key = String.format(VEHICLE_TAKE_OVER_SOURCE, vehicleName);
    boolean hasKey = RedissonUtils.hasKey(key);
    if (!hasKey) {
      return Optional.absent();
    }
    VehicleTakeOverEntity takeOverEntity = RedissonUtils.getObject(key);
    if (Objects.isNull(takeOverEntity)) {
      return Optional.absent();
    }
    return Optional.fromNullable(takeOverEntity);
  }

  /**
   * <p>
   * Delete vehicle take over info cache.
   * </p>
   *
   * @param vehicleName the key of vehicle name
   * @throws IllegalArgumentException if the argument is invalid.
   */
  @LocalCacheEvict(value = VEHICLE_TAKE_OVER_SOURCE_PREFIX, key = "#vehicleName")
  public void remove(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    String key = String.format(VEHICLE_TAKE_OVER_SOURCE, vehicleName);
    RedissonUtils.deleteObject(key);
  }

  /**
   * 判断是否存在接管
   *
   * @param vehicleName
   * @return
   */
  public Boolean haskey(String vehicleName) {
    String key = String.format(VEHICLE_TAKE_OVER_SOURCE, vehicleName);
    return RedissonUtils.hasKey(key);
  }

  /**
   * 批量获取车辆接管状态
   *
   * @param vehicleNameList
   * @return
   */
  public Map<String, VehicleTakeOverEntity> listMap(List<String> vehicleNameList) {
    List<String> redisKeyList = vehicleNameList.stream().map(vehicleName -> String.format(VEHICLE_TAKE_OVER_SOURCE, vehicleName))
            .collect(Collectors.toList());
    Map<String, VehicleTakeOverEntity> map = RedissonUtils.getRedissonClient()
            .getBuckets().get(redisKeyList.toArray(new String[0]));
    Map<String, VehicleTakeOverEntity> result = map.entrySet().stream().collect(Collectors.toMap(
            entry -> StringUtils.substringAfter(entry.getKey(), VEHICLE_TAKE_OVER_SOURCE_PREFIX),
            entry -> entry.getValue(), (v1, v2) -> v1));
    return result;
  }

  /**
   * 判断用户接管的车辆
   *
   * @param userName
   * @return
   */
  public List<VehicleTakeOverEntity> getTakeOverVehicle(String userName, List<String> vehicleNameList) {
    List<List<String>> vehicleNamePartitionList = Lists.partition(Lists.newArrayList(vehicleNameList), 1000);
    List<VehicleTakeOverEntity> takeOverEntities = new ArrayList<>();
    vehicleNamePartitionList.stream().forEach(vehicleList -> {
      List<String> redisKeyList = vehicleNameList.stream().map(vehicleName -> String.format(VEHICLE_TAKE_OVER_SOURCE, vehicleName))
              .collect(Collectors.toList());
      Map<String, VehicleTakeOverEntity> map = RedissonUtils.getRedissonClient()
              .getBuckets().get(redisKeyList.toArray(new String[0]));
      takeOverEntities.addAll(map.values().stream().filter(entity -> !Objects.isNull(entity) && StringUtils.equals(entity.getUserName(), userName)).collect(Collectors.toList()));
    });
    return takeOverEntities;
  }

  /**
   * <p>
   * Calculate the remaining seconds of current day
   * </p>
   *
   * @return The remaining seconds of current day
   */
  private Long getRemaingSeconds() {
    Date currentDate = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
    return DateUtil.between(currentDate, DateUtil.endOfDay(currentDate), DateUnit.SECOND);
  }


}
