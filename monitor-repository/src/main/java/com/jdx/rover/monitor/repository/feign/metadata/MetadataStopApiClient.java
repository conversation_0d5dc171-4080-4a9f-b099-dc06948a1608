/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.metadata.api.service.v2.stop.StopBasicApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 停靠点基础数据客户端
 *
 * <AUTHOR>
 * @date 2023-06-19
 */
@FeignClient(contextId = "metadata-stop",value = "metadata-web")
public interface MetadataStopApiClient extends StopBasicApi {
}
