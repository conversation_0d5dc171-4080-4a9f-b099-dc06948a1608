package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基本信息
 *
 * <AUTHOR>
 */
@Service
public class VehicleBasicRepository {
  /**
   * 通过车辆名称获取基本信息
   *
   * @param vehicleName
   * @return
   */
  @Cacheable(value = LocalCacheConstant.BASIC_VEHICLE, unless = "#result == null", key = "#vehicleName")
  public VehicleBasicDTO get(String vehicleName) {
    String redisKey = getKey(vehicleName);
    VehicleBasicDTO result = RedissonUtils.getObject(redisKey);
    return result;
  }

  /**
   * 通过车辆名称获取基本信息
   *
   * @param vehicleNameList
   * @return
   */
  public Map<String, VehicleBasicDTO> listMap(List<String> vehicleNameList) {
    List<String> redisKeyList = vehicleNameList.stream().map(VehicleBasicRepository::getKey).collect(Collectors.toList());
    Map<String, VehicleBasicDTO> map = RedissonUtils.getRedissonClient().getBuckets()
        .get(redisKeyList.toArray(new String[0]));
    Map<String, VehicleBasicDTO> result = map.entrySet().stream().collect(Collectors.toMap(
        entry -> StringUtils.substringAfter(entry.getKey(), RedisKeyEnum.BASIC_VEHICLE_PREFIX.getValue()),
        entry -> entry.getValue()));
    return result;
  }

  /**
   * 通过车辆名称获取基本信息
   *
   * @param vehicleNameList
   * @return
   */
  public List<VehicleBasicDTO> list(List<String> vehicleNameList) {
    Map<String, VehicleBasicDTO> map = listMap(vehicleNameList);
    List<VehicleBasicDTO> list = new ArrayList<>(map.values());
    return list;
  }

  /**
   * 设置
   *
   * @param dto
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.BASIC_VEHICLE, key = "#dto.name")
  public void set(VehicleBasicDTO dto) {
    String redisKey = getKey(dto.getName());
    RedissonUtils.setObject(redisKey, dto);
  }

  /**
   * 删除
   *
   * @param vehicleName
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.BASIC_VEHICLE, key = "#vehicleName")
  public void remove(String vehicleName) {
    String redisKey = getKey(vehicleName);
    RedissonUtils.deleteObject(redisKey);
  }


  /**
   * 获取redis key
   *
   * @param vehicleName
   * @return
   */
  private static String getKey(String vehicleName) {
    String redisKey = RedisKeyEnum.BASIC_VEHICLE_PREFIX.getValue() + vehicleName;
    return redisKey;
  }
}
