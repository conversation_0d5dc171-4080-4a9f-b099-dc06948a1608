/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.drive;

import com.jdx.rover.monitor.entity.drive.ControlPadRequestIdDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.stereotype.Service;

/**
 * 驾舱操作ID Repository
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Service
public class CockpitRequestIdRepository {
    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "controlPad:requestIdMap";

    /**
     * 查询
     */
    public ControlPadRequestIdDO get(String requestId) {
        return RedissonUtils.getMapObject(CACHE_NAME, requestId);
    }

    /**
     * 设置
     */
    public boolean set(ControlPadRequestIdDO dto) {
        return RedissonUtils.fastPutMapValue(CACHE_NAME, dto.getRequestId(), dto);
    }

    /**
     * 删除
     */
    public long remove(String requestId) {
        return RedissonUtils.getMap(CACHE_NAME).fastRemove(requestId);
    }
}
