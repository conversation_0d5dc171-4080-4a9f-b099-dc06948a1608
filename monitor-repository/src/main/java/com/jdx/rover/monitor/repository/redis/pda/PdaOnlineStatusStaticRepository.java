/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.repository.redis.pda;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.entity.pda.PdaGroupOnlineStatusDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * pda更新实时状态暂存
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PdaOnlineStatusStaticRepository {
    /**
     * 缓存名称
     */
    public static final String CACHE_NAME = "pda:online:status:map";
    /**
     * 总数据统计
     */
    public static final String TOTAL_KEY = "ALL";

    /**
     * 获取值
     *
     */
    public PdaGroupOnlineStatusDO get(String groupNo) {
        PdaGroupOnlineStatusDO statusDo = RedissonUtils.getMapObject(CACHE_NAME, groupNo);
        return statusDo;
    }

    /**
     * 批量设置对象
     */
    public void putMapAll(Map<String, PdaGroupOnlineStatusDO> pdaGroupOnlineStatusMap) {
        RMap<String, PdaGroupOnlineStatusDO> rMap = RedissonUtils.getMap(CACHE_NAME);
        log.info("批量更新设备在离线统计{}", JsonUtils.writeValueAsString(rMap.readAllMap()));
        rMap.clear();
        rMap.putAll(pdaGroupOnlineStatusMap);
    }

    /**
     * 通过分组列表获取对象
     *
     * @param groupNameList 名称列表
     */
    public Map<String, PdaGroupOnlineStatusDO> listMap(List<String> groupNameList) {
        Map<String, PdaGroupOnlineStatusDO> result = new HashMap<>(groupNameList.size());
        for (String groupName : groupNameList) {
            PdaGroupOnlineStatusDO dto = get(groupName);
            result.put(groupName, dto);
        }
        return result;
    }

    /**
     * 获取所有对象
     *
     */
    public Map<String, PdaGroupOnlineStatusDO> listMap() {
        RMap<String, PdaGroupOnlineStatusDO> rMap = RedissonUtils.getMap(CACHE_NAME);
        return rMap.readAllMap();
    }

    /**
     * 通过分组名称获取对象
     *
     * @param groupNameList 名称列表
     */
    public Map<String, PdaGroupOnlineStatusDO> list(List<String> groupNameList) {
        Map<String, PdaGroupOnlineStatusDO> map = listMap(groupNameList);
        return map;
    }
}