/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.permission.api.service.basic.UserInfoBasicApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 用户权限数据客户端
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "rover-permission-server",value = "rover-permission-server")
public interface MetadataUserApiClient extends UserInfoBasicApi {
}