package com.jdx.rover.monitor.repository.redis;

import com.jd.rover.ota.api.domain.dto.ApplicationIssueInfoDto;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import org.springframework.stereotype.Service;

/**
 * 基本信息
 *
 * <AUTHOR>
 */
@Service
public class VehicleVersionRepository {

  private static final long expire = 600L;
  /**
   * 通过车辆名称获取云端信息
   *
   * @param vehicleName
   * @return
   */
  public ApplicationIssueInfoDto getCloudVersion(String vehicleName) {
    String redisKey = getKey(vehicleName);
    ApplicationIssueInfoDto result = RedissonUtils.getObject(redisKey);
    return result;
  }

  /**
   * 设置
   *
   * @param dto
   * @return
   */
  public void set(ApplicationIssueInfoDto dto) {
    String redisKey = getKey(dto.getVehicleName());
    RedissonUtils.setObject(redisKey, dto, expire);
  }

  /**
   * 获取redis key
   *
   * @param vehicleName
   * @return
   */
  private static String getKey(String vehicleName) {
    String redisKey = RedisKeyEnum.VEHICLE_CLOUD_VERSION_PREFIX.getValue() + vehicleName;
    return redisKey;
  }
}
