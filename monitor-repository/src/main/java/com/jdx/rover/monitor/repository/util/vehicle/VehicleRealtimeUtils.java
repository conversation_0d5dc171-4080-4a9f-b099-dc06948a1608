package com.jdx.rover.monitor.repository.util.vehicle;

import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车辆实时信息获取工具类
 *
 * <AUTHOR>
 */
public class VehicleRealtimeUtils {
  /**
   * 获取系统状态
   *
   * @param vehicleRealtimeInfoDTO
   * @return
   */
  public static String getSystemState(VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO) {
    String systemState;
    if (vehicleRealtimeInfoDTO != null && vehicleRealtimeInfoDTO.getSystemState() != null) {
      systemState = vehicleRealtimeInfoDTO.getSystemState();
    } else {
      systemState = SystemStateEnum.OFFLINE.getSystemState();
    }
    return systemState;
  }

  /**
   * 获取电量
   *
   * @param vehicleRealtimeInfoDTO
   * @return
   */
  public static double getPower(VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO) {
    double power;
    if (vehicleRealtimeInfoDTO == null || vehicleRealtimeInfoDTO.getPower() == null) {
      power = 0.0;
    } else {
      power = vehicleRealtimeInfoDTO.getPower();
    }
    return power;
  }

  /**
   * 获取胎压
   *
   * @param vehicleRealtimeInfoDTO
   * @return
   */
  public static Map<String, VehicleRealtimeWheelInfoDTO> getWheelInfo(VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO) {
    Map<String, VehicleRealtimeWheelInfoDTO> wheelInfo = new HashMap<>();
    if (vehicleRealtimeInfoDTO == null || vehicleRealtimeInfoDTO.getWheelInfo() == null) {
      return wheelInfo;
    }
    return vehicleRealtimeInfoDTO.getWheelInfo().stream().collect(
            Collectors.toMap(VehicleRealtimeWheelInfoDTO:: getWheelPosition, Function.identity()));
  }
}
