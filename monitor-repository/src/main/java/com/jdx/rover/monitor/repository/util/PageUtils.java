package com.jdx.rover.monitor.repository.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlInjectionUtils;
import com.baomidou.mybatisplus.core.toolkit.support.ColumnCache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.exception.AppException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Slf4j
public class PageUtils {

  /**
   * 把通用pageVO输入,转换成mybatis-plus需要的page入参格式
   *
   * @param pageVO 本系统分页对象
   * @return MyBatisPlus 分页
   */
  public static Page convertPageVO(PageVO pageVO) {
    Page page = new Page(pageVO.getPageNum(), pageVO.getPageSize());
    if (CollectionUtils.isEmpty(pageVO.getOrders())) {
      return page;
    }
    for (com.jdx.rover.common.domain.page.OrderItem order : pageVO.getOrders()) {
      OrderItem orderItem = new OrderItem();
      orderItem.setColumn(order.getColumn());
      orderItem.setAsc(order.isAsc());
      page.addOrder(orderItem);
    }
    return page;
  }

  /**
   * 把mybatis-plus的IPage输入,转换成自定义的PageDTO对象
   *
   * @param page mybatis-plus分页对象
   * @param function mybatis-plus 查询结果对象与响应真实对象转换方法
   * @return 需要返回的本系统分页对象
   */
  public static <T,R> PageDTO<T> convertPageDTO(IPage<R> page, Function<R,T> function) {
    PageDTO<T> pageDTO = new PageDTO<>();
    pageDTO.setTotal(page.getTotal());
    pageDTO.setPages((int) page.getPages());
    pageDTO.setPageSize((int) page.getSize());
    pageDTO.setPageNum((int) page.getCurrent());
    if (CollectionUtils.isEmpty(page.getRecords())){
      return pageDTO;
    }

    //执行对象转换
    List<T> result = new ArrayList<>(page.getRecords().size());
    for (R record : page.getRecords()) {
      result.add(function.apply(record));
    }
    pageDTO.setList(result);
    return pageDTO;
  }

  /**
   * 把mybatis-plus的IPage输入,转换成自定义的PageDTO对象
   *
   * @param page mybatis-plus分页对象
   * @return 需要返回的本系统分页对象
   */
  public static <T> PageDTO<T> convertPageDTO(IPage<T> page) {
    PageDTO<T> pageDTO = new PageDTO<>();
    pageDTO.setTotal(page.getTotal());
    pageDTO.setPageNum((int) page.getPages());
    pageDTO.setPageSize((int) page.getSize());
    if (CollectionUtils.isEmpty(page.getRecords())){
      return pageDTO;
    }
    pageDTO.setList(page.getRecords());
    return pageDTO;
  }

  /**
   * 把通用pageVO输入,转换成mybatis-plus需要的page入参格式
   *
   * @param pageVO rover 分页对象
   * @param paramClass 数据表映射实体类
   * @return MyBatisPlus 分页对象
   */
  public static <T> Page<T> toMpPage(PageVO pageVO,Class<T> paramClass) {

    //创建MyBatisPlus 分页对象
    Page<T> page = new Page(pageVO.getPageNum(), pageVO.getPageSize());

    //如果没有排序或没有实体Class传入则默认为不需要排序
    if (CollectionUtils.isEmpty(pageVO.getOrders()) || paramClass == null) {
      return page;
    }

    //获取属性名缓存（MybatisPlus 工具方法）
    //columnMap 中key值均使用大写，get时需要进行转换后获取，否则拿不到
    Map<String, ColumnCache> columnMap = LambdaUtils.getColumnMap(paramClass);

    //获取不到属性映射不进行排序处理
    if (columnMap == null){
      log.info("未能获取参数映射class:{}",paramClass);
      return page;
    }

    //排序字段处理
    pageVO.getOrders().forEach(orderItem -> {

      //传入参数校验，防止SQL注入
      if (SqlInjectionUtils.check(orderItem.getColumn())) {
        throw new AppException("非法参数:"+orderItem.getColumn());
      }

      //获取实体类字段名与数据库字段迎映射关系 目前只支持单实体方法处理（单表查询） 如有需求进行拓展
      ColumnCache columnCache = columnMap.get(orderItem.getColumn().toUpperCase());

      //未能获取到映射关系则不进行处理，并进行日志输出方便问题排查
      if (columnCache == null){
        log.info("未识别的参数:{}",orderItem.getColumn());
        return;
      }
      if(orderItem.isAsc()){
        page.addOrder(OrderItem.asc(columnCache.getColumn()));
      } else {
        page.addOrder(OrderItem.desc(columnCache.getColumn()));
      }
    });
    return page;
  }
}
