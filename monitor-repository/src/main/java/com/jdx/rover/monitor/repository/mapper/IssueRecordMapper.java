/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.IssueRecord;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * This is an issue record repository which contains operations on data base.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Mapper
public interface IssueRecordMapper extends BaseMapper<IssueRecord> {

  @Update({ "<script>", "UPDATE issue_record", "<set>", "<if test = 'jiraNo != null'>jira_no = #{jiraNo},</if>", "<if test = 'state != null'>state = #{state},</if></set>",
      "WHERE issue_no=#{issueNo}", "</script>" })
  public int addJiraNo(@Param(value = "issueNo") String issueNo, @Param(value = "state") String state, @Param(value = "jiraNo") String jiraNo);

  @Update({ "<script>", "UPDATE issue_record", "<set>", "<if test = 'state != null'>state = #{currentState},</if></set>",
          "WHERE state=#{state}", "</script>" })
  public int updateState(@Param(value = "state") String state, @Param(value = "currentState") String currentState);

  @Select("SELECT id, issue_no, vehicle_name, vehicle_business_type, alarm_type, city_name, station_name, source, state, report_user, operate_user, report_time, alarm_time, start_time, end_time, jira_no, result FROM issue_record WHERE issue_no=#{issueNo}")
  public IssueRecord getByIssueNo(@Param("issueNo") String issueNo);

}
