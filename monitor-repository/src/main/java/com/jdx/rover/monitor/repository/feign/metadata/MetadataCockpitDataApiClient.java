package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.metadata.api.service.v2.cockpit.CockpitDataApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @description: MetadataCockpitDataApiClient
 * @author: wangguotai
 * @create: 2024-06-20 09:20
 **/
@FeignClient(contextId = "metadata-cockpit-data", value = "metadata-web")
public interface MetadataCockpitDataApiClient extends CockpitDataApi {
}