package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import org.springframework.stereotype.Repository;

/**
 * 车辆连接告警信息
 *
 * <AUTHOR>
 */
@Repository
public class VehicleGuardianConnectRepository {

  /**
   * 通过车辆名称获取实时告警信息
   *
   * @param vehicleName
   * @return
   */
  public VehicleAlarmEventDTO get(String vehicleName) {
    String redisKey = getKey(vehicleName);
    VehicleAlarmEventDTO alarm = RedissonUtils.getObject(redisKey);
    return alarm;
  }

  /**
   * 添加
   *
   * @param vehicleName
   * @param vehicleAlarmDto
   */
  public void add(String vehicleName, VehicleAlarmEventDTO vehicleAlarmDto) {
    String redisKey = getKey(vehicleName);
    RedissonUtils.setObject(redisKey, vehicleAlarmDto);
  }

  /**
   * 删除
   *
   * @param vehicleName
   */
  public void remove(String vehicleName) {
    String redisKey = getKey(vehicleName);
    RedissonUtils.deleteObject(redisKey);
  }

  /**
   * 获取redis key
   *
   * @param vehicleName
   * @return
   */
  public static String getKey(String vehicleName) {
    String redisKey = RedisKeyEnum.GUARDIAN_CONNECT_VEHICLE_PREFIX.getValue() + vehicleName;
    return redisKey;
  }

}
