package com.jdx.rover.monitor.repository.feign.schedule;

import com.jdx.rover.schedule.api.schedule.VehicleMqttCommandApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2024/7/16 18:45
 * @description 调度远程指令
 */
@FeignClient(contextId = "schedule-vehicle-mqtt", value = "rover-schedule-web")
public interface VehicleMqttCommandApiClient extends VehicleMqttCommandApi {

}
