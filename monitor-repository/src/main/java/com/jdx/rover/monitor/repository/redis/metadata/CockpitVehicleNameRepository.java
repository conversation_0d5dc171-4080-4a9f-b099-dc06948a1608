package com.jdx.rover.monitor.repository.redis.metadata;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RList;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 驾驶舱与车辆绑定关系
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CockpitVehicleNameRepository {
  /**
   * 读取
   *
   * @param cockpitNumber
   * @return
   */
  @Cacheable(value = LocalCacheConstant.COCKPIT_VEHICLE_NAME_SET, key = "#cockpitNumber")
  public Set<String> get(String cockpitNumber) {
    String redisKey = getRedisKey(cockpitNumber);
    RList<String> rSet = RedissonUtils.getRedissonClient().getList(redisKey);
    Collection<String> result = rSet.readAll();
    log.info("cockpitVehicleNameRepository本地缓存数据cockpitNumber={},数据={}", cockpitNumber, result.size());
    return new TreeSet<>(result);
  }

  /**
   * 初始化设置(先删除,后写入)
   *
   * @param cockpitNumber
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.COCKPIT_VEHICLE_NAME_SET, key = "#cockpitNumber")
  public boolean initSet(String cockpitNumber, List<String> vehicleNameList) {
    String redisKey = getRedisKey(cockpitNumber);
    RList<String> vehicleNameSet = RedissonUtils.getRedissonClient().getList(redisKey);
    vehicleNameSet.delete();
    vehicleNameList.removeIf(Objects::isNull);
    boolean result = true;
    if (CollectionUtils.isNotEmpty(vehicleNameList)) {
      List<String> vehicleNameSortList = vehicleNameList.stream().sorted().distinct().collect(Collectors.toList());
      result = vehicleNameSet.addAll(vehicleNameSortList);
    }
    return result;
  }


  /**
   * 通过驾舱名获取redisKey
   *
   * @param cockpitNumber
   * @return
   */
  public static String getRedisKey(String cockpitNumber) {
    String redisKey = RedisKeyEnum.COCKPIT_VEHICLE_NAME_SET_PREFIX.getValue() + cockpitNumber;
    return redisKey;
  }
}
