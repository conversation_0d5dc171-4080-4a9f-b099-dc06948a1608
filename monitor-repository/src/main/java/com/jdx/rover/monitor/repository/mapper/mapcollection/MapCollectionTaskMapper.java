/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.repository.mapper.mapcollection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTask;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:58
 * @description 勘查任务表
 */
@Mapper
public interface MapCollectionTaskMapper extends BaseMapper<MapCollectionTask> {

}
