/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.redis.robot;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.entity.device.RobotScheduleDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机器人调度信息
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Repository
public class RobotScheduleRepository {

  /**
   * 通过设备名称获取调度任务
   *
   * @param deviceName
   * @return
   */
  @Cacheable(value = LocalCacheConstant.ROBOT_SCHEDULE_INFO, unless = "#result == null", key = "#deviceName")
  public RobotScheduleDO get(String deviceName) {
    String redisKey = getKey(deviceName);
    RobotScheduleDO issueDO = RedissonUtils.getObject(redisKey);
    return issueDO;
  }

  /**
   * 添加
   *
   * @param deviceName
   * @param issueDo
   */
  @LocalCacheEvict(value = LocalCacheConstant.ROBOT_SCHEDULE_INFO, key = "#deviceName")
  public void set(String deviceName, RobotScheduleDO issueDo) {
    String redisKey = getKey(deviceName);
    RedissonUtils.setObject(redisKey, issueDo);
  }

  /**
   * 删除
   *
   * @param deviceName
   */
  @LocalCacheEvict(value = LocalCacheConstant.ROBOT_SCHEDULE_INFO, key = "#deviceName")
  public void remove(String deviceName) {
    String redisKey = getKey(deviceName);
    RedissonUtils.deleteObject(redisKey);
  }

  /**
   * 通过设备名称列表获取调度任务
   *
   * @param deviceNameList
   * @return
   */
  public Map<String, RobotScheduleDO> listMap(List<String> deviceNameList) {
    List<String> redisKeyList = deviceNameList.stream().map(RobotScheduleRepository::getKey)
            .collect(Collectors.toList());
    Map<String, RobotScheduleDO> map = RedissonUtils.getRedissonClient()
            .getBuckets().get(redisKeyList.toArray(new String[0]));
    Map<String, RobotScheduleDO> result = map.entrySet().stream().collect(Collectors.toMap(
            entry -> StringUtils.substringAfter(entry.getKey(), RedisKeyEnum.ROBOT_SCHEDULE_PREFIX.getValue()),
            entry -> entry.getValue()));
    return result;
  }

  /**
   * 通过机器人名称获取调度任务
   *
   * @param deviceNameList
   * @return
   */
  public List<RobotScheduleDO> list(List<String> deviceNameList) {
    Map<String, RobotScheduleDO> map = listMap(deviceNameList);
    List<RobotScheduleDO> list = new ArrayList(map.values());
    return list;
  }

  /**
   * 获取redis key
   *
   * @param deviceName
   * @return
   */
  public static String getKey(String deviceName) {
    String redisKey = RedisKeyEnum.ROBOT_SCHEDULE_PREFIX.getValue() + deviceName;
    return redisKey;
  }

}
