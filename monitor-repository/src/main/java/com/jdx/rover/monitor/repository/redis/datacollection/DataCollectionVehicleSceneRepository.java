/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.datacollection;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.entity.datacollection.DataCollectionVehicleSceneDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 数采车采集场景缓存
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Service
public class DataCollectionVehicleSceneRepository {

    private static final String CACHE_NAME = "data:collection:vehicle:scene:";

    /**
     * 通过车辆名称获取实时信息
     *
     * @param vehicleName vehicleName
     * @return DataCollectionVehicleSceneDO
     */
    @Cacheable(value = LocalCacheConstant.DATA_COLLECTION_VEHICLE_SCENE, key = "#vehicleName")
    public DataCollectionVehicleSceneDO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.getObject(redisKey);
    }

    /**
     * 设置
     *
     */
    @LocalCacheEvict(value = LocalCacheConstant.DATA_COLLECTION_VEHICLE_SCENE, key = "#vehicleName")
    public void set(String vehicleName, DataCollectionVehicleSceneDO vehicleSceneDo) {
        String redisKey = getKey(vehicleName);
        // 设置过期时间+空5秒余量
        long expire = DateUtil.between(vehicleSceneDo.getReportTime(), vehicleSceneDo.getEndTime(), DateUnit.SECOND) + 5;
        RedissonUtils.setObject(redisKey, vehicleSceneDo, expire);
    }

    /**
     * 获取redis key
     *
     * @param vehicleName vehicleName
     * @return redis key
     */
    public static String getKey(String vehicleName) {
        return CACHE_NAME + vehicleName;
    }
}
