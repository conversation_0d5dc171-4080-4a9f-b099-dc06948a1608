/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 车辆状态
 *
 * <AUTHOR>
 */
@Service
public class VehicleStatusRepository {

    private static final String CACHE_NAME = "vehicle:status:map:";
    private static final String FIELD_RECORD_TIME = "recordTime";

    /**
     * 通过车辆名称获取实时信息
     *
     * @param vehicleName
     * @return
     */
    @Cacheable(value = CACHE_NAME, key = "#vehicleName")
    public VehicleStatusDO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        RMap<String, String> rMap = RedissonUtils.getMap(redisKey);
        Map<String, String> mapValue = rMap.readAllMap();
        VehicleStatusDO result = BeanUtil.mapToBean(mapValue, VehicleStatusDO.class, false, CopyOptions.create());
        result.setVehicleName(vehicleName);
        return result;
    }

    /**
     * 设置状态值
     *
     * @param vehicleName
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void putMapValue(String vehicleName, Func1<VehicleStatusDO, ?> func, String value) {
        Map<String, Object> paramMap = new ParamMap<VehicleStatusDO>().addProperty(func, value).toMap();
        putAllMapObject(vehicleName, paramMap);
    }

    /**
     * 移除状态值
     *
     * @param vehicleName
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public long fastRemoveMapKey(String vehicleName, Func1<VehicleStatusDO, ?>... func) {
        String redisKey = getKey(vehicleName);
        List<String> fieldNameList = Arrays.stream(func).map(LambdaUtil::getFieldName).collect(Collectors.toList());
        RMap<String, String> rMap = RedissonUtils.getMap(redisKey);
        return rMap.fastRemove(fieldNameList.toArray(new String[0]));
    }

    /**
     * 保存map数据
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void putAllMapObject(String vehicleName, Map<String, Object> mapObject) {
        mapObject.values().removeIf(Objects::isNull);
        if (Objects.isNull(mapObject.get(FIELD_RECORD_TIME))) {
            mapObject.put(FIELD_RECORD_TIME, new Date());
        }
        String redisKey = getKey(vehicleName);
        RedissonUtils.putAllMapObject(redisKey, mapObject);
    }

    /**
     * 通过车辆名称删除实时信息
     *
     * @param vehicleName
     * @return
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public boolean remove(String vehicleName) {
        String redisKey = getKey(vehicleName);
        boolean result = RedissonUtils.deleteObject(redisKey);
        return result;
    }

    /**
     * 通过车辆名称获取实时
     *
     * @param vehicleNameList
     * @return
     */
    public Map<String, VehicleStatusDO> listMap(List<String> vehicleNameList) {
        Map<String, VehicleStatusDO> result = new HashMap<>(vehicleNameList.size());
        for (String vehicleName : vehicleNameList) {
            VehicleStatusDO dto = get(vehicleName);
            result.put(vehicleName, dto);
        }
        return result;
    }

    /**
     * 通过车辆名称获取实时
     *
     * @param vehicleNameList
     * @return
     */
    public List<VehicleStatusDO> list(List<String> vehicleNameList) {
        Map<String, VehicleStatusDO> map = listMap(vehicleNameList);
        List<VehicleStatusDO> list = new ArrayList(map.values());
        return list;
    }

    /**
     * 获取redis key
     *
     * @param vehicleName
     * @return
     */
    public static String getKey(String vehicleName) {
        String redisKey = CACHE_NAME + vehicleName;
        return redisKey;
    }
}
