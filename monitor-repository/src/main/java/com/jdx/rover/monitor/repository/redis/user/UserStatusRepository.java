/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 用户状态信息
 *
 * <AUTHOR>
 */
@Service
public class UserStatusRepository {
    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "user:status:map:";
    /**
     * 记录时间字段
     */
    private static final String FIELD_RECORD_TIME = "recordTime";

    /**
     * 获取redis key
     *
     * @param userName 用户名称
     * @return
     */
    public static String getKey(String userName) {
        return CACHE_NAME + userName;
    }

    /**
     * 通过用户名称获取用户状态信息
     *
     * @param userName 用户名称
     */
    @Cacheable(value = CACHE_NAME, key = "#userName")
    public UserStatusDO get(String userName) {
        String redisKey = getKey(userName);
        RMap<String, Object> rMap = RedissonUtils.getMap(redisKey);
        Map<String, Object> mapValue = rMap.readAllMap();
        UserStatusDO result = BeanUtil.toBean(mapValue, UserStatusDO.class);
        result.setUserName(userName);
        return result;
    }

    /**
     * 设置状态值
     *
     * @param userName 用户名称
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public void putMapValue(String userName, Func1<UserStatusDO, ?> func, Object value) {
        Map<String, Object> paramMap = new ParamMap<UserStatusDO>().addProperty(func, value).toMap();
        putAllMapObject(userName, paramMap);
    }

    /**
     * 移除状态值
     *
     * @param userName 用户名称
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public long fastRemoveMapKey(String userName, Func1<UserStatusDO, ?>... func) {
        String redisKey = getKey(userName);
        List<String> fieldNameList = Arrays.stream(func).map(LambdaUtil::getFieldName).toList();
        RMap<String, Object> rMap = RedissonUtils.getMap(redisKey);
        return rMap.fastRemove(fieldNameList.toArray(new String[0]));
    }

    /**
     * 保存map数据
     *
     * @param userName 用户名称
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public void putAllMapObject(String userName, Map<String, Object> mapObject) {
        mapObject.values().removeIf(Objects::isNull);
        if (Objects.isNull(mapObject.get(FIELD_RECORD_TIME))) {
            mapObject.put(FIELD_RECORD_TIME, new Date());
        }
        String redisKey = getKey(userName);
        RedissonUtils.putAllMapObject(redisKey, mapObject);
    }

    /**
     * 通过用户名称删除用户状态信息
     *
     * @param userName 用户名称
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public boolean remove(String userName) {
        String redisKey = getKey(userName);
        return RedissonUtils.deleteObject(redisKey);
    }

    /**
     * 通过用户名称获取实时
     *
     * @param userNameList 用户名称列表
     */
    public Map<String, UserStatusDO> listMap(List<String> userNameList) {
        Map<String, UserStatusDO> result = new HashMap<>(userNameList.size());
        for (String userName : userNameList) {
            UserStatusDO dto = get(userName);
            result.put(userName, dto);
        }
        return result;
    }

    /**
     * 通过用户名称获取实时
     *
     * @param userNameList 用户名称列表
     */
    public List<UserStatusDO> list(List<String> userNameList) {
        Map<String, UserStatusDO> map = listMap(userNameList);
        return new ArrayList<>(map.values());
    }
}
