/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.schedule;

import com.jdx.rover.schedule.api.schedule.MonitorDadaApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 达达调度服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "schedule-dada",value = "rover-schedule-web")
public interface ScheduleDadaApiClient extends MonitorDadaApi {
}
