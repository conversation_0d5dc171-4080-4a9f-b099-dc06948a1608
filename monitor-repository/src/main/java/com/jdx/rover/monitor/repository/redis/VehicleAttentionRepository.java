package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RMap;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 车辆关注用户列表缓存
 */
@Service
@Slf4j
public class VehicleAttentionRepository {

    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "vehicle:attention:user:map";

    /**
     * 读取
     *
     * @param vehicleName
     * @return
     */
    public Set<String> hget(String vehicleName) {
        Set<String> userSet = RedissonUtils.getMapObject( CACHE_NAME,vehicleName);
        log.info("VehicleAttentionRepository本地缓存数据vehicleName={},数据={}", vehicleName, userSet);
        return userSet;
    }

    /**
     * 新增
     *
     * @param username
     * @return
     */
    @LocalCacheEvict(value = LocalCacheConstant.USER_ATTENTION_VEHICLE_NAME_SET, key = "#username")
    public void initSet(String username, List<String> vehicleNameList) {
        RMap<String, Set<String>> rMap = RedissonUtils.getMap(CACHE_NAME);
        Map<String, Set<String>> vehicleMap = rMap.getAll(new HashSet<>(vehicleNameList));
        vehicleMap.entrySet().stream().forEach(entry -> {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                vehicleMap.put(entry.getKey(), new HashSet<>(Arrays.asList(username)));
            } else {
                entry.getValue().add(username);
            }
        });
        rMap.putAll(vehicleMap);
    }

    /**
     * 删除缓存
     */
    public void delete(String username, List<String> vehicleNameList) {
        log.info("取消用户{}关注所有车辆列表", username);
        RMap<String, Set<String>> rMap = RedissonUtils.getMap(CACHE_NAME);
        Map<String, Set<String>> vehicleMap = rMap.getAll(new HashSet<>(vehicleNameList));
        vehicleMap.entrySet().stream().forEach(entry -> {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                entry.getValue().remove(username);
            }
        });
        rMap.putAll(vehicleMap);
    }

}
