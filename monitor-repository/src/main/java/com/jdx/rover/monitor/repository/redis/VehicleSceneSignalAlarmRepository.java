/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.redis;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.monitor.entity.VehicleSceneSignalEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <p>
 *  车辆定位置信度告警记录
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Slf4j
public class VehicleSceneSignalAlarmRepository {

  /**
   * <p>
   * the redis cache key.
   * </p>
   */
  static final String SCENE_SIGNAL_KEY = "scene_signal";

  /**
   * <p>
   *  保存记录
   * </p>
   *
   */
  public void save(String vehicleName, VehicleSceneSignalEntity vehicleSceneSignalEntity) {
    ParameterCheckUtility.checkNotNull(vehicleSceneSignalEntity, "vehicleSceneSignalEntity");
    ParameterCheckUtility.checkNotNull(vehicleName, "vehicleName");
    RedissonUtils.setMapObject(SCENE_SIGNAL_KEY, vehicleName, vehicleSceneSignalEntity, getRemaingSeconds());
  }

  /**
   * <p>
   *  获取值
   * </p>
   *
   */
  public VehicleSceneSignalEntity getByKey(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    return RedissonUtils.getMapObject(SCENE_SIGNAL_KEY, vehicleName);
  }

  /**
   * <p>
   * 获取过期时间
   * </p>
   *
   */
  private Long getRemaingSeconds() {
    Date currentDate = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
    return DateUtil.between(currentDate, DateUtil.endOfDay(currentDate), DateUnit.SECOND);
  }
}
