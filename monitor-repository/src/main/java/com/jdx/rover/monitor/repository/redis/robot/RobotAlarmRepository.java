/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.redis.robot;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 机器人设备实时告警信息
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Repository
public class RobotAlarmRepository {

  /**
   * 缓存名称
   */
  public static final String CACHE_NAME = "monitor:robot:alarm:";

  /**
   * 通过设备名称获取实时告警信息
   *
   * @param deviceName
   * @return
   */
  @Cacheable(value = CACHE_NAME, key = "#deviceName")
  public Map<String, RobotAlarmDO.DeviceAlarmEventDO> get(String deviceName) {
    String redisKey = getKey(deviceName);
    RMap<String, RobotAlarmDO.DeviceAlarmEventDO> rMap = RedissonUtils.getMap(redisKey);
    Map<String, RobotAlarmDO.DeviceAlarmEventDO> mapValue = rMap.readAllMap();
    return mapValue;
  }

  /**
   * 添加
   *
   * @param deviceName
   */
  @LocalCacheEvict(value = CACHE_NAME, key = "#deviceName")
  public void add(String deviceName, String alarmCode, RobotAlarmDO.DeviceAlarmEventDO value) {
    String redisKey = getKey(deviceName);
    RedissonUtils.fastPutMapValue(redisKey, alarmCode, value);
  }

  /**
   * 移除
   *
   * @param deviceName 设备名称
   */
  @LocalCacheEvict(value = CACHE_NAME, key = "#deviceName")
  public long fastRemoveMapKey(String deviceName, List<String> errorCodeList) {
    String redisKey = getKey(deviceName);
    RMap<String, Object> rMap = RedissonUtils.getMap(redisKey);
    return rMap.fastRemove(errorCodeList.toArray(new String[0]));
  }

  /**
   * 保存map数据
   *
   */
  @LocalCacheEvict(value = CACHE_NAME, key = "#deviceName")
  public void putAllMapObject(String deviceName, Map<String, RobotAlarmDO.DeviceAlarmEventDO> mapObject) {
    mapObject.values().removeIf(Objects::isNull);
    String redisKey = getKey(deviceName);
    RedissonUtils.putAllMapObject(redisKey, mapObject);
  }

  /**
   * 删除
   *
   * @param deviceName
   */
  @LocalCacheEvict(value = CACHE_NAME, key = "#deviceName")
  public void remove(String deviceName) {
    String redisKey = getKey(deviceName);
    RedissonUtils.deleteObject(redisKey);
  }

  /**
   * 通过设备名称获取告警
   *
   * @param deviceNameList
   * @return
   */
  public Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> listMap(List<String> deviceNameList) {
    Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> result = new HashMap<>(deviceNameList.size());
    for (String deviceName : deviceNameList) {
      Map<String, RobotAlarmDO.DeviceAlarmEventDO> dto = get(deviceName);
      result.put(deviceName, Optional.ofNullable(dto).map(data ->
              data.values().stream().collect(Collectors.toList())).orElse(new ArrayList<>()));
    }
    return result;
  }

  /**
   * 通过机器人名称获取告警
   *
   * @param deviceNameList
   * @return
   */
  public List<List<RobotAlarmDO.DeviceAlarmEventDO>> list(List<String> deviceNameList) {
    Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> map = listMap(deviceNameList);
    return new ArrayList<>(map.values());
  }

  /**
   * 获取redis key
   *
   * @param deviceName
   * @return
   */
  public static String getKey(String deviceName) {
    String redisKey = RedisKeyEnum.ALARM_ROBOT_PREFIX.getValue() + deviceName;
    return redisKey;
  }

}
