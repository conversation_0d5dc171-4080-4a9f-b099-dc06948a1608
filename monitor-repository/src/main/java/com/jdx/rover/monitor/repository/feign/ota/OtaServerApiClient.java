/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.ota;

import com.jd.rover.ota.api.service.ApplicationIssueInfoApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * ota基础数据客户端
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "ota-server",value = "rover-ota-server")
public interface OtaServerApiClient extends ApplicationIssueInfoApi {
}
