/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.repository.redis.datacollection;

import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.stereotype.Repository;

/**
 * 数采车车辆sse连接信息
 *
 * <AUTHOR>
 * @date 2025/07/23
 */
@Repository
public class DataCollectionVehicleUserRepository {

    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "data:collection:vehicleUserConnMap";

    /**
     * 保存
     * @param vehicleName 车辆名称
     * @param username 用户名
     */
    public void save(String vehicleName, String username) {
        RedissonUtils.fastPutMapValue(CACHE_NAME, vehicleName, username);
    }

    /**
     * 查询
     * @param vehicleName 车辆名称
     * @return 用户名
     */
    public String get(String vehicleName) {
        return RedissonUtils.getMapObject(CACHE_NAME, vehicleName);
    }

    /**
     * 删除
     * @param vehicleName 车辆名称
     * @param username 用户名
     */
    public void remove(String vehicleName, String username) {
        RedissonUtils.getMap(CACHE_NAME).remove(vehicleName, username);
    }
}
