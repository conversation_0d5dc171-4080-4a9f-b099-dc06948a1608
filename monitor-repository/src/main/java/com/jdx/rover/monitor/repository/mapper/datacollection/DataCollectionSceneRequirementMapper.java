/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.repository.mapper.datacollection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.datacollection.DataCollectionSceneRequirement;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数采场景关联需求mapper
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Mapper
public interface DataCollectionSceneRequirementMapper extends BaseMapper<DataCollectionSceneRequirement> {

}
