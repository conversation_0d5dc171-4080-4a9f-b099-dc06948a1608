package com.jdx.rover.monitor.repository.redis;

import com.google.common.collect.Lists;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 基本信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleSelectTypeRepository {
  /**
   * 匹配所有redis key
   */
  public static final String ALL_KEY_PATTERN = RedisKeyEnum.VEHICLE_NAME_SET_SELECT_TYPE_PREFIX.getValue() + "*";
  /**
   * 模糊匹配,一次最大取1000条
   */
  public static final int MAX_KEY_SIZE = 1000;

  /**
   * 读取
   *
   * @param businessType
   * @param useCase
   * @return
   */
  @Cacheable(value = LocalCacheConstant.VEHICLE_NAME_SET_SELECT_TYPE, unless = "#result?.size() == 0", key = "#businessType+'_'+#useCase")
  public Set<String> get(String businessType, String useCase) {
    String selectTypeKey = getSelectTypeKey(businessType, useCase);
    RSet<String> rSet = RedissonUtils.getRedissonClient().getSet(selectTypeKey);
    Set<String> result = rSet.readAll();
    log.info("VehicleSelectTypeRepository本地缓存数据businessType={},useCase={},数据={}", businessType, useCase, result.size());
    return result;
  }

  /**
   * 读取
   *
   * @param businessType
   * @param useCase
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.VEHICLE_NAME_SET_SELECT_TYPE)
  public void add(String businessType, String useCase, String vehicleName) {
    String sortTypeKeyAll = getSelectTypeKey(businessType, useCase);
    String sortTypeKeyUseCase = getSelectTypeKey(null, useCase);
    String sortTypeKeyBusinessType = getSelectTypeKey(businessType, null);
    String sortTypeKeyNull = getSelectTypeKey(null, null);
    List<String> keyList = Lists.newArrayList(sortTypeKeyAll, sortTypeKeyUseCase, sortTypeKeyBusinessType, sortTypeKeyNull);

    RedissonClient redisson = RedissonUtils.getRedissonClient();
    RBatch batch = redisson.createBatch();
    // 删除所有的历史记录
    List<String> currentKeyList = redisson.getKeys().getKeysStreamByPattern(ALL_KEY_PATTERN, MAX_KEY_SIZE).collect(Collectors.toList());
    Collection<String> allKeyList = CollectionUtils.union(currentKeyList, keyList);
    for (String key : allKeyList) {
      if (keyList.contains(key)) {
        batch.getSet(key).addAsync(vehicleName);
      } else {
        batch.getSet(key).removeAsync(vehicleName);
      }
    }
    batch.execute();
  }

  /**
   * 删除,不知道删除哪一个本地缓存,等待10分钟后自动删除本地缓存,不影响数据正确性
   *
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.VEHICLE_NAME_SET_SELECT_TYPE)
  public void remove(String vehicleName) {
    RedissonClient redisson = RedissonUtils.getRedissonClient();
    RBatch batch = redisson.createBatch();
    // 删除所有的历史记录
    List<String> allKeyList = redisson.getKeys().getKeysStreamByPattern(ALL_KEY_PATTERN, MAX_KEY_SIZE).collect(Collectors.toList());
    for (String key : allKeyList) {
      batch.getSet(key).removeAsync(vehicleName);
    }
    batch.execute();
    log.info("VehicleSelectTypeRepository删除全部本地缓存数据={}", vehicleName);
  }

  /**
   * 通过业务类型,和车辆用途,获取key
   *
   * @param businessType
   * @param useCase
   * @return
   */
  private String getSelectTypeKey(String businessType, String useCase) {
    StringBuilder selectTypeStr = new StringBuilder();
    selectTypeStr.append(RedisKeyEnum.VEHICLE_NAME_SET_SELECT_TYPE_PREFIX.getValue())
        .append(StringUtils.trimToEmpty(businessType))
        .append(":")
        .append(StringUtils.trimToEmpty(useCase));
    String selectTypeKey = selectTypeStr.toString();
    return selectTypeKey;
  }
}
