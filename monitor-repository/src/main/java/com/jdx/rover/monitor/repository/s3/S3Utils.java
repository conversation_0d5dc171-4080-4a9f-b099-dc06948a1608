package com.jdx.rover.monitor.repository.s3;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.SDKGlobalConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PresignedUrlDownloadRequest;
import com.amazonaws.services.s3.model.PresignedUrlUploadRequest;
import com.amazonaws.services.s3.model.PresignedUrlUploadResult;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.common.utils.fileUtil.FileUtil;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.net.URL;
import java.util.Date;

/**
 * s3工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/26
 */
public class S3Utils {
    /**
     * 创建s3客户端
     *
     * @param accessKey s3用户key
     * @param secretKey s3秘钥
     * @param endpoint  s3地址
     * @return
     */
    public static AmazonS3 createS3Client(String accessKey, String secretKey, String endpoint) {
        System.setProperty(SDKGlobalConfiguration.ENABLE_S3_SIGV4_SYSTEM_PROPERTY, "true");
        AWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
        ClientConfiguration config = new ClientConfiguration();
        config.setProtocol(Protocol.HTTP);
        AmazonS3 s3 = new AmazonS3Client(awsCredentials, config);
        s3.setEndpoint(endpoint);
        S3ClientOptions options = new S3ClientOptions();
        options.withChunkedEncodingDisabled(true); // Must have
        s3.setS3ClientOptions(options);
        return s3;
    }

    /**
     * 预签名URL
     * https://docs.jdcloud.com/cn/object-storage-service/transfer-for-mobile-apps
     *
     * @param accessKey  s3用户key
     * @param secretKey  s3秘钥
     * @param endpoint   s3地址
     * @param bucketName s3桶名称
     * @param keyName    s3文件名
     * @param method     请求方法PUT/GET/POST
     * @param expiration 过期日期
     * @return
     */
    public static URL generatePresignUrl(String accessKey, String secretKey, String endpoint, String bucketName
            , String keyName, HttpMethod method, Date expiration) {
        AmazonS3 s3 = createS3Client(accessKey, secretKey, endpoint);
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, keyName)
                .withMethod(method)
                .withExpiration(expiration);
        return s3.generatePresignedUrl(request);
    }

    /**
     * 下载文件
     * https://docs.jdcloud.com/cn/object-storage-service/simple-upload-s3
     *
     * @param accessKey  s3用户key
     * @param secretKey  s3秘钥
     * @param endpoint   s3地址
     * @param bucketName s3桶名称
     * @param keyName    s3文件名
     * @return
     */
    public static void downloadFile(String accessKey, String secretKey, String endpoint, String bucketName
            , String keyName, File file) {
        AmazonS3 s3 = createS3Client(accessKey, secretKey, endpoint);
        final Date expiration = DateUtil.offsetHour(new Date(), 72);
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, keyName)
                .withMethod(HttpMethod.GET)
                .withExpiration(expiration);
        URL url= s3.generatePresignedUrl(request);
        PresignedUrlDownloadRequest downloadRequest = new PresignedUrlDownloadRequest(url);
        s3.download(downloadRequest, file);
    }

    /**
     * 下载文件
     * https://docs.jdcloud.com/cn/object-storage-service/simple-upload-s3
     *
     * @param accessKey  s3用户key
     * @param secretKey  s3秘钥
     * @param endpoint   s3地址
     * @param bucketName s3桶名称
     * @param keyName    s3文件名
     * @return
     */
    public static void uploadFile(String accessKey, String secretKey, String endpoint, String bucketName
            , String keyName, File file) {
        AmazonS3 s3 = createS3Client(accessKey, secretKey, endpoint);
        final Date expiration = DateUtil.offsetHour(new Date(), 72);
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, keyName)
                .withMethod(HttpMethod.PUT)
                .withExpiration(expiration);
        URL url= s3.generatePresignedUrl(request);
        PresignedUrlUploadRequest uploadRequest = new PresignedUrlUploadRequest(url);
        PresignedUrlUploadResult result = s3.upload(uploadRequest.withFile(file));
    }

    /**
     * 下载文件
     * https://docs.jdcloud.com/cn/object-storage-service/simple-upload-s3
     *
     * @param accessKey  s3用户key
     * @param secretKey  s3秘钥
     * @param endpoint   s3地址
     * @param bucketName s3桶名称
     * @param keyName    s3文件名
     * @return
     */
    public static void uploadFile(String accessKey, String secretKey, String endpoint, String bucketName
            , String keyName, ByteArrayInputStream inputStream) {
        AmazonS3 s3 = createS3Client(accessKey, secretKey, endpoint);
        final Date expiration = DateUtil.offsetHour(new Date(), 72);
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, keyName)
                .withMethod(HttpMethod.PUT)
                .withExpiration(expiration);
        URL url= s3.generatePresignedUrl(request);
        PresignedUrlUploadRequest uploadRequest = new PresignedUrlUploadRequest(url);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(inputStream.available());
        PresignedUrlUploadResult result = s3.upload(uploadRequest.withMetadata(objectMetadata).withInputStream(inputStream));
    }

    /**
     * 文件上传
     *
     * @param file MultipartFile
     * @return FileDTO
     */
    public static String uploadFile(String accessKey, String secretKey, String endpoint, String bucketName, MultipartFile file) {
        String fileKey = file.getOriginalFilename();
        if (!FileUtil.jdPathCheck(fileKey)) {
            throw new AppException("文件名含有非法字符，不允许上传!");
        }
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byte[] contentBytes = IoUtil.readBytes(file.getInputStream());
            byteArrayInputStream = new ByteArrayInputStream(contentBytes);
            S3Utils.uploadFile(accessKey, secretKey, endpoint, bucketName, fileKey, byteArrayInputStream);
        } catch (Exception e) {
            throw new AppException(MonitorErrorEnum.ERROR_CALL_UPLOAD.getCode(), MonitorErrorEnum.ERROR_CALL_UPLOAD.getMessage());
        } finally {
            IoUtil.close(byteArrayInputStream);
        }
        return fileKey;
    }

}
