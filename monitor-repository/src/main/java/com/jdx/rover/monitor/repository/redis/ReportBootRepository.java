package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 启动信息
 *
 * <AUTHOR>
 */
@Service
public class ReportBootRepository {
    /**
     * 通过车辆名称获取启动信息
     *
     * @param vehicleName
     * @return
     */
    public VehicleBootDTO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        VehicleBootDTO result = RedissonUtils.getObject(redisKey);
        return result;
    }

    /**
     * 通过车辆名称设置启动信息
     *
     * @param dto
     */
    public void set(VehicleBootDTO dto) {
        String redisKey = getKey(dto.getVehicleName());
        RedissonUtils.setObject(redisKey, dto);
    }

    /**
     * 批量设置车辆启动信息
     *
     * @param dtoList
     */
    public void setList(List<VehicleBootDTO> dtoList) {
        Map<String, VehicleBootDTO> dtoMap = new HashMap<>(dtoList.size());
        for (VehicleBootDTO dto : dtoList) {
            String redisKey = getKey(dto.getVehicleName());
            dtoMap.put(redisKey, dto);
        }
        RedissonUtils.getRedissonClient().getBuckets().set(dtoMap);
    }

    /**
     * 通过车辆名称删除启动信息
     *
     * @param vehicleName
     * @return
     */
    public boolean remove(String vehicleName) {
        String redisKey = getKey(vehicleName);
        boolean result = RedissonUtils.deleteObject(redisKey);
        return result;
    }

    /**
     * 获取redis key
     *
     * @param vehicleName
     * @return
     */
    public static String getKey(String vehicleName) {
        String redisKey = RedisKeyEnum.REPORT_BOOT_PREFIX.getValue() + vehicleName;
        return redisKey;
    }

}
