/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.feign.server;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 电源管理命令API
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "power-manager-command", value = "rover-server-stream")
public interface PowerManagerCommandApiClient {
  /**
   * 远程重启/关闭
   */
  @PostMapping("/server/stream/command/power/manager")
  HttpResult<Void> publishPowerManagerCommand(PowerManagerCommandVO powerManagerCommandVO);
}
