/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.repository.redis;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.monitor.dto.vehicle.VehicleSpeedLimitDTO;
import java.util.Date;
import java.util.Set;
import org.redisson.api.RSet;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/8 15:28
 * @description 车辆限速Redis
 */
@Service
public class VehicleSpeedLimitRepository {

    /**
     * Speed Limit Key
     */
    private final static String VEHICLE_SPEED_LIMIT_KEY = "vehicle:speed-limit:%s";

    /**
     * Speed Limit Vehicle Set
     */
    private final static String SPEED_LIMIT_VEHICLE_SET_KEY = "vehicle:set:speed-limit";

    /**
     * 通过车辆名称获取限速信息
     *
     * @param vehicleName vehicleName
     * @return VehicleSpeedLimitDTO
     */
    public VehicleSpeedLimitDTO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.getObject(redisKey);
    }

    /**
     * 设置
     *
     * @param vehicleSpeedLimitDTO vehicleSpeedLimitDTO
     */
    public void set(VehicleSpeedLimitDTO vehicleSpeedLimitDTO) {
        String redisKey = getKey(vehicleSpeedLimitDTO.getVehicleName());
        RedissonUtils.setObject(redisKey, vehicleSpeedLimitDTO, calculateExpireSeconds());
        addVehicleToSet(vehicleSpeedLimitDTO.getVehicleName());
    }

    /**
     * 删除
     *
     * @param vehicleName vehicleName
     */
    public void remove(String vehicleName) {
        String redisKey = getKey(vehicleName);
        RedissonUtils.deleteObject(redisKey);
        removeVehicleFromSet(vehicleName);
    }

    /**
     * 车辆是否存在限速配置
     *
     * @param vehicleName vehicleName
     * @return boolean
     */
    public boolean existsSpeedLimit(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.hasKey(redisKey);
    }

    /**
     * 获取redis key
     *
     * @param vehicleName vehicleName
     * @return redis key
     */
    private static String getKey(String vehicleName) {
        return String.format(VEHICLE_SPEED_LIMIT_KEY, vehicleName);
    }

    /**
     * 获取缓存失效时间：当前时间到23:10间隔秒数
     */
    private long calculateExpireSeconds() {
        Date now = new Date();

        // 1. 获取今天的起始时间
        Date beginOfDay = DateUtil.beginOfDay(now);

        // 2. 将时间设置为当天的23点10分
        Date targetTime = DateUtil.offsetMinute(DateUtil.offsetHour(beginOfDay, 23), 10);

        // 3. 如果当前时间已经超过了目标时间，则目标应为明天的23:10
        if (now.after(targetTime)) {
            targetTime = DateUtil.offsetDay(targetTime, 1);
        }

        // 4. 计算当前时间与目标时间之间的秒数
        return DateUtil.between(now, targetTime, DateUnit.SECOND);
    }

    /**
     * 添加车辆到限速车辆集合
     *
     * @param vehicleName vehicleName
     */
    private void addVehicleToSet(String vehicleName) {
        RedissonUtils.getSet(SPEED_LIMIT_VEHICLE_SET_KEY).add(vehicleName);
    }

    /**
     * 从限速车辆集合中删除车辆
     *
     * @param vehicleName vehicleName
     */
    private void removeVehicleFromSet(String vehicleName) {
        RedissonUtils.getSet(SPEED_LIMIT_VEHICLE_SET_KEY).remove(vehicleName);
    }

    /**
     * 获取限速车辆集合
     *
     * @return 限速车辆集合
     */
    public Set<String> getSpeedLimitVehicleSet() {
        RSet<String> result = RedissonUtils.getSet(SPEED_LIMIT_VEHICLE_SET_KEY);
        return result.readAll();
    }
}
