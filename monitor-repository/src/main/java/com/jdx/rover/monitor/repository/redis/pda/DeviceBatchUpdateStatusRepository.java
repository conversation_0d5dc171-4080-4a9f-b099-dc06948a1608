/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.repository.redis.pda;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.entity.device.DeviceStatusChangeDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * pda更新实时状态暂存
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceBatchUpdateStatusRepository {
    /**
     * 缓存名称
     */
    private final ConcurrentHashMap<String, DeviceStatusChangeDO> changeStatusDeviceList = new ConcurrentHashMap<>();

    /**
     * 获取对象
     */
    public List<DeviceStatusChangeDO> get() {
        log.info("获取待更新设备列表数据={}", changeStatusDeviceList.size());
        return changeStatusDeviceList.values().stream().collect(Collectors.toList());
    }

    /**
     * 删除对象
     */
    public void delete() {
        changeStatusDeviceList.clear();
        log.info("删除待更新设备列表数据");
    }

    /**
     * 设置对象
     */
    public void batchAdd(Set<DeviceStatusChangeDO> deviceList) {
        if (deviceList.size()> 1000) {
            log.error("批量增加设备时设备数大于1000条{}", deviceList.size());
            return;
        }
        Map<String, DeviceStatusChangeDO> mapData = deviceList.stream().collect(Collectors.toMap(DeviceStatusChangeDO::getDeviceName, Function.identity()));
        changeStatusDeviceList.putAll(mapData);
        log.info("批量增加更新设备{}到缓存", JsonUtils.writeValueAsString(deviceList));
    }

}