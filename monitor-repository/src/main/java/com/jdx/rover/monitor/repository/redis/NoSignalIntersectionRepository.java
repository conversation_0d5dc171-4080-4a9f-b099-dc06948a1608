/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.dto.vehicle.SingleVehicleNoSignalIntersectionDTO;
import org.redisson.api.RMap;
import org.springframework.stereotype.Service;

/**
 * 车辆无信号路口Repository
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
@Service
public class NoSignalIntersectionRepository {
    private static final String REDIS_KEY = "NO_SIGNAL_INTERSECTION";

    /**
     * 获取
     *
     * @param vehicleName
     * @return
     */
    public SingleVehicleNoSignalIntersectionDTO get(String vehicleName) {
        SingleVehicleNoSignalIntersectionDTO result = RedissonUtils.getMapObject(REDIS_KEY, vehicleName);
        return result;
    }

    /**
     * 设置
     *
     * @param dto
     * @return
     */
    public void set(String vehicleName, SingleVehicleNoSignalIntersectionDTO dto) {
        RMap<String, SingleVehicleNoSignalIntersectionDTO> rMap = RedissonUtils.getMap(REDIS_KEY);
        rMap.fastPut(vehicleName, dto);
    }

    /**
     * 删除
     *
     * @param vehicleName
     * @return
     */
    public void remove(String vehicleName) {
        RMap<String, SingleVehicleNoSignalIntersectionDTO> rMap = RedissonUtils.getMap(REDIS_KEY);
        rMap.remove(vehicleName);
    }
}
