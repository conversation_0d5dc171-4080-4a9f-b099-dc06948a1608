/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.metadata.api.service.v2.vehicle.VehicleExceptionApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 新主数据异常客户端
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "metadata-vehicle-exception", value = "metadata-web")
public interface MetadataVehicleExceptionClient extends VehicleExceptionApi {
}