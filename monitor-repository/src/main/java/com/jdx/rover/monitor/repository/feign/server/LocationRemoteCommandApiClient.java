/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.server;

import com.jdx.rover.server.api.service.command.LocationRemoteCommandApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 车辆辅助定位客户端
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "location-server-command",value = "rover-server-stream")
public interface LocationRemoteCommandApiClient extends LocationRemoteCommandApi {
}
