package com.jdx.rover.monitor.repository.redis;

import com.google.common.collect.Lists;
import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 车辆调度pnc规划路线信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleSchedulePncRouteRepository {
  
  /**
   * <p>
   * Represents the redis template repository.
   * </p>
   */
  @Autowired
  private RedisTemplate<String, Object> clientRedisTemplate;

  /**
   * <p>
   * Get vehicle schedule location info cache.
   * </p>
   *
   * @param vehicleName the vehicle name of vehicle
   * @throws IllegalArgumentException   if the argument is invalid.
   */
  public List getByKey(String vehicleName) {
    try {
      String redisKey = RedisKeyEnum.SCHEDULE_PNC_ROUTE_PREFIX.getValue() + vehicleName;
      return clientRedisTemplate.opsForList().range(redisKey, 0, -1);
    } catch (Exception e) {
      log.error("Get schedule pnc routing exception", e);
      return Lists.newArrayList();
    }
  }

  /**
   * <p>
   * Delete vehicle schedule location info cache.
   * </p>
   *
   * @param vehicleName the vehicle name of vehicle
   * @throws IllegalArgumentException if the argument is invalid.
   */
  public void remove(String vehicleName) {
    String redisKey = RedisKeyEnum.SCHEDULE_PNC_ROUTE_PREFIX.getValue() + vehicleName;
    clientRedisTemplate.delete(redisKey);
  }

  /**
   * <p>
   * Set vehicle schedule location cache.
   * </p>
   *
   * @param vehicleName the vehicle name of vehicle
   * @throws IllegalArgumentException if the argument is invalid.
   */
  public boolean push(String vehicleName, List<MonitorRoutingPointEntity> routingPointEntityList) {
    String redisKey = RedisKeyEnum.SCHEDULE_PNC_ROUTE_PREFIX.getValue() + vehicleName;
    try {
      remove(vehicleName);
      clientRedisTemplate.opsForList().leftPushAll(redisKey, routingPointEntityList);
      clientRedisTemplate.expire(redisKey, getRemaingMilliSeconds(), TimeUnit.MILLISECONDS);
      return true;
    } catch (Exception e) {
      log.error("Push vehicle pnc routing point error", e);
      return false;
    }
  }

  /**
   * <p>
   * Calculate the remaining seconds of current day
   * </p>
   *
   * @return The remaining seconds of current day
   */
  private Long getRemaingMilliSeconds() {
    return Date
        .from(LocalDateTime.now().with(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant())
        .getTime()
        - Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()).getTime();
  }
}
