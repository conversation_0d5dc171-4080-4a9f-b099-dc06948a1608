/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.repository.mapper.datacollection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.datacollection.DataCollectionSceneTag;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数采场景关联标签mapper
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Mapper
public interface DataCollectionSceneTagMapper extends BaseMapper<DataCollectionSceneTag> {

}
