/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.entity.vehicle.VehiclePlanningStatusDO;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 车辆规划状态
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
@Service
public class VehiclePlanningStatusRepository {

    private static final String CACHE_NAME = "vehicle:planning:status:map:";

    /**
     * 通过车辆名称获取实时信息
     *
     * @param vehicleName
     * @return
     */
    @Cacheable(value = CACHE_NAME, key = "#vehicleName")
    public VehiclePlanningStatusDO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        RMap<String, String> rMap = RedissonUtils.getMap(redisKey);
        Map<String, String> mapValue = rMap.readAllMap();
        VehiclePlanningStatusDO result = BeanUtil.mapToBean(mapValue, VehiclePlanningStatusDO.class, false, CopyOptions.create());
        result.setVehicleName(vehicleName);
        return result;
    }

    /**
     * 保存map数据
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void putAllMapObject(String vehicleName, Map<String, Object> mapObject) {
        mapObject.values().removeIf(Objects::isNull);
        String redisKey = getKey(vehicleName);
        RedissonUtils.putAllMapObject(redisKey, mapObject);
    }

    /**
     * 通过车辆名称删除实时信息
     *
     * @param vehicleName
     * @return
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public boolean remove(String vehicleName) {
        String redisKey = getKey(vehicleName);
        boolean result = RedissonUtils.deleteObject(redisKey);
        return result;
    }

    /**
     * 获取redis key
     *
     * @param vehicleName
     * @return
     */
    public static String getKey(String vehicleName) {
        String redisKey = CACHE_NAME + vehicleName;
        return redisKey;
    }
}
