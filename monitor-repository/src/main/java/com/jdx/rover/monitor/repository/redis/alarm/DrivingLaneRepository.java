/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.alarm;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.entity.alarm.DrivingLaneAlarmDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 机动车道行驶检测告警
 *
 * <AUTHOR>
 */
@Service
public class DrivingLaneRepository {
    /**
     * 缓存名称
     */
    public static final String CACHE_NAME = "driving:lane:type:";

    /**
     * 获取redis key
     */
    public static String getKey(String vehicleName) {
        return CACHE_NAME + vehicleName;
    }

    /**
     * 获取对象
     */
    @Cacheable(value = CACHE_NAME, key = "#vehicleName")
    public DrivingLaneAlarmDO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.getObject(redisKey);
    }

    /**
     * 设置对象
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void set(String vehicleName, DrivingLaneAlarmDO alarmDo) {
        String redisKey = getKey(vehicleName);
        RedissonUtils.setObject(redisKey, alarmDo);
    }

    /**
     * 通过名称删除对象
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public boolean remove(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.deleteObject(redisKey);
    }

    /**
     * 通过名称判断是否存在
     */
    public boolean exist(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.hasKey(redisKey);
    }

}