package com.jdx.rover.monitor.repository.feign.shadow;

import com.jdx.rover.shadow.api.service.ShadowTrackingEventApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <p>
 * 影子系统报警事件客户端
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@FeignClient(contextId = "rover-shadow-tracking",value = "rover-shadow-web")
public interface ShadowTrackingEventApiClient extends ShadowTrackingEventApi {
}
