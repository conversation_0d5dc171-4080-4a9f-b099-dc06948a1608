package com.jdx.rover.monitor.repository.redis;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RList;
import org.redisson.api.RSortedSet;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 基本信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserVehicleNameRepository {
  /**
   * 读取
   *
   * @param username
   * @return
   */
  @Cacheable(value = LocalCacheConstant.USER_VEHICLE_NAME_SET, key = "#username")
  public Set<String> get(String username) {
    String redisKey = getRedisKey(username);
    RSortedSet<String> rSet = RedissonUtils.getRedissonClient().getSortedSet(redisKey);
    Collection<String> result = rSet.readAll();
    log.info("UserVehicleNameRepository本地缓存数据username={},数据={}", username, result.size());
    return new TreeSet<>(result);
  }

  /**
   * 设置
   *
   * @param username
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.USER_VEHICLE_NAME_SET, key = "#username")
  public boolean set(String username, List<String> vehicleNameList) {
    String redisKey = getRedisKey(username);
    RSortedSet<String> vehicleNameSet = RedissonUtils.getRedissonClient().getSortedSet(redisKey);
    boolean result = vehicleNameSet.addAll(vehicleNameList);
    return result;
  }

  /**
   * 初始化设置(先删除,后写入)
   *
   * @param username
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.USER_VEHICLE_NAME_SET, key = "#username")
  public boolean initSet(String username, List<String> vehicleNameList) {
    String redisKey = getRedisKey(username);
    RList<String> vehicleNameSet = RedissonUtils.getRedissonClient().getList(redisKey);
    vehicleNameSet.delete();

    vehicleNameList.removeIf(Objects::isNull);
    boolean result = true;
    if (CollectionUtils.isNotEmpty(vehicleNameList)) {
      List<String> vehicleNameSortList = vehicleNameList.stream().sorted().collect(Collectors.toList());
      result = vehicleNameSet.addAll(vehicleNameSortList);
    }
    return result;
  }

  /**
   * 设置
   *
   * @param username
   * @return
   */
  public boolean isContainVehicleName(String username, String vehicleName) {
    UserVehicleNameRepository self = SpringUtil.getBean(UserVehicleNameRepository.class);
    return self.get(username).contains(vehicleName);
  }

  /**
   * 通过用户名获取redisKey
   *
   * @param username
   * @return
   */
  public static String getRedisKey(String username) {
    String redisKey = RedisKeyEnum.USER_VEHICLE_NAME_SET_PREFIX.getValue() + username;
    return redisKey;
  }
}
