package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.dataobject.mapcollection.VehiclePointDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 采图模式真实路径信息
 */
@Repository
public class VehicleMapRealRouteRepository {

    /**
     * 根据车牌号获取路径
     *
     * @param vehicleName vehicleName
     * @return 点位列表
     */
    public List<VehiclePointDO> get(String vehicleName) {
        RList<VehiclePointDO> list = RedissonUtils.getRedissonClient().getList(getKey(vehicleName));
        List<VehiclePointDO> result = list.range(0, list.size() - 1);
        return result;
    }

    /**
     * 写入路径
     *
     * @param vehicleName vehicleName
     * @param entity entity
     * @return 是否成功
     */
    public boolean push(String vehicleName, VehiclePointDO entity) {
        RList<VehiclePointDO> list = RedissonUtils.getRedissonClient().getList(getKey(vehicleName));
        list.add(entity);
        return true;
    }

    /**
     * 删除key
     *
     * @param vehicleName vehicleName
     * @return 删除结果
     */
    public boolean remove(String vehicleName) {
        boolean result = RedissonUtils.deleteObject(getKey(vehicleName));
        return result;
    }

    /**
     * 获取key名称
     *
     * @param vehicleName vehicleName
     * @return key名称
     */
    public String getKey(String vehicleName) {
        return String.format(RedisKeyEnum.COLLECTION_REAL_ROUTE.getValue(), vehicleName);
    }
}
