package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.dataobject.mapcollection.VehicleDistanceDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采图模式车辆实际行驶距离信息
 */
@Repository
public class VehicleMapDistanceRepository {

    /**
     * 获取实际行驶距离
     *
     * @param vehicleName vehicleName
     * @return VehicleDistanceDO
     */
    public VehicleDistanceDO get(String vehicleName) {
        VehicleDistanceDO result = RedissonUtils.getObject(getKey(vehicleName));
        return result;
    }

    /**
     * 设置值
     *
     * @param vehicleName vehicleName
     * @param vehicleDistanceDO vehicleDistanceDO
     */
    public void set(String vehicleName, VehicleDistanceDO vehicleDistanceDO) {
        RedissonUtils.setObject(getKey(vehicleName), vehicleDistanceDO);
    }

    /**
     * 获取
     */
    public Map<String, VehicleDistanceDO> listMap(List<String> vehicleNameList) {
        List<String> redisKeyList = vehicleNameList.stream().map(VehicleMapDistanceRepository::getKey)
                .collect(Collectors.toList());
        Map<String, VehicleDistanceDO> map = RedissonUtils.getRedissonClient()
                .getBuckets().get(redisKeyList.toArray(new String[0]));
        Map<String, VehicleDistanceDO> result = map.entrySet().stream().collect(Collectors.toMap(
                entry -> StringUtils.substringAfter(entry.getKey(), RedisKeyEnum.COLLECTION_REAL_DISTANCE.getValue()),
                entry -> entry.getValue()));
        return result;
    }

    /**
     * 删除缓存
     *
     * @param vehicleName vehicleName
     * @return 删除结果
     */
    public boolean remove(String vehicleName) {
        boolean result = RedissonUtils.deleteObject(getKey(vehicleName));
        return result;
    }

    /**
     * 获取key名称
     *
     * @param vehicleName vehicleName
     * @return key名称
     */
    public static String getKey(String vehicleName) {
        return RedisKeyEnum.COLLECTION_REAL_DISTANCE.getValue() + vehicleName;
    }
}
