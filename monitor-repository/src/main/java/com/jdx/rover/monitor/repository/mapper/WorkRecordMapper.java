/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jdx.rover.monitor.bo.cockpit.GetTeamDurationBO;
import com.jdx.rover.monitor.po.WorkRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 工作记录mapper类
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkRecordMapper extends BaseMapper<WorkRecord> {

    /**
     * 获取当日历史工作时长（秒）
     *
     * @param username      username
     * @param cockpitNumber cockpitNumber
     * @return Integer
     */
    @Select({"SELECT SUM(TIMESTAMPDIFF(SECOND, start_time, end_time)) FROM work_record WHERE user_name = #{username} AND start_time BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY AND cockpit_status=#{status} AND end_time IS NOT NULL"})
    Integer getWorkTime(@Param(value = "username") String username, @Param(value = "status") String status);

    /**
     * 获取团队时长（秒）
     *
     * @param wrapper wrapper
     * @return Map<String, Long>
     */
    @Select({"SELECT cockpit_team_number , SUM(TIMESTAMPDIFF(SECOND, start_time, IFNULL(end_time, NOW()))) AS duration FROM work_record ${ew.customSqlSegment}"})
    List<GetTeamDurationBO> getDuration(@Param(Constants.WRAPPER) Wrapper<WorkRecord> wrapper);
}