package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 所有车辆名称信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AllVehicleNameSortRepository {
  /**
   * 获取
   *
   * @return
   */
  @Cacheable(value = LocalCacheConstant.ALL_VEHICLE_NAME_SORT_LIST, unless = "#result?.size() == 0"
      , key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).ALL_VEHICLE_NAME_SORT_LIST")
  public List<String> get() {
    List<String> vehicleNameSortList = RedissonUtils.getObject(RedisKeyEnum.ALL_VEHICLE_NAME_SORT_LIST.getValue());
    if (CollectionUtils.isEmpty(vehicleNameSortList)) {
      log.info("AllVehicleNameSortRepository本地缓存数据未获取到!");
      return new ArrayList<>();
    }
    log.info("AllVehicleNameSortRepository本地缓存数据={}", vehicleNameSortList.size());
    return vehicleNameSortList;
  }

  /**
   * 设置
   *
   * @param vehicleNameSortList
   */
  @LocalCacheEvict(value = LocalCacheConstant.ALL_VEHICLE_NAME_SORT_LIST)
  public void set(List<String> vehicleNameSortList) {
    RedissonUtils.setObject(RedisKeyEnum.ALL_VEHICLE_NAME_SORT_LIST.getValue(), vehicleNameSortList);
  }

  /**
   * 通过车辆名称删除
   *
   * @param vehicleName
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.ALL_VEHICLE_NAME_SORT_LIST)
  public void remove(String vehicleName) {
    List<String> vehicleNameSortList = RedissonUtils.getObject(RedisKeyEnum.ALL_VEHICLE_NAME_SORT_LIST.getValue());
    vehicleNameSortList.remove(vehicleName);
    set(vehicleNameSortList);
  }
}
