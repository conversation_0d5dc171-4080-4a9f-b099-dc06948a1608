package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RSetMultimap;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 用户关注站点记录
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserAttentionEventRepository {

  /**
   * 用户关注的event事件
   *
   * @return
   */
  public List<String> get(String event, String userName) {
    String redisKey = getRedisKey(event);
    RSetMultimap<String, String> userAttentionList = RedissonUtils.getRedissonClient().getSetMultimap(redisKey);
    Set<String> attentionSet = userAttentionList.getAll(userName);
    if (CollectionUtils.isEmpty(attentionSet)) {
      return new ArrayList<>();
    }
    List<String> attentionList = attentionSet.stream().toList();
    log.info("UserAttentionEventRepository缓存数据redisKey={},数据={}", redisKey, attentionList);
    return attentionList;
  }

  /**
   * 设置关注
   *
   * @param event 事件
   *
   * @return
   */
  public boolean set(String event, String userName, String value) {
    String redisKey = getRedisKey(event);
    boolean result = RedissonUtils.getRedissonClient().getSetMultimap(redisKey).put(userName, value);
    return result;
  }

  /**
   * 删除
   *
   * @return
   */
  public boolean delete(String event, String userName, String value) {
    String redisKey = getRedisKey(event);
    boolean result = RedissonUtils.getRedissonClient().getSetMultimap(redisKey).remove(userName, value);
    return result;
  }

  /**
   * 获取key
   *
   * @param event
   * @return
   */
  public static String getRedisKey(String event) {
    String redisKey = new StringBuilder(RedisKeyEnum.USER_ATTENTION_EVENT_PREFIX.getValue())
            .append(event).toString();
    return redisKey;
  }
}
