package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 车辆搜索记录信息
 */
@Service
@Slf4j
public class VehicleSearchRecordRepository {

    /**
     * 读取
     *
     * @param username
     * @return
     */
    @Cacheable(value = LocalCacheConstant.USER_VEHICLE_SEARCH_RECORD_PREFIX, key = "#username")
    public List<String> get(String username) {
        String redisKey = getRedisKey(username);
        RScoredSortedSet<String> scoredSortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(redisKey);
        List<String> vehicleNameList = (List<String>)scoredSortedSet.readAll();
        Collections.reverse(vehicleNameList);
        log.info("VehicleSearchRecordRepository本地缓存数据username={},数据={}", username, vehicleNameList.size());
        return vehicleNameList;
    }

    /**
     * 新增
     *
     * @param username
     * @return
     */
    @LocalCacheEvict(value = LocalCacheConstant.USER_VEHICLE_SEARCH_RECORD_PREFIX, key = "#username")
    public boolean add(String username, String vehicleName) {
        log.info("{}添加车辆{}搜索记录", username, vehicleName);
        String redisKey = getRedisKey(username);
        RScoredSortedSet<String> scoredSortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(redisKey);
        boolean result = scoredSortedSet.add(System.currentTimeMillis(), vehicleName);
        return result;
    }

    /**
     * 移除车辆
     */
    @LocalCacheEvict(value = LocalCacheConstant.USER_VEHICLE_SEARCH_RECORD_PREFIX, key = "#username")
    public boolean remove(String username, List<String> vehicleNameList) {
        log.info("{}移除车辆{}搜索记录", username, vehicleNameList);
        String redisKey = getRedisKey(username);
        RScoredSortedSet<String> scoredSortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(redisKey);
        boolean result = scoredSortedSet.removeAll(vehicleNameList);
        return result;
    }

    /**
     * 移除全部车辆
     */
    @LocalCacheEvict(value = LocalCacheConstant.USER_VEHICLE_SEARCH_RECORD_PREFIX, key = "#username")
    public boolean removeAll(String username) {
        log.info("{}移除用户车辆搜索记录", username);
        String redisKey = getRedisKey(username);
        return RedissonUtils.deleteObject(redisKey);
    }

    /**
     * 获取redis key
     *
     * @param username
     * @return
     */
    private static String getRedisKey(String username) {
        String redisKey = RedisKeyEnum.USER_VEHICLE_SEARCH_RECORD_PREFIX.getValue() + username;
        return redisKey;
    }
}
