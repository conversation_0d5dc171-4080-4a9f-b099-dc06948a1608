/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.user;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RListMultimap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户通知登记缓存
 * @Date 2024/11/1
 * <AUTHOR>
 */
@Service
public class UserNotifyRepository {
    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "user:notify:map";

    /**
     * 通知类型获取通知用户Erp列表
     *
     * @param notifyType 通知类型
     */
    @Cacheable(value = CACHE_NAME, key = "#notifyType")
    public List<String> get(String notifyType) {
        RListMultimap<String, String> map = RedissonUtils.getRedissonClient().getListMultimap(CACHE_NAME);
        List<String> valueList = map.get(notifyType);
        return valueList;
    }

    /**
     * 设置通知用户
     *
     * @param notifyType 通知类型
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#notifyType")
    public void putMapValue(String notifyType, String value) {
        RListMultimap<String, String> map = RedissonUtils.getRedissonClient().getListMultimap(CACHE_NAME);
        map.put(notifyType, value);
    }

    /**
     * 移除用户
     *
     * @param notifyType 通知类型
     * @param userErp 通知用户
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#notifyType")
    public long fastRemoveMapKey(String notifyType, String userErp) {
        RListMultimap<String, String> map = RedissonUtils.getRedissonClient().getListMultimap(CACHE_NAME);
        return map.fastRemove(notifyType, userErp);
    }

    /**
     * 批量保存数据
     *
     * @param notifyType 通知类型
     * @param userList 通知用户列表
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#notifyType")
    public void putAllMapObject(String notifyType, List<String> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        RListMultimap<String, String> map = RedissonUtils.getRedissonClient().getListMultimap(CACHE_NAME);
        map.putAll(notifyType, userList);
    }

    /**
     * 删除通知类型的全部值
     *
     * @param notifyType 通知类型
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#notifyType")
    public List<String> removeAll(String notifyType) {
        RListMultimap<String, String> map = RedissonUtils.getRedissonClient().getListMultimap(CACHE_NAME);
        return map.removeAll(notifyType);
    }

}
