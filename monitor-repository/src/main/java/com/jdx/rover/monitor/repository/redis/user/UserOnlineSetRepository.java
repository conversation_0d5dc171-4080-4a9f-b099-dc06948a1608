/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.user;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.redisson.api.RSet;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * 在线用户集合信息
 *
 * <AUTHOR>
 */
@Service
public class UserOnlineSetRepository {
    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "user:online:set";

    /**
     * 获取
     */
    @Cacheable(value = CACHE_NAME)
    public Set<String> get() {
        RSet<String> rSet = RedissonUtils.getRedissonClient().getSet(CACHE_NAME);
        return rSet.readAll();
    }

    /**
     * 设置对象
     */
    @LocalCacheEvict(value = CACHE_NAME)
    public void add(String userName) {
        RSet<String> rSet = RedissonUtils.getRedissonClient().getSet(CACHE_NAME);
        rSet.add(userName);
    }

    /**
     * 通过用户名称删除对象
     *
     * @param userName 用户名称
     */
    @LocalCacheEvict(value = CACHE_NAME)
    public boolean remove(String userName) {
        RSet<String> rSet = RedissonUtils.getRedissonClient().getSet(CACHE_NAME);
        return rSet.remove(userName);
    }
}