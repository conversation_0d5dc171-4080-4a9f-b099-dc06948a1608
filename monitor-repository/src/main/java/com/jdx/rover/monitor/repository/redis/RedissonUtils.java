/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.redis;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.monitor.common.exception.RedissionLockException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Redisson工具类(静态工具类版本)
 *
 * <AUTHOR>
 */
@Slf4j
public class RedissonUtils {
  private static RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);

  /**
   * 获取RedissonClient
   */
  public static RedissonClient getRedissonClient() {
    return redissonClient;
  }

  /**
   * 获取Bucket
   */
  public static <T> RBucket<T> getRBucket(String key) {
    RBucket<T> bucket = redissonClient.getBucket(key);
    return bucket;
  }

  /**
   * 保存数据
   */
  public static <T> void setObject(String key, T object) {
    RBucket<T> bucket = getRBucket(key);
    bucket.set(object);
  }

  /**
   * 保存数据（失效时间）
   */
  public static <T> void setObject(String key, T object, long expire) {
    RBucket<T> bucket = getRBucket(key);
    bucket.set(object, expire, TimeUnit.SECONDS);
  }

  /**
   * 获取数据
   */
  public static <T> T getObject(String key) {
    RBucket<T> bucket = getRBucket(key);
    return bucket.get();
  }

  /**
   * 删除数据
   */
  public static <T> boolean deleteObject(String key) {
    RBucket<T> bucket = getRBucket(key);
    return bucket.delete();
  }

  /**
   * 保存map数据
   */
  public static <K, V> V setMapObject(String key, K mapKey, V mapObject) {
    RMap<K, V> map = getMap(key);
    return map.put(mapKey, mapObject);
  }

  /**
   * 保存map数据
   */
  public static <K, V> void setMapObject(String key,  Map<K, V> mapObject) {
    RMap<K, V> map = getMap(key);
    map.putAll(mapObject);
  }

  /**
   * 获保存map数据（失效时间）
   */
  public static <K, V> V setMapObject(String key, K mapKey, V mapObject, long expire) {
    RMap<K, V> map = getMap(key);
    V previous = map.put(mapKey, mapObject);
    map.expire(Duration.ofSeconds(expire));
    return previous;
  }

  /**
   * 快速保存一个map键值对
   */
  public static <K, V> boolean fastPutMapValue(String key, K mapKey, V mapValue) {
    RMap<K, V> map = getMap(key);
    return map.fastPut(mapKey, mapValue);
  }

  /**
   * 获取RMAP
   */
  public static <K, V> RMap<K, V> getMap(String key) {
    RMap<K, V> map = redissonClient.getMap(key);
    return map;
  }

  /**
   * 获取map数据
   */
  public static <K, V> V getMapObject(String key, K mapKey) {
    RMap<K, V> map = getMap(key);
    return map.get(mapKey);
  }

  /**
   * 删除map数据
   */
  public static <K, V> boolean deleteMap(String key) {
    RMap<K, V> map = getMap(key);
    return map.delete();
  }

  /**
   * 获取RAtomicLong
   */
  public static RAtomicLong getAtomicLong(String key) {
    RAtomicLong rAtomicLong = redissonClient.getAtomicLong(key);
    return rAtomicLong;
  }

  /**
   * 自增原子long
   */
  public static void setAtomicLongNum(String key, long num, long expire) {
    try {
      RAtomicLong rAtomicLong = getAtomicLong(key);
      if (rAtomicLong.isExists()) {
        rAtomicLong.addAndGet(num);
      } else {
        rAtomicLong.set(num);
        rAtomicLong.expire(Duration.ofSeconds(expire));
      }
    } catch (Exception e) {
      log.error("redisson setAtomicLongNum error!key={}", key, e);
    }
  }

  /**
   * 获取原子long
   */
  public static long getAtomicLongNum(String key) {
    RAtomicLong bucket = getAtomicLong(key);
    return bucket.get();
  }

  public static boolean tryLock(String key) {
    try {
      return redissonClient.getLock(key).tryLock();
    } catch (Exception e) {
      log.error("redisson tryLock error!key={}", key, e);
    }
    return false;
  }

  public static boolean tryLock(String key, long time, TimeUnit unit) throws RedissionLockException{
    try {
      return redissonClient.getLock(key).tryLock(time, unit);
    } catch (Exception e) {
      log.error("redisson tryLock error!key={}", key, e);
      throw new RedissionLockException(e);
    }
  }

    /**
     * 尝试获取锁
     */
    public static boolean tryLock(String key, long waitTime, long leaseTime, TimeUnit timeUnit) {
        try {
            RLock lock = redissonClient.getLock(key);
            return lock.tryLock(waitTime, leaseTime, timeUnit);
        } catch (Exception e) {
            log.error("redisson tryLock error!key={}", key, e);
        }
        return false;
    }

  public static Integer getLockCount(String key) {
    return redissonClient.getLock(key).getHoldCount();
  }

  public static void unLock(String key) {
    try {
      redissonClient.getLock(key).unlock();
    } catch (Exception e) {
      log.error("redisson unlock error!key={}", key, e);
    }
  }

  /**
   * 获取消息队列主题
   *
   * @param topicName 消息队列主题名称
   * @return
   */
  public static RTopic getRTopic(String topicName) {
    RTopic rTopic = redissonClient.getTopic(topicName);
    return rTopic;
  }

  public static boolean hasKey(String key) {
    return redissonClient.getBucket(key).isExists();
  }

  /**
   * 保存map对象
   */
  public static <K, V> void putAllMapObject(String key, Map<K, V> mapObject) {
    mapObject.values().removeIf(Objects::isNull);
    RMap<K, V> map = getMap(key);
    map.putAll(mapObject);
  }

  /**
   * 获取key的过期时间
   * @param key
   * @return
   */
  public static long getKeyExpire(String key) {
    RKeys keys = redissonClient.getKeys();
    long remainTime = keys.remainTimeToLive(key);
    return remainTime < 0 ? 0 : remainTime / 1000;
  }

  /**
   * 获取GeoHash对象
   *
   * @param key key
   * @return RGeo
   */
  public static <T> RGeo<T> getGeo(String key) {
    return redissonClient.getGeo(key);
  }

    /**
     * 获取 RSet 实例
     *
     * @param key 实例key
     * @return RSet
     */
    public static <V> RSet<V> getSet(String key, Duration duration) {
        RSet<V> set = redissonClient.getSet(key);
        if (duration != null && set.remainTimeToLive() == -1) {
            set.expire(duration);
        }
        return set;
    }

    /**
     * 获取 RSet 实例
     *
     * @param key 实例key
     * @return RSet
     */
    public static <V> RSet<V> getSet(String key) {
        return getSet(key, null);
    }
}
