/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.repository.mapper.datacollection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.datacollection.DataCollectionRequirementTag;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数据采集需求关联标签表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Mapper
public interface DataCollectionRequirementTagMapper extends BaseMapper<DataCollectionRequirementTag> {
}
