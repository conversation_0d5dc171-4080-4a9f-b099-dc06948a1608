/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.metadata.api.service.v2.cockpit.CockpitInfoApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <p>
 * 主数据座舱服务相关接口
 * </p>
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "metadata-cockpit", value = "metadata-web")
public interface MetadataCockpitClient extends CockpitInfoApi {
}
