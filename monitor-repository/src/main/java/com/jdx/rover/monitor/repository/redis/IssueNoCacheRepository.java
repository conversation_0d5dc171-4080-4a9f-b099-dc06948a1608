/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.redis;

import java.util.Date;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * This is issue no cache Repository.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Slf4j
public class IssueNoCacheRepository {
  /**
   * <p>
   * Represents the redis template repository.
   * </p>
   */
  @Autowired
  private RedisTemplate<String, Object> clientRedisTemplate;

  /**
   * <p>
   * the redis cache key.
   * </p>
   */
  static final String ISSUE_NO_CACHE_KEY = "issue_no_%s";

  public synchronized String getIssueNo() {
    // “GD”+年月日+四位流水(自0001开始至9999，每日重新编号) ,redis中编号,issue_id_yyyyMMdd
    String dateStr = DateFormatUtils.format(new Date(), "yyyyMMdd");
    String key = String.format(ISSUE_NO_CACHE_KEY, dateStr);
    RedisAtomicLong counter = new RedisAtomicLong(key, clientRedisTemplate.getConnectionFactory());
    if (counter.getExpire() < 0) {
      counter.expire(1, TimeUnit.DAYS);
    }
    Long no = counter.incrementAndGet();
    String noStr = String.format("%05d", no);
    String issueNo = "GD" + DateFormatUtils.format(new Date(), "yyyyMMdd") + noStr;
    return issueNo;
  }
}
