/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.repository.redis.datacollection;

import com.jdx.rover.monitor.entity.datacollection.DataCollectionPlanRoutePointDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数采车规划路径缓存
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Repository
public class DataCollPlanRouteRepository {

    /**
     * 缓存名称
     * 格式：vehicleName-username
     */
    private static final String CACHE_NAME = "data:collection:vehicle:plan:route:%s-%s";

    /**
     * 保存规划路径
     * @param vehicleName 车辆名称
     * @param username 用户名
     */
    public void save(String vehicleName, String username, List<DataCollectionPlanRoutePointDO> dataCollectionPlanRoutePointDOList) {
        RedissonUtils.setObject(getKey(vehicleName, username), dataCollectionPlanRoutePointDOList);
    }

    /**
     * 获取规划路径
     * @param vehicleName 车辆名称
     * @param username 用户名
     * @return 规划路径
     */
    public List<DataCollectionPlanRoutePointDO> get(String vehicleName, String username) {
        return RedissonUtils.getObject(getKey(vehicleName, username));
    }

    /**
     * 删除规划路径
     * @param vehicleName 车辆名称
     * @param username 用户名
     */
    public void remove(String vehicleName, String username) {
        RedissonUtils.deleteObject(getKey(vehicleName, username));
    }

    /**
     * 获取redis key
     * @param vehicleName 车辆名称
     * @param username 用户名
     * @return 缓存key
     */
    private String getKey(String vehicleName, String username) {
        return String.format(CACHE_NAME, vehicleName, username);
    }
}
