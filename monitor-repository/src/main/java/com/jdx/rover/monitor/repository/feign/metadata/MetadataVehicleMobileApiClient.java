package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.metadata.api.service.v2.vehicle.VehicleMobileApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @description: MetadataVehicleMobileApiClient
 * @author: wangguotai
 * @create: 2024-07-15 19:36
 **/
@FeignClient(contextId = "metadata-vehicle-mobile", value = "metadata-web")
public interface MetadataVehicleMobileApiClient extends VehicleMobileApi {
}