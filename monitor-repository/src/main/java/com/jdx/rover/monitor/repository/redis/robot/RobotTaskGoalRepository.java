/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.redis.robot;

import com.jdx.rover.monitor.entity.device.RobotTaskGoalDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.springframework.stereotype.Repository;

/**
 * 机器人停靠点跟踪信息
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Repository
public class RobotTaskGoalRepository {

  /**
   * 获取停靠点跟踪信息
   *
   * @param deviceName
   * @return
   */
  public RobotTaskGoalDO get(String deviceName) {
    String redisKey = getKey();
    RobotTaskGoalDO robotTaskGoalDo = RedissonUtils.getMapObject(redisKey, deviceName);
    return robotTaskGoalDo;
  }

  /**
   * 添加数据
   *
   * @param deviceName
   * @param taskGoalDo
   */
  public void add(String deviceName, RobotTaskGoalDO taskGoalDo) {
    String redisKey = getKey();
    RedissonUtils.setMapObject(redisKey, deviceName, taskGoalDo);
  }

  /**
   * 删除
   *
   * @param deviceName
   */
  public void remove(String deviceName) {
    String redisKey = getKey();
    RedissonUtils.getMap(redisKey).remove(deviceName);
  }

  /**
   * 获取redis key
   *
   * @return
   */
  public static String getKey() {
    String redisKey = RedisKeyEnum.ROBOT_TASK_GOAL.getValue();
    return redisKey;
  }

}
