package com.jdx.rover.monitor.repository.redis.metadata;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.metadata.api.domain.dto.vehicle.VehicleBootUpExceptionInfoDTO;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆启动模块配置
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleBootModuleRepository {
    /**
     * 获取
     *
     * @return
     */
    @Cacheable(value = LocalCacheConstant.VEHICLE_BOOT_MODULE, unless = "#result?.size() == 0"
            , key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).VEHICLE_BOOT_MODULE")
    public List<VehicleBootUpExceptionInfoDTO> get() {
        List<VehicleBootUpExceptionInfoDTO> vehicleNameSortList = RedissonUtils.getObject(RedisKeyEnum.VEHICLE_BOOT_MODULE.getValue());
        log.info("VehicleBootModuleRepository本地缓存数据={}", vehicleNameSortList.size());
        return vehicleNameSortList;
    }

    /**
     * 设置
     *
     * @param list
     */
    @LocalCacheEvict(value = LocalCacheConstant.VEHICLE_BOOT_MODULE)
    public void set(List<VehicleBootUpExceptionInfoDTO> list) {
        RedissonUtils.setObject(RedisKeyEnum.VEHICLE_BOOT_MODULE.getValue(), list);
    }
}
