package com.jdx.rover.monitor.repository.feign.schedule;

import com.jdx.rover.schedule.api.schedule.MiniMonitorFreeApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @description: MiniMonitorFreeApiClient
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-07-17 08:58
 **/
@FeignClient(contextId = "rover-schedule-web-free", value = "rover-schedule-web")
public interface ScheduleMiniMonitorFreeApiClient extends MiniMonitorFreeApi {
}