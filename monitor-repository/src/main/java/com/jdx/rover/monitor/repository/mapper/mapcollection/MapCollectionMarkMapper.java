/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.repository.mapper.mapcollection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionMark;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:59
 * @description 勘查标记表
 */
@Mapper
public interface MapCollectionMarkMapper extends BaseMapper<MapCollectionMark> {

}
