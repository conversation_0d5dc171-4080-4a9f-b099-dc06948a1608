/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.repository.redis.user;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.entity.user.UserIssueDO;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户工单信息
 *
 * <AUTHOR>
 */
@Service
public class UserIssueRepository {
    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "user:issue:object:";

    /**
     * 获取redis key
     *
     * @param userName 用户名称
     */
    public static String getKey(String userName) {
        return CACHE_NAME + userName;
    }

    /**
     * 通过用户名称获取对象
     *
     * @param userName 用户名称
     */
    @Cacheable(value = CACHE_NAME, key = "#userName")
    public UserIssueDO get(String userName) {
        String redisKey = getKey(userName);
        return RedissonUtils.getObject(redisKey);
    }

    /**
     * 设置对象
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public void set(UserIssueDO userIssueDO) {
        String redisKey = getKey(userIssueDO.getUserName());
        RedissonUtils.setObject(redisKey, userIssueDO);
    }

    /**
     * 通过用户名称删除对象
     *
     * @param userName 用户名称
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#userName")
    public boolean remove(String userName) {
        String redisKey = getKey(userName);
        return RedissonUtils.deleteObject(redisKey);
    }

    /**
     * 通过用户名称获取对象
     *
     * @param userNameList 用户名称列表
     */
    public Map<String, UserIssueDO> listMap(List<String> userNameList) {
        List<String> redisKeyList = userNameList.stream().map(UserIssueRepository::getKey).toList();
        Map<String, UserIssueDO> map = RedissonUtils.getRedissonClient().getBuckets()
                .get(redisKeyList.toArray(new String[0]));
        return map.entrySet().stream().collect(Collectors.toMap(
                entry -> StringUtils.substringAfter(entry.getKey(), CACHE_NAME),
                Map.Entry::getValue));
    }

    /**
     * 通过用户名称获取对象
     *
     * @param userNameList 用户名称列表
     */
    public List<UserIssueDO> list(List<String> userNameList) {
        Map<String, UserIssueDO> map = listMap(userNameList);
        return new ArrayList<>(map.values());
    }
}