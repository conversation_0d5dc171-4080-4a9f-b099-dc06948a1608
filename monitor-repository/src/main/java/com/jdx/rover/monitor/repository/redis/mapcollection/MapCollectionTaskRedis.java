/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.repository.redis.mapcollection;

import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/12/10 16:59
 * @description 地图勘查任务Redis
 */
@Repository
public class MapCollectionTaskRedis {

    /**
     * 勘查任务当前处理人
     * key: #0 taskId, value: username
     */
    public static final String MAP_COLLECTION_PROCESSOR = "collection:processor:%s";

    /**
     * 勘查任务锁
     * key: #0 taskId
     */
    private static final String MAP_COLLECTION_TASK_LOCK = "collection:task:lock:%s";

    /**
     * 勘查任务锁，等待时间5S
     *
     * @param taskId taskId
     * @return 是否加锁成功
     */
    public boolean lockTask(Integer taskId) {
        return RedissonUtils.tryLock(String.format(MAP_COLLECTION_TASK_LOCK, taskId), 5, TimeUnit.SECONDS);
    }

    /**
     * 解锁勘查任务
     *
     * @param taskId taskId
     */
    public void unlockTask(Integer taskId) {
        RedissonUtils.unLock(String.format(MAP_COLLECTION_TASK_LOCK, taskId));
    }

    /**
     * 开始勘查任务，绑定任务与用户
     * 若存在key，说明已有绑定关系，返回当前绑定用户做前端提示
     *
     * @param taskId taskId
     * @param username username
     * @return username
     */
    public String processTask(Integer taskId, String username) {
        String key = String.format(MAP_COLLECTION_PROCESSOR, taskId);
        RBucket<String> rBucket = RedissonUtils.getRBucket(key);
        if (rBucket.isExists()) {
            String processor = rBucket.get();

            // 处理人与调用方相同，续期90s
            if (StringUtils.equals(processor, username)) {
                rBucket.expire(Duration.ofSeconds(90));
            }
            return processor;
        }
        rBucket.set(username, Duration.ofSeconds(90));
        return null;
    }

    /**
     * 结束勘查任务
     *
     * @param taskId taskId
     */
    public void endTask(Integer taskId) {
        String key = String.format(MAP_COLLECTION_PROCESSOR, taskId);
        RBucket<String> rBucket = RedissonUtils.getRBucket(key);
        rBucket.delete();
    }

    /**
     * 获取任务当前处理人
     *
     * @param taskId taskId
     * @return username
     */
    public String getTaskProcessor(Integer taskId) {
        String key = String.format(MAP_COLLECTION_PROCESSOR, taskId);
        RBucket<String> rBucket = RedissonUtils.getRBucket(key);
        return rBucket.get();
    }
}
