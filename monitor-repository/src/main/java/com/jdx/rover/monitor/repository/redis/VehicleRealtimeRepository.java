package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实时信息
 *
 * <AUTHOR>
 */
@Service
public class VehicleRealtimeRepository {
  /**
   * 通过车辆名称获取实时信息
   *
   * @param vehicleName
   * @return
   */
  public VehicleRealtimeInfoDTO get(String vehicleName) {
    String redisKey = getKey(vehicleName);
    VehicleRealtimeInfoDTO result = RedissonUtils.getObject(redisKey);
    return result;
  }

  /**
   * 通过车辆名称设置实时信息
   *
   * @param dto
   */
  public void set(VehicleRealtimeInfoDTO dto) {
    String redisKey = getKey(dto.getVehicleName());
    RedissonUtils.setObject(redisKey, dto);
  }

  /**
   * 批量设置车辆实时信息
   *
   * @param dtoList
   */
  public void setList(List<VehicleRealtimeInfoDTO> dtoList) {
    Map<String, VehicleRealtimeInfoDTO> dtoMap = new HashMap<>(dtoList.size());
    for (VehicleRealtimeInfoDTO dto : dtoList) {
      String redisKey = getKey(dto.getVehicleName());
      dtoMap.put(redisKey, dto);
    }
    RedissonUtils.getRedissonClient().getBuckets().set(dtoMap);
  }

  /**
   * 通过车辆名称删除实时信息
   *
   * @param vehicleName
   * @return
   */
  public boolean remove(String vehicleName) {
    String redisKey = getKey(vehicleName);
    boolean result = RedissonUtils.deleteObject(redisKey);
    return result;
  }

  /**
   * 通过车辆名称获取实时
   *
   * @param vehicleNameList
   * @return
   */
  public Map<String, VehicleRealtimeInfoDTO> listMap(List<String> vehicleNameList) {
    List<String> redisKeyList = vehicleNameList.stream().map(VehicleRealtimeRepository::getKey)
        .collect(Collectors.toList());
    Map<String, VehicleRealtimeInfoDTO> map = RedissonUtils.getRedissonClient()
        .getBuckets().get(redisKeyList.toArray(new String[0]));
    Map<String, VehicleRealtimeInfoDTO> result = map.entrySet().stream().collect(Collectors.toMap(
        entry -> StringUtils.substringAfter(entry.getKey(), RedisKeyEnum.REALTIME_VEHICLE_PREFIX.getValue()),
        entry -> entry.getValue()));
    return result;
  }

  /**
   * 通过车辆名称获取实时
   *
   * @param vehicleNameList
   * @return
   */
  public List<VehicleRealtimeInfoDTO> list(List<String> vehicleNameList) {
    Map<String, VehicleRealtimeInfoDTO> map = listMap(vehicleNameList);
    List<VehicleRealtimeInfoDTO> list = new ArrayList(map.values());
    return list;
  }

  /**
   * 获取redis key
   *
   * @param vehicleName
   * @return
   */
  public static String getKey(String vehicleName) {
    String redisKey = RedisKeyEnum.REALTIME_VEHICLE_PREFIX.getValue() + vehicleName;
    return redisKey;
  }


  /**
   * 通过车辆名称获取系统状态
   *
   * @param vehicleName
   * @return
   */
  public String getSystemState(String vehicleName) {
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = this.get(vehicleName);
    if (vehicleRealtimeInfoDTO == null || vehicleRealtimeInfoDTO.getSystemState() == null) {
      return SystemStateEnum.OFFLINE.getSystemState();
    }
    return vehicleRealtimeInfoDTO.getSystemState();
  }

}
