/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.repository.feign.map;

import com.jdx.rover.map.api.service.MapInfoApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 地图基础数据客户端
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@FeignClient(contextId = "map-info",value = "rover-map-web")
public interface MapInfoApiClient extends MapInfoApi {
}
