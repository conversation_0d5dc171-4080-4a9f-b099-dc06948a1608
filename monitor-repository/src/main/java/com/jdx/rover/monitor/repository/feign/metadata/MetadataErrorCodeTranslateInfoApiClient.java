/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.repository.feign.metadata;

import com.jdx.rover.metadata.api.service.v2.technical.ErrorCodeTranslateInfoApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 主数据错误码转换接口
 *
 * <AUTHOR>
 * @date 2024/08/05
 */
@FeignClient(contextId = "metadata-error-translate", value = "metadata-web")
public interface MetadataErrorCodeTranslateInfoApiClient extends ErrorCodeTranslateInfoApi {
}
