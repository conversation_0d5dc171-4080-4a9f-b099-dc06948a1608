package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.local.cache.autoconfigure.service.LocalCacheEvictService;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.entity.UserStopInfoEntity;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 用户停靠点缓存
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserStopRepository {

  @Autowired
  private LocalCacheEvictService localCacheEvictService;
  /**
   * 读取
   *
   * @param username
   * @return
   */
  @Cacheable(value = LocalCacheConstant.USER_STOP_LIST, unless = "#result?.size() == 0", key = "#username")
  public List<UserStopInfoEntity> get(String username) {
    String redisKey = getRedisKey(username);
    RList<UserStopInfoEntity> rList = RedissonUtils.getRedissonClient().getList(redisKey);
    List<UserStopInfoEntity> result = rList.readAll();
    log.info("UserStopRepository本地缓存数据username={},数据={}", username, result.size());
    return result;
  }

  /**
   * 设置
   *
   * @param username
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.USER_STOP_LIST, key = "#username")
  public boolean set(String username, List<UserStopInfoEntity> userStopList) {
    String redisKey = getRedisKey(username);
    RList<UserStopInfoEntity> userStopCacheList = RedissonUtils.getRedissonClient().getList(redisKey);
    boolean result = userStopCacheList.addAll(userStopList);
    return result;
  }

  /**
   * 删除用户下停靠点
   *
   * @param userName
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.USER_STOP_LIST, key = "#username")
  public void remove(String userName) {
    RedissonUtils.deleteObject(getRedisKey(userName));
  }

  /**
   * 删除停靠点关联所有缓存
   *
   * @return
   */
  public void clearAll() {
    RedissonClient redisson = RedissonUtils.getRedissonClient();
    long num = redisson.getKeys().deleteByPattern(RedisKeyEnum.USER_STOP_LIST_PREFIX.getValue() + "*");
    log.info("清除用户停靠点信息数{}", num);
    localCacheEvictService.publishByName(new String[]{LocalCacheConstant.USER_STOP_LIST});
  }

  /**
   * 初始化设置(先删除,后写入)
   *
   * @param username
   * @return
   */
  @LocalCacheEvict(value = LocalCacheConstant.USER_STOP_LIST, key = "#username")
  public boolean initList(String username, List<UserStopInfoEntity> userStopList) {
    String redisKey = getRedisKey(username);
    RList<UserStopInfoEntity> userStopCacheList = RedissonUtils.getRedissonClient().getList(redisKey);
    userStopCacheList.delete();

    userStopList.removeIf(Objects::isNull);
    boolean result = true;
    if (CollectionUtils.isNotEmpty(userStopList)) {
      result = userStopCacheList.addAll(userStopList);
    }
    return result;
  }

  /**
   * 通过用户名获取redisKey
   *
   * @param username
   * @return
   */
  public static String getRedisKey(String username) {
    String redisKey = RedisKeyEnum.USER_STOP_LIST_PREFIX.getValue() + username;
    return redisKey;
  }

}
