package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RList;
import org.redisson.api.RSortedSet;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

/**
 * 用户关注车辆列表缓存
 */
@Service
@Slf4j
public class UserAttentionRepository {

    /**
     * 读取
     *
     * @param username
     * @return
     */
    @Cacheable(value = LocalCacheConstant.USER_ATTENTION_VEHICLE_NAME_SET, key = "#username")
    public Set<String> get(String username) {
        String redisKey = getRedisKey(username);
        RSortedSet<String> rSet = RedissonUtils.getRedissonClient().getSortedSet(redisKey);
        Collection<String> result = rSet.readAll();
        log.info("UserAttentionVehicleNameRepository本地缓存数据username={},数据={}", username, result.size());
        return new TreeSet<>(result);
    }

    /**
     * 初始化 (先删除，后增加)
     *
     * @param username
     * @return
     */
    @LocalCacheEvict(value = LocalCacheConstant.USER_ATTENTION_VEHICLE_NAME_SET, key = "#username")
    public boolean initSet(String username, List<String> vehicleNameList) {
        String redisKey = getRedisKey(username);
        RList<String> vehicleNameSet = RedissonUtils.getRedissonClient().getList(redisKey);
        vehicleNameSet.delete();
        boolean result = true;
        if (CollectionUtils.isNotEmpty(vehicleNameList)) {
            result = vehicleNameSet.addAll(vehicleNameList);
        }
        return result;
    }

    /**
     * 删除缓存
     */
    @LocalCacheEvict(value = LocalCacheConstant.USER_ATTENTION_VEHICLE_NAME_SET, key = "#username")
    public void delete(String username) {
        log.info("取消用户{}关注所有车辆信息", username);
        String redisKey = getRedisKey(username);
        RList<String> vehicleNameList = RedissonUtils.getRedissonClient().getList(redisKey);
        vehicleNameList.delete();
    }

    /**
     * 设置
     *
     * @param username
     * @return
     */
    public boolean isContainVehicleName(String username, String vehicleName) {
        String redisKey = getRedisKey(username);
        boolean result = RedissonUtils.getRedissonClient().getList(redisKey).contains(vehicleName);
        return result;
    }

    /**
     * 通过用户名获取redisKey
     *
     * @param username
     * @return
     */
    public static String getRedisKey(String username) {
        String redisKey = RedisKeyEnum.USER_ATTENTION_VEHICLE_NAME_SET_PREFIX.getValue() + username;
        return redisKey;
    }
}
