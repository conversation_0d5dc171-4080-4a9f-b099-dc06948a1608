package com.jdx.rover.monitor.repository.redis.sort;

import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 所有车辆名称信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PowerScoreSortedSetRepository {
  /**
   * 获取
   *
   * @return
   */
  @Cacheable(value = LocalCacheConstant.SORTED_SET_POWER, unless = "#result?.size() == 0"
      , key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).SORTED_SET_POWER")
  public Collection<String> get() {
    RScoredSortedSet<String> rScoredSortedSet = getRScoredSortedSet();
    Collection<String> result = rScoredSortedSet.readAll();
    log.info("PowerScoreSortedSetRepository本地缓存数据={}", result.size());
    return result;
  }

  /**
   * 获取
   *
   * @return
   */
  @Cacheable(value = LocalCacheConstant.SORTED_SET_POWER, unless = "#result?.size() == 0"
      , key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).SORTED_SET_POWER+'_MAP'")
  public Map<String, Double> getValueAndScoreMap() {
    Collection<String> collection = get();
    List<String> vehicleNameList = new ArrayList<>(collection);
    RScoredSortedSet<String> rScoredSortedSet = getRScoredSortedSet();
    List<Double> result = rScoredSortedSet.getScore(vehicleNameList);
    Map<String, Double> map = new LinkedHashMap<>();
    for (int i = 0; i < vehicleNameList.size(); i++) {
      map.put(vehicleNameList.get(i), result.get(i));
    }
    log.info("PowerScoreSortedSetRepository本地缓存数据={}", map.size());
    return map;
  }

  /**
   * 设置
   */
  @LocalCacheEvict(value = LocalCacheConstant.SORTED_SET_POWER)
  public void set(double score, String vehicleName) {
    RScoredSortedSet<String> rScoredSortedSet = getRScoredSortedSet();
    Double scoreDb = rScoredSortedSet.getScore(vehicleName);
    if (scoreDb != null && score == scoreDb) {
      return;
    }
    rScoredSortedSet.add(score, vehicleName);
  }

  /**
   * 删除
   */
  @LocalCacheEvict(value = LocalCacheConstant.SORTED_SET_POWER)
  public boolean remove(String vehicleName) {
    RScoredSortedSet<String> rScoredSortedSet = getRScoredSortedSet();
    boolean result = rScoredSortedSet.remove(vehicleName);
    return result;
  }

  private RScoredSortedSet<String> getRScoredSortedSet() {
    RScoredSortedSet<String> rScoredSortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(RedisKeyEnum.SORTED_SET_POWER.getValue());
    return rScoredSortedSet;
  }
}
