package com.jdx.rover.monitor.repository.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jdx.rover.monitor.bo.accident.AccidentBO;
import com.jdx.rover.monitor.bo.accident.AccidentBoardDetailBO;
import com.jdx.rover.monitor.bo.accident.AccidentBoardDetailQueryBO;
import com.jdx.rover.monitor.bo.accident.AccidentExportBO;
import com.jdx.rover.monitor.bo.accident.DefectiveAccidentBO;
import com.jdx.rover.monitor.po.Accident;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 事故信息mapper
 */
@Mapper
public interface AccidentMapper extends BaseMapper<Accident> {

    @Select("select a.*, a.create_time as accidentTime, a.accident_report_time as accidentReportTime, a.issue_number as issueNumber, ad.operation_user as operationUser, ad.operation_handle_method, ad.safety_group_accident_type, ad.safety_group_accident_level, ad.safety_group_accident_tag as accidentTag, " +
            "ad.safety_group_accident_module as accidentModule, ad.safety_group_accident_resolution_status, ad.safety_group_description, ad.safety_group_accident_solution from accident a " +
            "left join accident_detail ad on ad.accident_no = a.accident_no " +
            "${ew.customSqlSegment}")
    IPage<AccidentBO> getPageList(IPage<Accident> iPage, @Param(Constants.WRAPPER)QueryWrapper<AccidentBO> accidentBOQueryWrapper);

    @Select("select * from accident a left join accident_detail ad on ad.accident_no = a.accident_no ${ew.customSqlSegment}")
    List<AccidentExportBO> getAccidentExportList(@Param(Constants.WRAPPER)QueryWrapper<AccidentExportBO> accidentBOQueryWrapper);

    @Select("<script>" +
            "SELECT a.accident_no, a.is_safety_group_edit, ad.technical_support_accident_level, a.accident_day_time FROM accident a LEFT JOIN accident_detail ad on ad.accident_no = a.accident_no " +
            "WHERE bug_code is not null " +
            "<if test='startTime != null'>AND a.accident_report_time &gt;= #{startTime} </if>" +
            "<if test='endTime != null'>AND a.accident_report_time &lt;= #{endTime} </if>" +
            "</script>")
    List<DefectiveAccidentBO> getDefectiveAccidents(Date startTime, Date endTime);

    @Select("<script>" +
            "SELECT a.accident_no, a.vehicle_name, a.accident_report_time as accidentTime, a.bug_create_time as bugCreateTime, a.bug_code as bugCode, a.bug_status as bugStatus, mbr.description as bugDescription, " +
            "ad.technical_support_accident_level as accidentLevel, ad.safety_group_accident_module as accidentModule, ad.safety_group_accident_tag as accidentTag, ad.safety_group_description " +
            "FROM accident a LEFT JOIN accident_detail ad on ad.accident_no = a.accident_no LEFT JOIN monitor_bug_record mbr on mbr.bug_code = a.bug_code " +
            "WHERE a.bug_code is not null " +
            "<if test='queryBO.startTime != null'> AND a.accident_report_time &gt;= #{queryBO.startTime} </if>" +
            "<if test='queryBO.endTime != null'> AND a.accident_report_time &lt;= #{queryBO.endTime} </if>" +
            "<if test='queryBO.accidentLevel != null'> AND ad.technical_support_accident_level = #{queryBO.accidentLevel} </if>" +
            "<if test='queryBO.bugStatusList != null and !queryBO.bugStatusList.isEmpty()'> " +
                "and a.bug_status in " +
                "<foreach item='busStatus' index='index' collection='queryBO.bugStatusList' open='(' separator=',' close=')'>" +
                "#{busStatus}" +
                "</foreach>" +
            "</if>" +
            "<if test='queryBO.isSafetyGroupEdit != null'> AND a.is_safety_group_edit = #{queryBO.isSafetyGroupEdit} </if>" +
            "order by a.accident_report_time " +
            "</script>")
    List<AccidentBoardDetailBO> getAccidentBoardDetailList(@Param("queryBO") AccidentBoardDetailQueryBO queryBO);
}
