<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rover-monitor</artifactId>
        <groupId>com.jdx.rover.monitor</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>monitor-domain</artifactId>
    <name>monitor-domain</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-monitor-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-ota-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover.monitor</groupId>
            <artifactId>monitor-common</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-datacenter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>map-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-permission-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-server-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-schedule-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-metadata-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-shadow-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-ticket-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>intelligent-device-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-transport-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover.geometry</groupId>
            <artifactId>rover-geometry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>infrastructure-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.k2</groupId>
            <artifactId>k2-indoor-map-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover.integrate</groupId>
            <artifactId>integrate-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>net.postgis</groupId>
            <artifactId>postgis-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
    </dependencies>
</project>