/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is the entity for vehilce take over info cache.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleTakeOverEntity {

  /**
   * <p>
   * Represents the user's name. The default value is null. It's changeable.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * Represents the command source. The default value is null. It's changeable.
   * </p>
   */
  private String commandSource;
  
  /**
   * <p>
   * Represents the status of operation. The default value is null. It's changeable.
   * </p>
   */
  private String operationStatus;

  /**
   * <p>
   * Represents the date of operation. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date operateTime;

  /**
   * <p>
   * 座席编号
   * </p>
   */
  private String cockpitNumber;

}
