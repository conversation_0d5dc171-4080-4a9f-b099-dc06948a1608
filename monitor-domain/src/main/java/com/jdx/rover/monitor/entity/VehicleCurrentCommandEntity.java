/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import com.google.common.base.Objects;
import lombok.Data;

/**
 * <p>
 * This is the entity for vehilce current operation cache.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleCurrentCommandEntity {

  /**
   * <p>
   * Represents the name of user. The default value is 0. It's changeable.
   * </p>
   */
  private Integer userName;

  /**
   * <p>
   * Represents the target velocity. The default value is 0.0. It's changeable.
   * </p>
   */
  private Double targetVelocity;

  /**
   * <p>
   * Represents the target angle. The default value is 0.0. It's changeable.
   * </p>
   */
  private Double targetAngle;

  /**
   * <p>
   * Represents the name of module. It's changeable.
   * </p>
   */
  private String moduleName;

  @Override
  public boolean equals(Object obj) {
    if (this == obj) {
      return true;
    }
    if (obj == null) {
      return false;
    }
    if (obj instanceof VehicleCurrentCommandEntity) {
      VehicleCurrentCommandEntity other = (VehicleCurrentCommandEntity) obj;
      return Objects.equal(this.userName, other.userName)
        && Objects.equal(this.targetVelocity, other.targetVelocity)
        && Objects.equal(this.targetAngle, other.targetAngle);
    }
    return false;
  }

}
