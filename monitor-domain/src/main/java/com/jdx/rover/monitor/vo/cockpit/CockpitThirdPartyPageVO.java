/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.cockpit;

import com.jdx.rover.common.domain.page.PageVO;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/8/25 16:35
 * @description 三方车座席多车页
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CockpitThirdPartyPageVO extends PageVO {

    /**
     * 供应商
     * @see com.jdx.rover.metadata.api.domain.enums.SupplierEnum
     */
    @NotBlank(message = "供应商不能为空")
    private String supplier;
}
