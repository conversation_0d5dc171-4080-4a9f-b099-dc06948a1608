/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import java.util.Date;
import lombok.Data;

/**
 * 工单操作缓存.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueOperateCacheEntity {
  /**
   * <p>
   * 操作用户.
   * </p>
   */
  private String operateUser;

  /**
   * <p>
   * 操作时间.
   * </p>
   */
  private Date operateTime;

  /**
   * <p>
   * 标题.
   * </p>
   */
  private String operateTitle;

  /**
   * <p>
   * 信息.
   * </p>
   */
  private String operateMsg;

  /**
   * <p>
   * 报警ID.
   * </p>
   */
  private Integer alarmId;
}
