/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.LinkedList;

/**
 * <p>
 * This is the entity for vehilce schedule manage.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleEntity extends ScheduleOrderEntity {

  /**
   * <p>
   * Represents the vehicle name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * The record time of the schedule.
   * </p>
   */
  @J<PERSON>NField(format = "yyyy-MM-dd HH:mm:ss")
  private Date recordTime;

  /**
   * <p>
   * Represents the schedule's no. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleNo;

  /**
   * <p>
   * Represents the vehicle's schedul state. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents the schedule task type. The default value is null. It's changeable.
   * </p>
   */
  private String taskType;

  /**
   * <p>
   * The start time of the schedule.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date startDateTime;

  /**
   * <p>
   * The depart stop time of the schedule.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date estDepartureTime;

  /**
   * <p>
   * The refresh id of the schedule.
   * </p>
   */
  private Integer navRefreshId = 999;

  /**
   * <p>
   * Represent the global mileage of schedule. The default value is null. It's changeable.
   * </p>
   */
  private Double globalMileage;

  /**
   * <p>
   * Represent the finished mileage of schedule. The default value is null. It's changeable.
   * </p>
   */
  private Double finishedMileage = 0.0;

  /**
   * <p>
   * Represent the total finished mileage of schedule. The default value is null. It's changeable.
   * </p>
   */
  private Double totalFinishedMileage = 0.0;

  /**
   * <p>
   * Represents the list of stop. The default value is null. It's changeable.
   * </p>
   */
  private LinkedList<MonitorScheduleStopEntity> stop = new LinkedList<>();

}
