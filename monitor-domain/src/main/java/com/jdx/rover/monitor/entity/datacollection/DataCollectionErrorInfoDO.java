/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.entity.datacollection;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 数据采集车辆异常DO
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class DataCollectionErrorInfoDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 异常编号
     */
    private String errorCode;

    /**
     * 异常名称
     */
    private String errorCodeName;

    /**
     * 异常分类
     */
    private String category;

    /**
     * 异常描述
     */
    private String description;

}