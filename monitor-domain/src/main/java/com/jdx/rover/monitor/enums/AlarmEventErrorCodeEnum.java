/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * 报警类型和错误异常关联.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AlarmEventErrorCodeEnum {
  /**
   * <p>
   * The enumerate alarm event type and solution type of vehicle.
   * </p>
   */
  VEHICLE_STOP_BUTTON("VEHICLE_STOP_BUTTON", "", "DRIVEMODE_TAKEOVER_EMERGENCY_STOP"),
  LOW_BATTERY("LOW_BATTERY", "-13005", "LOW_BATTERY_ERROR_CODE_TYPE"),
  VEHICLE_STOP_FATAL("VEHICLE_STOP_FATAL", "", "ABNORMAL_STOP_ERROR_LEVE_TYPE"),
  VEHICLE_STOP_GUARDIAN("VEHICLE_STOP_GUARDIAN", "-13600", "GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE"),
  VEHICLE_STOP_TIMEOUT("VEHICLE_STOP_TIMEOUT", "-13200", "STUCK_ERROR_CODE_TYPE"),
  VEHICLE_CRASH("VEHICLE_CRASH", "-1055", "ERROR_DRIVER_CHASSIS_STOP_BUMPER"),
  LOW_PRESSURE("LOW_PRESSURE", "-1209", "ERROR_DRIVER_CHASSIS_WHEEL_ALARM"),
  SENSOR_ERROR("SENSOR_ERROR", "-1306", "ERROR_DRIVER_CLASS_DATA_IS_ERROR"),
  VEHICLE_STOP_TRAFFICLIGHT_FAIL("VEHICLE_STOP_TRAFFICLIGHT_FAIL", "-9341", "ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_INVALID_GOAL"), 
  VEHICLE_STOP_INTERSECTION_STUCK("VEHICLE_STOP_INTERSECTION_STUCK", "-9342", "ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_NO_SOLUTION"),
  VEHICLE_STOP_TRAFFICLIGHT_TAKEUP("VEHICLE_STOP_TRAFFICLIGHT_TAKEUP", "-9400", "ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_INVALID_GOAL"),
  VEHICLE_STOP_TRAFFICLIGHT_ADJUST("VEHICLE_STOP_TRAFFICLIGHT_ADJUST", "-9401", "ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_NO_SOLUTION_TYPE"),
  VEHICLE_STOP_STOP_TAKEUP("VEHICLE_STOP_STOP_TAKEUP", "-9402", "ERROR_PLANNING_PARKING_INVALID_GOAL"),
  VEHICLE_STOP_STOP_ADJUST("VEHICLE_STOP_STOP_ADJUST", "-9403", "ERROR_PLANNING_PARKING_NO_SOLUTION_TYPE"),
  OPERATION_ALARM("OPERATION_ALARM", "", "OPERATION_ALARM"),
  PASS_NO_SIGNAL_INTERSECTION("PASS_NO_SIGNAL_INTERSECTION", "", "PASS_NO_SIGNAL_INTERSECTION");

  /**
   * <p>
   * 报警类型.
   * </p>
   */
  private String alarmEventType;

  /**
   * <p>
   * 错误数字编码.
   * </p>
   */
  private String errorNo;

  /**
   * <p>
   * 错误英文编码.
   * </p>
   */
  private String errorCode;

  public static AlarmEventErrorCodeEnum of(String alarmEventType) {
    for (AlarmEventErrorCodeEnum em : AlarmEventErrorCodeEnum.values()) {
      if (Objects.equals(alarmEventType, em.getAlarmEventType())) {
        return em;
      }
    }
    return null;
  }
}
