/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.constant;

/**
 * <AUTHOR>
 * @date 2025/4/1 17:33
 * @description 地图采集常量
 */
public class MapCollectConstant {

    /**
     * 车辆地图采集模式告警用时，单位：分钟
     */
    public final static long MAX_VEHICLE_MAP_COLLECT_TIME = 240;

    /**
     * 地图采集存储告警
     */
    public final static String VEHICLE_MAP_COLLECT_STORAGE_ALARM = "存储告警";

    /**
     * 告警消失时间：小时位
     */
    public final static int STORAGE_ALARM_DEADLINE_HOUR = 23;

    /**
     * 告警消失时间：分钟位
     */
    public final static int STORAGE_ALARM_DEADLINE_MIN = 0;

    /**
     * 地图采集任务锁
     */
    private final static String MAP_COLLECT_TASK_LOCK = "lock:map:collection:task:";

    /**
     * 延时任务topic
     */
    public static final String MAP_TASK_AUTO_UPLOAD_JOB_TASK = "mapTaskAutoUploadJobTask";

    /**
     * 采集任务锁
     *
     * @param taskId taskId
     * @return lock key
     */
    public static String getTaskLock(Integer taskId) {
        return MAP_COLLECT_TASK_LOCK + taskId;
    }
}
