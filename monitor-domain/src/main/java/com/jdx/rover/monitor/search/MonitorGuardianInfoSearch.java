/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.search;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is a monitor guardian info search entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorGuardianInfoSearch extends PageVO {

  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the start time of guardian info.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * <p>
   * Represents the end time of guardian info.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;

  /**
   * <p>
   * Represents the city name of guardian info.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the station name of guardian info.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the filter alarm info.
   * </p>
   */
  private List<String> filterAlarm;

  /**
   * 事件来源
   */
  private List<String> filterSource;
}