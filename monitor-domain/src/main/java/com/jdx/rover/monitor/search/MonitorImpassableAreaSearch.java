/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.search;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 * This is a ImpassableAreaSearch entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorImpassableAreaSearch extends PageVO {

  /**
   * <p>
   * Represents the no of vehicle. The default value is null It's changeable.
   * </p>
   */
  private String terminalNo;

  /**
   * <p>
   * Represents the utmLng of impassable area. The default value is null It's
   * changeable.
   * </p>
   */
  @NotBlank
  private String utmLng;

  /**
   * <p>
   * Represents the utmLat of impassable area. The default value is null It's
   * changeable.
   * </p>
   */
  @NotBlank
  private String utmLat;

  /**
   * <p>
   * Represents the type of impassable area coord. The default value is null. It's
   * changeable.
   * </p>
   */
  private Integer coordType;

  /**
   * <p>
   * Represents the status of impassable area coord. The default value is null.
   * It's changeable.
   * </p>
   */
  private Integer coordStatus;

  /**
   * <p>
   * Represents the type list of impassable area. The default value is null.
   * It's changeable.
   * </p>
   */
  private List<Integer> type;

  /**
   * <p>
   * Represents the sort type of the impassable area data. The default value is
   * null. It's changeable.
   * </p>
   */
  private Integer sortType;

  /**
   * <p>
   * Represents the coord of impassable area. The default value is null It's
   * changeable.
   * </p>
   */
  @NotBlank
  private String utmZone;

  /**
   * <p>
   * Represents the sidx of impassable area. The default value is null It's
   * changeable.
   * </p>
   */
  private String sidx;

  /**
   * <p>
   * Represents the userName of impassable area.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * Represents the effectType of impassable area.
   * </p>
   */
  private List<Integer> effectType;

  /**
   * <p>
   * Represents the effectState of impassable area.
   * </p>
   */
  private List<Integer> effectState;

}
