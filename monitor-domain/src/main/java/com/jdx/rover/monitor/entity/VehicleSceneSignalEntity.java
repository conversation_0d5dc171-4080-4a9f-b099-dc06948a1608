/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 定位置信度缓存.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleSceneSignalEntity {
  /**
   * <p>
   * 定位置信度值
   * </p>
   */
  private Double sceneSignal;

  /**
   * <p>
   * 告警触发时间
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date reportTime;

  /**
   * <p>
   * 追踪Id.
   * </p>
   */
  private String traceId;

}
