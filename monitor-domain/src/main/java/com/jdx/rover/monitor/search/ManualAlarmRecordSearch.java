/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.search;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * 人工告警查询
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ManualAlarmRecordSearch extends PageVO {

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 提报用户
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * 提报来源
   * </p>
   */
  private String source;

  /**
   * <p>
   * 开始时间
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * <p>
   * 结束时间
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;
}
