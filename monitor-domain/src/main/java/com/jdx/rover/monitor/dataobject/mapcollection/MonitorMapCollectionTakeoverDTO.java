package com.jdx.rover.monitor.dataobject.mapcollection;

import lombok.Data;

/**
 * @description: 中控地图采集清除接管消息
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2025-01-09 17:59
 **/
@Data
public class MonitorMapCollectionTakeoverDTO {

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 用户名
     */
    private String cockpitUserName;

    /**
     * 座舱编号
     */
    private String cockpitNumber;
}