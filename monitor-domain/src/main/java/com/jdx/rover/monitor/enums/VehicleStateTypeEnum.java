/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆状态类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleStateTypeEnum {
  VEHICLE_REALTIME_STATE("realtimeState", "车辆实时状态"),
  VEHICLE_SCHEDULE_STATE("scheduleState", "车辆业务状态"),
  VEHICLE_ISSUE_STATE("issueState", "车辆工单状态"),
  VEHICLE_CONTROL_STATE("controlState", "车辆接管状态"),
  ;

  /**
   * 值
   */
  private String value;

  /**
   * 名称
   */
  private String name;
}
