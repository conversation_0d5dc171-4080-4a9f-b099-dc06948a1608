/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.search;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * This is a basic search entity for monitor data.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorDataListSearch extends PageVO {

  /**
   * <p>
   * Represents the city id of data search. It's changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the station id of data search. It's changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the vehicle name of record search. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the start occurrence timestamp of data search. It's changeable.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * <p>
   * Represents the end occurrence timestamp of data search. It's changeable.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;

}
