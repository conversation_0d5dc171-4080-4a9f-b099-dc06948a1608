/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.cockpit;

import com.jdx.rover.common.domain.page.PageVO;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.ToString;

/**
 * 座席多车页
 *
 * <AUTHOR>
 */
@Data
@ToString
public class CockpitVehiclePageVO extends PageVO {
    /**
     * 坐席编号
     */
    @NotBlank
    private String cockpitNumber;
}
