/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.entity.datacollection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数采车规划路径DO
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataCollectionPlanRoutePointDO {

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;
}
