/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * This is a monitor user operation entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserOperationEntity implements Serializable {

  /**
   *Represents the serialVersion id.
   */
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * Represents the vehicle's name. The default value is 0. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the name of user. The default value is 0. It's changeable.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * Represents the operation type of monitor user operation log. It's changeable.
   * </p>
   */
  private String operationType;

  /**
   * <p>
   *  操作来源
   * </p>
   */
  private String operationSource;

  /**
   * <p>
   * Represents the operation message of monitor user operation log. It's changeable.
   * </p>
   */
  private String operationMessage;

  /**
   * <p>
   * Represents the record timestamp of monitor user operation log. The default value is
   * CURRENT_TIMESTAMP. It's changeable.
   * </p>
   */
  private Date timestamp;

  /**
   * 请求ID
   */
  private String requestId;

  /**
   * 操作状态(INIT,RUNNING,SUCCESS,FAIL)
   */
  private String state;
}
