/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.entity.device;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 分组信息
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class RobotGroupInfoDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分组编号
     */
    private String groupNo;

    /**
     * 产品
     */
    private String productKey;


    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 自动充电限制电量
     */
    private Double autoChargeLimit;

    /**
     * 任务充电限制电量
     */
    private Double missionChargeLimit;

    /**
     * 强制充电限制电量
     */
    private Double forceChargeLimit;

}
