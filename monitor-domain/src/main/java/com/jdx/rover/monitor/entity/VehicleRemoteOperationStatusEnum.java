/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a vehicle remoteoperation status enum.
 * </p>
 * 
 * <p>
 * <strong> vehicle remote operation status: </strong> enumeration of the class
 * vehicle operation status.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum VehicleRemoteOperationStatusEnum {
  /**
   * <p>
   * The enumerate vehicle remote operation status.
   * </p>
   */
  UNKNOWN("UNKNOWN"), NONE("NONE"), TAKEOVER("TAKEOVER"), TEMPORARY("TEMPORARY");

  /**
   * <p>
   * The vehicle remote operation status corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String operationStatus;
}
