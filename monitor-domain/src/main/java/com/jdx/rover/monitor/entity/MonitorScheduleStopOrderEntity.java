/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import lombok.Data;

/**
 * <p>
 * This is the entity for vehilce schedule stop order.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleStopOrderEntity extends ScheduleOrderEntity {

  /**
   * <p>
   * Represents the stop's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the stop's goal id. The default value is null. It's changeable.
   * </p>
   */
  private Integer goalId;

  /**
   * <p>
   * Represents the stop's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the stop's type. The default value is null. It's changeable.
   * </p>
   */
  @Deprecated
  private String type;

  /**
   * <p>
   * Represents the stop's action. The default value is null. It's changeable.
   * </p>
   */
  private String stopAction;

}
