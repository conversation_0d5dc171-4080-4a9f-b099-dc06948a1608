/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import lombok.Data;

/**
 * 工单缓存.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueCacheEntity {
  /**
   * <p>
   * 工单编号	,“GD”+年月日+四位流水(自0001开始至9999，每日重新编号).
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * 工单状态	(WAITING待处理,PROCESS处理中,FINISH处理完成).
   * </p>
   */
  private String issueState;

  /**
   * <p>
   * 订阅Id.
   * </p>
   */
  private Integer listenerId;

}
