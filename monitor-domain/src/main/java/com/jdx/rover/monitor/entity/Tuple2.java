/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import java.io.Serializable;

/**
 * <p>
 * 二元数组.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class Tuple2<T, R> implements Serializable {
    private static final long serialVersionUID = 1L;

    private T t;
    private R r;

    public Tuple2() {
    }

    private Tuple2(T t, R r) {
        this.t = t;
        this.r = r;
    }

    public Object getField(int pos) {
        switch (pos) {
            case 0:
                return this.t;
            case 1:
                return this.r;
            default:
                throw new IndexOutOfBoundsException(String.valueOf(pos));
        }
    }

    public static <T, R> Tuple2<T, R> tuple(T t, R r) {
        return new Tuple2<T, R>(t, r);
    }

    @Override
    public String toString() {
        return t.toString() + "_" + r.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (!(o instanceof Tuple2)) {
            return false;
        } else {
            Tuple2 tuple = (Tuple2) o;
            if (this.t != null) {
                if (!this.t.equals(tuple.t)) {
                    return false;
                }
            } else if (tuple.t != null) {
                return false;
            }
            if (this.r != null) {
                if (!this.r.equals(tuple.r)) {
                    return false;
                }
            } else if (tuple.r != null) {
                return false;
            }
            return true;
        }
    }

    @Override
    public int hashCode() {
        int result = this.t != null ? this.t.hashCode() : 0;
        result = 31 * result + (this.r != null ? this.r.hashCode() : 0);
        return result;
    }

}
