/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is the entity for vehilce schedule stop manage.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleStopEntity {

  /**
   * <p>
   * Represents the stop's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the stop's goal id. The default value is null. It's changeable.
   * </p>
   */
  private Integer goalId;

  /**
   * <p>
   * Represents the stop's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the stop's lat. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the stop's lon. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represents the stop's type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the stop's action. The default value is null. It's changeable.
   * </p>
   */
  private String stopAction;

  /**
   * <p>
   * Represents stop travel status. The default value is null. It's changeable.
   * </p>
   */
  private String travelStatus;

  /**
   * <p>
   * Represent the arrived time. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date arrivedTime;

  /**
   * <p>
   * Represent the start time. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  /**
   * <p>
   * Represent the depart time. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date departTime;

  /**
   * <p>
   * Represent the est depart time. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date estDepartTime;

  /**
   * <p>
   * Represent the waiting duration of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer waitingTime;

  /**
   * <p>
   * Represent the total order num of stop. The default value is null. It's changeable.
   * </p>
   */
  private Integer totalOrderNum;

  /**
   * <p>
   * Represent the global mileage of stop. The default value is null. It's changeable. 
   * </p>
   */
  private Double globalMileage;

}
