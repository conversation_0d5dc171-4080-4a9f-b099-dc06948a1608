/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.search;

import java.util.Date;
import lombok.Data;

/**
 * <p>
 * This is an issue record search entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorIssueRecordSearch {
  
  /**
   * <p>
   * Represents the no of issue.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the phone of user.
   * </p>
   */
  private String phone;

  /**
   * <p>
   * Represents the id of city.
   * </p>
   */
  private Integer cityId;

  /**
   * <p>
   * Represents the id of station.
   * </p>
   */
  private Integer stationId;

  /**
   * <p>
   * Represents the vehicle name of issue record.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the business type of vehicle.
   * </p>
   */
  private String vehicleBusinessType;

  /**
   * <p>
   * Represents the report user of issue record.
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * Represents the state of issue record.
   * </p>
   */
  private String state;

  /**
   * <p>
   * Represents the alarm type of issue record.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * Represents the start time of issue record.
   * </p>
   */
  private Date startTime;

  /**
   * <p>
   * Represents the end time of issue record.
   * </p>
   */
  private Date endTime;
}
