/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 工单报警缓存.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAlarmCacheEntity {
  /**
   * <p>
   * 报警类型	(OPERATION_ALARM运营报警,VEHICLE_ALARM车辆报警).
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * 报警事件类型.
   * </p>
   */
  private String alarmEventType;

  /**
   * <p>
   * 报警事件名称.
   * </p>
   */
  private String alarmEventName;

  /**
   * <p>
   * 上报时间/首次报警时间.
   * </p>
   */
  private Date reportTime;

  /**
   * <p>
   * 标题/错误码(运营报警为标题,其它为错误码).
   * </p>
   */
  private String title;

  /**
   * <p>
   * 描述/ErrorCode(运营报警为描述,其它为ErrorCode).
   * </p>
   */
  private String description;

  /**
   * <p>
   * 工单报警附件(运营报警有,其它为空).
   * </p>
   */
  private List<IssueAlarmAttachmentEntity> alarmAttachmentList;
}
