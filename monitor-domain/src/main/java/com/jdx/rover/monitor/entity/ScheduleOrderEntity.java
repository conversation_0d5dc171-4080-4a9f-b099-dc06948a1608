/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import lombok.Data;

/**
 * <p>
 * This is the entity for vehilce schedule stop order.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ScheduleOrderEntity {

  /**
   * <p>
   * 总配送订单数
   * </p>
   */
  private Integer totalDeliveryOrderNum = 0;

  /**
   * <p>
   * 取消配送订单数
   * </p>
   */
  private Integer canceledDeliveryOrderNum = 0;

  /**
   * <p>
   * 完成配送订单数
   * </p>
   */
  private Integer finishedDeliveryOrderNum = 0;

  /**
   * <p>
   * 总揽收订单数
   * </p>
   */
  private Integer totalCollectOrderNum = 0;

  /**
   * <p>
   * 取消揽收订单数
   * </p>
   */
  private Integer canceledCollectOrderNum = 0;

  /**
   * <p>
   * 完成揽收订单数
   * </p>
   */
  private Integer finishedCollectOrderNum = 0;

  public ScheduleOrderEntity updateTotalCollectOrderNum(Integer orderNum) {this.totalCollectOrderNum += orderNum;  totalCollectOrderNum = totalCollectOrderNum<0? 0:totalCollectOrderNum; return this;}

  public ScheduleOrderEntity updateFinishedCollectOrderNum(Integer orderNum) {this.finishedCollectOrderNum += orderNum; finishedCollectOrderNum = finishedCollectOrderNum<0? 0:finishedCollectOrderNum; return this;}

  public ScheduleOrderEntity updateCanceledCollectOrderNum(Integer orderNum) {this.canceledCollectOrderNum += orderNum; canceledCollectOrderNum = canceledCollectOrderNum<0? 0:canceledCollectOrderNum; return this;}

  public ScheduleOrderEntity updateTotalDeliveryOrderNum(Integer orderNum) {this.totalDeliveryOrderNum += orderNum; totalDeliveryOrderNum = totalDeliveryOrderNum<0? 0:totalDeliveryOrderNum; return this;}

  public ScheduleOrderEntity updateFinishedDeliveryOrderNum(Integer orderNum) {this.finishedDeliveryOrderNum += orderNum; finishedDeliveryOrderNum = finishedDeliveryOrderNum<0? 0:finishedDeliveryOrderNum; return this;}

  public ScheduleOrderEntity updateCanceledDeliveryOrderNum(Integer orderNum) {this.canceledDeliveryOrderNum += orderNum; canceledDeliveryOrderNum = canceledDeliveryOrderNum<0? 0:canceledDeliveryOrderNum; return this;}
}
