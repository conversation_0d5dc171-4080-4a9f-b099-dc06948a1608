package com.jdx.rover.monitor.dataobject.mapcollection;

import lombok.Data;

/**
 * @description: 采图车辆行驶距离-缓存-数据对象
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-12-18 17:43
 **/
@Data
public class VehicleDistanceDO {

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 开始-暂停一段时间行驶里程（单位米）
     */
    private Double distance;

    /**
     * 总行驶里程（单位米）
     */
    private Double totalDistance;
}