/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.entity;

import lombok.Data;

/**
 * 用户停靠点信息.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class UserStopInfoEntity {
  /**
   * <p>
   * 停靠点id
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * 停靠点名
   * </p>
   */
  private String name;

  /**
   * 停靠点来源
   */
  private String stopSource;

}
