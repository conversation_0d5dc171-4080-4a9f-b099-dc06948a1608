/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.search;

import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * <p>
 * This is a JiraDebugTimeSearch entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class JiraImageLinkSearch {

  /**
   * <p>
   * Represents the id of jira record. The default value is null. It's changeable.
   * </p>
   */
  @Min(0)
  private Integer jiraId;

  /**
   * <p>
   * Represents the image id The default value is null. It's changeable.
   * </p>
   */
  @Min(0)
  private Integer imageId;
}
