/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.entity.drive;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 控制屏请求ID
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
public class ControlPadRequestIdDO implements Serializable {
    /**
     * 序列化ID
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 驾驶舱编号
     */
    private String cockpitNumber;

    /**
     * 驾驶舱用户名称
     */
    private String cockpitUserName;

    /**
     * 远程指令类型
     */
    private String remoteCommandType;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date requestTime;
}
