/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.search;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a JiraDebugTimeSearch entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class JiraDebugTimeSearch {

  /**
   * <p>
   * Represents the id of jira record. The default value is null. It's changeable.
   * </p>
   */
  @Min(0)
  private Integer jiraId;

  /**
   * <p>
   * Represents the timestamp of jira event. The default value is null. It's changeable.
   * </p>
   */
  private Date debugTime;

}
