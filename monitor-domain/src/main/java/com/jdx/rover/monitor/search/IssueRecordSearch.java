/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.search;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * This is an issue record search entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueRecordSearch extends PageVO {
  
  /**
   * <p>
   * Represents the no of issue.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the name of city.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the name of station.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the vehicle name of issue record.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the report user of issue record.
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * Represents the report user phone of issue record.
   * </p>
   */
  private String phone;

  /**
   * <p>
   * Represents the state of issue record.
   * </p>
   */
  private String state;

  /**
   * <p>
   * Represents the alarm type of issue record.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * Represents the business type of vehicle.
   * </p>
   */
  private String businessType;

  /**
   * <p>
   * Represents the start time of issue record.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * <p>
   * Represents the end time of issue record.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;
}
