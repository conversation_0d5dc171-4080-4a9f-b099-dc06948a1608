/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.web.jsf.api.cockpit;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.CockpitRealStatusDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitSingleVehicleDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitStationVehicleListDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitVehicleDTO;
import com.jdx.rover.monitor.vo.CockpitNumberVO;
import com.jdx.rover.monitor.vo.cockpit.BindVehicleVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitSingleVehicleVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitThirdPartyPageVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitVehiclePageVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitVehicleSearchVO;
import com.jdx.rover.monitor.dto.cockpit.HDMapInfoDTO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 座席车辆
 *
 * <AUTHOR>
 */
public interface CockpitVehicleWebJsfService {

    /**
     * 获取车辆列表
     */
    HttpResult<PageDTO<CockpitVehicleDTO>> getVehicleList(@Valid CockpitVehiclePageVO cockpitVehiclePageVO);

    /**
     * 获取多车页三方车辆列表
     */
    HttpResult<PageDTO<CockpitVehicleDTO>> getThirdPartyVehicleList(@NotNull(message = "请求参数不能为空") @Valid CockpitThirdPartyPageVO cockpitThirdPartyPageVO);

    /**
     * 搜索车辆列表
     * 根据经纬度获取地图信息
     */
    HttpResult<List<CockpitVehicleDTO>> searchVehicleList(@Valid CockpitVehicleSearchVO cockpitVehicleSearchVO);

    /**
     * 座席绑定/解绑车辆
     */
    HttpResult<Void> bindVehicle(@Valid BindVehicleVO bindVehicleVO);

    /**
     * 获取站点车辆列表
     */
    HttpResult<List<CockpitStationVehicleListDTO>> getStationVehicleList(CockpitNumberVO cockpitNumberVo);

    /**
     * 获取座席状态
     */
    HttpResult<List<CockpitRealStatusDTO>> getAllCockpitRealStatus();

    /**
     * 获取坐席单车页信息
     */
    HttpResult<CockpitSingleVehicleDTO> getSingleVehicle(@Valid CockpitSingleVehicleVO cockpitSingleVehicleVO);

    /**
     * 根据经纬度获取地图信息
     */
    HttpResult<HDMapInfoDTO> getMapInfoByPosition(@NotNull(message = "请求参数不能为空") @Valid PositionVO positionVO);
}