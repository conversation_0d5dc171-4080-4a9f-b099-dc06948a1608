/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.config;

import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <p>
 * This is a class that provide configuration properties for operation history.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Data
@ConfigurationProperties(prefix = "operation-history")
public class OperationHistoryProperties {

  /**
   * <p>
   * Represents the sql table for operation source.
   * </p>
   */
  private Map<String, List<String>> handleTables;

  /**
   * <p>
   * Represents the ignore sql table for operation source.
   * </p>
   */
  private Map<String, List<String>> ignoreTables;
}
