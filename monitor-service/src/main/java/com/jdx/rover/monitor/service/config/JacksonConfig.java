/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.config;

import com.bedatadriven.jackson.datatype.jts.JtsModule;
import com.jdx.rover.common.utils.JsonUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * jts的json装换配置
 *
 * <AUTHOR>
 * @date 2024/01/11
 */
@Configuration
public class JacksonConfig {
    @Bean
    public JtsModule jtsModule() {
        JsonUtils.getObjectMapper().registerModule(new JtsModule());
        return new JtsModule();
    }
}