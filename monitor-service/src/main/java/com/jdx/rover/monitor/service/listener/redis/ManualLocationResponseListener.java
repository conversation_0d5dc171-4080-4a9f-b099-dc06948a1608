package com.jdx.rover.monitor.service.listener.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.dto.MonitorLocationCloudMapDTO;
import com.jdx.rover.monitor.dto.MonitorLocationPoseDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import java.io.IOException;
import java.util.Objects;

/**
 * 人工定位响应监听器
 *
 * <AUTHOR>
 */
@Slf4j
public class ManualLocationResponseListener implements MessageListener<String> {
  private Session session;

  private Long requestId;

  public ManualLocationResponseListener(Session session,  Long requestId) {
    this.session = session;
    this.requestId = requestId;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    log.info("收到人工定位响应消息{}", msg);
    if (this.session == null || !this.session.isOpen()) {
      log.info("人工定位连接已经关闭{}", session.getId());
      return;
    }
    WsResult input = JsonUtils.readValue(msg, WsResult.class);
    String vehicleName;
    if (Objects.equals(input.getEventType(), WebsocketEventTypeEnum.VEHICLE_LOCATION_MAP_RESPONSE.getValue())) {
      TypeReference<WsResult<MonitorLocationPoseDTO>> typeReference = new TypeReference<WsResult<MonitorLocationPoseDTO>>() {
      };
      WsResult<MonitorLocationPoseDTO> inputConnect = JsonUtils.readValue(msg, typeReference);
      if (!Objects.equals(requestId, inputConnect.getData().getId())) {
        return;
      }
    } else {
      TypeReference<WsResult<MonitorLocationCloudMapDTO>> typeReference = new TypeReference<WsResult<MonitorLocationCloudMapDTO>>() {
      };
      WsResult<MonitorLocationCloudMapDTO> inputConnect = JsonUtils.readValue(msg, typeReference);
      if (!Objects.equals(requestId, inputConnect.getData().getId())) {
        return;
      }
    }
    synchronized (this.session) {
      try {
        this.session.getBasicRemote().sendText(msg);
      } catch (IOException e) {
        log.info("人工辅助定位发送数据失败{}", e);
        throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_CLOUDMAP.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_CLOUDMAP.getMessage());
      }
    }
  }
}
