/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import com.amazonaws.HttpMethod;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.map.api.domain.dto.MapInfoDTO;
import com.jdx.rover.map.api.domain.dto.MapTilesDTO;
import com.jdx.rover.map.api.domain.enums.s3.EndpointTypeEnum;
import com.jdx.rover.map.api.domain.vo.DownLoadPcdTileVO;
import com.jdx.rover.map.api.service.MapInfoJsfService;
import com.jdx.rover.map.api.service.MapOtaJsfService;
import com.jdx.rover.monitor.dto.MonitorLocationCloudMapDTO;
import com.jdx.rover.monitor.dto.MonitorLocationPoseDTO;
import com.jdx.rover.monitor.dto.MonitorVehicleLocationPoseDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.map.MapTileManager;
import com.jdx.rover.monitor.manager.utils.map.TileUtils;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.service.config.RoverMapServiceProperties;
import com.jdx.rover.monitor.vo.MonitorLocationCloudMapVO;
import com.jdx.rover.monitor.vo.MonitorRequestVehicleCloudMapCommandVO;
import com.jdx.rover.monitor.vo.MonitorResetVehiclePoseCommandVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.RemoteCommandTypeEnum;
import com.jdx.rover.server.api.domain.vo.LocationManualVehiclePoseCommandVO;
import com.jdx.rover.server.api.domain.vo.LocationPullPointMapCommandVO;
import com.jdx.rover.server.api.jsf.service.command.LocationRemoteCommandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 定位服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
@Slf4j
public class MonitorLocationService {

  @Autowired
  private LocationRemoteCommandService locationRemoteCommandJsfService;
  @Autowired
  private MapInfoJsfService mapInfoJsfService;
  @Autowired
  private MapOtaJsfService mapOtaJsfService;
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private S3Properties s3Properties;
  @Autowired
  private RoverMapServiceProperties roverMapServiceProperties;
  @Autowired
  private MapTileManager mapTileManager;
  private static final String MAP_TILE_UNCOMPRESS_PCD_KEY = "%s/%s/pcd-tile/%s-uncompress.pcd";

  private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(4, 10, 1,
          TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(200),
          new CustomizableThreadFactory("Location-Init-"), new ThreadPoolExecutor.AbortPolicy());

  /**
   * 请求车端定位地图信息
   */
  public HttpResult requestVehicleMap(MonitorRequestVehicleCloudMapCommandVO requestMapCommandVo) {
    log.info("请求车端位姿地图{}", JsonUtils.writeValueAsString(requestMapCommandVo));
    LocationPullPointMapCommandVO pullPointMapCommandVo = new LocationPullPointMapCommandVO();
    pullPointMapCommandVo.setVehicleName(requestMapCommandVo.getVehicleName());
    pullPointMapCommandVo.setRequestId(requestMapCommandVo.getId());
    pullPointMapCommandVo.setNeed_upload(Boolean.TRUE);
    pullPointMapCommandVo.setReceiveTimeStamp(requestMapCommandVo.getTimeStamp());
    pullPointMapCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = locationRemoteCommandJsfService.publishGeneratePointMapCommand(pullPointMapCommandVo);
    if (!HttpResult.isSuccess(result)) {
      log.info("请求车辆定位点云失败!{},{}", JsonUtils.writeValueAsString(result), JsonUtils.writeValueAsString(requestMapCommandVo));
      return result;
    }
    return HttpResult.success();
  }

  /**
   * 请求云端定位地图信息
   */
  public HttpResult requestCloudMap(String vehicleName, Double distance) {
    log.info("请求车辆{}云端点云地图{}", vehicleName, distance);
    // 获取车辆实时位置
    MonitorLocationCloudMapDTO cloudMapDto = new MonitorLocationCloudMapDTO();
    cloudMapDto.setVehicleName(vehicleName);
    VehicleRealtimeInfoDTO vehicleRealtimeInfo = vehicleRealtimeRepository.get(vehicleName);
    if (Objects.isNull(vehicleRealtimeInfo)) {
      return HttpResult.error("车辆经纬度不存在");
    }
    // 实时位置获取地图信息
    HttpResult<MapInfoDTO> mapInfoResult = mapInfoJsfService.getMapByPosition(vehicleRealtimeInfo.getLon(), vehicleRealtimeInfo.getLat());
    if (!HttpResult.isSuccess(mapInfoResult) || Objects.isNull(mapInfoResult.getData())) {
      return HttpResult.error("云端请求地图不存在");
    }
    MapInfoDTO mapInfo = mapInfoResult.getData();
    if (Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getId())) {
      return HttpResult.error("云端地图数据不存在");
    }
    // 试试位置获取范围内瓦片地图
    List<Long> tileList = TileUtils.getTileIdList(vehicleRealtimeInfo.getLon(), vehicleRealtimeInfo.getLat(), distance);
    if (CollectionUtils.isEmpty(tileList)) {
      return HttpResult.error("瓦片地图不存在");
    }
    // 生成瓦片地图下载链接
    List<String> urlList = generateCloudMap(vehicleName, mapInfo.getId(), mapInfo.getMapVersion(), tileList);
    log.info("上报地图点云数据到S3,地址为{}", JsonUtils.writeValueAsString(urlList));
    cloudMapDto.setCloudPathList(urlList);
    return HttpResult.success(cloudMapDto);
  }

  /**
   * 车端上报地图信息
   */
  public HttpResult uploadVehicleMapPose(MonitorLocationCloudMapVO locationCloudMapVo) {
    log.info("收到车端{}上报定位信息{},位姿信息{}", locationCloudMapVo.getVehicleName(),
            locationCloudMapVo.getRequestId(), JsonUtils.writeValueAsString(locationCloudMapVo.getPoseList()));
    MonitorLocationPoseDTO locationPoseDto = new MonitorLocationPoseDTO();
    locationPoseDto.setVehicleName(locationCloudMapVo.getVehicleName());
    locationPoseDto.setId(Long.valueOf(locationCloudMapVo.getRequestId()));
    if (CollectionUtils.isNotEmpty(locationCloudMapVo.getPoseList())) {
      locationPoseDto.setPoseList(locationCloudMapVo.getPoseList().stream().map(locationPose ->
              BeanUtil.toBean(locationPose, MonitorVehicleLocationPoseDTO.class)).collect(Collectors.toList()));
    }
    locationPoseDto.setTileSize(Objects.isNull(locationCloudMapVo.getTileList()) ? 0 : locationCloudMapVo.getTileList().size());
    try {
      threadPool.execute(() -> {
         String url = uploadVehicleCloudMap(locationCloudMapVo.getVehicleName(),
                locationCloudMapVo.getRequestId(), locationCloudMapVo.getPoseData(), "pcd");
        locationPoseDto.setCloudPath(url);
        String jpgUrl = uploadVehicleCloudMap(locationCloudMapVo.getVehicleName(),
                locationCloudMapVo.getRequestId(), locationCloudMapVo.getLocResult(), "jpg");
        locationPoseDto.setPhotoPath(jpgUrl);
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.VEHICLE_LOCATION_MAP_RESPONSE.getValue(), locationPoseDto);
        pushVehicleCloudData(locationCloudMapVo.getVehicleName(), wsResult);
      });
    } catch (Exception e) {
      log.error("执行上传车辆点云点图异常", e);
    }
    return HttpResult.success();
  }

  /**
   * 下发车辆定位位姿信息
   */
  public HttpResult resetVehicleLocationPose(MonitorResetVehiclePoseCommandVO remoteCommandVo) {
    log.info("下发人工位姿到车端{}", JsonUtils.writeValueAsString(remoteCommandVo));
    LocationManualVehiclePoseCommandVO restPoseVo = new LocationManualVehiclePoseCommandVO();
    restPoseVo.setVehicleName(remoteCommandVo.getVehicleName());
    restPoseVo.setPitch(remoteCommandVo.getPitch());
    restPoseVo.setRoll(remoteCommandVo.getRoll());
    restPoseVo.setX(remoteCommandVo.getX());
    restPoseVo.setY(remoteCommandVo.getY());
    restPoseVo.setZ(remoteCommandVo.getZ());
    restPoseVo.setYaw(remoteCommandVo.getYaw());
    restPoseVo.setCommandType(RemoteCommandTypeEnum.NORMAL);
    restPoseVo.setNeedResponse(Boolean.FALSE);
    restPoseVo.setRequestId(remoteCommandVo.getId());
    restPoseVo.setReceiveTimeStamp(new Date());
    restPoseVo.setTransitTimeStamp(new Date());
    HttpResult result = locationRemoteCommandJsfService.publishResetLocationPoseCommand(restPoseVo);
    if (!HttpResult.isSuccess(result)) {
      log.info("重置定位失败!{},{}", result, restPoseVo);
      return result;
    }
    return HttpResult.success();
  }


  private String uploadVehicleCloudMap(String vehicleName, Long requestId, String poseData, String fileType) {
    if (StringUtils.isBlank(poseData)) {
      return null;
    }
    Base64 base64 = new Base64();
    byte[] dataArray = base64.decode(poseData);
    for (int i = 0; i < dataArray.length; ++i) {
      if (dataArray[i] < 0) {
        dataArray[i] += 256;
      }
    }
    String key = new StringBuilder(vehicleName)
            .append(requestId).append(new Date().getTime()).append("." + fileType).toString();
    ByteArrayInputStream byteArrayInputStream = null;
    try {
      byteArrayInputStream = new ByteArrayInputStream(dataArray);
      S3Utils.uploadFile(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getEndpoint(),
              roverMapServiceProperties.getAttachmentBucketName(), key, byteArrayInputStream);
    } catch (Exception e) {
      log.error("upload file exception", e);
      return "";
    } finally {
      dataArray = null;
      IoUtil.close(byteArrayInputStream);
    }
    final Date expiration = DateUtil.offsetHour(new Date(), 24);
    URL downloadUrl =
            S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(),
                    roverMapServiceProperties.getAttachmentBucketName(), key, HttpMethod.GET, expiration);
    log.info("Add vehicle {} cloud map {} to s3, url{}", vehicleName, requestId, downloadUrl.toString());
    return downloadUrl.toString();
  }

  /**
   * 推送车辆点云地图信息
   *
   * @param vehicleName
   * @param object
   */
  private long pushVehicleCloudData(String vehicleName, Object object) {
    String topicName = RedisTopicEnum.REMOTE_CONTROL_CLOUD_MAP_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    String jsonStr = JsonUtils.writeValueAsString(object);
    if (rTopic.countSubscribers() > 0) {
      long publishCnt = rTopic.publish(jsonStr);
      log.info("发送[{}]条车辆[{}]点云数据", publishCnt, vehicleName);
    }
    return 0;
  }

  private List<String> generateCloudMap(String vehicleName, long mapId, long mapVersion, List<Long> tileList) {
    List<String> urlList = new ArrayList<>();
    List<MapTilesDTO.TileInfoDTO> tileMap = buildMapTile(vehicleName, mapId, mapVersion, tileList);
    List<Callable<String>> callableList = new ArrayList<>();
    File directory = new File("/export/package/pcd-" + mapId + "-" + System.currentTimeMillis());
    if (!directory.exists()) {
      directory.mkdirs();
    }
    tileMap.stream().filter(tileInfo -> !Objects.isNull(tileInfo)).forEach(tileInfo ->
            callableList.add(() -> {
              String outKey = String.format(MAP_TILE_UNCOMPRESS_PCD_KEY, mapId, mapVersion, tileInfo.getTileId());
              return mapTileManager.getCloudMapTile(tileInfo.getTileId(),
                      tileInfo.getUrl(), roverMapServiceProperties.getAttachmentBucketName(), outKey, directory.getPath());
            }));
    try {
      List<Future<String>> futureList = threadPool.invokeAll(callableList);
      urlList.addAll(futureList.stream().map(future -> {
        try {
          return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
          log.error("线程等待超时异常", e);
          return "";
        }
      }).filter(url -> StringUtils.isNotBlank(url)).collect(Collectors.toList()));
    } catch (Exception e) {
      log.error("地图{}版本{}执行异步解压缩失败!!", mapId, mapVersion, e);
    } finally {
      log.info("地图{}版本{}执行异步解压缩完成，删除临时文件{}", mapId, mapVersion, directory.getName());
      FileUtils.deleteQuietly(directory);
    }
    return urlList;
  }

  private List<MapTilesDTO.TileInfoDTO> buildMapTile(String vehicleName, long mapId, long mapVersion, List<Long> tileList) {
    if (CollectionUtils.isEmpty(tileList)) {
      return new ArrayList<>();
    }
    DownLoadPcdTileVO downLoadPcdTile = new DownLoadPcdTileVO();
    downLoadPcdTile.setVehicleName("monitor" + vehicleName);
    downLoadPcdTile.setMapId((int) mapId);
    downLoadPcdTile.setMapVersion((int) mapVersion);
    downLoadPcdTile.setEndpointType(EndpointTypeEnum.INTERNAL.getCode());
    downLoadPcdTile.setTileList(tileList.stream().map(tile ->
            new DownLoadPcdTileVO.Tile(tile, null)).collect(Collectors.toList()));
    HttpResult<MapTilesDTO> httpResult = mapOtaJsfService.downloadTileUrlList(downLoadPcdTile);
    if (!HttpResult.isSuccess(httpResult) || Objects.isNull(httpResult.getData())) {
      return new ArrayList<>();
    }
    return httpResult.getData().getList();
  }


}
