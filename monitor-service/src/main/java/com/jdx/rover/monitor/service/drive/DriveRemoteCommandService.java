/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.drive;

import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.enums.VehicleOperateSourceEnum;
import com.jdx.rover.monitor.dto.transport.UserOperateCommandDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePncTaskAndTrafficLightDTO;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.drive.command.DriveRemoteCommandTypeEnum;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.enums.mobile.H5RemotePowerEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.drive.ButtonCommandManager;
import com.jdx.rover.monitor.manager.drive.CockpitConnectVehicleManager;
import com.jdx.rover.monitor.manager.drive.DriveRemoteCommandManager;
import com.jdx.rover.monitor.manager.drive.PowerCommandManager;
import com.jdx.rover.monitor.manager.drive.ServerToVehicleManager;
import com.jdx.rover.monitor.manager.mobile.CommandManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMonitorJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 远程指令服务
 *
 * <AUTHOR>
 * @date 2025/2/19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DriveRemoteCommandService {
    private final CockpitConnectVehicleManager cockpitConnectVehicleManager;
    private final PowerCommandManager powerCommandManager;
    private final DriveRemoteCommandManager driveRemoteCommandManager;
    private final ServerToVehicleManager serverToVehicleManager;
    private final ButtonCommandManager buttonCommandManager;
    private final Producer producer;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final CockpitStatusRepository cockpitStatusRepository;
    private final ScheduleMonitorJsfService scheduleMonitorJsfService;
    private final CommandManager commandManager;
    private final JmqProducerService jmqProducerService;

    /**
     * monitor下发远程控制指令
     */
    private static final String MONITOR_REMOTE_COMMAND_LOG = "monitor_remote_command_log";

    /**
     * 释放按钮急停
     */
    public HttpResult<?> sendCommand(DriveRemoteCommandVO vo) {
        log.info("接收指令参数={}", JsonUtils.writeValueAsString(vo));
        if (Objects.isNull(vo.getRequestTime())) {
            vo.setRequestTime(new Date());
        }
        if (!Objects.isNull(vo.getCockpitUserName()) && !Objects.isNull(vo.getCockpitNumber())) {
            CockpitStatusDO cockpitStatus = cockpitStatusRepository.get(vo.getCockpitNumber());
            if (Objects.isNull(cockpitStatus) || !Objects.equals(cockpitStatus.getCockpitUserName(), vo.getCockpitUserName())) {
                cockpitStatusRepository.putMapValue(vo.getCockpitNumber(), CockpitStatusDO::getCockpitUserName, vo.getCockpitUserName());
                log.info("设置更新用户名={},{}", cockpitStatus, vo);
            }
        }
        DriveRemoteCommandTypeEnum commandType = DriveRemoteCommandTypeEnum.valueOf(vo.getRemoteCommandType());
        StringBuilder sb = new StringBuilder();
        sb.append("用户").append(vo.getCockpitUserName()).append("通过驾驶舱").append(vo.getCockpitNumber())
                .append("操作").append(vo.getVehicleName()).append(commandType.getTitle());
        vo.setOperationMessage(sb.toString());
        HttpResult<?> httpResult = HttpResult.error();
        switch (commandType) {
            case REMOTE_REQUEST_AS_ARRIVED -> {
                // 判断车辆PNC是否在任务中，如果当前PNC任务不是ACTIVE状态，则调用调度接口进行业务到达
                try {
                    SingleVehiclePncTaskAndTrafficLightDTO dto = RedissonUtils.getObject(RedisKeyEnum.TRAFFIC_LIGHT_SET_VEHICLE.getValue() + vo.getVehicleName());
                    if (Objects.nonNull(dto) && StringUtils.equals("ACTIVE", dto.getPncTaskType())) {
                        httpResult = driveRemoteCommandManager.sendCommand(vo);
                    } else {
                        httpResult = scheduleMonitorJsfService.vehicleEnd(vo.getVehicleName());
                    }
                } catch (Exception e) {
                    httpResult = driveRemoteCommandManager.sendCommand(vo);
                    log.error("调用调度接口执行到达操作异常，车辆={}", vo.getVehicleName(), e);
                }
            }
            case REMOTE_CONTROL_POWER_OFF -> {
                MonitorErrorEnum powerEnum = commandManager.remoteShutdown(vo.getVehicleName());
                MonitorErrorEnum pduEnum = commandManager.sendPdu(vo.getVehicleName(), CommandTypeEnum.REMOTE_SHUTDOWN);
                if (MonitorErrorEnum.OK.equals(powerEnum) || MonitorErrorEnum.OK.equals(pduEnum)) {
                    httpResult = HttpResult.success();
                } else {
                    httpResult = HttpResult.error(MonitorErrorEnum.ERROR_PDU_CONNECT.getCode(), MonitorErrorEnum.ERROR_PDU_CONNECT.getMessage());
                }
                // 发送mq消息
                try {
                    UserOperateCommandDTO commandDTO = UserOperateCommandDTO.builder()
                            .operateUser(vo.getCockpitUserName())
                            .vehicleName(vo.getVehicleName())
                            .operateTime(vo.getRequestTime())
                            .source(RemoteCommandSourceEnum.REMOTE_JOYSTICK.getCommandSource())
                            .operateType(H5RemotePowerEnum.POWER_OFF.getValue())
                            .operateResult(HttpResult.isSuccess(httpResult) ? 1 : 0)
                            .build();
                    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_H5_TRANSPORT_COMMAND.getTopic(), commandDTO.getVehicleName(), commandDTO);
                } catch (Exception e) {
                    log.error("发送mq消息失败", e);
                }
            }
            case REMOTE_CONTROL_POWER_REBOOT -> {
                MonitorErrorEnum powerEnum = commandManager.vehicleReboot(vo.getVehicleName());
                MonitorErrorEnum pduEnum = commandManager.sendPdu(vo.getVehicleName(), CommandTypeEnum.VEHICLE_REBOOT);
                if (MonitorErrorEnum.OK.equals(powerEnum) || MonitorErrorEnum.OK.equals(pduEnum)) {
                    httpResult = HttpResult.success();
                } else {
                    httpResult = HttpResult.error(MonitorErrorEnum.ERROR_PDU_CONNECT.getCode(), MonitorErrorEnum.ERROR_PDU_CONNECT.getMessage());
                }
            }
            case REMOTE_REQUEST_RELIEVE_BUTTON_STOP, REMOTE_REQUEST_REMOTE_RESTART
            , RUN_HAVE_MAP, RUN_NO_MAP -> httpResult = driveRemoteCommandManager.sendCommand(vo);
            case REMOTE_DRIVE_ENTER_TAKE_OVER, REMOTE_DRIVE_EXIT_TAKE_OVER ->
                    httpResult = cockpitConnectVehicleManager.sendCommand(vo);
            case REMOTE_VELOCITY_CONTROL, MAX_TORQUE -> httpResult = serverToVehicleManager.sendCommand(vo);
            case GEAR_PARK, GEAR_DRIVE, GEAR_NEUTRAL, GEAR_REVERSE, HAZARD_LIGHT_LEFT, HAZARD_LIGHT_RIGHT,
                 HAZARD_LIGHT_BOTH, HAZARD_LIGHT_OFF, HEAD_LIGHT_ON, HEAD_LIGHT_OFF, SUPER_MODE_ON, SUPER_MODE_OFF ->
                    httpResult = buttonCommandManager.sendCommand(vo);
            default -> {
            }
        }
        // 成功发送监控系统操作日志
        if (HttpResult.isSuccess(httpResult)) {
            sendUserOperation(vo);
        }
        log.info("指令响应结果={}", httpResult);

        // 如果是切换地图操作,再执行释放接管操作,放到最后
        if (StringUtils.equalsAny(vo.getRemoteCommandType(), DriveRemoteCommandTypeEnum.RUN_HAVE_MAP.name()
                , DriveRemoteCommandTypeEnum.RUN_NO_MAP.name())) {
            vo.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.name());
            sendCommand(vo);
        }
        return httpResult;
    }

    /**
     * 发送监控系统,用户操作记录
     *
     * @param driveRemoteCommandVO
     */
    private void sendUserOperation(DriveRemoteCommandVO driveRemoteCommandVO) {
        String vehicleName = driveRemoteCommandVO.getVehicleName();
        MonitorUserOperationEntity userOperation = new MonitorUserOperationEntity();
        userOperation.setUserName(driveRemoteCommandVO.getCockpitUserName());
        userOperation.setOperationType(driveRemoteCommandVO.getRemoteCommandType());
        userOperation.setOperationSource(VehicleOperateSourceEnum.REMOTE_JOYSTICK.getSource());
        userOperation.setOperationMessage(driveRemoteCommandVO.getOperationMessage());
        userOperation.setTimestamp(new Date());
        userOperation.setVehicleName(vehicleName);
        sendUserOperation(userOperation);
    }

    /**
     * 发送监控系统,用户操作记录
     */
    public void sendUserOperation(MonitorUserOperationEntity userOperation) {
        try {
            Message message = new Message(JmqProducerTopicEnum.MONITOR_REMOTE_COMMAND_LOG.getTopic(), JsonUtils.writeValueAsString(userOperation));
            message.setBusinessId(userOperation.getVehicleName());
            message.setOrdered(true);
            producer.send(message);

            //指令架构有接入，此处暂时双发
            kafkaTemplate.send(MONITOR_REMOTE_COMMAND_LOG, userOperation.getVehicleName(), JsonUtils.writeValueAsString(userOperation));
        } catch (JMQException e) {
            log.error("JMQ send error message={}", userOperation, e);
        }
    }
}
