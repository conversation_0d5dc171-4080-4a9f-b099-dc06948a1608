/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import com.jdx.rover.ticket.jsf.service.TicketDataJsfService;
import com.jdx.rover.ticket.jsf.service.TicketOperateJsfService;
import com.jdx.rover.ticket.jsf.service.TicketQueryJsfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 工单服务jsf配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@Component
public class TicketJsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册 TicketDataJsfService
     */
    @Bean
    public TicketDataJsfService ticketIssueDataJsfService() {
        return jsfConsumerRegister.createConsumerConfig(TicketDataJsfService.class).refer();
    }

    /**
     * 注册 TicketQueryJsfService
     */
    @Bean
    public TicketQueryJsfService ticketQueryJsfService() {
        return jsfConsumerRegister.createConsumerConfig(TicketQueryJsfService.class).refer();
    }

    /**
     * 注册 TicketOperateJsfService
     */
    @Bean
    public TicketOperateJsfService ticketOperateJsfService() {
        return jsfConsumerRegister.createConsumerConfig(TicketOperateJsfService.class).refer();
    }
}