package com.jdx.rover.monitor.service.ops;

import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

/**
 * 监控OPS接口
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OpsService {

    /**
     * 清除遥控页车辆响应信息监听器
     *
     * @param vehicleName vehicleName
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum clearRemoteVehicleResponse(String vehicleName) {
        String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        if (rTopic.countSubscribers() > 0) {
            log.info("remove all remote vehicle response listeners, vehicleName:{}, userName:{}", vehicleName, LoginUtils.getUsername());
            rTopic.removeAllListeners();
        }
        return MonitorErrorEnum.OK;
    }
}
