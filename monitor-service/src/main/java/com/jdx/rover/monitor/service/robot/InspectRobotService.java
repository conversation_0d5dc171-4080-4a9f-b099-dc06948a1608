/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.device.jsfapi.domain.enums.OperationTypeEnum;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceGroupMessageData;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceMessageData;
import com.jdx.rover.monitor.enums.device.DevicePropertyCodeEnum;
import com.jdx.rover.monitor.enums.device.DeviceRealtimeStateEnum;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.transport.api.domain.dto.status.PropertyChangeDTO;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物联网巡检机器人服务
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class InspectRobotService implements IRobotDeviceService {

    /**
     * 机器人实时信息服务。
     */
    private final RobotRealtimeInfoManager robotRealtimeInfoManager;

    /**
     * 设备实时服务接口
     */
    public final TransportDeviceApiManager realtimeDeviceApiManager;

    @Override
    public void batchUpdateDeviceGroup(Map<String, List<DeviceMessageData>> mapResult) {
        if (CollectionUtil.isEmpty(mapResult)) {
            return;
        }
        log.info("批量更新机器人基础分组信息数据量{}, 详情{}", mapResult.size(), JsonUtils.writeValueAsString(mapResult));
        List<DeviceMessageData> addDeviceList = mapResult.get(OperationTypeEnum.ADD.getValue());
        if (CollectionUtils.isNotEmpty(addDeviceList)) {
            List<RobotRealtimeInfo> robotList = buildUpdateRealtimeInfo(addDeviceList);
            robotRealtimeInfoManager.saveBatchData(robotList.stream().map(robot -> {
                robot.setRealtimeStatus(DeviceRealtimeStateEnum.OFFLINE.getValue());
                return robot;
            }).collect(Collectors.toList()));
        }
        List<DeviceMessageData> updateDeviceList = mapResult.get(OperationTypeEnum.EDIT.getValue());
        if (CollectionUtils.isNotEmpty(updateDeviceList)) {
            robotRealtimeInfoManager.updateBatch(buildUpdateRealtimeInfo(updateDeviceList));
        }
        List<DeviceMessageData> deleteDeviceList = mapResult.get(OperationTypeEnum.DELETE.getValue());
        if (CollectionUtils.isNotEmpty(deleteDeviceList)) {
            robotRealtimeInfoManager.deleteBatch(buildUpdateRealtimeInfo(deleteDeviceList));
        }
    }

    /**
     * 批量更新设备状态。
     * @param deviceNameList 设备名称列表。
     */
    @Override
    public void batchUpdateDeviceState(List<String> deviceNameList) {
        if (CollectionUtils.isEmpty(deviceNameList)) {
            return;
        }
        List<Map<String, Object>> result = realtimeDeviceApiManager.getDeviceStatusList(deviceNameList, DevicePropertyCodeEnum.MONITOR_ROBOT_STATUS.getPropertyList());

        if (CollectionUtils.isEmpty(result)) {
            log.error("查询设备列表{}实时信息不存在", deviceNameList.toArray());
            return;
        }
        Map<String, RobotRealtimeInfo> resultMap = result.stream().map(mapData -> {
            try {
                RobotRealtimeInfo realtimeInfo = BeanUtil.mapToBean(mapData, RobotRealtimeInfo.class, true);
                Object onlineStatus = mapData.get("onlineStatus");
                if (Objects.nonNull(onlineStatus)) {
                    realtimeInfo.setRealtimeStatus((Integer)onlineStatus == 1 ? DeviceRealtimeStateEnum.ONLINE.getValue() : DeviceRealtimeStateEnum.OFFLINE.getValue());
                } else {
                    realtimeInfo.setRealtimeStatus(DeviceRealtimeStateEnum.OFFLINE.getValue());
                }
                return realtimeInfo;
            } catch (Exception e) {
                log.error("设备{}实时信息转换异常", JsonUtils.writeValueAsString(mapData), e);
                return null;
            }
        }).collect(Collectors.toMap(RobotRealtimeInfo::getDeviceName, Function.identity()));
        robotRealtimeInfoManager.batchUpdateState(resultMap.values().stream().toList());
    }

    /**
     * 处理设备消息上报事件。
     */
    @Override
    public void eventsReport(TransportDeviceMessage transportDeviceMessage) {

    }

    @Override
    public void propertyReport(String transportDeviceMessage) {

    }

    @Override
    public void eventsReply(TransportDeviceMessage transportDeviceMessage) {

    }

    @Override
    public void statusChange(PropertyChangeDTO propertyChangeDto) {

    }

    /**
     * 构建机器人实时信息列表。
     * @param deviceList 设备消息数据列表。
     * @return 机器人实时信息列表。
     */
    private List<RobotRealtimeInfo> buildUpdateRealtimeInfo(List<DeviceMessageData> deviceList) {
        List<RobotRealtimeInfo> robotList =
                deviceList.stream().map(device -> {
                    RobotRealtimeInfo deviceRealtimeInfo = new RobotRealtimeInfo();
                    deviceRealtimeInfo.setDeviceName(device.getDeviceName());
                    deviceRealtimeInfo.setProductModelNo(device.getProductModelNo());
                    deviceRealtimeInfo.setProductModelName(device.getProductModelName());
                    deviceRealtimeInfo.setProductKey(device.getProductKey());
                    DeviceGroupMessageData deviceGroupMessageData = device.getDeviceGroupMessageData();
                    if (!Objects.isNull(deviceGroupMessageData)) {
                        if (Objects.isNull(deviceGroupMessageData.getLevel()) || deviceGroupMessageData.getLevel() == 1) {
                            deviceRealtimeInfo.setGroupOne(deviceGroupMessageData.getGroupNo());
                            deviceRealtimeInfo.setGroupName(deviceGroupMessageData.getGroupName());
                        } else if (deviceGroupMessageData.getLevel() == 2 && !Objects.isNull(deviceGroupMessageData.getParent())) {
                            deviceRealtimeInfo.setGroupOne(deviceGroupMessageData.getParent().getGroupNo());
                            deviceRealtimeInfo.setGroupTwo(deviceGroupMessageData.getGroupNo());
                            deviceRealtimeInfo.setGroupName(deviceGroupMessageData.getLevelName());
                        } else if (deviceGroupMessageData.getLevel() == 3 && !Objects.isNull(deviceGroupMessageData.getParent()) &&
                                !Objects.isNull(deviceGroupMessageData.getParent().getParent())) {
                            deviceRealtimeInfo.setGroupOne(deviceGroupMessageData.getParent().getParent().getGroupNo());
                            deviceRealtimeInfo.setGroupTwo(deviceGroupMessageData.getParent().getGroupNo());
                            deviceRealtimeInfo.setGroupThree(deviceGroupMessageData.getGroupNo());
                            deviceRealtimeInfo.setGroupName(deviceGroupMessageData.getLevelName());
                        }
                    }
                    return deviceRealtimeInfo;
                }).collect(Collectors.toList());
        return robotList;
    }
}
