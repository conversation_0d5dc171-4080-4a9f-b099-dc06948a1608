/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.monitor.dto.MonitorOperationRecordDTO;
import com.jdx.rover.monitor.po.MonitorUserOperationLog;
import com.jdx.rover.monitor.search.MonitorDataListSearch;
import com.jdx.rover.monitor.manager.operation.OperationRecordLogManager;
import com.jdx.rover.monitor.repository.mapper.MonitorUserOperationLogMapper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * This is a monitor operation record service.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class MonitorOperationRecordService extends ServiceImpl<MonitorUserOperationLogMapper, MonitorUserOperationLog> {
  @Autowired
  private OperationRecordLogManager operationRecordLogManager;

  /**
   * <p>
   * Search supervisor operation record.
   * </p>
   *
   * @param
   */
  public PageDTO<MonitorOperationRecordDTO> search(MonitorDataListSearch monitorDataListSearch) {
    ParameterCheckUtility.checkNotNull(monitorDataListSearch, "pageableVo");
    ParameterCheckUtility.checkNotNull(monitorDataListSearch.getPageNum(), "pageVo#pageNum");
    ParameterCheckUtility.checkPositive(monitorDataListSearch.getPageNum(), "pageVo#pageNum");
    ParameterCheckUtility.checkNotNull(monitorDataListSearch.getPageSize(), "pageVo#pageSize");
    ParameterCheckUtility.checkNotNegative(monitorDataListSearch.getPageSize(), "pageVo#pageSize");
    PageDTO<MonitorOperationRecordDTO> pageInfo = operationRecordLogManager.search(monitorDataListSearch,monitorDataListSearch);
    return pageInfo;
  }

}
