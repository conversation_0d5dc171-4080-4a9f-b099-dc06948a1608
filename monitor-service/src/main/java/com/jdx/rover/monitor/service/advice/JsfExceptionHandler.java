/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.service.advice;

import com.jd.jsf.gd.msg.RequestMessage;
import com.jd.jsf.gd.msg.ResponseMessage;
import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.exception.ExceptionHandler;
import com.jdx.rover.monitor.common.exception.AppException;
import org.springframework.stereotype.Component;

/**
 * <p>
 * JSF 接口全局异常处理
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
@Component
public class JsfExceptionHandler implements ExceptionHandler {

    @Override
    public boolean handleException(RequestMessage request, ResponseMessage response, Throwable e) {

        //处理参数异常 不进行日志打印
        if (e instanceof IllegalArgumentException){
            response.setResponse(HttpResult.error(HttpCodeEnum.BAD_REQUEST.getValue(),e.getMessage()));
            return false;
        }

        if (e instanceof AppException){
            response.setResponse(HttpResult.error(HttpCodeEnum.INNER_SERVER_ERROR.getValue(),e.getMessage()));
            return true;
        }

        response.setResponse(HttpResult.error());
        return true;
    }
}
