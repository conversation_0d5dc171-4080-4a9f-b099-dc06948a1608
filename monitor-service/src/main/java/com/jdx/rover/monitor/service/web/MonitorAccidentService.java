/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.service.web;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.amazonaws.HttpMethod;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.infrastructure.api.domain.entity.dto.xingyun.BugDTO;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.StatusEnum;
import com.jdx.rover.metadata.api.domain.enums.RoverBucketEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.metadata.api.domain.enums.common.YesOrNoEnum;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleBasicDto;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import com.jdx.rover.monitor.bo.accident.AccidentBO;
import com.jdx.rover.monitor.bo.accident.AccidentExportBO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.accident.AccidentBasicInfoDTO;
import com.jdx.rover.monitor.dto.accident.AccidentExportDTO;
import com.jdx.rover.monitor.dto.accident.AccidentFlowDTO;
import com.jdx.rover.monitor.dto.accident.AccidentTagDTO;
import com.jdx.rover.monitor.dto.accident.GetAccidentPageListDTO;
import com.jdx.rover.monitor.dto.accident.MonitorAccidentAttachmentDTO;
import com.jdx.rover.monitor.dto.accident.MonitorAccidentDetailDTO;
import com.jdx.rover.monitor.dto.accident.MonitorAccidentOperateDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.VideoCaptureStatus;
import com.jdx.rover.monitor.enums.accident.AccidentSourceEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentFlowEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentHandleMethodEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentLogEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentModuleEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperationJudgeEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperationStatusEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperationTypeEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentSupervisionEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentTagEnum;
import com.jdx.rover.monitor.enums.mobile.CompensateTypeEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentTypeEnum;
import com.jdx.rover.monitor.enums.mobile.SafetyGroupAccidentLevelEnum;
import com.jdx.rover.monitor.enums.mobile.SafetyGroupAccidentTypeEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.video.VideoDirectionEnum;
import com.jdx.rover.monitor.event.AccidentEvent;
import com.jdx.rover.monitor.manager.accident.AccidentAttachmentManager;
import com.jdx.rover.monitor.manager.accident.AccidentDetailManager;
import com.jdx.rover.monitor.manager.accident.AccidentFlowLogManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.accident.AccidentRecordLogManager;
import com.jdx.rover.monitor.manager.bug.BugReportManager;
import com.jdx.rover.monitor.manager.config.RoverVideoProperties;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.manager.shadow.ShadowAccidentManager;
import com.jdx.rover.monitor.manager.ticket.TicketOperateApiManager;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleVersionManager;
import com.jdx.rover.monitor.manager.video.VideoManager;
import com.jdx.rover.monitor.manager.xingyun.InfrastructureBugApiManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentAttachment;
import com.jdx.rover.monitor.po.AccidentDetail;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.po.AccidentRecordLog;
import com.jdx.rover.monitor.po.BugRecord;
import com.jdx.rover.monitor.repository.redis.NumberCacheRepository;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.service.tencent.TencentMapService;
import com.jdx.rover.monitor.vo.BugAddVO;
import com.jdx.rover.monitor.vo.accident.AccidentExportDataVO;
import com.jdx.rover.monitor.vo.accident.GetAccidentPageListVO;
import com.jdx.rover.monitor.vo.accident.ManualCreateAccidentVO;
import com.jdx.rover.monitor.vo.accident.MonitorAccidentAttachmentVO;
import com.jdx.rover.monitor.vo.accident.SafetyGroupEditAccidentVO;
import com.jdx.rover.monitor.vo.accident.TechnicalSupportEditAccidentVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import com.jdx.rover.shadow.api.domain.dto.ShadowTrackingEventDTO;
import com.jdx.rover.shadow.api.domain.vo.ShadowTrackingEventVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;

import static com.jdx.rover.monitor.manager.utils.map.TransformUtility.isOutOfChina;

/**
 * <p>
 * 事故提报相关服务
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Slf4j
@Service
public class MonitorAccidentService implements ApplicationContextAware {

    /**
     * The monitor version manager.
     */
    @Autowired
    private VehicleVersionManager vehicleVersionManager;

    /**
     * 单车页推送管理
     */
    @Autowired
    private SingleVehicleManager singleVehicleManager;

    /**
     * The monitor version manager.
     */
    @Autowired
    private MetadataVehicleApiManager metadataVehicleApiManager;

    /**
     * 影子系统 事故相关
     */
    @Autowired
    private ShadowAccidentManager shadowAccidentManager;

    @Autowired
    private AccidentManager accidentManager;

    @Autowired
    private AccidentDetailManager accidentDetailManager;

    @Autowired
    private AccidentAttachmentManager accidentAttachmentManager;

    @Autowired
    private AccidentRecordLogManager accidentRecordLogManager;

    @Autowired
    private AccidentFlowLogManager accidentFlowLogManager;

    @Autowired
    private VehicleRealtimeRepository vehicleRealtimeRepository;

    @Autowired
    private NumberCacheRepository numberCacheRepository;

    @Autowired
    private S3Properties s3Properties;

    @Autowired
    private AccidentJdmePushManager accidentJdmePushManager;

    @Autowired
    private JmqProducerService jmqProducerService;

    @Autowired
    private BugReportManager bugReportManager;

    @Autowired
    private InfrastructureBugApiManager infrastructureBugApiManager;

    @Autowired
    private MonitorVideoService monitorVideoService;

    @Autowired
    private VideoManager videoManager;

    @Autowired
    private RoverVideoProperties roverVideoProperties;

    @Autowired
    private TencentMapService tencentMapService;

    @Autowired
    private TicketOperateApiManager ticketOperateApiManager;

    private ApplicationContext applicationContext;

    /**
     * 自动碰撞事故提报描述
     * 参数列表：0 车号，1 时间，2 枚举：guardian，前bumper，后bumper
     */
    private static final String AUTO_ACCIDENT_DESC = "%s于%s，上报“%s碰撞”报警事件。";

    /**
     * 告警错误码映射
     */
    private static final Map<String,String> ERR_CODE = new HashMap<>();
    static {
        ERR_CODE.put("-1042","前bumper");
        ERR_CODE.put("-1043","后bumper");
        ERR_CODE.put("-1055","全bumper");
        ERR_CODE.put("-13601","guardian");
    }

    /**
     * <p>
     * 自动事故提报
     * </p>
     *
     * @param vehicleName 车辆名称
     * @param alarmEventList 报警时间列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoAccidentReport(String vehicleName, List<AlarmInfoDTO> alarmEventList) {
        try {
            alarmEventList.forEach(vehicleAlarmEvent -> {
                //非碰撞告警不进行处理
                VehicleAlarmEnum alarmTypeEnum = VehicleAlarmEnum.of(vehicleAlarmEvent.getAlarmType());
                if (alarmTypeEnum == null || alarmTypeEnum != VehicleAlarmEnum.VEHICLE_CRASH){
                    return;
                }
                //判断车辆归属方
                VehicleBasicDto vehicleInfo = metadataVehicleApiManager.getVehicleInfo(vehicleName);
                if (vehicleInfo == null) {
                    return ;
                }
                if (VehicleOwnerUseCaseEnum.PRODUCT.getValue().equals(vehicleInfo.getOwnerUseCase())|| VehicleOwnerUseCaseEnum.SCHEDULE.getValue().equals(vehicleInfo.getOwnerUseCase()) || VehicleOwnerUseCaseEnum.FIX.getValue().equals(vehicleInfo.getOwnerUseCase())) {
                    return ;
                }
                String accidentNo = numberCacheRepository.getAccidentNo();

                //通知视频服务视频截图
                monitorVideoService.getVehicleSnapshot(vehicleName, vehicleAlarmEvent.getStartTime(), VideoDirectionEnum.ALL.getValue(), "default", accidentNo);
                Accident accident = new Accident();
                accident.setAlarmNumber(vehicleAlarmEvent.getAlarmNumber());
                accident.setAccidentSource(AccidentSourceEnum.VEHICLE_REPORT.getValue());
                String accidentDesc = String.format(AUTO_ACCIDENT_DESC, vehicleName, DateUtil.formatDateTime(vehicleAlarmEvent.getStartTime()), ERR_CODE.get(vehicleAlarmEvent.getErrorCode()));
                accident.setAccidentDesc(accidentDesc);
                accident.setAccidentReportTime(vehicleAlarmEvent.getStartTime());
                accident.setVehicleName(vehicleName);
                accident.setAccidentNo(accidentNo);
                accident.setCreateUser("报警事件");
                accident.setModifyUser("报警事件");
                accident.setAccidentDayTime(DateUtil.date());
                accident.setOperationStatus(AccidentOperationStatusEnum.NO_OPS.getValue());

                AccidentDetail accidentDetail = new AccidentDetail();
                accidentDetail.setCreateUser("报警事件");
                accidentDetail.setModifyUser("报警事件");
                addAccident(accident, accidentDetail, null, true);
            });
        } catch (Exception e) {
            log.error("碰撞事故自动提报异常！",e);
        }
    }

    /**
     * 事故信息沟通
     * @param accidentNo 事故编号
     * @param xataTaskId xata平台生成的任务ID
     * @param jiraNo 提报的JIRA号
     * @param modifyUser 更新人
     * @param bugAddVO BUG信息
     * @param isNoOpsStatus 一线处理是否无操作状态
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean reportJiraOrXata(String accidentNo,String xataTaskId,String jiraNo, String modifyUser, BugAddVO bugAddVO, boolean isNoOpsStatus){
        if (StringUtils.isBlank(jiraNo)){
            return false;
        }
        //保存事故流程
        String content = String.format("远程安全员，%s-%s，生成缺陷", modifyUser, DateUtil.now());
        AccidentFlowLog accidentFlowLog = new AccidentFlowLog();
        accidentFlowLog.setAccidentNo(accidentNo);
        accidentFlowLog.setUserName(modifyUser);
        accidentFlowLog.setSource(AccidentFlowEnum.BUG_REPORT.getValue());
        accidentFlowLog.setContent(content);
        accidentFlowLog.setCreateUser(modifyUser);
        accidentFlowLog.setModifyUser(modifyUser);
        accidentFlowLogManager.save(accidentFlowLog);
        //更新事故等级和事故类型
        accidentDetailManager.lambdaUpdate()
                .eq(AccidentDetail::getAccidentNo, accidentNo)
                .set(AccidentDetail::getTechnicalSupportAccidentType, bugAddVO.getAccidentType())
                .set(AccidentDetail::getTechnicalSupportAccidentLevel, bugAddVO.getAccidentLevel())
                .set(AccidentDetail::getSafetyGroupAccidentType, bugAddVO.getAccidentType())
                .set(AccidentDetail::getSafetyGroupAccidentLevel, bugAddVO.getAccidentLevel())
                .set(StringUtils.isNotBlank(modifyUser),AccidentDetail::getModifyUser,modifyUser)
                .update();
        //入库成功事务提交后进行单车页数据推送
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //发送事故kafka
                pushAccidentFlowEvent(accidentNo, AccidentFlowEnum.BUG_REPORT.getValue(), modifyUser);
            }
        });


        return accidentManager.lambdaUpdate()
                .eq(Accident::getAccidentNo, accidentNo)
                .set(StringUtils.isNotBlank(jiraNo),Accident::getBugCode,jiraNo)
                .set(Accident::getIsSafetyGroup, YesOrNoEnum.YES.getValue())
                .set(Accident::getAccidentOwner, bugAddVO.getFollower())
                .set(StringUtils.isNotBlank(modifyUser),Accident::getModifyUser,modifyUser)
                .set(Accident::getBugStatus, StatusEnum.NEW.getValue())
                .set(Accident::getBugCreateTime, DateUtil.date())
                .set(isNoOpsStatus, Accident::getOperationStatus, AccidentOperationStatusEnum.PENDING.getValue())
                .update();
    }

    /**
     * 分页获取事故列表
     * @param getAccidentPageListVO getAccidentPageListVO
     * @return PageDTO<GetAccidentPageListDTO>
     */
    public PageDTO<GetAccidentPageListDTO> getAccidentPageList(GetAccidentPageListVO getAccidentPageListVO) {
        IPage<Accident> iPage = new Page<>(getAccidentPageListVO.getPageNum(), getAccidentPageListVO.getPageSize());
        QueryWrapper<AccidentBO> accidentBOQueryWrapper = new QueryWrapper<>();
        accidentBOQueryWrapper.like(StringUtils.isNotBlank(getAccidentPageListVO.getAccidentNo()), "a.accident_no", getAccidentPageListVO.getAccidentNo());
        accidentBOQueryWrapper.eq(StringUtils.isNotBlank(getAccidentPageListVO.getVehicleName()), "a.vehicle_name", getAccidentPageListVO.getVehicleName());
        accidentBOQueryWrapper.between(getAccidentPageListVO.getStartTime() != null && getAccidentPageListVO.getEndTime() != null, "a.create_time", getAccidentPageListVO.getStartTime(), getAccidentPageListVO.getEndTime());
        accidentBOQueryWrapper.between(getAccidentPageListVO.getAccidentReportStartTime() != null && getAccidentPageListVO.getAccidentReportEndTime() != null, "a.accident_report_time", getAccidentPageListVO.getAccidentReportStartTime(), getAccidentPageListVO.getAccidentReportEndTime());
        accidentBOQueryWrapper.eq(StringUtils.isNotBlank(getAccidentPageListVO.getOperationStatus()), "a.operation_status", getAccidentPageListVO.getOperationStatus());
        accidentBOQueryWrapper.eq(getAccidentPageListVO.getIsSafetyGroup() != null, "a.is_safety_group", Boolean.TRUE.equals(getAccidentPageListVO.getIsSafetyGroup()) ? 1 : 0);
        accidentBOQueryWrapper.in(!CollectionUtils.isEmpty(getAccidentPageListVO.getSafetyGroupAccidentLevelList()), "ad.safety_group_accident_level", getAccidentPageListVO.getSafetyGroupAccidentLevelList());
        accidentBOQueryWrapper.in(!CollectionUtils.isEmpty(getAccidentPageListVO.getSafetyGroupAccidentTypeList()) , "ad.safety_group_accident_type", getAccidentPageListVO.getSafetyGroupAccidentTypeList());
        accidentBOQueryWrapper.in(!CollectionUtils.isEmpty(getAccidentPageListVO.getBugStatusList()) , "a.bug_status", getAccidentPageListVO.getBugStatusList());
        accidentBOQueryWrapper.eq(StringUtils.isNotBlank(getAccidentPageListVO.getAccidentOwner()), "a.accident_owner", getAccidentPageListVO.getAccidentOwner());
        if (!CollectionUtils.isEmpty(getAccidentPageListVO.getAccidentModuleList())) {
            int accidentModule = 0;
            for (int i : getAccidentPageListVO.getAccidentModuleList()) {
                accidentModule += 1 << i;
            }
            accidentBOQueryWrapper.gt("ad.safety_group_accident_module & " + accidentModule, 0);
        }

        if (!CollectionUtils.isEmpty(getAccidentPageListVO.getAccidentTagList())) {
            long accidentTag = 0;
            for (int i : getAccidentPageListVO.getAccidentTagList()) {
                accidentTag += 1L << i;
            }
            accidentBOQueryWrapper.gt("ad.safety_group_accident_tag & " + accidentTag, 0);
        }
        accidentBOQueryWrapper.eq(getAccidentPageListVO.getSafetyGroupAccidentResolutionStatus() != null, "ad.safety_group_accident_resolution_status", getAccidentPageListVO.getSafetyGroupAccidentResolutionStatus());
        accidentBOQueryWrapper.eq(getAccidentPageListVO.getTechnicalSupportHandleStatus() != null, "a.is_technical_support_handle", getAccidentPageListVO.getTechnicalSupportHandleStatus());
        accidentBOQueryWrapper.orderByDesc("a.create_time");
        IPage<AccidentBO> pageList = accidentManager.getBaseMapper().getPageList(iPage, accidentBOQueryWrapper);
        List<GetAccidentPageListDTO> getAccidentPageListDTOList = pageList.getRecords().stream().map(accidentBO -> accidentBO.convertBoToDto(accidentBO)).collect(Collectors.toList());
        PageDTO<GetAccidentPageListDTO> pageDTO = new PageDTO<>();
        pageDTO.setPageNum((int) pageList.getCurrent());
        pageDTO.setPageSize((int) pageList.getSize());
        pageDTO.setPages((int) pageList.getPages());
        pageDTO.setTotal(pageList.getTotal());
        pageDTO.setList(getAccidentPageListDTOList);
        return pageDTO;
    }

    /**
     * 获取事故基础信息
     * @param accidentNo accidentNo
     * @return AccidentBasicInfoDTO
     */
    public AccidentBasicInfoDTO getAccidentBasicInfo(String accidentNo) {
        Accident accident = accidentManager.lambdaQuery().eq(Accident::getAccidentNo, accidentNo).one();
        AccidentDetail accidentDetail = accidentDetailManager.lambdaQuery().eq(AccidentDetail::getAccidentNo, accidentNo).one();
        AccidentBasicInfoDTO accidentBasicInfoDTO = new AccidentBasicInfoDTO();
        accidentBasicInfoDTO.setAccidentNo(accidentNo);
        accidentBasicInfoDTO.setShadowEventId(accident.getShadowEventId());
        accidentBasicInfoDTO.setVehicleName(accident.getVehicleName());
        accidentBasicInfoDTO.setStationName(accident.getStationName());
        accidentBasicInfoDTO.setCityName(accident.getCityName());
        accidentBasicInfoDTO.setReportUser(accident.getCreateUser());
        accidentBasicInfoDTO.setAccidentReportTime(accident.getAccidentReportTime());
        accidentBasicInfoDTO.setAccidentDesc(accident.getAccidentDesc());
        accidentBasicInfoDTO.setRoverVersion(accident.getRoverVersion());
        accidentBasicInfoDTO.setBugCode(accident.getBugCode());
        accidentBasicInfoDTO.setAccidentType(accidentDetail.getTechnicalSupportAccidentType());
        accidentBasicInfoDTO.setAccidentLevel(accidentDetail.getTechnicalSupportAccidentLevel());
        if (accident.getBugCode() != null) {
            BugRecord bugRecord = bugReportManager.lambdaQuery().eq(BugRecord::getBugCode, accident.getBugCode()).one();
            if (bugRecord != null) {
                accidentBasicInfoDTO.setBugTitle(bugRecord.getTopic());
            }
        }
        return accidentBasicInfoDTO;
    }

    /**
     * 获取事故详情
     * @param accidentNo accidentNo
     * @return MonitorAccidentDetailDTO
     */
    public MonitorAccidentDetailDTO getAccidentDetail(String accidentNo) {
        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if (accident == null) {
            log.error("获取事故详情,事故不存在,accidentNo:{}", accidentNo);
            return null;
        }
        MonitorAccidentDetailDTO result = new MonitorAccidentDetailDTO();
        AccidentDetail accidentDetail = accidentDetailManager.lambdaQuery().eq(AccidentDetail::getAccidentNo, accidentNo).one();
        List<AccidentAttachment> accidentAttachmentList = accidentAttachmentManager.lambdaQuery().eq(AccidentAttachment::getAccidentNo, accidentNo).list();
        buildMonitorAccidentDetailDTO(result, accident, accidentDetail, accidentAttachmentList);
        return result;
    }

    /**
     * 构建事故详情返回对象
     * @param result
     * @param accident
     * @param accidentDetail
     * @param accidentAttachmentList
     */
    private void buildMonitorAccidentDetailDTO(MonitorAccidentDetailDTO result, Accident accident, AccidentDetail accidentDetail, List<AccidentAttachment> accidentAttachmentList) {
        //事故基础信息
        result.setAccidentNo(accident.getAccidentNo());
        result.setVehicleName(accident.getVehicleName());
        result.setStationName(accident.getStationName());
        result.setAccidentTime(accident.getAccidentReportTime());
        result.setAccidentDesc(accident.getAccidentDesc());
        result.setAccidentSource(accident.getAccidentSource());
        result.setAccidentSourceName(AccidentSourceEnum.getNameByValue(accident.getAccidentSource()));
        result.setAccidentAddress(accident.getAccidentAddress());
        result.setBugCode(accident.getBugCode());
        result.setOperationUser(accident.getOperationUser());
        //事故快照
        List<MonitorAccidentAttachmentDTO> snapshotAttachmentUrlList = new ArrayList<>();
        for (AccidentAttachment accidentAttachment : accidentAttachmentList) {
            if (!Objects.equals(accidentAttachment.getSource(), AccidentAttachmentSourceEnum.VIDEO_SNAPSHOT.getValue())) {
                continue;
            }
            MonitorAccidentAttachmentDTO monitorAccidentAttachmentDTO = new MonitorAccidentAttachmentDTO();
            monitorAccidentAttachmentDTO.setFileKey(accidentAttachment.getFileKey());
            monitorAccidentAttachmentDTO.setType(accidentAttachment.getType());
            final Date expiration = DateUtil.offsetHour(new Date(), 24);
            String videoUrl = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), accidentAttachment.getBucket(), accidentAttachment.getFileKey(), HttpMethod.GET, expiration).toString();
            monitorAccidentAttachmentDTO.setUrl(videoUrl);
            monitorAccidentAttachmentDTO.setName(VideoDirectionEnum.getNameByValue(accidentAttachment.getLocation()));
            snapshotAttachmentUrlList.add(monitorAccidentAttachmentDTO);
        }
        //事故快照排序
        snapshotAttachmentUrlList.sort(new Comparator<>() {
            private static final Map<String, Integer> directionOrder = new HashMap<>();
            static {
                directionOrder.put(VideoDirectionEnum.FRONT.getName(), 1);
                directionOrder.put(VideoDirectionEnum.BACK.getName(), 2);
                directionOrder.put(VideoDirectionEnum.LEFT.getName(), 3);
                directionOrder.put(VideoDirectionEnum.RIGHT.getName(), 4);
            }
            @Override
            public int compare(MonitorAccidentAttachmentDTO o1, MonitorAccidentAttachmentDTO o2) {
                if(StrUtil.isBlank(o1.getName()) || StrUtil.isBlank(o2.getName())) {
                    return 0;
                }
                return Integer.compare(directionOrder.get(o1.getName()), directionOrder.get(o2.getName()));
            }
        });

        result.setSnapshotAttachmentUrlList(snapshotAttachmentUrlList);
        result.setOperationHandleMethodName(AccidentHandleMethodEnum.getNameByValue(accidentDetail.getOperationHandleMethod()));
        result.setOperationCompensatedName(CompensateTypeEnum.getNameByValue(accidentDetail.getOperationCompensated()));
        result.setOperationAmount(accidentDetail.getOperationAmount());
        result.setOperationIsReportVehicleNetName(AccidentSupervisionEnum.getNameByValue(accidentDetail.getOperationIsReportVehicleNet()));
        result.setOperationAccidentTypeName(AccidentOperationTypeEnum.getNameByValue(accidentDetail.getOperationAccidentType()));
        result.setOperationAccidentJudgeName(AccidentOperationJudgeEnum.getNameByValue(accidentDetail.getOperationAccidentJudge()));
        result.setOperationAccidentReason(accidentDetail.getOperationAccidentReason());
        List<MonitorAccidentAttachmentDTO> operationAttachmentUrlList = generateAttachment(accidentAttachmentList, AccidentAttachmentSourceEnum.OPERATION.getValue());
        result.setOperationAttachmentUrlList(operationAttachmentUrlList);
        //日志
        List<MonitorAccidentOperateDTO> opertaionLogList = new ArrayList<>();
        result.setOpertaionLogList(opertaionLogList);
        List<AccidentRecordLog> accidentRecordLogList = accidentRecordLogManager.lambdaQuery().eq(AccidentRecordLog::getAccidentNo, accident.getAccidentNo()).orderByDesc(AccidentRecordLog::getId).list();
        for (AccidentRecordLog recordLog : accidentRecordLogList) {
            MonitorAccidentOperateDTO monitorAccidentOperateDTO = new MonitorAccidentOperateDTO();
            monitorAccidentOperateDTO.setTitle(recordLog.getTitle());
            monitorAccidentOperateDTO.setContent(recordLog.getContent());
            opertaionLogList.add(monitorAccidentOperateDTO);
        }
        //技术支持跟进信息
        result.setTechnicalSupportAccidentType(accidentDetail.getTechnicalSupportAccidentType());
        result.setTechnicalSupportAccidentLevel(accidentDetail.getTechnicalSupportAccidentLevel());
        result.setTechnicalSupportDescription(accidentDetail.getTechnicalSupportDescription());
        List<MonitorAccidentAttachmentDTO> technicalSupportAttmentUrlList = generateAttachment(accidentAttachmentList, AccidentAttachmentSourceEnum.TECHNICAL_SUPPORT.getValue());
        result.setTechnicalSupportAttmentUrlList(technicalSupportAttmentUrlList);
        //安全组跟进信息
        result.setSafetyGroupAccidentType(accidentDetail.getSafetyGroupAccidentType());
        result.setSafetyGroupAccidentLevel(accidentDetail.getSafetyGroupAccidentLevel());
        result.setSafetyGroupDescription(accidentDetail.getSafetyGroupDescription());
        //解析模块
        List<Integer> moduleList = new ArrayList<>();
        result.setSafetyGroupAccidentModuleList(moduleList);
        Integer accidentModule = accidentDetail.getSafetyGroupAccidentModule();
        if (accidentModule != null && accidentModule > 0) {
            for (AccidentModuleEnum accidentModuleEnum : AccidentModuleEnum.values()) {
                Integer value = accidentModuleEnum.getValue();
                if ((accidentModule & 1 << value) > 0) {
                    moduleList.add(value);
                }
            }
        }
        //解析标签
        List<Integer> tagList = new ArrayList<>();
        result.setSafetyGroupAccidentTagList(tagList);
        Long accidentTag = accidentDetail.getSafetyGroupAccidentTag();
        if (accidentTag != null && accidentTag > 0) {
            for (AccidentTagEnum accidentTagEnum : AccidentTagEnum.values()) {
                Integer value = accidentTagEnum.getValue();
                if ((accidentTag & 1L << value) > 0) {
                    tagList.add(value);
                }
            }
        }
        result.setSafetyGroupAccidentSolution(accidentDetail.getSafetyGroupAccidentSolution());
        result.setSafetyGroupAccidentSuspendReason(accidentDetail.getSafetyAccidentSuspendReason());
        List<MonitorAccidentAttachmentDTO> safetyGroupAttmentUrlList = generateAttachment(accidentAttachmentList, AccidentAttachmentSourceEnum.SAFETY_GROUP.getValue());
        result.setSafetyGroupAttmentUrlList(safetyGroupAttmentUrlList);
        result.setBugStatus(accident.getBugStatus());
        result.setBugStatusName(StatusEnum.getAliasByValue(accident.getBugStatus()));
        result.setAccidentOwner(accident.getAccidentOwner());
        result.setVideoCaptureStatus(accident.getVideoCaptureStatus());
    }

    /**
     * 生成附件
     * @param accidentAttachmentList
     * @param source
     * @return
     */
    private List<MonitorAccidentAttachmentDTO> generateAttachment(List<AccidentAttachment> accidentAttachmentList, String source) {
        final Date expiration = DateUtil.offsetHour(new Date(), 24);
        List<MonitorAccidentAttachmentDTO> result = accidentAttachmentList.stream().filter(accidentAttachment -> Objects.equals(source, accidentAttachment.getSource()))
                .map(accidentAttachment -> {
                    MonitorAccidentAttachmentDTO monitorAccidentAttachmentDTO = new MonitorAccidentAttachmentDTO();
                    monitorAccidentAttachmentDTO.setType(accidentAttachment.getType());
                    monitorAccidentAttachmentDTO.setFileKey(accidentAttachment.getFileKey());
                    monitorAccidentAttachmentDTO.setUrl(S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), accidentAttachment.getBucket(), accidentAttachment.getFileKey(), HttpMethod.GET, expiration).toString());
                    return monitorAccidentAttachmentDTO;
                }).collect(Collectors.toList());
        return result;
    }

    /**
     * 技术支持编辑事故
     * @param technicalSupportEditAccidentVO technicalSupportEditAccidentVO
     * @return MonitorErrorEnum
     */
    @Transactional(rollbackFor = Exception.class)
    public MonitorErrorEnum technicalSupportEditAccident(TechnicalSupportEditAccidentVO technicalSupportEditAccidentVO) {
        String loginUsername = UserUtils.getLoginUser();
        Accident accident = accidentManager.selectByAccidentNo(technicalSupportEditAccidentVO.getAccidentNo());
        if (accident == null) {
            log.error("技术支持编辑事故,事故不存在,accidentNo:{}", technicalSupportEditAccidentVO.getAccidentNo());
            throw new AppException(MonitorErrorEnum.ERROR_ACCIDENT_NOT_EXIST.getCode(), MonitorErrorEnum.ERROR_ACCIDENT_NOT_EXIST.getMessage());
        }
        //更新技术支持填写信息
        accidentDetailManager.lambdaUpdate()
                .eq(AccidentDetail::getAccidentNo, technicalSupportEditAccidentVO.getAccidentNo())
                .set(AccidentDetail::getTechnicalSupportAccidentType, technicalSupportEditAccidentVO.getTechnicalSupportAccidentType())
                .set(AccidentDetail::getTechnicalSupportAccidentLevel, technicalSupportEditAccidentVO.getTechnicalSupportAccidentLevel())
                .set(AccidentDetail::getTechnicalSupportDescription, technicalSupportEditAccidentVO.getTechnicalSupportDescription())

                .set(AccidentDetail::getSafetyGroupAccidentType, technicalSupportEditAccidentVO.getTechnicalSupportAccidentType())
                .set(AccidentDetail::getSafetyGroupAccidentLevel, technicalSupportEditAccidentVO.getTechnicalSupportAccidentLevel())

                .update();
        //更新事故表状态
        accidentManager.lambdaUpdate()
                .eq(Accident::getAccidentNo, technicalSupportEditAccidentVO.getAccidentNo())
                .set(Accident::getIsTechnicalSupportHandle, YesOrNoEnum.YES.getValue())
                .set(technicalSupportEditAccidentVO.isSendCard() && AccidentOperationStatusEnum.NO_OPS.getValue().equals(accident.getOperationStatus()),
                        Accident::getOperationStatus, AccidentOperationStatusEnum.PENDING.getValue())
                .update();
        //更新附件
        accidentAttachmentManager.lambdaUpdate()
                .eq(AccidentAttachment::getAccidentNo, technicalSupportEditAccidentVO.getAccidentNo())
                .eq(AccidentAttachment::getSource, AccidentAttachmentSourceEnum.TECHNICAL_SUPPORT.getValue())
                .remove();
        if (!CollectionUtils.isEmpty(technicalSupportEditAccidentVO.getTechnicalSupportAttmentList())) {
            for (MonitorAccidentAttachmentVO accidentAttachmentVO: technicalSupportEditAccidentVO.getTechnicalSupportAttmentList()) {
                AccidentAttachment accidentAttachment = getAccidentAttachment(technicalSupportEditAccidentVO.getAccidentNo(),
                        accidentAttachmentVO, AccidentAttachmentSourceEnum.TECHNICAL_SUPPORT, loginUsername);
                accidentAttachmentManager.getBaseMapper().insert(accidentAttachment);
            }
        }
        //保存日志
        accidentRecordLogManager.save(createAccidentRecordLog(accident.getAccidentNo(), loginUsername, AccidentLogEnum.TECHNICAL_SUPPORT_EDIT));
        //保存事故流程
        String editContent = String.format("%s/%s/%s", PreliminaryAccidentTypeEnum.getNameByValue(
                technicalSupportEditAccidentVO.getTechnicalSupportAccidentType()),
                PreliminaryAccidentLevelEnum.getNameByValue(technicalSupportEditAccidentVO.getTechnicalSupportAccidentLevel()),
                technicalSupportEditAccidentVO.getTechnicalSupportDescription());
        String content = String.format("远程安全员，%s-%s，初步分析:%s", loginUsername, DateUtil.now(), editContent);
        AccidentFlowLog accidentFlowLog = new AccidentFlowLog();
        accidentFlowLog.setAccidentNo(accident.getAccidentNo());
        accidentFlowLog.setUserName(loginUsername);
        accidentFlowLog.setSource(AccidentFlowEnum.TECHNICAL_SUPPORT_EDIT.getValue());
        accidentFlowLog.setContent(content);
        accidentFlowLog.setCreateUser(loginUsername);
        accidentFlowLog.setModifyUser(loginUsername);
        accidentFlowLogManager.save(accidentFlowLog);
        //更新配置
        LambdaUpdateWrapper<AccidentJdmePush> accidentJdmePushLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        accidentJdmePushLambdaUpdateWrapper.eq(AccidentJdmePush::getAccidentNo, technicalSupportEditAccidentVO.getAccidentNo());
        accidentJdmePushLambdaUpdateWrapper.set(AccidentJdmePush::getLevel, technicalSupportEditAccidentVO.getTechnicalSupportAccidentLevel());
        accidentJdmePushLambdaUpdateWrapper.set(AccidentJdmePush::getTitle, technicalSupportEditAccidentVO.getTechnicalSupportDescription());
        accidentJdmePushManager.update(accidentJdmePushLambdaUpdateWrapper);

        //入库成功事务提交后进行单车页数据推送
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //推送单车数据
                singleVehicleManager.pushSingleVehicleAccident(accident.getVehicleName());
                //消息来自于前端用户点击的“提交并报备”或“保存并报备”
                if (technicalSupportEditAccidentVO.isSendCard()) {
                    //发送卡片
                    pushAccidentFlowEvent(accident.getAccidentNo(), AccidentFlowEnum.TECHNICAL_SUPPORT_EDIT.getValue(), loginUsername);
                    //推送京小鸽
                    AccidentEvent accidentEvent = new AccidentEvent(this, accident);
                    applicationContext.publishEvent(accidentEvent);
                }

                // 事故工单解除报备提醒
                ticketOperateApiManager.processAccident(accident.getAlarmNumber());
            }
        });
        return MonitorErrorEnum.OK;
    }

    private AccidentRecordLog createAccidentRecordLog(String accidentNo, String userName, AccidentLogEnum accidentLogEnum) {
        AccidentRecordLog accidentRecordLog = new AccidentRecordLog();
        accidentRecordLog.setAccidentNo(accidentNo);
        accidentRecordLog.setUserName(userName);
        accidentRecordLog.setSource(accidentLogEnum.getValue());
        accidentRecordLog.setTitle(accidentLogEnum.getTitle());
        accidentRecordLog.setContent(String.format(accidentLogEnum.getContent(), userName, DateUtil.now()));
        accidentRecordLog.setCreateUser(userName);
        accidentRecordLog.setModifyUser(userName);
        return accidentRecordLog;
    }

    /**
     * 安全组编辑事故
     * @param safetyGroupEditAccidentVO safetyGroupEditAccidentVO
     * @return MonitorErrorEnum
     */
    @Transactional(rollbackFor = Exception.class)
    public MonitorErrorEnum safetyGroupEditAccident(SafetyGroupEditAccidentVO safetyGroupEditAccidentVO) {
        String loginUsername = UserUtils.getLoginUser();
        Accident accident = accidentManager.selectByAccidentNo(safetyGroupEditAccidentVO.getAccidentNo());
        if (accident == null) {
            log.error("安全组编辑事故,事故不存在,accidentNo:{}", safetyGroupEditAccidentVO.getAccidentNo());
            throw new AppException(MonitorErrorEnum.ERROR_ACCIDENT_NOT_EXIST.getCode(), MonitorErrorEnum.ERROR_ACCIDENT_NOT_EXIST.getMessage());
        }
        //更新安全组已编辑
        accidentManager.lambdaUpdate().eq(Accident::getAccidentNo, safetyGroupEditAccidentVO.getAccidentNo()).set(Accident::getIsSafetyGroupEdit, YesOrNoEnum.YES.getValue()).update();
        //更新安全组信息
        accidentDetailManager.lambdaUpdate()
                .eq(AccidentDetail::getAccidentNo, safetyGroupEditAccidentVO.getAccidentNo())
                .set(AccidentDetail::getSafetyGroupAccidentLevel, safetyGroupEditAccidentVO.getSafetyGroupAccidentLevel())
                .set(AccidentDetail::getSafetyGroupAccidentType, safetyGroupEditAccidentVO.getSafetyGroupAccidentType())
                .set(AccidentDetail::getSafetyGroupDescription, safetyGroupEditAccidentVO.getSafetyGroupDescription())
                .set(AccidentDetail::getSafetyGroupAccidentModule, listToLong(safetyGroupEditAccidentVO.getSafetyGroupAccidentModuleList()))
                .set(AccidentDetail::getSafetyGroupAccidentTag, listToLong(safetyGroupEditAccidentVO.getSafetyGroupAccidentTagList()))
                .set(AccidentDetail::getSafetyGroupAccidentSolution, safetyGroupEditAccidentVO.getSafetyGroupAccidentSolution())
                .set(AccidentDetail::getSafetyAccidentSuspendReason, safetyGroupEditAccidentVO.getSafetyGroupAccidentSuspendReason())

                .set(AccidentDetail::getTechnicalSupportAccidentType, safetyGroupEditAccidentVO.getSafetyGroupAccidentType())
                .set(AccidentDetail::getTechnicalSupportAccidentLevel, safetyGroupEditAccidentVO.getSafetyGroupAccidentLevel())

                .update();
        //删除安全组历史附件
        accidentAttachmentManager.lambdaUpdate().eq(AccidentAttachment::getAccidentNo, safetyGroupEditAccidentVO.getAccidentNo()).eq(AccidentAttachment::getSource, AccidentAttachmentSourceEnum.SAFETY_GROUP.getValue()).remove();
        //更新安全组附件
        if (!CollectionUtils.isEmpty(safetyGroupEditAccidentVO.getSafetyGroupAccidentReport())) {
            for (MonitorAccidentAttachmentVO monitorAccidentAttachmentVO : safetyGroupEditAccidentVO.getSafetyGroupAccidentReport()) {
                AccidentAttachment accidentAttachment = getAccidentAttachment(safetyGroupEditAccidentVO.getAccidentNo(), monitorAccidentAttachmentVO, AccidentAttachmentSourceEnum.SAFETY_GROUP, loginUsername);
                accidentAttachmentManager.getBaseMapper().insert(accidentAttachment);
            }
        }
        //保存日志
        accidentRecordLogManager.save(createAccidentRecordLog(accident.getAccidentNo(), loginUsername, AccidentLogEnum.SAFETY_GROUP_EDIT));
        //保存事故流程
        String editContent = buildAccidentSafetyContent(safetyGroupEditAccidentVO);
        String content = String.format("安全组，%s-%s，事故分析:%s", loginUsername, DateUtil.now(), editContent);
        AccidentFlowLog accidentFlowLog = new AccidentFlowLog();
        accidentFlowLog.setAccidentNo(accident.getAccidentNo());
        accidentFlowLog.setUserName(loginUsername);
        accidentFlowLog.setSource(AccidentFlowEnum.SAFETY_GROUP_EDIT.getValue());
        accidentFlowLog.setContent(content);
        accidentFlowLog.setCreateUser(loginUsername);
        accidentFlowLog.setModifyUser(loginUsername);
        accidentFlowLogManager.save(accidentFlowLog);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //推送单车数据
                singleVehicleManager.pushSingleVehicleAccident(accident.getVehicleName());
                //发送卡片
                pushAccidentFlowEvent(accident.getAccidentNo(), AccidentFlowEnum.SAFETY_GROUP_EDIT.getValue(), loginUsername);
            }
        });
        return MonitorErrorEnum.OK;
    }

    private String buildAccidentSafetyContent(SafetyGroupEditAccidentVO safetyGroupEditAccidentVO) {
        String content = "";
        content += SafetyGroupAccidentLevelEnum.getNameByValue(safetyGroupEditAccidentVO.getSafetyGroupAccidentLevel()) + "/";
        content += SafetyGroupAccidentTypeEnum.getNameByValue(safetyGroupEditAccidentVO.getSafetyGroupAccidentType()) + "/";
        content += safetyGroupEditAccidentVO.getSafetyGroupDescription() + "/";
        //设置事故模块
        List<String> accidentModuleNameList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(safetyGroupEditAccidentVO.getSafetyGroupAccidentModuleList())) {
            for (Integer module : safetyGroupEditAccidentVO.getSafetyGroupAccidentModuleList()) {
                String moduleName = AccidentModuleEnum.getNameByValue(module);
                accidentModuleNameList.add(moduleName);
            }
            content += String.join("，", accidentModuleNameList) + "/";
        }
        //设置事故标签
        List<String> accidentTagNameList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(safetyGroupEditAccidentVO.getSafetyGroupAccidentTagList())) {
            for (Integer tag : safetyGroupEditAccidentVO.getSafetyGroupAccidentTagList()) {
                String tagName = AccidentTagEnum.getNameByValue(tag);
                accidentTagNameList.add(tagName);
            }
            content += String.join("，", accidentTagNameList) + "/";
        }
        if (safetyGroupEditAccidentVO.getSafetyGroupAccidentSuspendReason() != null) {
            content += safetyGroupEditAccidentVO.getSafetyGroupAccidentSuspendReason() + "/";
        }
        if (safetyGroupEditAccidentVO.getSafetyGroupAccidentSolution() != null) {
            content += safetyGroupEditAccidentVO.getSafetyGroupAccidentSolution() + "/";
        }
        content = StringUtils.chop(content);
        return content;
    }

    private AccidentAttachment getAccidentAttachment(String accidentNo, MonitorAccidentAttachmentVO monitorAccidentAttachmentVO,
                                                     AccidentAttachmentSourceEnum accidentAttachmentSourceEnum, String username) {
        AccidentAttachment accidentAttachment = new AccidentAttachment();
        accidentAttachment.setAccidentNo(accidentNo);
        accidentAttachment.setType(monitorAccidentAttachmentVO.getType());
        accidentAttachment.setFileKey(monitorAccidentAttachmentVO.getFileKey());
        accidentAttachment.setBucket(RoverBucketEnum.ROVER_OPERATION.getValue());
        accidentAttachment.setSource(accidentAttachmentSourceEnum.getValue());
        accidentAttachment.setCreateUser(username);
        accidentAttachment.setModifyUser(username);
        return accidentAttachment;
    }

    /**
     * 将List<String> 转换为 String ;分割
     * @param list
     * @return
     */
    private Long listToLong(List<Integer> list) {
        Long tag = 0L;
        if (!CollectionUtils.isEmpty(list)) {
            for (int i : list) {
                tag += 1L << i;
            }
        }
        return tag;
    }

    /**
     * 手动创建事故
     * @param manualCreateAccidentVO manualCreateAccidentVO
     * @return MonitorErrorEnum
     */
    @Transactional(rollbackFor = Exception.class)
    public MonitorErrorEnum manualCreateAccident(ManualCreateAccidentVO manualCreateAccidentVO) {
        String loginUserName = UserUtils.getLoginUser();
        //设置事故
        String accidentNo = numberCacheRepository.getAccidentNo();
        monitorVideoService.getVehicleHistorySnapshot(manualCreateAccidentVO.getVehicleName(), manualCreateAccidentVO.getAccidentTime(), VideoDirectionEnum.ALL.getValue(), "default", accidentNo);
        Accident accident = new Accident();
        accident.setAccidentNo(accidentNo);
        accident.setAccidentSource(AccidentSourceEnum.MANUALLY_CREATED.getValue());
        accident.setAccidentReportTime(manualCreateAccidentVO.getAccidentTime());
        accident.setAccidentStartTime(manualCreateAccidentVO.getAccidentStartTime());
        accident.setAccidentEndTime(manualCreateAccidentVO.getAccidentEndTime());
        accident.setVehicleName(manualCreateAccidentVO.getVehicleName());
        accident.setIsTechnicalSupportHandle(YesOrNoEnum.YES.getValue());
        String accidentDesc = String.format("%s曾于%s，经人工核实发生事故，核实人%s", accident.getVehicleName(), (manualCreateAccidentVO.getAccidentTime() != null ? DateUtil.format(manualCreateAccidentVO.getAccidentTime(), "yyyy-MM-dd HH:mm:ss") : (DateUtil.format(manualCreateAccidentVO.getAccidentStartTime(), "yyyy-MM-dd HH:mm:ss") + "-" + (DateUtil.format(manualCreateAccidentVO.getAccidentEndTime(), "yyyy-MM-dd HH:mm:ss")))), loginUserName);
        accident.setAccidentDesc(accidentDesc);
        accident.setAccidentAddress(manualCreateAccidentVO.getAccidentAddress());
        accident.setCreateUser(loginUserName);
        accident.setModifyUser(loginUserName);
        accident.setAccidentDayTime(DateUtil.date(manualCreateAccidentVO.getAccidentTime()));
        accident.setOperationStatus(AccidentOperationStatusEnum.PENDING.getValue());
        //设置事故详情
        AccidentDetail accidentDetail = new AccidentDetail();
        accidentDetail.setAccidentNo(accidentNo);
        accidentDetail.setTechnicalSupportAccidentType(manualCreateAccidentVO.getTechnicalSupportAccidentType());
        accidentDetail.setTechnicalSupportAccidentLevel(manualCreateAccidentVO.getTechnicalSupportAccidentLevel());
        accidentDetail.setTechnicalSupportDescription(manualCreateAccidentVO.getTechnicalSupportDescription());
        accidentDetail.setSafetyGroupAccidentType(manualCreateAccidentVO.getTechnicalSupportAccidentType());
        accidentDetail.setSafetyGroupAccidentLevel(manualCreateAccidentVO.getTechnicalSupportAccidentLevel());
        accidentDetail.setCreateUser(loginUserName);
        accidentDetail.setModifyUser(loginUserName);
        addAccident(accident, accidentDetail, manualCreateAccidentVO.getTechnicalSupportAttmentList(), false);
        return MonitorErrorEnum.OK;
    }

    /**
     * 创建事故
     * @param accident
     * @param accidentDetail
     * @param accidnetAttmentList
     * @param isAutoReport 是否自动上报
     */
    @Transactional(rollbackFor = Exception.class)
    public void addAccident(Accident accident, AccidentDetail accidentDetail, List<MonitorAccidentAttachmentVO> accidnetAttmentList, boolean isAutoReport){

        //车辆信息校验
        VehicleBasicDTO vehicleInfo  = metadataVehicleApiManager.getByName(accident.getVehicleName());
        if (vehicleInfo == null){
            throw new AppException(MonitorErrorEnum.ERROR_VEHICLE_ABSENT.getCode(),MonitorErrorEnum.ERROR_VEHICLE_ABSENT.getMessage());
        }

        //登录信息判断,判断是否存在用户信息，无用户信息抛出异常
        if (StringUtils.isEmpty(accident.getCreateUser())){
            throw new AppException(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(),MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
        }

        //设置编号
        String accidentNo = accident.getAccidentNo() != null ? accident.getAccidentNo() : numberCacheRepository.getAccidentNo();
        accident.setAccidentNo(accidentNo);
        accidentDetail.setAccidentNo(accidentNo);

        //查询rover版本号
        Map<String, String> vehicleVersion = vehicleVersionManager.getVehicleVersion(accident.getVehicleName());
        accident.setRoverVersion(vehicleVersion.get(OtaMoudleEnum.ROVER.getName()));

        //站点名称设置
        accident.setStationName(vehicleInfo.getStationName());
        accident.setCityName(vehicleInfo.getCityName());

        //车端上报的设置经纬度和发生地址
        if (AccidentSourceEnum.VEHICLE_REPORT.getValue().equals(accident.getAccidentSource())) {
            //经纬度设置
            VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(accident.getVehicleName());
            if (vehicleRealtimeInfoDTO != null) {
                accident.setLatitude(vehicleRealtimeInfoDTO.getLat());
                accident.setLongitude(vehicleRealtimeInfoDTO.getLon());
                if (vehicleRealtimeInfoDTO.getLat() != null && vehicleRealtimeInfoDTO.getLon() != null) {
                    //判断是否在中国境内
                    if (!isOutOfChina(vehicleRealtimeInfoDTO.getLat(), vehicleRealtimeInfoDTO.getLon())) {
                        accident.setAccidentAddress(tencentMapService.wgs84ToAddress(vehicleRealtimeInfoDTO.getLat(), vehicleRealtimeInfoDTO.getLon()));
                    }
                }
            }
        }
        //同步影子系统
        ShadowTrackingEventVO shadowTrackingEventVO = new ShadowTrackingEventVO();
        shadowTrackingEventVO.setReportTime(accident.getAccidentReportTime() != null ? accident.getAccidentReportTime() : new Date());
        shadowTrackingEventVO.setStartTime(DateUtil.offsetSecond(accident.getAccidentReportTime(), -1 * 15));
        shadowTrackingEventVO.setEndTime(DateUtil.offsetSecond(accident.getAccidentReportTime(), 5));
        shadowTrackingEventVO.setVehicleName(accident.getVehicleName());
        shadowTrackingEventVO.setEventNo("PC_MONITOR1129585914");
        shadowTrackingEventVO.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
        String[] param = new String[]{accident.getVehicleName(), DateUtil.formatDateTime(accident.getCreateTime())};
        shadowTrackingEventVO.setBodyArray(param);
        ShadowTrackingEventDTO shadowTrackingEventDTO = shadowAccidentManager.addTrackingEvent(shadowTrackingEventVO);
        accident.setShadowEventId(shadowTrackingEventDTO.getId());
        //事故快照截取状态
        String key = RedisKeyEnum.VIDEO_CAPTURE_STATUS.getValue() + accidentNo;
        String videoCaptureStatus = RedissonUtils.getObject(key);
        if(StrUtil.isNotBlank(videoCaptureStatus)) {
            accident.setVideoCaptureStatus(videoCaptureStatus);
        } else {
            accident.setVideoCaptureStatus(VideoCaptureStatus.CAPTURING.getCode());
        }
        //保存信息
        accidentManager.save(accident);

        //保存详情
        accidentDetail.setAccidentNo(accidentNo);
        accidentDetailManager.save(accidentDetail);

        //设置事故附件
        if (!CollectionUtils.isEmpty(accidnetAttmentList)) {
            for (MonitorAccidentAttachmentVO accidentAttachmentVO: accidnetAttmentList) {
                AccidentAttachment accidentAttachment = getAccidentAttachment(accidentNo, accidentAttachmentVO, AccidentAttachmentSourceEnum.TECHNICAL_SUPPORT, accident.getCreateUser());
                accidentAttachmentManager.getBaseMapper().insert(accidentAttachment);
            }
        }

        String accidentFlowEnum = AccidentFlowEnum.VEHICLE_REPORT.getValue();;

        //保存日志
        if (AccidentSourceEnum.MANUALLY_CREATED.getValue().equals(accident.getAccidentSource())) {
            String content = String.format(AccidentLogEnum.MANUAL_REPORT.getContent(), accident.getVehicleName(), accident.getAccidentReportTime() != null ? DateUtil.format(accident.getAccidentReportTime(), "yyyy-MM-dd HH:mm:ss") : DateUtil.format(accident.getAccidentStartTime(), "yyyy-MM-dd HH:mm:ss") + "-" + (DateUtil.format(accident.getAccidentEndTime(), "yyyy-MM-dd HH:mm:ss")), accident.getCreateUser(), DateUtil.now());
            AccidentRecordLog accidentRecordLog = createAccidentLog(accident, AccidentLogEnum.MANUAL_REPORT, content);
            accidentRecordLogManager.save(accidentRecordLog);
            String flowContent = String.format("上报碰撞，%s曾于%s，经人工核实发生事故，核实人%s", accident.getVehicleName(), DateUtil.format(accident.getAccidentReportTime(), "yyyy-MM-dd HH:mm:ss"), accident.getCreateUser());
            AccidentFlowLog accidentFlowLog = createAccidentFlowLog(accident, flowContent, AccidentFlowEnum.MANUALLY_CREATED);
            accidentFlowLogManager.save(accidentFlowLog);
            accidentFlowEnum = AccidentFlowEnum.MANUALLY_CREATED.getValue();
        } else if (AccidentSourceEnum.VEHICLE_REPORT.getValue().equals(accident.getAccidentSource())) {
            String content = accident.getAccidentDesc();
            AccidentRecordLog accidentRecordLog = createAccidentLog(accident, AccidentLogEnum.VEHICLE_REPORT, content);
            accidentRecordLogManager.save(accidentRecordLog);
            String flowContent = "上报碰撞，" + accident.getAccidentDesc();
            AccidentFlowLog accidentFlowLog = createAccidentFlowLog(accident, flowContent, AccidentFlowEnum.VEHICLE_REPORT);
            accidentFlowLogManager.save(accidentFlowLog);
            accidentFlowEnum = AccidentFlowEnum.VEHICLE_REPORT.getValue();
        }
        //发布创建事故消息
        //自动上报过来的，不需要推送京小鸽用户待办
        if(!isAutoReport) {
            AccidentEvent accidentEvent = new AccidentEvent(this, accident);
            applicationContext.publishEvent(accidentEvent);
        }
        //入库成功事务提交后进行单车页数据推送
        String finalAccidentFlowEnum = accidentFlowEnum;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //推送单车页数据
                singleVehicleManager.pushSingleVehicleAccident(accident.getVehicleName());
                //手动创建的事故发送卡片消息
                pushAccidentFlowEvent(accidentNo, finalAccidentFlowEnum, accident.getCreateUser());
            }
        });
    }

    /**
     * 生成事故时，创建事故流程记录
     * @param accident
     * @param flowContent
     * @param accidentFlowEnum
     */
    private AccidentFlowLog createAccidentFlowLog(Accident accident, String flowContent, AccidentFlowEnum accidentFlowEnum) {
        AccidentFlowLog accidentFlowLog = new AccidentFlowLog();
        accidentFlowLog.setAccidentNo(accident.getAccidentNo());
        accidentFlowLog.setUserName(accident.getCreateUser());
        accidentFlowLog.setSource(accidentFlowEnum.getValue());
        accidentFlowLog.setContent(flowContent);
        accidentFlowLog.setCreateUser(accident.getCreateUser());
        accidentFlowLog.setModifyUser(accident.getCreateUser());
        return accidentFlowLog;
    }

    /**
     * 生成事故时,创建事故记录
     * @param accident
     * @param accidentLogEnum
     * @return
     */
    private AccidentRecordLog createAccidentLog(Accident accident, AccidentLogEnum accidentLogEnum, String content) {
        AccidentRecordLog accidentRecordLog = new AccidentRecordLog();
        accidentRecordLog.setAccidentNo(accident.getAccidentNo());
        accidentRecordLog.setUserName(accident.getCreateUser());
        accidentRecordLog.setSource(accidentLogEnum.getValue());
        accidentRecordLog.setTitle(accidentLogEnum.getTitle());
        accidentRecordLog.setContent(content);
        accidentRecordLog.setCreateUser(accident.getCreateUser());
        accidentRecordLog.setModifyUser(accident.getCreateUser());
        return accidentRecordLog;
    }

    /**
     * 回调,设置上下文
     * @param applicationContext
     * @throws BeansException
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public List<AccidentTagDTO> getTagList() {
        Map<AccidentModuleEnum, List<AccidentTagDTO>> accidentModuleEnumListMap = new TreeMap<>();
        for (AccidentTagEnum accidentTagEnum : AccidentTagEnum.values()) {
            AccidentTagDTO accidentTagDTO = new AccidentTagDTO();
            accidentTagDTO.setValue(accidentTagEnum.getValue());
            accidentTagDTO.setName(accidentTagEnum.getName());
            accidentModuleEnumListMap.computeIfAbsent(accidentTagEnum.getAccidentModuleEnum(), k -> new ArrayList<>()).add(accidentTagDTO);
        }
        List<AccidentTagDTO> result = new ArrayList<>();
        accidentModuleEnumListMap.forEach((k, v) -> {
            AccidentTagDTO accidentTagDTO = new AccidentTagDTO();
            accidentTagDTO.setName(k.getName());
            accidentTagDTO.setValue(k.getValue());
            accidentTagDTO.setChild(v);
            result.add(accidentTagDTO);
        });
        return result;
    }

    private void pushAccidentFlowEvent(String accidentNo, String accidentFlowEnum, String userName) {
        AccidentFlowDTO accidentFlowDTO = new AccidentFlowDTO();
        accidentFlowDTO.setAccidentNo(accidentNo);
        accidentFlowDTO.setAccidentFlowEnum(accidentFlowEnum);
        accidentFlowDTO.setOperator(userName);
        log.info("发送事故流程Kafka:{}",JsonUtils.writeValueAsString(accidentFlowDTO));
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_ACCIDENT_FLOW_EVENT.getTopic(), accidentFlowDTO);
    }

    //TODO 导出数据
    /**
     * 导出指定时间事故数据
     * @param accidentExportDataVO accidentExportDataVO
     * @param response response
     */
    public void exportAccidentData(AccidentExportDataVO accidentExportDataVO, HttpServletResponse response) throws IOException {

        QueryWrapper<AccidentExportBO> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("a.create_time", accidentExportDataVO.getStartTime(), accidentExportDataVO.getEndTime());
        List<AccidentExportBO> accidentExportBOList = accidentManager.getBaseMapper().getAccidentExportList(queryWrapper);
        List<AccidentExportDTO> result = accidentExportBOList.stream().map(accidentExportBO -> accidentExportBO.convertBoToDto(accidentExportBO)).toList();
        if (CollectionUtils.isEmpty(result)) {
            log.info("无事故导出,username:{}, 时间段:{} - {}", UserUtils.getLoginUser(), accidentExportDataVO.getStartTime(), accidentExportDataVO.getEndTime());
            return;
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String name = "事故数据导出-" + System.currentTimeMillis() + ".xlsx";
        String fileName = URLEncoder.encode(name, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
        EasyExcel.write(response.getOutputStream(), AccidentExportDTO.class)
                .sheet("事故数据")
                .doWrite(result);
    }

    /**
     * 更新bug状态
     */
    public void updateBugStatus() {
        log.info("update bug status start");
        // 加锁避免并发执行数据不一致
        String accidentBugSyncLock = RedisKeyEnum.ACCIDENT_BUG_SYNC_LOCK.getValue();
        if (RedissonUtils.tryLock(accidentBugSyncLock)) {
            try {
                Date previousYear = DateUtil.offsetDay(new Date(), -365);
                List<Accident> accidentList = accidentManager.lambdaQuery().gt(Accident::getCreateTime, previousYear).isNotNull(Accident::getBugCode).list();
                //同步行云状态
                if (!CollectionUtils.isEmpty(accidentList)) {
                    for (Accident accident : accidentList) {
                        try {
                            BugDTO bugDTO = infrastructureBugApiManager.queryBug(accident.getBugCode());
                            if (bugDTO == null) {
                                continue;
                            }
                            bugDTO.setAccidentType(PreliminaryAccidentTypeEnum.getValueByName(bugDTO.getAccidentType()));
                            bugDTO.setAccidentLevel(PreliminaryAccidentLevelEnum.getValueByName(bugDTO.getAccidentLevel()));
                            accidentManager.lambdaUpdate().eq(Accident::getAccidentNo, accident.getAccidentNo())
                                    .set(Accident::getAccidentOwner, bugDTO.getAssignee()).set(Accident::getBugStatus, bugDTO.getStatus()).update();
                            accidentDetailManager.lambdaUpdate().eq(AccidentDetail::getAccidentNo, accident.getAccidentNo())
                                    .set(AccidentDetail::getTechnicalSupportAccidentLevel, bugDTO.getAccidentLevel()).set(AccidentDetail::getTechnicalSupportAccidentType, bugDTO.getAccidentType())
                                    .set(AccidentDetail::getSafetyGroupAccidentLevel, bugDTO.getAccidentLevel()).set(AccidentDetail::getSafetyGroupAccidentType, bugDTO.getAccidentType())
                                    .update();
                            //更新配置
                            accidentJdmePushManager.lambdaUpdate().eq(AccidentJdmePush::getAccidentNo, accident.getAccidentNo())
                                    .set(AccidentJdmePush::getLevel, bugDTO.getAccidentLevel()).set(AccidentJdmePush::getFollower, bugDTO.getAssignee()).update();
                            Thread.sleep(50);
                        } catch (Exception e) {
                            log.error("同步事故缺陷状态出错, accidentNo:{}", accident.getAccidentNo());
                        }
                    }
                    try {
                        log.info("删除事故看板事故跟进消息");
                        RedissonUtils.deleteObject(RedisKeyEnum.ACCIDENT_BOARD_FOLLOW_UP.getValue());
                    } catch (Exception e) {
                        log.error("删除事故看板事故跟进消息失败");
                    }
                }
                log.info("Finish update bug status task");
            } finally {
                RedissonUtils.unLock(accidentBugSyncLock);
            }
        }
    }
}