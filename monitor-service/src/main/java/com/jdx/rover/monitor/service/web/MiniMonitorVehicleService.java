/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.enums.StationUseCaseEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.StationVehicleBasicDTO;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorAlarmEventDTO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorStopDTO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorVehicleDTO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorVehicleDetailDTO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorVehicleSearchDTO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorVehicleStatusDTO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorVehicleTakeOverStatusDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehicleExceptionDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePncTaskAndTrafficLightDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.IssueCacheEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.MiniMonitorVehicleRealtimeStateEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.VehicleStateTypeEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.vehicle.online.VehicleOnlineEnum;
import com.jdx.rover.monitor.manager.abnormal.GuardianVehicleAbnormalManager;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.manager.vehicle.SystemStateManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleDadaStateManager;
import com.jdx.rover.monitor.po.GuardianVehicleAbnormal;
import com.jdx.rover.monitor.repository.redis.IssueCacheRepository;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSearchRecordRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.vo.MiniMonitorVehicleStateRequestVO;
import com.jdx.rover.schedule.api.domain.dto.monitor.DadaWorkStatusDTO;
import com.jdx.rover.schedule.api.domain.enums.StopTravelStatus;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a vehicle service.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
public class MiniMonitorVehicleService {

  @Autowired
  private MetadataVehicleApiManager metadataVehicleApiManager;
  @Autowired
  private MetadataStationApiManager metadataStationApiManager;
  @Autowired
  private IssueCacheRepository issueCacheRepository;

  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private MiniMonitorOrderService miniMonitorOrderService;

  @Autowired
  private GuardianVehicleAbnormalManager guardianVehicleAbnormalManager;

  @Autowired
  private VehicleSearchRecordRepository vehicleSearchRecordRepository;

  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;

  @Autowired
  private VehicleBasicRepository vehicleBasicRepository;

  @Autowired
  private VehicleDadaStateManager vehicleDadaStateManager;

  /**
   * <p>
   * Get all vehicle list by request.
   * </p>
   *
   */
  public HttpResult getVehicleList(MiniMonitorVehicleStateRequestVO vehicleStateRequestVo) {
    List<StationVehicleBasicDTO> vehicleMonitorListDtoList = metadataVehicleApiManager.listByStation(vehicleStateRequestVo.getStationId(), vehicleStateRequestVo.getVehicleBusinessType());
    if (CollectionUtil.isEmpty(vehicleMonitorListDtoList)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_VEHICLE_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_VEHICLE_ABSENT.getMessage());
    }
    Map<Integer, StationBasicDTO> stationMap = metadataStationApiManager.getStationById(vehicleStateRequestVo.getStationId());
    if (CollectionUtil.isEmpty(stationMap)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getMessage());
    }
    StationBasicDTO stationDetail = stationMap.get(vehicleStateRequestVo.getStationId());
    String selectedVehicleName = vehicleStateRequestVo.getVehicleName();
    Map<String, String> vehicleMap = vehicleMonitorListDtoList.stream().filter(
            vehicleDto -> StringUtils.isBlank(selectedVehicleName) || selectedVehicleName.equals(vehicleDto.getVehicleName())).collect(
            HashMap :: new, (map,vehicleDto)-> map.put(vehicleDto.getVehicleName(), vehicleDto.getVehicleBusinessType()), HashMap :: putAll);
    List<String> vehicleNameList = Lists.newArrayList(vehicleMap.keySet());
    Map<String, MonitorScheduleEntity> scheduleEntityMap = vehicleScheduleRepository.listMap(vehicleNameList);
    Map<String, VehicleRealtimeInfoDTO> realtimeInfoMap = vehicleRealtimeRepository.listMap(vehicleNameList);
    Map<String, IssueCacheEntity> issueCacheEntityMap = issueCacheRepository.getBatch(vehicleNameList);
    Map<String, VehicleAlarmDO> vehicleAlarmMap = vehicleAlarmRepository.listMap(vehicleNameList);
    Map<String, VehicleTakeOverEntity> vehicleTakeOverEntityMap = vehicleTakeOverRepository.listMap(vehicleNameList);
    Map<String, DadaWorkStatusDTO> dadaWorkStatusMap = new HashMap<>();
    if (StringUtils.equals( StationUseCaseEnum.TIMELY_DELIVERY.getValue(), stationDetail.getStationUseCase())) {
      dadaWorkStatusMap = vehicleDadaStateManager.getDadaWorkStatusByVehicleList(vehicleNameList);
    }
    List<MiniMonitorVehicleDTO> vehicleDtoList = new ArrayList<>();
    for (String vehicleName : vehicleNameList) {
      MonitorScheduleEntity scheduleEntity = scheduleEntityMap.get(vehicleName);
      String scheduleState = vehicleStateRequestVo.getScheduleState();
      if (StringUtils.isNotBlank(scheduleState)) {
        if (scheduleEntity == null && !StringUtils.equals(scheduleState, VehicleScheduleState.WAITING.getVehicleScheduleState())) {
          continue;
        }
        if (scheduleEntity != null && !Objects.equals(scheduleEntity.getScheduleState(), scheduleState)) {
          continue;
        }
      }
      if (!isBelongToStop(vehicleStateRequestVo.getStopId(), scheduleEntity)) {
        continue;
      }
      MiniMonitorVehicleDTO vehicleDto = new MiniMonitorVehicleDTO();
      vehicleDto.setName(vehicleName);
      vehicleDto.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
      vehicleDto.setVehicleBusinessType(vehicleMap.get(vehicleName));
      if (scheduleEntity != null) {
        vehicleDto.setScheduleState(scheduleEntity.getScheduleState());
        vehicleDto.setTaskType(scheduleEntity.getTaskType());
        vehicleDto.setLeftOrderNum(scheduleEntity.getTotalCollectOrderNum() + scheduleEntity.getTotalDeliveryOrderNum());
      }
      DadaWorkStatusDTO dadaWorkStatus = dadaWorkStatusMap.get(vehicleName);
      if (!Objects.isNull(dadaWorkStatus)) {
        vehicleDto.setDadaWorkState(dadaWorkStatus.getWorkStatus());
      }
      //车辆接管信息
      VehicleTakeOverEntity vehicleTakeOverEntity = vehicleTakeOverEntityMap.get(vehicleName);
      if (vehicleTakeOverEntity != null) {
        vehicleDto.setTakeOverUsername(vehicleTakeOverEntity.getUserName());
        vehicleDto.setTakeOverSource(vehicleTakeOverEntity.getCommandSource());
      }

      String vehicleOnline = vehicleStateRequestVo.getVehicleOnline();
      VehicleRealtimeInfoDTO realtimeInfo = realtimeInfoMap.get(vehicleName);
      if (Objects.equals(VehicleOnlineEnum.ONLINE.getOnline(), vehicleOnline)) {
        // 在线筛选下,离线和失联车辆不展示
        if (realtimeInfo == null || SystemStateManager.isOfflineOrLost(realtimeInfo.getSystemState())) {
          continue;
        }
      } else if (realtimeInfo == null) {
        // 全部筛选下,实时信息不存直接返回
        vehicleDtoList.add(vehicleDto);
        continue;
      }

      MapPointBO mapPoint = TransformUtility.toGCJ02Point(realtimeInfo.getLat(), realtimeInfo.getLon());
      if (mapPoint != null) {
        vehicleDto.setLat(mapPoint.getLatitude());
        vehicleDto.setLon(mapPoint.getLongitude());
      }
      IssueCacheEntity issueCacheEntity = issueCacheEntityMap.get(vehicleName);
      VehicleAlarmDO vehicleAlarm = vehicleAlarmMap.get(vehicleName);
      MiniMonitorVehicleStatusDTO vehicleStatusDto = buildVehicleStatus(stationDetail, realtimeInfo, scheduleEntity, issueCacheEntity, vehicleAlarm);
      vehicleDto.setStatus(vehicleStatusDto);
      vehicleDtoList.add(vehicleDto);
    }
    return HttpResult.success(vehicleDtoList);
  }

  /**
   * <p>
   * Get vehicle detail info include schedule and realtime state.
   * </p>
   */
  public HttpResult getVehicleDetailInfo(String vehicleName, Boolean errorFlag) {
    MiniMonitorVehicleDetailDTO vehicleDetailDto = new MiniMonitorVehicleDetailDTO();
    vehicleDetailDto.setName(vehicleName);
    vehicleDetailDto.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
    //设置错误信息
    if (errorFlag) {
      List<GuardianVehicleAbnormal> guardianVehicleAbnormalList = guardianVehicleAbnormalManager.listTodayAbnormal(vehicleName);
      List<SingleVehicleExceptionDTO> list = new ArrayList<>();
      for (GuardianVehicleAbnormal tmp : guardianVehicleAbnormalList) {
        SingleVehicleExceptionDTO singleVehicleExceptionDTO = new SingleVehicleExceptionDTO();
        singleVehicleExceptionDTO.setReportTime(tmp.getStartTime());
        singleVehicleExceptionDTO.setErrorLevel(tmp.getErrorLevel());
        singleVehicleExceptionDTO.setErrorCode(tmp.getErrorCode());
        String errorMessage = tmp.getModuleName() + "," + tmp.getErrorMessage();
        singleVehicleExceptionDTO.setErrorMessage(errorMessage);
        singleVehicleExceptionDTO.setTranslateMessage(tmp.getTranslateMessage());
        list.add(singleVehicleExceptionDTO);
      }
      vehicleDetailDto.setVehicleExceptionList(list);
    }
    Map<String, DadaWorkStatusDTO> dadaWorkStatusMap = new HashMap<>();
    StationBasicDTO stationBasicDto = metadataVehicleApiManager.getStationInfoByVehicle(vehicleName);
    if (!Objects.isNull(stationBasicDto) &&
            StringUtils.equals( StationUseCaseEnum.TIMELY_DELIVERY.getValue(), stationBasicDto.getStationUseCase())) {
      dadaWorkStatusMap = vehicleDadaStateManager.getDadaWorkStatusByVehicleList(Lists.newArrayList(vehicleName));
      vehicleDetailDto.setDadaWorkState(dadaWorkStatusMap.get(vehicleName).getWorkStatus());
    }
    MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.get(vehicleName);
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
    IssueCacheEntity issueCacheEntity = issueCacheRepository.getByKey(vehicleName);
    VehicleAlarmDO vehicleAlarm = vehicleAlarmRepository.getVehicleAlarmDTO(vehicleName);
    String redisKey = RedisKeyEnum.TRAFFIC_LIGHT_SET_VEHICLE.getValue() + vehicleName;
    SingleVehiclePncTaskAndTrafficLightDTO dto = RedissonUtils.getObject(redisKey);
    vehicleDetailDto.setStatus(buildVehicleStatus(new StationBasicDTO(), vehicleRealtimeInfoDTO, scheduleEntity, issueCacheEntity, vehicleAlarm));
    com.google.common.base.Optional<VehicleTakeOverEntity> takeOverOp = vehicleTakeOverRepository.getByKey(vehicleName);
    vehicleDetailDto.setPncTaskType(Optional.ofNullable(dto).map(pnc -> pnc.getPncTaskType()).orElse(null));
    if (takeOverOp.isPresent()) {
      vehicleDetailDto.setTakeOverUsername(takeOverOp.get().getUserName());
      vehicleDetailDto.setTakeOverSource(takeOverOp.get().getCommandSource());
    }
    if (scheduleEntity == null) {
      return HttpResult.success(vehicleDetailDto);
    }
    vehicleDetailDto.setScheduleState(scheduleEntity.getScheduleState());
    vehicleDetailDto.setTaskType(scheduleEntity.getTaskType());
    if (scheduleEntity.getEstDepartureTime() != null) {
      vehicleDetailDto.setScheduleStartTime(scheduleEntity.getEstDepartureTime().getTime());
    }
    if (scheduleEntity.getScheduleNo() == null) {
      return HttpResult.success(vehicleDetailDto);
    }
    List<MonitorScheduleStopEntity> stopEntityList = scheduleEntity.getStop();
    vehicleDetailDto.setStop(stopEntityList.stream().map(entity -> toStopDto(entity)).collect(Collectors.toList()));
    vehicleDetailDto.setOrder(miniMonitorOrderService.getOrderInfoDtoList(scheduleEntity.getScheduleNo(), scheduleEntity.getTaskType()));
    return HttpResult.success(vehicleDetailDto);
  }

  /**
   * <p>
   * 获取车辆实时类型的状态
   * </p>
   */
  public HttpResult getVehicleStateInfoByType(String vehicleName, String stateType) {
    if (StringUtils.equals(VehicleStateTypeEnum.VEHICLE_REALTIME_STATE.getValue(), stateType)) {
      MiniMonitorVehicleStatusDTO vehicleStatusDto = new MiniMonitorVehicleStatusDTO();
      VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
      if (!Objects.isNull(vehicleRealtimeInfoDto)) {
        vehicleStatusDto.setPower(vehicleRealtimeInfoDto.getPower());
        vehicleStatusDto.setSpeed(vehicleRealtimeInfoDto.getSpeed());
        vehicleStatusDto.setSystemState(vehicleRealtimeInfoDto.getSystemState());
        vehicleStatusDto.setVehicleState(vehicleRealtimeInfoDto.getVehicleState());
      }
      return HttpResult.success(vehicleStatusDto);
    } else if (StringUtils.equals(VehicleStateTypeEnum.VEHICLE_CONTROL_STATE.getValue(), stateType)) {
      com.google.common.base.Optional<VehicleTakeOverEntity> takeOverOp = vehicleTakeOverRepository.getByKey(vehicleName);
      MiniMonitorVehicleTakeOverStatusDTO takeOverStatusDto = new MiniMonitorVehicleTakeOverStatusDTO();
      if (takeOverOp.isPresent()) {
        takeOverStatusDto.setTakeOverSource(takeOverOp.get().getCommandSource());
        takeOverStatusDto.setTakeOverUserName(takeOverOp.get().getUserName());
      }
      return HttpResult.success(takeOverStatusDto);
    }
    return HttpResult.success();
  }

  private MiniMonitorVehicleStatusDTO buildVehicleStatus(StationBasicDTO stationDetail,
          VehicleRealtimeInfoDTO vehicleRealtimeInfoDto, MonitorScheduleEntity scheduleEntity, IssueCacheEntity issueCacheEntity, VehicleAlarmDO vehicleAlarm) {
    MiniMonitorVehicleStatusDTO vehicleStatusDto = new MiniMonitorVehicleStatusDTO();
    vehicleStatusDto.setRealtimeState(MiniMonitorVehicleRealtimeStateEnum.NORMAL.getValue());
    if (scheduleEntity != null) {
      vehicleStatusDto.setMileage(scheduleEntity.getGlobalMileage());
      vehicleStatusDto.setFinishedMileage(getArrivedMileage(scheduleEntity, vehicleRealtimeInfoDto));
    }
    if (issueCacheEntity != null) {
      vehicleStatusDto.setIssueNo(issueCacheEntity.getIssueNo());
      vehicleStatusDto.setIssueState(issueCacheEntity.getIssueState());
    }
    if (vehicleRealtimeInfoDto != null) {
      vehicleStatusDto.setPower(vehicleRealtimeInfoDto.getPower());
      vehicleStatusDto.setSpeed(vehicleRealtimeInfoDto.getSpeed());
      vehicleStatusDto.setSystemState(vehicleRealtimeInfoDto.getSystemState());
      vehicleStatusDto.setVehicleState(vehicleRealtimeInfoDto.getVehicleState());
    }
    if (!Objects.isNull(vehicleAlarm) && CollectionUtil.isNotEmpty(vehicleAlarm.getAlarmEventList())) {
      List<MiniMonitorAlarmEventDTO> miniMonitorAlarmEventDtoList = vehicleAlarm.getAlarmEventList().stream().map(temp -> {
        MiniMonitorAlarmEventDTO alarmEventDto = new MiniMonitorAlarmEventDTO();
        alarmEventDto.setType(temp.getType());
        alarmEventDto.setStartTimestamp(temp.getReportTime());
        return alarmEventDto;
      }).collect(Collectors.toList());
      vehicleStatusDto.setAlarmEvent(miniMonitorAlarmEventDtoList);
      vehicleStatusDto.setRealtimeState(MiniMonitorVehicleRealtimeStateEnum.ALARM.getValue());
    } else if (!Objects.isNull(stationDetail) && !Objects.isNull(stationDetail.getLatitude()) &&
            !Objects.isNull(stationDetail.getLongitude()) && !Objects.isNull(vehicleRealtimeInfoDto)){
        double distance = TransformUtility.calculateDistance(stationDetail.getLatitude(),
                stationDetail.getLongitude(), vehicleRealtimeInfoDto.getLat(), vehicleRealtimeInfoDto.getLon());
        vehicleStatusDto.setRealtimeState(Objects.isNull(scheduleEntity) && distance < 0.01 ?
                        MiniMonitorVehicleRealtimeStateEnum.NEAR_STATION.getValue() : MiniMonitorVehicleRealtimeStateEnum.NORMAL.getValue());
    }
    return vehicleStatusDto;
  }

  private boolean isBelongToStop(Integer stopId, MonitorScheduleEntity scheduleEntity) {
    if (stopId == null) {
      return true;
    }
    if (scheduleEntity == null || CollectionUtils.isEmpty(scheduleEntity.getStop())) {
      return false;
    }
    return scheduleEntity.getStop().stream().anyMatch(scheduleStop -> Objects.equals(scheduleStop.getId(), stopId) && Objects
            .equals(StopTravelStatus.STAY.getTravelStatus(), scheduleStop.getTravelStatus()));
  }

  private MiniMonitorStopDTO toStopDto(MonitorScheduleStopEntity scheduleStop) {
    MiniMonitorStopDTO stop = new MiniMonitorStopDTO();
    stop.setId(scheduleStop.getId());
    stop.setName(scheduleStop.getName());
    stop.setStopAction(scheduleStop.getStopAction());
    stop.setTravelStatus(scheduleStop.getTravelStatus());
    stop.setWaitingTime(scheduleStop.getWaitingTime());
    if (scheduleStop.getLat() != null && scheduleStop.getLon() != null) {
      MapPointBO mapPoint =
              TransformUtility.toGCJ02Point(scheduleStop.getLat(), scheduleStop.getLon());
      if (mapPoint != null) {
        stop.setLat(mapPoint.getLatitude());
        stop.setLon(mapPoint.getLongitude());
      }
    }
    if (scheduleStop.getArrivedTime() != null) {
      stop.setArrivedTimestamp(scheduleStop.getArrivedTime().getTime());
    }
    if (scheduleStop.getDepartTime() != null) {
      stop.setDepartTimestamp(scheduleStop.getDepartTime().getTime());
    }
    if (scheduleStop.getEstDepartTime() != null) {
      stop.setEstDepartTimestamp(scheduleStop.getEstDepartTime().getTime());
    }
    if (scheduleStop.getStartTime() != null) {
      stop.setStartTimestamp(scheduleStop.getStartTime().getTime());
    }
    return stop;
  }

  /**
   * 当前调度完成里程
   *
   * @param monitorScheduleEntity
   * @param realtime
   * @return
   */
  private Double getArrivedMileage(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
    Double finishedMileage = Optional.ofNullable(monitorScheduleEntity.getFinishedMileage()).orElse(0.0);
    if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(),
            VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(),
            VehicleScheduleState.RETURN.getVehicleScheduleState())) {
      Double currentStopFinishedMileage;
      if (realtime == null || realtime.getCurrentStopFinishedMileage() == null) {
        currentStopFinishedMileage = 0.0;
      } else {
        currentStopFinishedMileage = realtime.getCurrentStopFinishedMileage();
      }
      finishedMileage += currentStopFinishedMileage;
    }
    return finishedMileage;
  }

  /**
   * 添加车辆搜索记录
   * @param vehicleName
   * @return
   */
  public HttpResult addSearchRecord(String vehicleName) {
    String username = LoginUtils.getUsername("default");
    vehicleSearchRecordRepository.add(username, vehicleName);
    return HttpResult.success();
  }

  /**
   * 获取车辆搜索记录
   * @return
   */
  public HttpResult getSearchRecord() {
    String username = LoginUtils.getUsername("default");
    List<String> vehicleNameList = vehicleSearchRecordRepository.get(username).stream().limit(10).collect(Collectors.toList());
    Map<String, VehicleBasicDTO> stringVehicleBasicDTOMap = vehicleBasicRepository.listMap(vehicleNameList);
    List<MiniMonitorVehicleSearchDTO> miniMonitorVehicleSearchDTOS = new ArrayList<>();
    for (String vehicleName : vehicleNameList) {
      MiniMonitorVehicleSearchDTO miniMonitorVehicleSearchDTO = new MiniMonitorVehicleSearchDTO();
      VehicleBasicDTO vehicleBasicDTO = stringVehicleBasicDTOMap.get(vehicleName);
      if (vehicleBasicDTO != null && vehicleBasicDTO.getStationId() != null) {
        miniMonitorVehicleSearchDTO.setVehicleName(vehicleName);
        miniMonitorVehicleSearchDTO.setStationId(vehicleBasicDTO.getStationId());
        miniMonitorVehicleSearchDTOS.add(miniMonitorVehicleSearchDTO);
      }
    }
    return HttpResult.success(miniMonitorVehicleSearchDTOS);
  }

  /**
   * 获取车辆搜索记录
   * @return
   */
  public HttpResult getVehicleSearchRecord() {
    String username = LoginUtils.getUsername("default");
    List<String> vehicleNameList = vehicleSearchRecordRepository.get(username).stream().limit(10).collect(Collectors.toList());
    return HttpResult.success(vehicleNameList);
  }

  /**
   * 清空用户车辆搜索记录
   * @return
   */
  public HttpResult clearVehicleSearchRecord() {
    String username = LoginUtils.getUsername("default");
    vehicleSearchRecordRepository.removeAll(username);
    return HttpResult.success();
  }



}
