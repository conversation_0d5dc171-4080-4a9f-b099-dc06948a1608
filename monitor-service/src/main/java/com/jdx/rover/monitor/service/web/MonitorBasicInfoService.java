/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.user.UserMonitorStationTreeDto;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleMonitorInfoDTO;
import com.jdx.rover.metadata.domain.vo.user.UserMonitorStationTreeVo;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorBasicDataInfoUnderUseDTO;
import com.jdx.rover.monitor.dto.MonitorUserCityBasicInfoDTO;
import com.jdx.rover.monitor.dto.MonitorUserStationBasicInfoDTO;
import com.jdx.rover.monitor.dto.MonitorUserVehiceBasicInfoDTO;
import com.jdx.rover.monitor.dto.MonitorUserVehicleStatisticInfoDTO;
import com.jdx.rover.monitor.dto.MonitorVehiceBasicInfoDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.UserStopInfoEntity;
import com.jdx.rover.monitor.enums.UserMetaDataEnum;
import com.jdx.rover.monitor.enums.VehicleRealtimeStateEnum;
import com.jdx.rover.monitor.manager.cockpit.CockpitManager;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.stop.MetadataStopApiManager;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.manager.utils.vehicle.SupplierUtils;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleOwnerUseCaseManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleVersionManager;
import com.jdx.rover.monitor.repository.redis.UserAttentionRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeStateRepository;
import com.jdx.rover.monitor.repository.redis.metadata.CockpitVehicleNameRepository;
import com.jdx.rover.monitor.vo.CockpitVehicleBasicInfoRequestVO;
import com.jdx.rover.monitor.vo.MonitorUserMetaDataInfoRequestVO;
import com.jdx.rover.monitor.vo.MonitorUserVehicleBasicInfoRequestVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a basic infomation service with vehicle, user-vehicle and jira
 * function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
@Slf4j
public class MonitorBasicInfoService {

    /**
     * <p>
     * The station metadata manager.
     * </p>
     */
    @Autowired
    private MetadataStationApiManager metadataStationApiManager;

    @Autowired
    private CockpitManager cockpitManager;

    /**
     * <p>
     * The stop metadata manager.
     * </p>
     */
    @Autowired
    private MetadataStopApiManager metadataStopApiManager;

    /**
     * <p>
     * The monitor vehicle manager.
     * </p>
     */
    @Autowired
    private VehicleManager vehicleManager;

    /**
     * <p>
     * The monitor version manager.
     * </p>
     */
    @Autowired
    private VehicleVersionManager vehicleVersionManager;

    /**
     * <p>
     * The monitor vehicle realtime repo.
     * </p>
     */
    @Autowired
    private VehicleRealtimeStateRepository vehicleRealtimeStateRepository;

  /**
   * 驾舱车辆缓存
   */
  @Autowired
  private CockpitVehicleNameRepository cockpitVehicleNameRepository;

    /**
     * <p>
     * The monitor vehicle usecase manager.
     * </p>
     */
    @Autowired
    private VehicleOwnerUseCaseManager vehicleOwnerUseCaseManager;

    private static final String businessType = "business";

    private static final String useCaseType = "usecase";

  /**
   * <p>
   * The monitor vehicle realtime repo.
   * </p>
   */
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  /**
   * <p>
   * The vehicle metadata manager.
   * </p>
   */
  @Autowired
  private MetadataVehicleApiManager metadataVehicleApiManager;

  @Autowired
  private UserAttentionRepository userAttentionRepository;

  /**
     * <p>
     * find all basic infomation include city,station,stop,vehicle from user.
     * </p>
     *
     * @return The user basic info include its city,station,stop,vehicle info.
     */
    public List<MonitorUserCityBasicInfoDTO> getUserVehicleBasicInfo(MonitorUserVehicleBasicInfoRequestVO basicInfoRequestVo) {
        String userName = UserUtils.getAndCheckLoginUser();
        if (StringUtils.isBlank(userName)) {
            return new ArrayList<>();
        }
        com.jdx.rover.metadata.domain.vo.user.UserMonitorStationTreeVo stationTreeVo = new com.jdx.rover.metadata.domain.vo.user.UserMonitorStationTreeVo();
        stationTreeVo.setUsername(userName);
        if (basicInfoRequestVo != null) {
            stationTreeVo.setBusinessType(basicInfoRequestVo.getBusinessType());
            stationTreeVo.setOwnerUseCaseList(basicInfoRequestVo.getUseCaseList());
        }
        List<com.jdx.rover.metadata.domain.dto.user.UserMonitorStationTreeDto> userStationTreeDto = metadataStationApiManager.getStationTreeByUser(stationTreeVo);
        if (CollectionUtils.isEmpty(userStationTreeDto)) {
            return new ArrayList<>();
        }
        Map<Integer, MonitorUserCityBasicInfoDTO> cityDtoMap = new HashMap<>();
        List<String> vehicleName = Lists.newArrayList();
        userStationTreeDto.stream().forEach(station -> {
            vehicleName.addAll(station.getVehicleInfoList().stream().map(VehicleMonitorInfoDTO::getVehicleName).collect(Collectors.toList()));
            MonitorUserCityBasicInfoDTO cityBasicInfo = Optional.ofNullable(cityDtoMap.get(station.getCityId())).orElse(new MonitorUserCityBasicInfoDTO());
            cityBasicInfo.setId(station.getCityId());
            cityBasicInfo.setName(station.getCityName());
            cityBasicInfo.setTotal(cityBasicInfo.getTotal() + station.getVehicleInfoList().size());
            cityDtoMap.put(station.getCityId(), cityBasicInfo);
        });
        Map<String, String> vehicleRealtimeInfoDtoMap = vehicleRealtimeStateRepository.listMap(vehicleName);
        Set<String> attentionVehicleNameSet = userAttentionRepository.get(userName);
        userStationTreeDto.stream().forEach(station -> {
            MonitorUserStationBasicInfoDTO stationDto = new MonitorUserStationBasicInfoDTO();
            stationDto.setName(station.getStationName());
            if (!Objects.isNull(station.getLat()) && !Objects.isNull(station.getLon())) {
                MapPointBO mapPoint = TransformUtility.toGCJ02Point(station.getLat(), station.getLon());
                if (!Objects.isNull(mapPoint)) {
                    stationDto.setLat(mapPoint.getLatitude());
                    stationDto.setLon(mapPoint.getLongitude());
                }
            }
            stationDto.setId(station.getStationId());
            stationDto.setVehicle(station.getVehicleInfoList().stream().map(vehicle -> {
                MonitorUserVehiceBasicInfoDTO vehicleDto = new MonitorUserVehiceBasicInfoDTO();
                vehicleDto.setName(vehicle.getVehicleName());
                vehicleDto.setTagList(vehicle.getTagList());
                vehicleDto.setVehicleState(Optional.ofNullable(vehicleRealtimeInfoDtoMap.get(vehicle.getVehicleName())).orElse(VehicleRealtimeStateEnum.OFFLINE.getName()));
                vehicleDto.setAttentionState(attentionVehicleNameSet.contains(vehicle.getVehicleName()));
                return vehicleDto;
            }).collect(Collectors.toList()));
            stationDto.setTotal(stationDto.getVehicle().size());
            cityDtoMap.get(station.getCityId()).getStation().add(stationDto);
        });
        return Lists.newArrayList(cityDtoMap.values());
    }

  public List<MonitorUserCityBasicInfoDTO> getCockpitVehicleStationTree(CockpitVehicleBasicInfoRequestVO basicInfoRequestVo) {
    List<MonitorUserCityBasicInfoDTO> cityDtoList = new ArrayList<>();
    Set<String> vehicleNameList = cockpitVehicleNameRepository.get(basicInfoRequestVo.getCockpitNumber());
    if (CollectionUtils.isEmpty(vehicleNameList)) {
      return new ArrayList<>();
    }
    List<VehicleBasicDTO> vehicleBasicList = vehicleManager.listBasicByName(Lists.newArrayList(vehicleNameList));
    Map<String, String> vehicleRealtimeInfoDtoMap = vehicleRealtimeStateRepository.listMap(Lists.newArrayList(vehicleNameList));
    Function<VehicleBasicDTO, String> cityCompositeKey = basicInfo ->
            new StringBuilder().append(basicInfo.getCityId()).append(";").append(basicInfo.getCityName()).toString();
    Function<VehicleBasicDTO, String> stationCompositeKey = basicInfo ->
            new StringBuilder().append(basicInfo.getStationId()).append(";").append(basicInfo.getStationName()).toString();
    Map<String, Map<String, List<VehicleBasicDTO>>> stationMap = vehicleBasicList.stream().filter(basicInfo ->
            StringUtils.isBlank(basicInfoRequestVo.getBusinessType()) ||
                    StringUtils.equals(basicInfo.getBusinessType(), basicInfoRequestVo.getBusinessType())).filter(basicInfo ->
            CollectionUtils.isEmpty(basicInfoRequestVo.getUseCaseList()) || basicInfoRequestVo.getUseCaseList().contains(basicInfo.getOwnerUseCase())
    ).filter(basicInfo -> !Objects.isNull(basicInfo.getStationId()) && !Objects.isNull(basicInfo.getCityId()))
            .collect(Collectors.groupingBy(cityCompositeKey, Collectors.groupingBy(stationCompositeKey)));
    stationMap.entrySet().stream().forEach(cityMapEntry -> {
      MonitorUserCityBasicInfoDTO cityBasicInfo = new MonitorUserCityBasicInfoDTO();
      String[] cityInfo = cityMapEntry.getKey().split(";");
      if (ArrayUtils.isEmpty(cityInfo)) {
          return;
      }
      try {
          cityBasicInfo.setId(Integer.valueOf(cityInfo[0]));
          cityBasicInfo.setName(cityInfo[1]);
          AtomicInteger total = new AtomicInteger();
          Map<String, List<VehicleBasicDTO>> stationInfoList = cityMapEntry.getValue();
          List<MonitorUserStationBasicInfoDTO> stationBasicInfoDTOList = stationInfoList.entrySet().stream().map(stationEntry -> {
              MonitorUserStationBasicInfoDTO stationBasicInfo = new MonitorUserStationBasicInfoDTO();
              String[] stationInfo = stationEntry.getKey().split(";");
              if (ArrayUtils.isEmpty(stationInfo)) {
                  return stationBasicInfo;
              }
              stationBasicInfo.setId(Integer.valueOf(stationInfo[0]));
              stationBasicInfo.setName(stationInfo[1]);
              stationBasicInfo.setVehicle(stationEntry.getValue().stream().map(vehicle -> {
                  MonitorUserVehiceBasicInfoDTO vehicleDto = new MonitorUserVehiceBasicInfoDTO();
                  vehicleDto.setName(vehicle.getName());
                  vehicleDto.setTagList(vehicle.getTagList());
                  vehicleDto.setVehicleState(Optional.ofNullable(vehicleRealtimeInfoDtoMap.get(vehicle.getName()))
                          .orElse(VehicleRealtimeStateEnum.OFFLINE.getValue()));
                  return vehicleDto;
              }).collect(Collectors.toList()));
              stationBasicInfo.setTotal(stationBasicInfo.getVehicle().size());
              total.addAndGet(stationBasicInfo.getTotal());
              return stationBasicInfo;
          }).collect(Collectors.toList());
          cityBasicInfo.setStation(stationBasicInfoDTOList);
          cityBasicInfo.setTotal(total.get());
          cityDtoList.add(cityBasicInfo);
      } catch (Exception e) {
          log.error("车辆{}统计数据异常", cityInfo);
      }
    });
    return cityDtoList;
  }

  /**
   * <p>
   * Find vehicle basic infomation.
   * </p>
   */
  public MonitorVehiceBasicInfoDTO getVehicleBasicInfo(String vehicleName) {
    String userName = UserUtils.getAndCheckLoginUser();
    MonitorVehiceBasicInfoDTO basicInfoDto = new MonitorVehiceBasicInfoDTO();
    VehicleBasicDTO vehicleInfo = metadataVehicleApiManager.getByName(vehicleName);

    if (vehicleInfo == null) {
      return basicInfoDto;
    }

    basicInfoDto.setVehicleName(vehicleInfo.getName());
    basicInfoDto.setBusinessType(vehicleInfo.getBusinessType());
    basicInfoDto.setTagList(vehicleInfo.getTagList());
    Map<String, String> versionMap = vehicleVersionManager.getVehicleVersion(vehicleName);
    basicInfoDto.setVehicleVersion(versionMap.get(OtaMoudleEnum.ROVER.getName()));
    basicInfoDto.setVideoVersion(versionMap.get(OtaMoudleEnum.VIDEO.getName()));
    basicInfoDto.setAndroidVersion(versionMap.get(OtaMoudleEnum.ANDROID.getName()));
    basicInfoDto.setMapVersion(versionMap.get(OtaMoudleEnum.MAP.getName()));

    //车辆位置信息封装
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
    if (!Objects.isNull(vehicleRealtimeInfoDTO) && !Objects.isNull(vehicleRealtimeInfoDTO.getLat())
            && !Objects.isNull(vehicleRealtimeInfoDTO.getLon())){
      MapPointBO gcj02Point = TransformUtility.toGCJ02Point(vehicleRealtimeInfoDTO.getLat(), vehicleRealtimeInfoDTO.getLon());
      if (gcj02Point != null){
        basicInfoDto.setVehicleLatitude(gcj02Point.getLatitude());
        basicInfoDto.setVehicleLongitude(gcj02Point.getLongitude());
      }
    }
    //车型名称
    basicInfoDto.setVehicleTypeName(metadataVehicleApiManager.getVehicleTypeName(vehicleInfo.getVehicleTypeId()));
    //车辆是否被关注
    basicInfoDto.setVehicleAttentionState(userAttentionRepository.isContainVehicleName(userName, vehicleName));

    basicInfoDto.setSupplier(vehicleInfo.getSupplier());
    //处理站点信息
    if (vehicleInfo.getStationId() != null) {
      StationBasicDTO station = metadataStationApiManager.getById(vehicleInfo.getStationId());
      if (station != null) {
        MapPointBO stopMapPoint = TransformUtility.toGCJ02Point(station.getLatitude(), station.getLongitude());
        if (stopMapPoint != null ){
            station.setLatitude(stopMapPoint.getLatitude());
            station.setLongitude(stopMapPoint.getLongitude());
        }
        basicInfoDto.setStation(station);
      }
    }
    return basicInfoDto;
  }

    public List getUserMetaDataList(String dataType, MonitorUserMetaDataInfoRequestVO metaDataInfoRequestVo) {
        String userName = UserUtils.getAndCheckLoginUser();
        log.info("获取用户{}权限下基础数据类型为{}，过滤字段{}数据。", userName, dataType, JsonUtils.writeValueAsString(metaDataInfoRequestVo));
        boolean isFilter = Objects.isNull(metaDataInfoRequestVo) || StringUtils.isBlank(metaDataInfoRequestVo.getName());
        if (StringUtils.equals(dataType, UserMetaDataEnum.VEHICLE.getType())) {
            List<VehicleBasicDTO> vehicleInUserList = vehicleManager.listVehicleByUserName(userName);
            return vehicleInUserList.stream().filter(vehicle ->
                    isFilter || StringUtils.contains(vehicle.getName(),metaDataInfoRequestVo.getName())).map(vehicle -> {
                MonitorBasicDataInfoUnderUseDTO vehicleDto = new MonitorBasicDataInfoUnderUseDTO();
                vehicleDto.setName(vehicle.getName());
                return vehicleDto;
            }).collect(Collectors.toList());
        } else if (StringUtils.equals(dataType, UserMetaDataEnum.STOP.getType())) {
                List<UserStopInfoEntity> userStopInfoList = metadataStopApiManager.getStopListByUser(userName);
                return userStopInfoList.stream().filter(stop ->
                        isFilter || StringUtils.contains(stop.getName(),metaDataInfoRequestVo.getName())).map(stop -> {
                    MonitorBasicDataInfoUnderUseDTO stopDto = new MonitorBasicDataInfoUnderUseDTO();
                    stopDto.setId(stop.getId());
                    stopDto.setName(stop.getName());
                    return stopDto;
                }).limit(100).collect(Collectors.toList());
        } else if (StringUtils.equals(dataType, UserMetaDataEnum.STATION.getType())) {
            List<StationBasicDTO> userStationList =  metadataStationApiManager.getStationListByUser(userName);
            List<StationBasicDTO> filterStationList = userStationList.stream().filter(station ->
                    isFilter || StringUtils.contains(station.getStationName(),metaDataInfoRequestVo.getName())).collect(Collectors.toList());
           return buildCityStationUnderUserInfo(filterStationList);
        }
        return new ArrayList<>();
    }

    private List<MonitorBasicDataInfoUnderUseDTO> buildCityStationUnderUserInfo(List<StationBasicDTO> userStationList) {
        Map<Integer, MonitorBasicDataInfoUnderUseDTO> cityDtoMap = new HashMap<>();
        userStationList.stream().forEach(station -> {
            MonitorBasicDataInfoUnderUseDTO stationDto = new MonitorBasicDataInfoUnderUseDTO();
            stationDto.setName(station.getStationName());
            stationDto.setId(station.getStationId());
            Integer cityId = station.getAddressInfo().getCityId();
            if (Objects.isNull(cityDtoMap.get(cityId))) {
                MonitorBasicDataInfoUnderUseDTO cityBasicInfo = new MonitorBasicDataInfoUnderUseDTO();
                cityBasicInfo.setId(station.getAddressInfo().getCityId());
                cityBasicInfo.setName(station.getAddressInfo().getCityName());
                cityBasicInfo.getChild().add(stationDto);
                cityDtoMap.put(cityId, cityBasicInfo);
            } else {
                cityDtoMap.get(cityId).getChild().add(stationDto);
            }
        });
        return Lists.newArrayList(cityDtoMap.values());
    }

    /**
     * <p>
     * Get city station and vehicle basic info under user.
     * </p>
     */
    public List<MonitorBasicDataInfoUnderUseDTO> getUserMetaData(String dataType, String userName) {
        List<MonitorBasicDataInfoUnderUseDTO> cityDataList = new ArrayList<>();
        if (UserMetaDataEnum.VEHICLE.getType().equals(dataType)) {
            List<VehicleBasicDTO> vehicleInUserList = vehicleManager.listVehicleByUserName(userName);
            return vehicleInUserList.stream().filter(vehicle -> SupplierUtils.isJdVehicle(vehicle.getSupplier())).map(vehicle -> {
                MonitorBasicDataInfoUnderUseDTO vehicleDto = new MonitorBasicDataInfoUnderUseDTO();
                vehicleDto.setName(vehicle.getName());
                return vehicleDto;
            }).collect(Collectors.toList());
        }
        UserMonitorStationTreeVo stationTreeVo = new UserMonitorStationTreeVo();
        stationTreeVo.setUsername(userName);
        List<UserMonitorStationTreeDto> userStationTreeDto = metadataStationApiManager.getStationTreeByUser(stationTreeVo);
        if (CollectionUtils.isEmpty(userStationTreeDto)) {
            return new ArrayList<>();
        }
        Map<Integer, MonitorBasicDataInfoUnderUseDTO> cityMap = new HashMap<>();
        List<String> vehicleNames = userStationTreeDto.stream()
                .map(UserMonitorStationTreeDto::getVehicleInfoList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(VehicleMonitorInfoDTO::getVehicleName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<VehicleBasicDTO> vehicleBasicList = Optional.ofNullable(vehicleManager.listBasicByName(vehicleNames)).orElse(new ArrayList<>());
        Map<String, VehicleBasicDTO> mapData = vehicleBasicList.stream().collect(
                        Collectors.toMap(VehicleBasicDTO::getName, Function.identity(), (existing, replacement) -> existing));

        userStationTreeDto.stream().forEach(station -> {
            MonitorBasicDataInfoUnderUseDTO stationBasicInfo = new MonitorBasicDataInfoUnderUseDTO();
            stationBasicInfo.setId(station.getStationId());
            stationBasicInfo.setName(station.getStationName());
            if (station.getVehicleInfoList() != null) {
                stationBasicInfo.setChild(station.getVehicleInfoList().stream().filter(vehicle ->
                        SupplierUtils.isJdVehicle(mapData.getOrDefault(vehicle.getVehicleName(), new VehicleBasicDTO()).getSupplier())).map(vehicle -> {
                    MonitorBasicDataInfoUnderUseDTO vehicleUnderUserDto = new MonitorBasicDataInfoUnderUseDTO();
                    vehicleUnderUserDto.setName(vehicle.getVehicleName());
                    return vehicleUnderUserDto;
                }).collect(Collectors.toList()));
            }
            if (cityMap.get(station.getCityId()) == null) {
                MonitorBasicDataInfoUnderUseDTO cityBasicInfo = new MonitorBasicDataInfoUnderUseDTO();
                cityBasicInfo.setId(station.getCityId());
                cityBasicInfo.setName(station.getCityName());
                cityBasicInfo.getChild().add(stationBasicInfo);
                cityMap.put(station.getCityId(), cityBasicInfo);
            } else {
                cityMap.get(station.getCityId()).getChild().add(stationBasicInfo);
            }
        });
        for (Map.Entry<Integer, MonitorBasicDataInfoUnderUseDTO> entry : cityMap.entrySet()) {
            MonitorBasicDataInfoUnderUseDTO basicDataInfo = new MonitorBasicDataInfoUnderUseDTO();
            basicDataInfo.setId(entry.getKey());
            basicDataInfo.setName(entry.getValue().getName());
            basicDataInfo.setChild(Lists.newArrayList(entry.getValue()));
            cityDataList.add(basicDataInfo);
        }
        return new ArrayList<>(cityMap.values());
    }

    public List<MonitorUserVehicleStatisticInfoDTO> getUserVehicleTypeSize(String selectedType) {
        Map<String, Integer> result;
        String userName = UserUtils.getAndCheckLoginUser();
        List<MonitorUserVehicleStatisticInfoDTO> resultList = new ArrayList<>();
        if (StringUtils.equals(selectedType, useCaseType)) {
            result = vehicleOwnerUseCaseManager.getOwnerUseCaseCountByUsername(userName);
            for (Map.Entry<String, Integer> entry : result.entrySet()) {
                MonitorUserVehicleStatisticInfoDTO info = new MonitorUserVehicleStatisticInfoDTO();
                info.setType(entry.getKey());
                info.setName(VehicleOwnerUseCaseEnum.getNameByValue(entry.getKey()));
                info.setCount(entry.getValue());
                resultList.add(info);
            }
        } else if (StringUtils.equals(selectedType, businessType)) {
            result = vehicleOwnerUseCaseManager.getBusinessTypeCountByUsername(userName);
            for (Map.Entry<String, Integer> entry : result.entrySet()) {
                MonitorUserVehicleStatisticInfoDTO info = new MonitorUserVehicleStatisticInfoDTO();
                info.setType(entry.getKey());
                info.setName(VehicleBusinessTypeEnum.getNameByValue(entry.getKey()));
                info.setCount(entry.getValue());
                resultList.add(info);
            }
        }

        return resultList;
    }
}
