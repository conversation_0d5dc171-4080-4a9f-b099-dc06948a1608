package com.jdx.rover.monitor.service.web;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.datacenter.domain.dto.warehouse.mileage.HistoryMileageQueryJsfDTO;
import com.jdx.rover.datacenter.domain.dto.warehouse.mileage.VehicleMileageQueryJsfDTO;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.StatusEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.metadata.api.domain.enums.common.YesOrNoEnum;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.AccidentAnalysisDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.AccidentBoardDetailDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.MonthlyAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.RecentAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.WeeklyAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.AccidentBoardDetailVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.MonthlyAccidentTrendsVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.RecentAccidentTrendsVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.WeeklyAccidentTrendsVO;
import com.jdx.rover.monitor.bo.accident.AccidentBoardDetailBO;
import com.jdx.rover.monitor.bo.accident.AccidentBoardDetailQueryBO;
import com.jdx.rover.monitor.bo.accident.AccidentBoardFollowUpBO;
import com.jdx.rover.monitor.bo.accident.AccidentWeekCycleBO;
import com.jdx.rover.monitor.bo.accident.DefectiveAccidentBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.mobile.AccidentBoardTypeEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.datacenter.MileageManager;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 事故看板Service
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MonitorAccidentBoardService implements CommandLineRunner {

    private final AccidentManager accidentManager;

    private final MileageManager mileageManager;

    private final MetadataVehicleApiManager metadataVehicleApiManager;

    static Map<String, Function<String, AccidentBoardDetailQueryBO>> typeAccidentLogicMap = new HashMap<>();

    /**
     * 初始化事故看板获取对用方法MAP
     * @param args incoming main method arguments
     * @throws Exception
     */
    @Override
    public void run(String... args) throws Exception {
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.TODAY_HIGH_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentByLevel);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.TODAY_MEDIUM_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentByLevel);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.TODAY_NO_FOLLOW_ACCIDENT.getValue(), MonitorAccidentBoardService::getUnfollowedAccidents);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.MORE_THAN_30_DAYS_UNRESOLVED_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentsByStatus);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.MORE_THAN_90_DAYS_UNRESOLVED_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentsByStatus);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.SUSPENDED_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentsByStatus);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.WEEK_TOTAL_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentByLevel);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.WEEK_HIGH_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentByLevel);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.WEEK_MEDIUM_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentByLevel);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.MONTH_TOTAL_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentByLevel);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.MONTH_HIGH_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentByLevel);
        typeAccidentLogicMap.put(AccidentBoardTypeEnum.MONTH_MEDIUM_ACCIDENT.getValue(), MonitorAccidentBoardService::getAccidentByLevel);
    }

    /**
     * 获取事故分析
     * @return
     */
    public AccidentAnalysisDTO getAccidentAnalysis() {
        AccidentAnalysisDTO result = new AccidentAnalysisDTO();
        setTodayData(result);
        setAccidentFollowUpData(result);
        setWeekData(result);
        setMonthData(result);
        return result;
    }

    private void setTodayData(AccidentAnalysisDTO result) {
        DateTime startOfDay = DateUtil.beginOfDay(DateUtil.date());
        List<DefectiveAccidentBO> todayDefectiveAccidents = accidentManager.getBaseMapper().getDefectiveAccidents(startOfDay, null);
        int highCount = 0;
        int mediumCount = 0;
        int noFollowUpCount = 0;
        for (DefectiveAccidentBO defectiveAccidentBO : todayDefectiveAccidents) {
            if (PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                highCount++;
            }
            if (PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                mediumCount++;
            }
            if (YesOrNoEnum.NO.getValue().equals(defectiveAccidentBO.getIsSafetyGroupEdit())) {
                noFollowUpCount++;
            }
        }
        result.setTodayHighAccidents(highCount);
        result.setTodayMediumAccidents(mediumCount);
        result.setTodayNoFollowUpAccidents(noFollowUpCount);
        Double mileage = getTodayVehicleMile();
        result.setTodayVehicleMileage(String.format("%.2f", mileage / 1000));
    }

    /**
     * 获取今日行驶里程
     * @return
     */
    private double getTodayVehicleMile() {
        //这里先从缓存取 不存在需要查库
        Double todayMileage = RedissonUtils.getObject(RedisKeyEnum.ACCIDENT_BOARD_TODAY_MILEAGE.getValue());
        if (todayMileage != null) {
            return todayMileage;
        }
        List<VehicleMileageQueryJsfDTO> vehicleMileageQueryJsfDTOS = mileageManager.vehicleMileageQuery(DateUtil.beginOfDay(new Date()), new Date());
        List<VehicleBasicDTO> vehicleBasicDTOS = metadataVehicleApiManager.listAll();
        //过滤
        Set<String> vehicleNameSet = vehicleBasicDTOS.stream().filter(vehicleBasicDTO -> VehicleOwnerUseCaseEnum.SOLUTION.getValue().equals(vehicleBasicDTO.getOwnerUseCase()) || VehicleOwnerUseCaseEnum.OPEN.getValue().equals(vehicleBasicDTO.getOwnerUseCase()) ||
                        VehicleOwnerUseCaseEnum.RD.getValue().equals(vehicleBasicDTO.getOwnerUseCase()) || VehicleOwnerUseCaseEnum.TEST.getValue().equals(vehicleBasicDTO.getOwnerUseCase()) || VehicleOwnerUseCaseEnum.FIX.getValue().equals(vehicleBasicDTO.getOwnerUseCase()) || VehicleOwnerUseCaseEnum.HARDWARE.getValue().equals(vehicleBasicDTO.getOwnerUseCase()))
                .map(VehicleBasicDTO::getName).collect(Collectors.toSet());
        log.info("跑行车辆统计:{}", JsonUtils.writeValueAsString(vehicleNameSet));
        double totalMileage = vehicleMileageQueryJsfDTOS.stream().filter(mileageQueryDTO -> vehicleNameSet.contains(mileageQueryDTO.getVehicleName())).mapToDouble(VehicleMileageQueryJsfDTO::getTotalMileage).sum();
        RedissonUtils.setObject(RedisKeyEnum.ACCIDENT_BOARD_TODAY_MILEAGE.getValue(), totalMileage, 10 * 60);
        return totalMileage;
    }

    /**
     * 设置事故跟进数据
     * @param result
     */
    private void setAccidentFollowUpData(AccidentAnalysisDTO result) {
        //从缓存中获取
        AccidentBoardFollowUpBO accidentBoardFollowUpBO = RedissonUtils.getObject(RedisKeyEnum.ACCIDENT_BOARD_FOLLOW_UP.getValue());
        if (accidentBoardFollowUpBO != null) {
            result.setAccidentsUnresolvedMoreThan30Days(accidentBoardFollowUpBO.getAccidentsUnresolvedMoreThan30Days());
            result.setAccidentsUnresolvedMoreThan90Days(accidentBoardFollowUpBO.getAccidentsUnresolvedMoreThan90Days());
            result.setSuspendedAccidents(accidentBoardFollowUpBO.getSuspendedAccidents());
            return ;
        }

        //从数据库查询
        DateTime startOfDayThirtyDaysAgo = DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -30));
        DateTime startOfDayNinthDaysAgo = DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -90));
        List<String> bugStatusList = Arrays.asList(StatusEnum.REOPEN.getValue(), StatusEnum.NEW.getValue(), StatusEnum.HANDLE.getValue());
        Long accidentsUnresolvedMoreThan30Days = accidentManager.lambdaQuery().in(Accident::getBugStatus, bugStatusList).lt(Accident::getAccidentReportTime, startOfDayThirtyDaysAgo).count();
        Long accidentsUnresolvedMoreThan90Days = accidentManager.lambdaQuery().in(Accident::getBugStatus, bugStatusList).lt(Accident::getAccidentReportTime, startOfDayNinthDaysAgo).count();
        Long suspendedAccidents = accidentManager.lambdaQuery().eq(Accident::getBugStatus, StatusEnum.HANG.getValue()).count();
        result.setAccidentsUnresolvedMoreThan30Days(accidentsUnresolvedMoreThan30Days.intValue());
        result.setAccidentsUnresolvedMoreThan90Days(accidentsUnresolvedMoreThan90Days.intValue());
        result.setSuspendedAccidents(suspendedAccidents.intValue());

        //放入缓存 设置过期时间
        AccidentBoardFollowUpBO insertAccidentBoardFollowUpBo = new AccidentBoardFollowUpBO();
        insertAccidentBoardFollowUpBo.setAccidentsUnresolvedMoreThan30Days(result.getAccidentsUnresolvedMoreThan30Days());
        insertAccidentBoardFollowUpBo.setAccidentsUnresolvedMoreThan90Days(result.getAccidentsUnresolvedMoreThan90Days());
        insertAccidentBoardFollowUpBo.setSuspendedAccidents(result.getSuspendedAccidents());
        long secondsUntilMidnight = TimeUnit.MILLISECONDS.toSeconds(DateUtil.endOfDay(DateUtil.date()).getTime() - DateUtil.date().getTime());
        if (secondsUntilMidnight < 60) {
            //距离当天结束小于1分钟,不计入缓存
            return ;
        }
        RedissonUtils.setObject(RedisKeyEnum.ACCIDENT_BOARD_FOLLOW_UP.getValue(), insertAccidentBoardFollowUpBo, secondsUntilMidnight);
    }

    private void setWeekData(AccidentAnalysisDTO result) {
        Date today = new Date();
        DateTime lastFriday = DateUtil.offsetDay(DateUtil.beginOfWeek(today), -3); // 上周五是上周的第三天
        DateTime thisThursday = DateUtil.offsetDay(DateUtil.beginOfWeek(today), 3);
        DateTime thisFriday = DateUtil.offsetDay(DateUtil.beginOfWeek(today), 4); // 这周五是本周的第五天

        List<DefectiveAccidentBO> defectiveAccidents = accidentManager.getBaseMapper().getDefectiveAccidents(lastFriday, thisFriday);
        int highCount = 0;
        int mediumCount = 0;
        for (DefectiveAccidentBO defectiveAccidentBO : defectiveAccidents) {
            if (PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                highCount++;
            }
            if (PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                mediumCount++;
            }
        }
        String weekDatePeriod = String.format("%s-%s", DateUtil.format(lastFriday, "MM/dd"), DateUtil.format(thisThursday, "MM/dd"));
        result.setWeekDatePeriod(weekDatePeriod);
        result.setWeekTotalAccidents(defectiveAccidents.size());
        result.setWeekHighAccidents(highCount);
        result.setWeekMediumAccidents(mediumCount);
        //设置里程相关数据 需要加上今天实时数据
        List<HistoryMileageQueryJsfDTO> historyMileageQueryJsfDTOS = mileageManager.historyMileageQuery(lastFriday, thisThursday);
        double weekMileage = 0;
        if (DateUtil.dayOfWeek(new Date()) >= 2 && DateUtil.dayOfWeek(new Date()) <= 5) {
            weekMileage = getTodayVehicleMile();
        }
        for (HistoryMileageQueryJsfDTO historyMileageQueryJsfDTO : historyMileageQueryJsfDTOS) {
            weekMileage += historyMileageQueryJsfDTO.getTotalMileage();
        }
        weekMileage = weekMileage / 1000.0;
        result.setWeekVehicleMileage(String.format("%.2f", weekMileage));
        if (weekMileage > 0) {
            result.setWeekAccidentRate(String.format("%.2f", (highCount + mediumCount) * 10000 / weekMileage));
        }
    }

    private void setMonthData(AccidentAnalysisDTO result) {
        Date now = new Date();
        DateTime firstDayOfMonth = DateUtil.beginOfMonth(now);
        DateTime lastDayOfMonth = DateUtil.endOfMonth(now);
        List<DefectiveAccidentBO> defectiveAccidents = accidentManager.getBaseMapper().getDefectiveAccidents(firstDayOfMonth, lastDayOfMonth);
        int highCount = 0;
        int mediumCount = 0;
        for (DefectiveAccidentBO defectiveAccidentBO : defectiveAccidents) {
            if (PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                highCount++;
            }
            if (PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                mediumCount++;
            }
        }
        String monthDatePeriod = String.format("%s-%s", DateUtil.format(firstDayOfMonth, "MM/dd"), DateUtil.format(now, "MM/dd"));
        result.setMouthDatePeriod(monthDatePeriod);
        result.setMouthTotalAccidents(defectiveAccidents.size());
        result.setMouthHighAccidents(highCount);
        result.setMouthMediumAccidents(mediumCount);
        //设置本月里程信息 需要加上今天实时里程信息
        double monthMileage = getTodayVehicleMile();
        List<HistoryMileageQueryJsfDTO> historyMileageQueryJsfDTOS = mileageManager.historyMileageQuery(firstDayOfMonth, lastDayOfMonth);
        for (HistoryMileageQueryJsfDTO historyMileageQueryJsfDTO : historyMileageQueryJsfDTOS) {
            monthMileage += historyMileageQueryJsfDTO.getTotalMileage();
        }
        monthMileage = monthMileage / 1000.0;
        result.setMouthVehicleMileage(String.format("%.2f", monthMileage / 10000));
        if (monthMileage > 0) {
            result.setMouthAccidentRate(String.format("%.2f", (highCount + mediumCount) * 10000 / monthMileage));
        }
    }

    /**
     * 最近一段时间事故趋势
     * @param recentAccidentTrendsVO
     * @return
     */
    public List<RecentAccidentTrendsDTO> recentAccidentTrends(RecentAccidentTrendsVO recentAccidentTrendsVO) {
        List<RecentAccidentTrendsDTO> result = new ArrayList<>();
        List<DefectiveAccidentBO> defectiveAccidents = accidentManager.getBaseMapper().getDefectiveAccidents(DateUtil.beginOfDay(recentAccidentTrendsVO.getStartDate()), DateUtil.endOfDay(recentAccidentTrendsVO.getEndDate()));
        Map<Date, List<DefectiveAccidentBO>> defectiveAccidentDateMap = defectiveAccidents.stream().collect(Collectors.groupingBy(defectiveAccidentBO -> DateUtil.date(defectiveAccidentBO.getAccidentDayTime())));
        List<DateTime> dateTimeList = DateUtil.rangeToList(recentAccidentTrendsVO.getStartDate(), recentAccidentTrendsVO.getEndDate(), DateField.DAY_OF_MONTH);
        for (DateTime dateTime : dateTimeList) {
            RecentAccidentTrendsDTO recentAccidentTrendsDTO = new RecentAccidentTrendsDTO();
            recentAccidentTrendsDTO.setDate(DateUtil.format(dateTime, "MM/dd"));
            List<DefectiveAccidentBO> defectiveAccidentBOList = defectiveAccidentDateMap.getOrDefault(dateTime, Collections.emptyList());
            recentAccidentTrendsDTO.setTotalAccidents(defectiveAccidentBOList.size());
            recentAccidentTrendsDTO.setHighAndMediumAccidents(getHighAndMediumCount(defectiveAccidentBOList));
            result.add(recentAccidentTrendsDTO);
        }
        return result;
    }

    private int getHighAndMediumCount(List<DefectiveAccidentBO> defectiveAccidentBOList) {
        int count = 0;
        for (DefectiveAccidentBO defectiveAccidentBO : defectiveAccidentBOList) {
            if (PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel()) || PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                count++;
            }
        }
        return count;
    }

    /**
     * 周度事故统计趋势图
     * @param weeklyAccidentTrendsVO
     * @return
     */
    public List<WeeklyAccidentTrendsDTO> weeklyAccidentTrends(WeeklyAccidentTrendsVO weeklyAccidentTrendsVO) {
        List<WeeklyAccidentTrendsDTO> result = new ArrayList<>();
        List<AccidentWeekCycleBO> weeklyCycles = getWeeklyCycles(weeklyAccidentTrendsVO.getStartDate(), weeklyAccidentTrendsVO.getEndDate());
        List<DefectiveAccidentBO> defectiveAccidents = accidentManager.getBaseMapper().getDefectiveAccidents(DateUtil.beginOfDay(weeklyCycles.get(0).getStartTime()), DateUtil.endOfDay(weeklyCycles.get(weeklyCycles.size() - 1).getEndTime()));
        List<HistoryMileageQueryJsfDTO> historyMileageQueryJsfDTOS = mileageManager.historyMileageQuery(weeklyCycles.get(0).getStartTime(), weeklyCycles.get(weeklyCycles.size() - 1).getEndTime());
        for (AccidentWeekCycleBO accidentWeekCycleBO : weeklyCycles) {
            WeeklyAccidentTrendsDTO weeklyAccidentTrendsDTO = new WeeklyAccidentTrendsDTO();
            String weekDatePeriod = String.format("%s-%s", DateUtil.format(accidentWeekCycleBO.getStartTime(), "MM/dd"), DateUtil.format(accidentWeekCycleBO.getEndTime(), "MM/dd"));
            weeklyAccidentTrendsDTO.setWeekDatePeriod(weekDatePeriod);
            int totalAccidentCount = 0;
            int highAndMediumAccidentCount = 0;
            for (DefectiveAccidentBO defectiveAccidentBO : defectiveAccidents) {
                if (DateUtil.date(accidentWeekCycleBO.getStartTime()).isBeforeOrEquals(defectiveAccidentBO.getAccidentDayTime()) && DateUtil.date(accidentWeekCycleBO.getEndTime()).isAfterOrEquals(defectiveAccidentBO.getAccidentDayTime())) {
                    totalAccidentCount++;
                    if (PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel()) || PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                        highAndMediumAccidentCount++;
                    }
                }
            }
            weeklyAccidentTrendsDTO.setTotalAccidents(totalAccidentCount);
            weeklyAccidentTrendsDTO.setHighAndMediumAccidents(highAndMediumAccidentCount);
            double weekMileage = 0;
            if (DateUtil.beginOfDay(accidentWeekCycleBO.getStartTime()).isBeforeOrEquals(DateTime.now()) && DateUtil.endOfDay(accidentWeekCycleBO.getEndTime()).isAfterOrEquals(DateTime.now())) {
                weekMileage += getTodayVehicleMile();
            }
            for (HistoryMileageQueryJsfDTO historyMileageQueryJsfDTO : historyMileageQueryJsfDTOS) {
                if (DateUtil.beginOfDay(accidentWeekCycleBO.getStartTime()).isBeforeOrEquals(historyMileageQueryJsfDTO.getDate()) && DateUtil.endOfDay(accidentWeekCycleBO.getEndTime()).isAfterOrEquals(historyMileageQueryJsfDTO.getDate())) {
                    weekMileage += historyMileageQueryJsfDTO.getTotalMileage();
                }
            }
            weekMileage = weekMileage / 1000;
            if (weekMileage > 0) {
                weeklyAccidentTrendsDTO.setAccidentRate(String.format("%.2f", highAndMediumAccidentCount * 10000 / weekMileage));
            }
            result.add(weeklyAccidentTrendsDTO);
        }
        return result;
    }

    /**
     * 获取 周五-周四 周期
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<AccidentWeekCycleBO> getWeeklyCycles(Date startDate, Date endDate) {
        List<AccidentWeekCycleBO> cycles = new ArrayList<>();
        //获取当前日期是
        Week startWeek = DateUtil.dayOfWeekEnum(startDate);
        int daysToFriday = startWeek.getValue() - Week.FRIDAY.getValue();
        DateTime currentStart;
        if (startWeek.getValue() < Week.FRIDAY.getValue()) {
            //上个周五是开始
            currentStart = DateUtil.offsetDay(startDate, -daysToFriday-7);
        } else {
            currentStart = DateUtil.offsetDay(startDate, -daysToFriday);
        }
        // 找到结束日期所在周的周四
        DateTime lastThursday;
        Week endWeek = DateUtil.dayOfWeekEnum(endDate);
        int daysToThursday = endWeek.getValue() - Week.THURSDAY.getValue();
        if (endWeek.getValue() > Week.THURSDAY.getValue()) {
            //上个周五是开始
            lastThursday = DateUtil.offsetDay(endDate, -daysToThursday + 7);
        } else {
            lastThursday = DateUtil.offsetDay(endDate, -daysToThursday);
        }
        // 遍历每个周期
        while (!currentStart.after(lastThursday)) {
            DateTime currentEnd = DateUtil.offsetDay(currentStart, 6);
            cycles.add(formatCycle(currentStart, currentEnd));
            // 移动到下一个周期
            currentStart = DateUtil.offsetDay(currentStart, 7);
        }
        return cycles;
    }

    private static AccidentWeekCycleBO formatCycle(Date start, Date end) {
        AccidentWeekCycleBO accidentWeekCycleBO = new AccidentWeekCycleBO();
        accidentWeekCycleBO.setStartTime(start);
        accidentWeekCycleBO.setEndTime(end);
        log.info("从{}到{}", DateUtil.formatDate(start), DateUtil.formatDate(end));
        return accidentWeekCycleBO;
    }

    public List<MonthlyAccidentTrendsDTO> monthlyAccidentTrends(MonthlyAccidentTrendsVO monthlyAccidentTrendsVO) {
        List<MonthlyAccidentTrendsDTO> result = new ArrayList<>();

        Date startTime =  DateUtil.parse(monthlyAccidentTrendsVO.getStartDate(), "yyyy-MM");
        Date endTime = DateUtil.parse(monthlyAccidentTrendsVO.getEndDate(), "yyyy-MM");;

        List<DateTime> months = getMonthlyCycles(startTime, endTime);
        List<DefectiveAccidentBO> defectiveAccidents = accidentManager.getBaseMapper().getDefectiveAccidents(DateUtil.beginOfMonth(months.get(0)), DateUtil.endOfMonth(months.get(months.size() - 1)));
        List<HistoryMileageQueryJsfDTO> historyMileageQueryJsfDTOS = mileageManager.historyMileageQuery(DateUtil.beginOfMonth(months.get(0)), DateUtil.endOfMonth(months.get(months.size() - 1)));
        for (DateTime monthTime : months) {
            MonthlyAccidentTrendsDTO monthlyAccidentTrendsDTO = new MonthlyAccidentTrendsDTO();
            int totalAccidentCount = 0;
            int highAndMediumAccidentCount = 0;
            for (DefectiveAccidentBO defectiveAccidentBO : defectiveAccidents) {
                if (DateUtil.beginOfDay(monthTime).isBeforeOrEquals(defectiveAccidentBO.getAccidentDayTime()) && DateUtil.endOfMonth(monthTime).isAfterOrEquals(defectiveAccidentBO.getAccidentDayTime())) {
                    totalAccidentCount++;
                    if (PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel()) || PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(defectiveAccidentBO.getTechnicalSupportAccidentLevel())) {
                        highAndMediumAccidentCount++;
                    }
                }
            }
            monthlyAccidentTrendsDTO.setMonthDatePeriod(DateUtil.format(monthTime, "yy/MM"));
            monthlyAccidentTrendsDTO.setTotalAccidents(totalAccidentCount);
            monthlyAccidentTrendsDTO.setHighAndMediumAccidents(highAndMediumAccidentCount);
            double monthMileage = 0;
            Date now = new Date();
            if (DateUtil.beginOfDay(monthTime).isBeforeOrEquals(now) && DateUtil.endOfMonth(monthTime).isAfterOrEquals(now)) {
                monthMileage += getTodayVehicleMile();
            }
            for (HistoryMileageQueryJsfDTO historyMileageQueryJsfDTO : historyMileageQueryJsfDTOS) {
                if (DateUtil.beginOfDay(monthTime).isBeforeOrEquals(historyMileageQueryJsfDTO.getDate()) && DateUtil.endOfMonth(monthTime).isAfterOrEquals(historyMileageQueryJsfDTO.getDate())) {
                    monthMileage += historyMileageQueryJsfDTO.getTotalMileage();
                }
            }
            monthMileage = monthMileage / 1000;
            monthlyAccidentTrendsDTO.setVehicleMileage(String.format("%.2f", monthMileage));
            if (monthMileage > 0) {
                monthlyAccidentTrendsDTO.setAccidentRate(String.format("%.2f", highAndMediumAccidentCount * 10000 / monthMileage));
            }
            result.add(monthlyAccidentTrendsDTO);
        }
        return result;
    }

    /**
     * 获取月份周期(返回每个月第一天)
     * @param startDate
     * @param endDate
     * @return
     */
    private List<DateTime> getMonthlyCycles(Date startDate, Date endDate) {
        List<DateTime> monthList = new ArrayList<>();
        DateTime startMonth = DateUtil.beginOfMonth(startDate);
        DateTime endMonth = DateUtil.endOfMonth(endDate);

        DateTime current =startMonth;
        while (current.isBeforeOrEquals(endMonth)) {
            monthList.add(current);
            current = current.offsetNew(DateField.MONTH, 1);
        }
        return monthList;
    }

    /**
     * 获取事故明细
     * @param accidentBoardDetailVO
     * @return
     */
    public List<AccidentBoardDetailDTO> getAccidentDetail(AccidentBoardDetailVO accidentBoardDetailVO) {
        List<AccidentBoardDetailDTO> result = new ArrayList<>();
        Function<String, AccidentBoardDetailQueryBO> function = typeAccidentLogicMap.get(accidentBoardDetailVO.getType());
        AccidentBoardDetailQueryBO accidentBoardDetailQueryBO = new AccidentBoardDetailQueryBO();
        if (function != null) {
            accidentBoardDetailQueryBO = function.apply(accidentBoardDetailVO.getType());
        }
        List<AccidentBoardDetailBO> accidentBoardDetailList = accidentManager.getBaseMapper().getAccidentBoardDetailList(accidentBoardDetailQueryBO);
        result = accidentBoardDetailList.stream().map(accidentBoardDetailBO -> accidentBoardDetailBO.convertBOToDTO(accidentBoardDetailBO)).collect(Collectors.toList());
        return result;
    }

    public static AccidentBoardDetailQueryBO getAccidentByLevel(String type) {
        AccidentBoardDetailQueryBO result = new AccidentBoardDetailQueryBO();
        //设置事故等级
        if (StringUtils.equalsAny(type, AccidentBoardTypeEnum.TODAY_HIGH_ACCIDENT.getValue(), AccidentBoardTypeEnum.WEEK_HIGH_ACCIDENT.getValue(), AccidentBoardTypeEnum.MONTH_HIGH_ACCIDENT.getValue())) {
            result.setAccidentLevel(PreliminaryAccidentLevelEnum.HIGH_RISK.getValue());
        } else if (StringUtils.equalsAny(type, AccidentBoardTypeEnum.TODAY_MEDIUM_ACCIDENT.getValue(), AccidentBoardTypeEnum.WEEK_MEDIUM_ACCIDENT.getValue(), AccidentBoardTypeEnum.MONTH_MEDIUM_ACCIDENT.getValue())) {
            result.setAccidentLevel(PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue());
        }
        //设置事故时间
        DateTime now = DateUtil.date();
        if (StringUtils.equalsAny(type, AccidentBoardTypeEnum.TODAY_HIGH_ACCIDENT.getValue(), AccidentBoardTypeEnum.TODAY_MEDIUM_ACCIDENT.getValue())) {
            result.setStartTime(DateUtil.beginOfDay(now));
        } else if (StringUtils.equalsAny(type, AccidentBoardTypeEnum.WEEK_HIGH_ACCIDENT.getValue(), AccidentBoardTypeEnum.WEEK_MEDIUM_ACCIDENT.getValue(), AccidentBoardTypeEnum.WEEK_TOTAL_ACCIDENT.getValue())) {
            DateTime startOfLastFriday = DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.beginOfWeek(now), -3));
            DateTime endOfThisThursday = DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.beginOfWeek(now), 3));
            result.setStartTime(startOfLastFriday);
            result.setEndTime(endOfThisThursday);
        } else if (StringUtils.equalsAny(type, AccidentBoardTypeEnum.MONTH_HIGH_ACCIDENT.getValue(), AccidentBoardTypeEnum.MONTH_MEDIUM_ACCIDENT.getValue(), AccidentBoardTypeEnum.MONTH_TOTAL_ACCIDENT.getValue())) {
            result.setStartTime(DateUtil.beginOfMonth(now));
        }
        return result;
    }

    public static AccidentBoardDetailQueryBO getUnfollowedAccidents(String type) {
        AccidentBoardDetailQueryBO result = new AccidentBoardDetailQueryBO();
        result.setIsSafetyGroupEdit(YesOrNoEnum.NO.getValue());
        result.setStartTime(DateUtil.beginOfDay(DateUtil.date()));
        return result;
    }

    public static AccidentBoardDetailQueryBO getAccidentsByStatus(String type) {
        AccidentBoardDetailQueryBO result = new AccidentBoardDetailQueryBO();
        List<String> bugStatusList = Arrays.asList(StatusEnum.NEW.getValue(), StatusEnum.HANDLE.getValue(), StatusEnum.REOPEN.getValue());
        result.setBugStatusList(bugStatusList);
        if (AccidentBoardTypeEnum.SUSPENDED_ACCIDENT.getValue().equals(type)) {
            List<String> suspendedStatusList = Arrays.asList(StatusEnum.HANG.getValue());
            result.setBugStatusList(suspendedStatusList);
        }
        // 设置时间
        if (AccidentBoardTypeEnum.MORE_THAN_30_DAYS_UNRESOLVED_ACCIDENT.getValue().equals(type)) {
            DateTime thirtyDaysAgo = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -30));
            result.setEndTime(thirtyDaysAgo);
        } else if (AccidentBoardTypeEnum.MORE_THAN_90_DAYS_UNRESOLVED_ACCIDENT.getValue().equals(type)) {
            DateTime ninthDaysAgo = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -90));
            result.setEndTime(ninthDaysAgo);
        }
        return result;
    }
}
