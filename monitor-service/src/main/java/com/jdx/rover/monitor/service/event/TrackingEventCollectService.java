/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.event;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.api.domain.enums.MonitorTrackingEventEnum;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.MonitorChangeVideoModeVO;
import com.jdx.rover.monitor.constant.BugConstant;
import com.jdx.rover.monitor.domain.event.MonitorEventVO;
import com.jdx.rover.monitor.domain.event.ShadowJiraEventVO;
import com.jdx.rover.monitor.domain.event.ShadowTrackingEventVO;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.entity.VehicleSceneSignalEntity;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.VideoModeEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSceneSignalAlarmRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.service.tencent.TencentMapService;
import com.jdx.rover.monitor.vo.BugAddVO;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * <p>
 * This is a tracking event service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class TrackingEventCollectService {

  @Autowired
  private JmqProducerService jmqProducerService;
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private VehicleSceneSignalAlarmRepository sceneSignalAlarmRepository;
  @Autowired
  private TencentMapService tencentMapService;

  /**
   * <p>
   * 推送监控用户操作事件.
   * </p>
   */
  public void pushUserEvent(MonitorUserOperationEntity monitorUserOperationEntity) {
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(monitorUserOperationEntity.getTimestamp());
    event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
    event.setVehicleName(monitorUserOperationEntity.getVehicleName());
    MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.of(monitorUserOperationEntity.getOperationType());
    if (trackingEventEnum == null) {
      return;
    }
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(monitorUserOperationEntity.getVehicleName());
    eventBody.setTime(monitorUserOperationEntity.getTimestamp());
    eventBody.setUserName(monitorUserOperationEntity.getUserName());
    eventBody.setAction(trackingEventEnum.getName());
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    event.setEventNo(trackingEventEnum.getCode());
    String eventJson = JsonUtils.writeValueAsString(event);
    log.info("推送监控用户操作{}到影子系统", eventJson);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), monitorUserOperationEntity.getVehicleName(), event);
  }

  /**
   * <p>
   * 推送车辆告警事件.
   * </p>
   */
  public void pushAlarmEvent(String vehicleName, List<AlarmInfoDTO> alarmEventList) {
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    alarmEventList.stream().forEach(alarmEvent -> {
      event.setReportTime(alarmEvent.getStartTime());
      event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
      event.setVehicleName(vehicleName);
      MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.of(alarmEvent.getAlarmType());
      if (trackingEventEnum == null || trackingEventEnum == MonitorTrackingEventEnum.LOCALIZATION_ERROR) {
        return;
      }
      event.setEventType(trackingEventEnum.getValue());
      event.setOperationType(trackingEventEnum.getValue());
      MonitorEventVO eventBody = new MonitorEventVO();
      eventBody.setVehicleName(vehicleName);
      eventBody.setTime(alarmEvent.getStartTime());
      eventBody.setAction(trackingEventEnum.getName());
      if (trackingEventEnum == MonitorTrackingEventEnum.VEHICLE_CRASH
              && StringUtils.equals("-13601", alarmEvent.getErrorCode())) {
        eventBody.setAction(MonitorTrackingEventEnum.VEHICLE_GUARDIAN_CRASH.getName());
      } else if (trackingEventEnum == MonitorTrackingEventEnum.PASS_NO_SIGNAL_INTERSECTION
        || trackingEventEnum == MonitorTrackingEventEnum.GATE_STUCK || trackingEventEnum == MonitorTrackingEventEnum.ATTENTION_REGION_WARN) {
        VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
        if (vehicleRealtimeInfoDTO != null && vehicleRealtimeInfoDTO.getLat() != null && vehicleRealtimeInfoDTO.getLon() != null) {
          String address = tencentMapService.wgs84ToAddress(vehicleRealtimeInfoDTO.getLat(), vehicleRealtimeInfoDTO.getLon());
          Map<String, Object> extendMap = new HashMap<>();
          extendMap.put("address", address);
          eventBody.setExtendMap(extendMap);
        }
      }
      event.setEventBody(JsonUtils.writeValueAsString(eventBody));
      event.setEventNo(trackingEventEnum.getCode());
      jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), vehicleName, event);
    });
  }

  /**
   * <p>
   * 推送车辆pnc规划事件.
   * </p>
   */
  public void pushPncTask(VehiclePncInfoDTO vehiclePncInfoDto) {
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(vehiclePncInfoDto.getRecordTime());
    event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
    event.setVehicleName(vehiclePncInfoDto.getVehicleName());
    event.setEventNo(MonitorTrackingEventEnum.PNC_PLANNING.getCode());
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(vehiclePncInfoDto.getVehicleName());
    eventBody.setTime(vehiclePncInfoDto.getRecordTime());
    eventBody.setAction(String.valueOf(vehiclePncInfoDto.getNaviRefreshId()));
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), vehiclePncInfoDto.getVehicleName(), event);
  }

  /**
   * <p>
   * 推送车辆JIRA事件.
   * </p>
   */
  public void pushJiraEvent(BugAddVO bugAddVO, String jiraNo, String userName) {
    ShadowJiraEventVO trackingEventDto = new ShadowJiraEventVO();
    if(StringUtils.isBlank(userName)) {
      userName = BugConstant.userName;
    }
    trackingEventDto.setReportTime(bugAddVO.getDebugTime().get(0));
    trackingEventDto.setReportUser(userName);
    trackingEventDto.setDebugTime(bugAddVO.getDebugTime());
    trackingEventDto.setVehicleName(bugAddVO.getVehicleName());
    trackingEventDto.setJiraNo(jiraNo);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_JIRA_EVENT.getTopic(), bugAddVO.getVehicleName(), trackingEventDto);
  }

  /**
   * <p>
   * 推送车辆零偏.
   * </p>
   */
  public void pushSteerZeroStatus(String vehicleName, String steerZeroStatus) {
    if (StringUtils.isBlank(steerZeroStatus)) {
      return;
    }
    double value = Double.valueOf(steerZeroStatus);
    if (value < 1) {
      return;
    }
    String key = RedisKeyEnum.VEHICLE_STEER_ZERO_STATUS.getValue() + vehicleName;
    Double lastAlarmValue = RedissonUtils.getObject(key);
    // 左正右负
    if (Objects.isNull(lastAlarmValue) || Math.abs(value) > NumberUtil.mul(Math.abs(lastAlarmValue.floatValue()), 1.5)) {
      Date currentDate = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
      RedissonUtils.setObject(key, value, DateUtil.between(currentDate, DateUtil.endOfDay(currentDate), DateUnit.SECOND));
      ShadowTrackingEventVO event = new ShadowTrackingEventVO();
      event.setReportTime(new Date());
      event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
      event.setVehicleName(vehicleName);
      MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.VEHICLE_STEER_ZERO_FAULT;
      MonitorEventVO eventBody = new MonitorEventVO();
      eventBody.setVehicleName(vehicleName);
      eventBody.setTime(new Date());
      eventBody.setAction(trackingEventEnum.getName());
      Map<String, Object> extendMap = new HashMap<>();
      extendMap.put("steerZero", steerZeroStatus);
      eventBody.setExtendMap(extendMap);
      event.setEventBody(JsonUtils.writeValueAsString(eventBody));
      event.setEventNo(trackingEventEnum.getCode());
      String eventJson = JsonUtils.writeValueAsString(event);
      log.info("推送车辆零偏事件{}到影子系统", eventJson);
      jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), vehicleName, event);
    }
  }

  /**
   * <p>
   * 推送车辆定位置信度.
   * </p>
   */
  public void pushSceneSignalStatus(String vehicleName, String oldValue, String newValue) {
    double newSignal = Double.valueOf(newValue);
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(new Date());
    event.setVehicleName(vehicleName);
    MonitorTrackingEventEnum trackingEventEnum = null;
    Map<String, Object> extendMap = new HashMap<>();
    extendMap.put("sceneSignal", newSignal);
    if (StringUtils.isNotBlank(oldValue) && Double.valueOf(oldValue) <= 3.5 && newSignal > 3.5) {
      // 发送恢复的影子事件
      VehicleSceneSignalEntity sceneSignalEntity = sceneSignalAlarmRepository.getByKey(vehicleName);
      if (Objects.isNull(sceneSignalEntity)) {
        return;
      }
      trackingEventEnum = MonitorTrackingEventEnum.VEHICLE_SCENE_SIGNAL_NORMAL;
      event.setParentTraceId(sceneSignalEntity.getTraceId());
      event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
      extendMap.put("startTime", sceneSignalEntity.getReportTime());
      extendMap.put("duration", DateUtil.between(sceneSignalEntity.getReportTime(), new Date(), DateUnit.SECOND));
    } else if (newSignal < 3.5 && (StringUtils.isBlank(oldValue) || Double.valueOf(oldValue) > 3.5)) {
      // 发送新的告警影子事件
      String traceId = String.valueOf(RequestIdUtils.getRequestId());
      event.setTraceId(traceId);
      trackingEventEnum = MonitorTrackingEventEnum.LOCALIZATION_ERROR;
      VehicleSceneSignalEntity sceneSignalEntity = new VehicleSceneSignalEntity();
      sceneSignalEntity.setSceneSignal(newSignal);
      sceneSignalEntity.setReportTime(new Date());
      sceneSignalEntity.setTraceId(traceId);
      sceneSignalAlarmRepository.save(vehicleName, sceneSignalEntity);
    }
    if (Objects.isNull(trackingEventEnum)) {
      return;
    }
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(vehicleName);
    eventBody.setTime(new Date());
    eventBody.setAction(trackingEventEnum.getName());
    eventBody.setExtendMap(extendMap);
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    event.setEventNo(trackingEventEnum.getCode());
    String eventJson = JsonUtils.writeValueAsString(event);
    log.info("推送车辆定位置信度事件{}到影子系统", eventJson);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), vehicleName, event);
  }

  /**
   * <p>
   * 推送人工告警事件.
   * </p>
   */
  public void pushManualAlarmEvent(ManualAlarmSourceEnum sourceEnum, MonitorManualAlarmReportVO manualAlarmReportVo, String userName) {
    String vehicleName = manualAlarmReportVo.getVehicleName();
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(new Date());
    event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
    event.setVehicleName(vehicleName);
    MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.MANUAL_REPORT_ALARM;
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(vehicleName);
    eventBody.setTime(new Date());
    eventBody.setUserName(userName);
    eventBody.setAction(trackingEventEnum.getName());
    Map<String, Object> extendMap = new HashMap<>();
    extendMap.put("msg", manualAlarmReportVo.getRemark());
    extendMap.put("source", sourceEnum.getSourceName());
    eventBody.setExtendMap(extendMap);
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    event.setEventNo(trackingEventEnum.getCode());
    String eventJson = JsonUtils.writeValueAsString(event);
    log.info("推送人工告警事件{}到影子系统", eventJson);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), manualAlarmReportVo.getVehicleName(), event);
  }

  /**
   * <p>
   * 推送机动车道行驶检测
   * </p>
   */
  public void pushDrivingLaneEvent(String vehicleName, Date reportTime, String userName) {
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(reportTime);
    event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
    event.setVehicleName(vehicleName);
    MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.VEHICLE_DRIVING_LANE;
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(vehicleName);
    eventBody.setTime(reportTime);
    eventBody.setUserName(userName);
    eventBody.setAction(trackingEventEnum.getName());
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    event.setEventNo(trackingEventEnum.getCode());
    String eventJson = JsonUtils.writeValueAsString(event);
    log.info("推送机动车道监测{}到影子系统", eventJson);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), vehicleName, event);
  }

  /**
   * <p>
   * 推送修改视频模式
   * </p>
   */
    public void pushVideoChangeMode(MonitorChangeVideoModeVO changeVideoModeVo, String userName) {
      ShadowTrackingEventVO event = new ShadowTrackingEventVO();
      event.setReportTime(new Date());
      event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
      event.setVehicleName(changeVideoModeVo.getVehicleName());
      MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.VEHICLE_VIDEO_MODE_CHANGE;
      MonitorEventVO eventBody = new MonitorEventVO();
      eventBody.setVehicleName(changeVideoModeVo.getVehicleName());
      eventBody.setTime(new Date());
      eventBody.setUserName(userName);
      eventBody.setAction(VideoModeEnum.of(changeVideoModeVo.getVideoMode()).getName());
      event.setEventBody(JsonUtils.writeValueAsString(eventBody));
      event.setEventNo(trackingEventEnum.getCode());
      String eventJson = JsonUtils.writeValueAsString(event);
      log.info("推送修改视频清晰度{}到影子系统", eventJson);
      jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), changeVideoModeVo.getVehicleName(), event);
    }

  /**
   * <p>
   * 推送机器人行走日志
   * </p>
   */
  public void pushDeviceDrivingEvent(String productKey, String deviceName, Date reportTime, String action) {
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(reportTime);
    event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
    event.setProductKey(productKey);
    event.setDeviceName(deviceName);
    MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.ROBOT_DEVICE_DRIVE_LOG;
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(deviceName);
    eventBody.setTime(reportTime);
    eventBody.setUserName("");
    eventBody.setAction(action);
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    event.setEventNo(trackingEventEnum.getCode());
    String eventJson = JsonUtils.writeValueAsString(event);
    log.info("推送车辆行走日志{}到影子系统", eventJson);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), deviceName, event);
  }

  /**
   * <p>
   * 推送机器人任务执行日志
   * </p>
   */
  public void pushDeviceTaskEvent(String productKey, String deviceName, Date reportTime, String action) {
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(reportTime);
    event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
    event.setProductKey(productKey);
    event.setDeviceName(deviceName);
    MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.ROBOT_DEVICE_TASK_LOG;
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(deviceName);
    eventBody.setTime(reportTime);
    eventBody.setUserName("");
    eventBody.setAction(action);
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    event.setEventNo(trackingEventEnum.getCode());
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), deviceName, event);
  }

  /**
   * <p>
   * 推送数采车场景采集事件
   * </p>
   */
  public void pushTaskCollectionEvent(String productKey, String deviceName, Date reportTime) {
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(reportTime);
    String traceId = "DATA_COLLECTION_" + DateUtil.formatDate(reportTime) + "_" + deviceName;
    event.setTraceId(traceId);
    event.setProductKey(productKey);
    event.setDeviceName(deviceName);
    MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.DATA_COLLECTION_TASK_REPORT;
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(deviceName);
    eventBody.setTime(reportTime);
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    event.setEventNo(trackingEventEnum.getCode());
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), deviceName, event);
  }

  /**
   * <p>
   * 推送数采车场景采集事件
   * </p>
   */
  public void pushSceneCollectionEvent(String productKey, String deviceName, Date reportTime) {
    ShadowTrackingEventVO event = new ShadowTrackingEventVO();
    event.setReportTime(reportTime);
    event.setTraceId(String.valueOf(RequestIdUtils.getRequestId()));
    String traceId = "DATA_COLLECTION_" + DateUtil.formatDate(reportTime) + "_" + deviceName;
    event.setParentTraceId(traceId);
    event.setProductKey(productKey);
    event.setDeviceName(deviceName);
    MonitorTrackingEventEnum trackingEventEnum = MonitorTrackingEventEnum.DATA_COLLECTION_SCENE_REPORT;
    MonitorEventVO eventBody = new MonitorEventVO();
    eventBody.setVehicleName(deviceName);
    eventBody.setTime(reportTime);
    event.setEventBody(JsonUtils.writeValueAsString(eventBody));
    event.setEventNo(trackingEventEnum.getCode());
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SHADOW_TRACKING_EVENT.getTopic(), deviceName, event);
  }
}