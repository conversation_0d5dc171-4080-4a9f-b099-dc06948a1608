package com.jdx.rover.monitor.service.mobile;

import com.jdx.rover.metadata.domain.dto.box.BoxGridInfoDto;
import com.jdx.rover.monitor.dto.mobile.box.GetBoxGridDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleBoxManager;
import com.jdx.rover.monitor.vo.mobile.box.VehicleOpenBoxVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/16 15:03
 * @description 车辆货箱Service
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleBoxService {

    /**
     * MetadataVehicleApiManager
     */
    private final MetadataVehicleApiManager metadataVehicleApiManager;

    /**
     * VehicleBoxManager
     */
    private final VehicleBoxManager vehicleBoxManager;

    /**
     * 获取货箱格口列表
     *
     * @param vehicleName vehicleName
     * @return GetBoxGridDTO
     */
    public GetBoxGridDTO getBoxGrid(String vehicleName) {
        VehicleBasicDTO vehicle = metadataVehicleApiManager.getByName(vehicleName);
        if (null == vehicle) {
            return null;
        }
        BoxGridInfoDto boxGridInfoDto = vehicleBoxManager.getBoxById(vehicle.getBoxId());
        return new GetBoxGridDTO(boxGridInfoDto);
    }

    /**
     * 开箱
     *
     * @param vehicleOpenBoxVO vehicleOpenBoxVO
     */
    public void openBox(VehicleOpenBoxVO vehicleOpenBoxVO) {
        vehicleBoxManager.openBox(vehicleOpenBoxVO.getVehicleName(), vehicleOpenBoxVO.getGridNoList());
    }
}
