/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.manager.report.ReportBootManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import com.jdx.rover.server.api.domain.enums.report.BootStatusEnum;
import jdx.rover.ota.proto.OtaDataDto;
import jdx.rover.ota.proto.OtaHeader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 监控ota升级服务
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MonitorOtaService {

    /**
     * mqtt消息发送服务
     */
    private final MqttManager mqttManager;

    /**
     * 启动上报Manager类
     */
    private final ReportBootManager reportBootManager;

    /**
     * 实时信息
     */
    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    /**
     * 地图转为静默下载
     */
    public HttpResult<Void> mapSilentDownload(String vehicleName) {
        log.info("MonitorOtaService.mapSilentDownload地图转为静默下载={}", vehicleName);
        if (StringUtils.isBlank(vehicleName)) {
            return HttpResult.error(HttpCodeEnum.BAD_REQUEST.getValue(), "车号不能为空!");
        }
        VehicleRealtimeInfoDTO realtimeInfo = vehicleRealtimeRepository.get(vehicleName);
        if (Objects.isNull(realtimeInfo)) {
            return HttpResult.error("车辆实时信息不存在!");
        }
        if (!Objects.equals(VehicleStateEnum.INITIALIZATION.getVehicleState(), realtimeInfo.getVehicleState())) {
            return HttpResult.error("车辆不是初始化,不能转为静默下载!");
        }

        String mapUpdaterStatus = reportBootManager.getMapUpdaterStatus(vehicleName);
        if (!StringUtils.equalsAny(mapUpdaterStatus, BootStatusEnum.WAIT_START.name(), BootStatusEnum.STARTING.name())) {
            return HttpResult.error("地图模块不是启动中,不能转为静默下载!");
        }

        OtaDataDto.OtaDataDTO dto = buildMapSilentDownload(vehicleName);
        String topic = "ota/updater_delay/server/" + vehicleName;
        mqttManager.sendMqttBytes(topic, dto.toByteArray());
        log.info("MonitorOtaService.mapSilentDownload地图转为静默下载操作完成={}", vehicleName);
        return HttpResult.success();
    }

    /**
     * 构建地图转为静默下载消息
     */
    public static OtaDataDto.OtaDataDTO buildMapSilentDownload(String vehicleName) {
        OtaHeader.RequestHeader.Builder requestHeaderBuilder = OtaHeader.RequestHeader.newBuilder();
        requestHeaderBuilder.setRequestId(RequestIdUtils.getRequestId());
        requestHeaderBuilder.setRequestTime(System.currentTimeMillis() * NumberConstant.MILLION);
        requestHeaderBuilder.setClientName(vehicleName);
        requestHeaderBuilder.setNeedResponse(false);
        requestHeaderBuilder.setRetry(false);

        OtaDataDto.OtaDataDTO.Builder dtoBuilder = OtaDataDto.OtaDataDTO.newBuilder();
        dtoBuilder.setRequestHeader(requestHeaderBuilder);
        dtoBuilder.addMessageType(OtaDataDto.MessageType.MAP_SILENT_DOWNLOAD);

        return dtoBuilder.build();
    }
}

