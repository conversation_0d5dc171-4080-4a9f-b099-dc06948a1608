package com.jdx.rover.monitor.service.listener.redis;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.issue.IssueChangeDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;

/**
 * 工单信息更新推送
 *
 * <AUTHOR>
 */
@Slf4j
public class IssueRecordChangeMessageListener implements MessageListener<String> {
  private Session session;
  private String userName;

  public IssueRecordChangeMessageListener(Session session, String userName) {
    this.session = session;
    this.userName = userName;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    log.info("Send issue change info {}", msg);
    IssueChangeDTO dto = JsonUtils.readValue(msg, IssueChangeDTO.class);
    if (dto == null) {
      return;
    }
    UserVehicleNameRepository userVehicleNameRepository = SpringUtil.getBean(UserVehicleNameRepository.class);
    boolean isContain = userVehicleNameRepository.isContainVehicleName(userName, dto.getVehicleName());
    if (!isContain) {
      return;
    }
    synchronized (session) {
      try {
        WsResult result = WsResult.success(WebsocketEventTypeEnum.ISSUE_UPDATE_EVENT.getValue(), dto);
        this.session.getBasicRemote().sendText(JsonUtils.writeValueAsString(result));
      } catch (Exception e) {
        log.error("Send issue change exception", e);
      }
    }

  }
}
