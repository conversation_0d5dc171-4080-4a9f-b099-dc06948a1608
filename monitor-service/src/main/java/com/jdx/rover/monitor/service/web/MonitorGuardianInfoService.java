/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.ModuleEnum;
import com.jdx.rover.metadata.api.domain.dto.technical.ErrorCodeTranslateInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.enums.AlarmSourceEnum;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import com.jdx.rover.monitor.bo.vehicle.VehicleAbnormalBootBO;
import com.jdx.rover.monitor.dto.GuardianAbnormalBootModuleDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmDetailInfoDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmInfoDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleExceptionInfoDTO;
import com.jdx.rover.monitor.dto.MonitorVehicleAbnormalBootDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.manager.abnormal.VehicleAbnormalBootManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleBootManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleWheelInfoManager;
import com.jdx.rover.monitor.po.GuardianVehicleAlarmInfo;
import com.jdx.rover.monitor.po.GuardianVehicleExceptionInfo;
import com.jdx.rover.monitor.po.VehicleAbnormalBoot;
import com.jdx.rover.monitor.repository.mapper.AssistJiraRecordMapper;
import com.jdx.rover.monitor.repository.mapper.GuardianVehicleAlarmInfoMapper;
import com.jdx.rover.monitor.repository.mapper.GuardianVehicleExceptionInfoMapper;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.metadata.ErrorCodeTranslateRepository;
import com.jdx.rover.monitor.repository.util.PageUtils;
import com.jdx.rover.monitor.search.MonitorGuardianInfoSearch;
import com.jdx.rover.monitor.service.config.AssistJiraRecordProperties;
import com.jdx.rover.monitor.service.tencent.TencentMapService;
import com.jdx.rover.monitor.vo.GuardianVehicleAlarmInfoVO;
import com.jdx.rover.server.api.domain.dto.guardian.ModuleVersionInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.api.jsf.service.vehicle.ServerVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a monitor guardian info service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class MonitorGuardianInfoService {

  @Autowired
  private AssistJiraRecordMapper assistJiraRecordMapper;

  @Autowired
  private GuardianVehicleExceptionInfoMapper guardianVehicleExceptionInfoMapper;

  @Autowired
  private GuardianVehicleAlarmInfoMapper guardianVehicleAlarmInfoMapper;

  @Autowired
  private VehicleBootManager vehicleBootManager;

  @Autowired
  private VehicleAbnormalBootManager vehicleAbnormalBootManager;

  @Autowired
  private VehicleWheelInfoManager vehicleWheelInfoManager;

  @Autowired
  private ServerVehicleService serverVehicleJsfService;

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private AssistJiraRecordProperties assistJiraRecordProperties;

  @Autowired
  private VehicleBasicRepository vehicleBasicRepository;

  @Autowired
  private ErrorCodeTranslateRepository errorCodeTranslateRepository;

  @Autowired
  private TencentMapService tencentMapService;

  /**
   * <p>
   * assist jira record topic part5 emergency stop.
   * </p>
   */
  private static final String ASSIST_JIRA_TOPIC_PART5_ESTOP = "离障碍物太近";

  /**
   * <p>
   * assist jira record topic part5 stuck.
   * </p>
   */
  private static final String ASSIST_JIRA_TOPIC_PART5_STUCK = "车辆被困";

  /**
   * 过杆遇阻告警描述
   */
  private static final String ASSIST_JIRA_STOP_CROSSING_BAR = "位于%s的升降杆未及时抬起";

 /**
   * <p>
   * Search guardian alarm info by search entity.
   * </p>
   *
   * @param guardianInfoSearch The search entity for monitor alarm info.
   */
  public HttpResult<PageDTO<GuardianVehicleAlarmInfoDTO>> getAlarmInfoList(PageVO pageVo, MonitorGuardianInfoSearch guardianInfoSearch) {
    if (pageVo == null || pageVo.getPageNum()==null || pageVo.getPageSize() == null) {
      pageVo = new PageVO();
    }
    LambdaQueryWrapper<GuardianVehicleAlarmInfo> wrapper = new LambdaQueryWrapper<>();
    if (guardianInfoSearch.getVehicleName() != null) {
      wrapper.eq(GuardianVehicleAlarmInfo:: getVehicleName, guardianInfoSearch.getVehicleName());
    }
    if (guardianInfoSearch.getCityName() != null) {
      wrapper.eq(GuardianVehicleAlarmInfo:: getCityName, guardianInfoSearch.getCityName());
    }
    if (guardianInfoSearch.getStationName()!= null) {
      wrapper.eq(GuardianVehicleAlarmInfo:: getStationName, guardianInfoSearch.getStationName());
    }
    if (CollectionUtils.isNotEmpty(guardianInfoSearch.getFilterAlarm())) {
      wrapper.in(GuardianVehicleAlarmInfo:: getAlarmEvent, guardianInfoSearch.getFilterAlarm());
    }
    if (guardianInfoSearch.getStartTime() != null && guardianInfoSearch.getEndTime() != null) {
      wrapper.ge(GuardianVehicleAlarmInfo:: getOperateTimestamp, guardianInfoSearch.getStartTime());
      wrapper.le(GuardianVehicleAlarmInfo:: getOperateTimestamp, guardianInfoSearch.getEndTime());
    }
    wrapper.in(CollectionUtils.isNotEmpty(guardianInfoSearch.getFilterSource()), GuardianVehicleAlarmInfo::getAlarmSource, guardianInfoSearch.getFilterSource());
    wrapper.orderByDesc(GuardianVehicleAlarmInfo::getOperateTimestamp);

    IPage<GuardianVehicleAlarmInfo> iPage = PageUtils.toMpPage(pageVo,GuardianVehicleAlarmInfo.class);
    IPage<GuardianVehicleAlarmInfo> operationLogs = guardianVehicleAlarmInfoMapper.selectPage(iPage, wrapper);
    PageDTO<GuardianVehicleAlarmInfoDTO> pageInfo = new PageDTO<>();
    pageInfo.setTotal(operationLogs.getTotal());
    pageInfo.setPages((int) operationLogs.getPages());
    pageInfo.setPageNum((int)operationLogs.getCurrent());
    pageInfo.setPageSize((int)operationLogs.getSize());
    pageInfo.setList(operationLogs.getRecords().stream().map(GuardianVehicleAlarmInfo :: toGuardianAlarmInfoDto).collect(Collectors.toList()));
    return HttpResult.success(pageInfo);
  }

  /**
   * <p>
   * Search guardian exception info by search entity.
   * </p>
   *
   * @param guardianInfoSearch The search entity for monitor exception info.
   */
  public HttpResult<PageDTO<GuardianVehicleExceptionInfoDTO>> getExceptionInfoList(PageVO pageVo, MonitorGuardianInfoSearch guardianInfoSearch) {
    if (pageVo == null || pageVo.getPageSize() == null || pageVo.getPageNum() == null) {
      pageVo = new PageVO();
    }
    if (guardianInfoSearch == null) {
      guardianInfoSearch = new MonitorGuardianInfoSearch();
    }
    LambdaQueryWrapper<GuardianVehicleExceptionInfo> wrapper = new LambdaQueryWrapper<>();
    if (guardianInfoSearch.getVehicleName() != null) {
      wrapper.eq(GuardianVehicleExceptionInfo:: getVehicleName, guardianInfoSearch.getVehicleName());
    }
    if (guardianInfoSearch.getCityName() != null) {
      wrapper.eq(GuardianVehicleExceptionInfo:: getCityName, guardianInfoSearch.getCityName());
    }
    if (guardianInfoSearch.getStationName()!= null) {
      wrapper.eq(GuardianVehicleExceptionInfo:: getStationName, guardianInfoSearch.getStationName());
    }
    if (guardianInfoSearch.getStartTime() != null && guardianInfoSearch.getEndTime() != null) {
      wrapper.ge(GuardianVehicleExceptionInfo:: getOperateTimestamp, guardianInfoSearch.getStartTime());
      wrapper.le(GuardianVehicleExceptionInfo:: getOperateTimestamp, guardianInfoSearch.getEndTime());
    }
    wrapper.orderByDesc(GuardianVehicleExceptionInfo::getOperateTimestamp);
    IPage<GuardianVehicleExceptionInfo> iPage = PageUtils.toMpPage(pageVo,GuardianVehicleExceptionInfo.class);
    IPage<GuardianVehicleExceptionInfo> operationLogs = guardianVehicleExceptionInfoMapper.selectPage(iPage, wrapper);
    PageDTO<GuardianVehicleExceptionInfoDTO> pageInfo = new PageDTO<>();
    pageInfo.setTotal(operationLogs.getTotal());
    pageInfo.setPageNum((int)operationLogs.getCurrent());
    pageInfo.setPageSize((int)operationLogs.getSize());
    pageInfo.setList(operationLogs.getRecords().stream().map(GuardianVehicleExceptionInfo:: toGuardianExceptionInfoDto).collect(Collectors.toList()));
    return HttpResult.success(pageInfo);
  }

  /**
   * <p>
   * Update vehicle alarm info.
   * </p>
   * 
   */
  public HttpResult update(GuardianVehicleAlarmInfoVO vehicleAlarmInfoVo) {
    ParameterCheckUtility.checkNotNull(vehicleAlarmInfoVo, "GuardianVehicleAlarmInfoVO");
    ParameterCheckUtility.checkNotNull(vehicleAlarmInfoVo.getId(), "GuardianVehicleAlarmInfoVO#id");
    GuardianVehicleAlarmInfo alarmInfoPo = new GuardianVehicleAlarmInfo();
    alarmInfoPo.setId(vehicleAlarmInfoVo.getId());
    if (vehicleAlarmInfoVo.getComponentId() != null) {
      alarmInfoPo.setComponentId(vehicleAlarmInfoVo.getComponentId());
    }
    if (vehicleAlarmInfoVo.getComponentName() != null) {
      alarmInfoPo.setComponentName(vehicleAlarmInfoVo.getComponentName());
    }
    if (vehicleAlarmInfoVo.getDescription() != null) {
      alarmInfoPo.setDescription(vehicleAlarmInfoVo.getDescription());
    }
    guardianVehicleAlarmInfoMapper.updateById(alarmInfoPo);
    return HttpResult.success();
  }

  /**
   * <p>
   * 人工告警增加描述信息
   * </p>
   *
   */
  public void updateByAlarmNumber(GuardianVehicleAlarmInfo vehicleAlarmInfo) {
    LambdaUpdateWrapper<GuardianVehicleAlarmInfo> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper.set(StringUtils.isNotBlank(vehicleAlarmInfo.getVehicleName()),
            GuardianVehicleAlarmInfo :: getVehicleName, vehicleAlarmInfo.getVehicleName());
    updateWrapper.eq(StringUtils.isNotBlank(vehicleAlarmInfo.getAlarmNumber()),
            GuardianVehicleAlarmInfo :: getAlarmNumber, vehicleAlarmInfo.getAlarmNumber());
    if (vehicleAlarmInfo.getDescription() != null) {
      updateWrapper.set(StringUtils.isNotBlank(vehicleAlarmInfo.getDescription()),GuardianVehicleAlarmInfo :: getDescription, vehicleAlarmInfo.getDescription());
    }
    guardianVehicleAlarmInfoMapper.update(updateWrapper);
  }

  /**
   * <p>
   * 告警跟工单关联
   * </p>
   *
   */
  public void updateAlarmIssueNo(String vehicleName, List<String> alarmNumberList, String issueNumber) {
    LambdaUpdateWrapper<GuardianVehicleAlarmInfo> updateWrapper = new LambdaUpdateWrapper();
    updateWrapper.isNull(GuardianVehicleAlarmInfo::getIssueNumber);
    updateWrapper.eq(StringUtils.isNotBlank(vehicleName), GuardianVehicleAlarmInfo::getVehicleName, vehicleName);
    if (CollectionUtils.isNotEmpty(alarmNumberList)) {
      updateWrapper.in(GuardianVehicleAlarmInfo::getAlarmNumber, alarmNumberList);
    }
    updateWrapper.orderByDesc(GuardianVehicleAlarmInfo::getId);
    updateWrapper.set(GuardianVehicleAlarmInfo::getIssueNumber, issueNumber);
    Integer result = guardianVehicleAlarmInfoMapper.update(null, updateWrapper);
  }

  /**
   * <p>
   * Update vehicle alarm info.
   * </p>
   *
   */
  public void addVehicleAlarmRecord(String vehicleName, List<AlarmInfoDTO> vehicleAlarmEventDtoList) {
    try {
      List<AlarmInfoDTO> recordAlarmList = vehicleAlarmEventDtoList.stream()
              .filter(alarmEvent -> !Objects.equals(alarmEvent.getAlarmType(), VehicleAlarmEnum.LOW_BATTERY)).collect(Collectors.toList());
      if (recordAlarmList.isEmpty()) {
        return;
      }
      VehicleBasicDTO vehicleBasicDto = Optional.ofNullable(vehicleBasicRepository.get(vehicleName)).orElse(new VehicleBasicDTO());
      String stationName = Optional.ofNullable(vehicleBasicDto.getStationName()).orElse("");
      String cityName = Optional.ofNullable(vehicleBasicDto.getCityName()).orElse("");
      HttpResult<Map<String, ModuleVersionInfoDTO>> result = serverVehicleJsfService.getVehicleOtaVersionInfo(vehicleName);
      String version = assistJiraRecordProperties.getVersionValue();
      if (HttpResult.isSuccess(result)) {
        Map<String, ModuleVersionInfoDTO> data = result.getData();
        if (data != null) {
          version = Optional.ofNullable(data.get(OtaMoudleEnum.ROVER.getName())).map(infoDto -> infoDto.getCurVersion()).orElse("");
        }
      }
      final String vehicleVersion = version;
      recordAlarmList.forEach(alarmEvent -> {
        GuardianVehicleExceptionInfo exceptionInfo = new GuardianVehicleExceptionInfo();
        if (StringUtils.isNotBlank(alarmEvent.getErrorCode())) {
          exceptionInfo.setErrorCode(alarmEvent.getErrorCode());
          exceptionInfo.setErrorLevel(alarmEvent.getErrorLevel());
          exceptionInfo.setErrorMessage(StringUtils.substring(alarmEvent.getErrorMessage(), 0, 1000));
          exceptionInfo.setOperateTimestamp(alarmEvent.getStartTime());
          exceptionInfo.setVehicleName(vehicleName);
          exceptionInfo.setCityName(cityName);
          exceptionInfo.setStationName(stationName);
          guardianVehicleExceptionInfoMapper.insert(exceptionInfo);
        }
        VehicleAlarmEnum vehicleAlarmEnum = VehicleAlarmEnum.of(alarmEvent.getAlarmType());
        GuardianVehicleAlarmInfo alarmInfoPo = new GuardianVehicleAlarmInfo();
        alarmInfoPo.setComponentId(ModuleEnum.DISTRIBUTED.getValue());
        alarmInfoPo.setComponentName(ModuleEnum.DISTRIBUTED.getName());
        alarmInfoPo.setVehicleVersion(vehicleVersion);
        alarmInfoPo.setAlarmSource(vehicleAlarmEnum.getSource());
        VehicleRealtimeInfoDTO realtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
        StringBuilder topic = new StringBuilder();
        StringBuilder description = new StringBuilder();
        if (Objects.equals(vehicleAlarmEnum, VehicleAlarmEnum.VEHICLE_STOP_GUARDIAN)) {
          topic.append(ASSIST_JIRA_TOPIC_PART5_ESTOP);
          description.append(ASSIST_JIRA_TOPIC_PART5_ESTOP);
        } else if (Objects.equals(vehicleAlarmEnum, VehicleAlarmEnum.VEHICLE_STOP_TIMEOUT)) {
          topic.append(ASSIST_JIRA_TOPIC_PART5_STUCK);
          description.append(ASSIST_JIRA_TOPIC_PART5_STUCK);
        } else if (Objects.equals(vehicleAlarmEnum, VehicleAlarmEnum.LOW_PRESSURE)) {
          topic.append(AlarmTypeEnum.LOW_PRESSURE.getTitle());
          vehicleWheelInfoManager.getVehicleWheelInfo(vehicleName).stream().forEach(wheel ->
                  description.append(wheel.getWheelPosition()).append(":").append(wheel.getPressureStatus()).append(" ").append(wheel.getPressure()));
        } else if (Objects.equals(vehicleAlarmEnum, VehicleAlarmEnum.PASS_NO_SIGNAL_INTERSECTION)) {
          topic.append(AlarmTypeEnum.PASS_NO_SIGNAL_INTERSECTION.getTitle());
          description.append("逆地址：");
          String address = tencentMapService.wgs84ToAddress(realtimeInfoDto.getLat(), realtimeInfoDto.getLon());
          if (StringUtils.isBlank(address)) {
            description.append(realtimeInfoDto.getLat()).append(",").append(realtimeInfoDto.getLon());
          } else {
            description.append(address);
          }
        } else if (StringUtils.equalsAny(alarmEvent.getAlarmType(), VehicleAlarmEnum.BOOT_FAIL.getValue(),
                VehicleAlarmEnum.BOOT_TIMEOUT.getValue(), VehicleAlarmEnum.BOOT_ABNORMAL_FAIL.getValue())) {
          topic.append(vehicleAlarmEnum.getName());
          VehicleAbnormalBootBO vehicleAbnormalBootBo = vehicleBootManager.getAbnormalModuleBootList(vehicleName);
          if (CollectionUtils.isNotEmpty(vehicleAbnormalBootBo.getAbnormalModuleBootList())) {
            String bootUuid = vehicleAbnormalBootBo.getAbnormalModuleBootList().get(0).getBootUuid();
            Integer bootId = vehicleAbnormalBootBo.getAbnormalModuleBootList().get(0).getBootId();
            List<VehicleAbnormalBoot> currentAbnormalBoot = vehicleAbnormalBootManager.listAbnormalWithBootUuid(bootUuid,bootId);
            if (CollectionUtils.isEmpty(currentAbnormalBoot)) {
              vehicleAbnormalBootManager.saveBatch(vehicleAbnormalBootBo.getAbnormalModuleBootList());
            }
            vehicleAbnormalBootBo.getAbnormalModuleBootList().stream().filter(moduleBoot -> !Objects.isNull(moduleBoot.getErrorCode())).forEach(moduleBoot -> {
              description.append(moduleBoot.getErrorCode() + "," + moduleBoot.getErrorMessage());
              alarmInfoPo.setBootUuid(moduleBoot.getBootUuid());
              alarmInfoPo.setBootId(moduleBoot.getBootId());
            });
          }
        } else if (Objects.equals(vehicleAlarmEnum, VehicleAlarmEnum.GATE_STUCK)) {
          topic.append(VehicleAlarmEnum.GATE_STUCK.getName());
          String address = tencentMapService.wgs84ToAddress(realtimeInfoDto.getLat(), realtimeInfoDto.getLon());
          if (StringUtils.isBlank(address)) {
            address = realtimeInfoDto.getLat() + "," + realtimeInfoDto.getLon();
          }
          description.append(String.format(ASSIST_JIRA_STOP_CROSSING_BAR, address));
        } else if (Objects.equals(vehicleAlarmEnum, VehicleAlarmEnum.MANUAL_REPORT)) {
          topic.append(VehicleAlarmEnum.MANUAL_REPORT.getName());
          description.append(alarmEvent.getErrorMessage());
          alarmInfoPo.setAlarmSource(AlarmSourceEnum.MANUAL_ALARM.getSource());
        } else if (Objects.equals(vehicleAlarmEnum, VehicleAlarmEnum.PRESSURE_SHARP_DECREASE)) {
          topic.append(VehicleAlarmEnum.PRESSURE_SHARP_DECREASE.getName());
          description.append(alarmEvent.getErrorMessage());
        } else {
          topic.append(Optional.ofNullable(vehicleAlarmEnum).map(alarm -> alarm.getName()).orElse(""));
          if (StringUtils.isNotBlank(alarmEvent.getErrorCode())) {
            description.append(alarmEvent.getErrorCode()).append(",").append(StringUtils.substring(alarmEvent.getErrorMessage(), 0, 500));
          }
        }
        alarmInfoPo.setTopic(topic.toString());
        alarmInfoPo.setDescription(description.toString());
        alarmInfoPo.setAlarmEvent(alarmEvent.getAlarmType());
        alarmInfoPo.setAlarmNumber(alarmEvent.getAlarmNumber());
        alarmInfoPo.setStationName(stationName);
        alarmInfoPo.setCityName(cityName);
        alarmInfoPo.setVehicleName(vehicleName);
        alarmInfoPo.setOperateTimestamp(alarmEvent.getStartTime());
        alarmInfoPo.setSystemState(Optional.ofNullable(realtimeInfoDto).map(info -> info.getSystemState()).orElse(""));
        alarmInfoPo.setUseCase(Optional.ofNullable(vehicleBasicDto.getOwnerUseCase()).orElse(""));
        if (exceptionInfo.getId() != null) {
          alarmInfoPo.setExceptionInfoId(exceptionInfo.getId());
        }
        guardianVehicleAlarmInfoMapper.insert(alarmInfoPo);
      });
    } catch(Exception e) {
      log.error("Save vehicle alarm record error", e);
    }
  }

  /**
   * <p>
   * Search guardian exception info by key.
   * </p>
   *
   */
  public HttpResult<GuardianVehicleExceptionInfoDTO> getExceptionInfoById(Integer id) {
    GuardianVehicleExceptionInfo exceptionInfoPo = guardianVehicleExceptionInfoMapper.selectById(id);
    if (exceptionInfoPo == null) {
      return HttpResult.success(new GuardianVehicleExceptionInfoDTO());
    }
    GuardianVehicleExceptionInfoDTO guardianVehicleExceptionInfo = exceptionInfoPo.toGuardianExceptionInfoDto();
    if(StringUtils.isNotBlank(guardianVehicleExceptionInfo.getErrorMessage())) {
      List<ErrorCodeTranslateInfoDTO> translateList = errorCodeTranslateRepository.get(guardianVehicleExceptionInfo.getErrorCode());
      if (CollectionUtil.isNotEmpty(translateList)) {
        Optional<ErrorCodeTranslateInfoDTO> optional = translateList.stream().filter(translate -> guardianVehicleExceptionInfo.getErrorMessage().contains(translate.getErrorMessage())).findFirst();
        if (optional.isPresent()) {
          guardianVehicleExceptionInfo.setTranslateMessage(optional.get().getErrorTranslate());
        }
      }
    }
    return HttpResult.success(guardianVehicleExceptionInfo);
  }

  /**
   * <p>
   * Search guardian wheel info by key.
   * </p>
   * 
   * @param vehicleName The search entity for wheel info.
   */
  public HttpResult getVehicleWheelInfo(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    return serverVehicleJsfService.getWheelInfo(vehicleName);
  }

  /**
   * <p>
   * Search guardian lauch state info by key.
   * </p>
   * 
   * @param vehicleName The search entity for wheel info.
   */
  public HttpResult<VehicleLauchStateInfoDTO> getVehiclelauchStateInfo(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    return serverVehicleJsfService.getVehicleStartInitStateInfo(vehicleName);
  }

  public HttpResult<GuardianVehicleAlarmDetailInfoDTO> getAlarmDetailInfoById(Integer id) {
    GuardianVehicleAlarmInfo vehicleAlarmInfo = guardianVehicleAlarmInfoMapper.selectById(id);
    if (Objects.isNull(vehicleAlarmInfo)) {
     return HttpResult.success();
    }
    GuardianVehicleAlarmDetailInfoDTO alarmDetailInfo = new GuardianVehicleAlarmDetailInfoDTO();
    alarmDetailInfo.setId(id);
    alarmDetailInfo.setDescription(vehicleAlarmInfo.getDescription());
    alarmDetailInfo.setOperateTimestamp(vehicleAlarmInfo.getOperateTimestamp());
    alarmDetailInfo.setStationName(vehicleAlarmInfo.getStationName());
    alarmDetailInfo.setVehicleName(vehicleAlarmInfo.getVehicleName());
    AlarmTypeEnum alarmTypeEnum = AlarmTypeEnum.of(vehicleAlarmInfo.getAlarmEvent());
    if (!Objects.isNull(alarmTypeEnum)) {
      alarmDetailInfo.setAlarmEvent(alarmTypeEnum.getTitle());
      if (StringUtils.isNotBlank(vehicleAlarmInfo.getBootUuid()) && !Objects.isNull(vehicleAlarmInfo.getBootId())) {
        List<VehicleAbnormalBoot> vehicleAbnormalBootList = vehicleAbnormalBootManager.listAbnormalWithBootUuid(vehicleAlarmInfo.getBootUuid(), vehicleAlarmInfo.getBootId());
        if (CollectionUtils.isNotEmpty(vehicleAbnormalBootList)) {
          Map<Integer, List<VehicleAbnormalBoot>> bootMap = vehicleAbnormalBootList.stream().sorted(new Comparator<VehicleAbnormalBoot>() {
            @Override
            public int compare(VehicleAbnormalBoot o1, VehicleAbnormalBoot o2) {
              return o1.getBootCount() - o2.getBootCount();
            }
          }).collect(Collectors.groupingBy(VehicleAbnormalBoot::getBootCount));
          List<GuardianAbnormalBootModuleDTO> vehicleAbnormalModuleBootList = new ArrayList<>();
          for (Map.Entry<Integer, List<VehicleAbnormalBoot>> entry : bootMap.entrySet()) {
            GuardianAbnormalBootModuleDTO abnormalBootModuleDto = new GuardianAbnormalBootModuleDTO();
            abnormalBootModuleDto.setBootCount(entry.getKey());
            abnormalBootModuleDto.setBootDetail(BeanUtil.copyToList(entry.getValue(), MonitorVehicleAbnormalBootDTO.class));
            vehicleAbnormalModuleBootList.add(abnormalBootModuleDto);
          }
          alarmDetailInfo.setBootDetail(vehicleAbnormalModuleBootList);
        }
      }
    }
    return HttpResult.success(alarmDetailInfo);
  }

  /**
   * 消失的告警，增加结束时间
   * @param vehicleName
   * @param disappearAlarmList
   */
  public void endVehicleAlarmRecord(String vehicleName, List<AlarmInfoDTO> disappearAlarmList, Date endDate) {
    List<String> alarmNumberList =
            disappearAlarmList.stream().map(AlarmInfoDTO::getAlarmNumber).collect(Collectors.toList());
    LambdaUpdateWrapper<GuardianVehicleAlarmInfo> updateWrapper = new LambdaUpdateWrapper();
    updateWrapper.eq(StringUtils.isNotBlank(vehicleName), GuardianVehicleAlarmInfo::getVehicleName, vehicleName);
    Date beginTime = DateUtil.beginOfDay(endDate).toJdkDate();
    Date endTime = DateUtil.endOfDay(endDate).toJdkDate();
    updateWrapper.isNull(GuardianVehicleAlarmInfo::getEndTimestamp);
    updateWrapper.le(GuardianVehicleAlarmInfo::getOperateTimestamp,endTime);
    updateWrapper.ge(GuardianVehicleAlarmInfo::getOperateTimestamp,beginTime);
    if (CollectionUtils.isNotEmpty(disappearAlarmList)) {
      updateWrapper.in(GuardianVehicleAlarmInfo::getAlarmNumber, alarmNumberList);
    }
    updateWrapper.orderByDesc(GuardianVehicleAlarmInfo::getId);
    updateWrapper.set(GuardianVehicleAlarmInfo::getEndTimestamp, endDate);
    Integer result = guardianVehicleAlarmInfoMapper.update(null, updateWrapper);
  }

}
