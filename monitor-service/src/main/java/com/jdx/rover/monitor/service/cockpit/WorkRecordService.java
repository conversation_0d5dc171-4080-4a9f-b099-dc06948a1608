/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.service.cockpit;

import com.jdx.rover.monitor.po.WorkRecord;
import com.jdx.rover.monitor.repository.mapper.WorkRecordMapper;
import com.jdx.rover.monitor.service.base.BaseService;
import org.springframework.stereotype.Service;

/**
 * 工作记录service类
 *
 * <AUTHOR>
 */
@Service
public class WorkRecordService extends BaseService<WorkRecordMapper, WorkRecord> {

}