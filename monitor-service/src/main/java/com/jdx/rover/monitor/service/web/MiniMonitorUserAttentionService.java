/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.entity.UserAttentionEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.ToggleStateEnum;
import com.jdx.rover.monitor.enums.UserMetaDataEnum;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.repository.redis.UserAttentionEventRepository;
import com.jdx.rover.monitor.repository.redis.UserAttentionStationRepository;
import com.jdx.rover.monitor.vo.MiniMonitorAttentionVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import com.jdx.rover.permission.domain.dto.basic.UserInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 监控运营端用户关注服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
@Slf4j
public class MiniMonitorUserAttentionService {

  @Resource
  private UserAttentionStationRepository userAttentionStationRepository;
  @Resource
  private UserAttentionEventRepository userAttentionEventRepository;
  @Resource
  private MetadataUserApiManager metadataUserApiManager;
  @Resource
  private MetadataStationApiManager metadataStationApiManager;

  /**
   * 增加用户关注type类型下的event事件
   * @param type
   * @param event
   * @param miniMonitorAttentionVo
   * @return
   */
  public HttpResult operateAttentionEvent(String type, String event, MiniMonitorAttentionVO miniMonitorAttentionVo) {
    String userName = LoginUtils.getUsername();
    if (Objects.isNull(userName)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(), MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
    }
    log.info("Received user {} operate {} attention event {}, {}", userName, type, event, JsonUtils.writeValueAsString(miniMonitorAttentionVo));
    if (StringUtils.equals(UserMetaDataEnum.STATION.getType(), type)) {
      Integer stationId = Integer.valueOf(miniMonitorAttentionVo.getKey());
      Map<Integer, StationBasicDTO> stationMap = metadataStationApiManager.getStationById(stationId);
      if (CollectionUtil.isEmpty(stationMap)) {
        return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getMessage());
      }
      UserExtendInfoDTO extendInfoDto = metadataUserApiManager.getUserExtendInfoByName(userName);
      if (Objects.isNull(extendInfoDto)) {
        return HttpResult.error(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(), MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
      }
      StationBasicDTO stationDetail = stationMap.get(stationId);
      if (StringUtils.equals(ToggleStateEnum.ON.getValue() ,miniMonitorAttentionVo.getState())) {
        UserAttentionEntity userAttentionEntity = new UserAttentionEntity();
        userAttentionEntity.setUserName(userName);
        userAttentionEntity.setPhone(extendInfoDto.getPhone());
        userAttentionStationRepository.set(stationId, event, userAttentionEntity);
        userAttentionEventRepository.set(event, userName, String.valueOf(stationId));
      } else if (StringUtils.equals(ToggleStateEnum.OFF.getValue() ,miniMonitorAttentionVo.getState())){
        UserInfoDTO userInfoDto = metadataUserApiManager.getUserInfoByPhone(stationDetail.getContact());
        if (Objects.isNull(userInfoDto)) {
          return HttpResult.error(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(), MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
        }
        if (StringUtils.equals(userName, userInfoDto.getUserName())) {
          return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_USER_ATTENTION.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_USER_ATTENTION.getMessage());
        }
        userAttentionStationRepository.delete(stationId, event, Lists.newArrayList(userName));
        userAttentionEventRepository.delete(event, userName, String.valueOf(stationId));
      }
    }
    return HttpResult.success();
  }


  /**
   * 获取用户关注type类型下的event事件
   * @return
   */
  public HttpResult listUserAttention() {
    String userName = LoginUtils.getUsername();
    if (Objects.isNull(userName)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(), MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
    }
    return HttpResult.success();
  }
}
