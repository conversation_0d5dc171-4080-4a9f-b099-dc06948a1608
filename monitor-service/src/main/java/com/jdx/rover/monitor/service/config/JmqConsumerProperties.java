/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * <p>
 * 监控消费JMQ主题
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "monitor-jmq-topic")
@Data
@Validated
public class JmqConsumerProperties {
  /**
   * <p>
   * 端云设备属性变化
   * </p>
   */
  private String transportPropertyChange = "transport_property_change_test";


  /**
   * <p>
   * 设备主数据属性变化
   * </p>
   */
  private String intelligentDeviceMessage = "intelligent_device_device_message_test";

}
