package com.jdx.rover.monitor.service.web;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.datacenter.domain.dto.warehouse.mileage.HistoryMileageQueryJsfDTO;
import com.jdx.rover.datacenter.domain.vo.warehouse.mileage.HistoryMileageQueryJsfVO;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.accident.AccidentDetailManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.datacenter.DataCenterVehicleMileageApiManager;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentDetail;
import com.jdx.rover.monitor.po.AccidentJdmeConfig;
import com.jdx.rover.monitor.repository.mapper.AccidentJdmeConfigMapper;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
 * 事故简报服务
 */
@Slf4j
@Service
public class MonitorAccidentReportService {

    @Autowired
    private AccidentJdmeConfigMapper accidentJdmeConfigMapper;

    @Autowired
    private AccidentManager accidentManager;

    @Autowired
    private AccidentDetailManager accidentDetailManager;

    @Autowired
    private AccidentJdmePushManager accidentJdmePushManager;

    @Autowired
    private DataCenterVehicleMileageApiManager dataCenterVehicleMileageApiManager;

    @Autowired
    private MetadataVehicleApiManager metadataVehicleApiManager;

    @Value("${accident.url:default}")
    private String accidentUrl;

    public void accidentDailyReportJob() {
        log.info("开始发送每日事故简报");
        // 加锁避免并发执行数据不一致
        String accidentBugSyncLock = RedisKeyEnum.ACCIDENT_DAILY_REPORT_LOCK.getValue();
        if (RedissonUtils.tryLock(accidentBugSyncLock)) {
            try {
                // 获取今天的 0 点时间
                Date startOfDay = DateUtil.beginOfDay(new Date());
                List<Accident> accidentList = accidentManager.lambdaQuery().isNotNull(Accident::getBugCode).gt(Accident::getAccidentReportTime, startOfDay).list();
                if (CollectionUtils.isEmpty(accidentList)) {
                    log.info("{}没有事故发生，不发送每日事故简报", startOfDay);
                    return ;
                }
                List<AccidentJdmeConfig> accidentJdmeConfigList = accidentJdmeConfigMapper.selectList(new LambdaQueryWrapper<>());
                AccidentJdmeConfig accidentJdmeConfig = accidentJdmeConfigList.get(0);
                int total = accidentList.size();
                int mediumHighRiskAccidentCount = 0;
                int notAnalyzedCount = 0;
                String accidentFollowErp = null;
                for (Accident accident : accidentList) {
                    AccidentDetail accidentDetail = accidentDetailManager.lambdaQuery().eq(AccidentDetail::getAccidentNo, accident.getAccidentNo()).one();
                    if (PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(accidentDetail.getTechnicalSupportAccidentLevel()) || PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(accidentDetail.getTechnicalSupportAccidentLevel())) {
                        mediumHighRiskAccidentCount++;
                    }
                    if (accident.getIsSafetyGroupEdit() == 0) {
                        notAnalyzedCount++;
                        if (accidentFollowErp == null) {
                            accidentFollowErp = accident.getAccidentOwner();
                        }
                    }
                }
                JSONObject cardData = buildCardData(total, mediumHighRiskAccidentCount, notAnalyzedCount, accidentFollowErp, accidentJdmeConfig);
                try {
                    accidentJdmePushManager.sendJUEMsg(cardData, "每日事故简报", accidentJdmeConfig.getDailyReportGroupId());
                } catch (Exception e) {
                    log.error("发送每日事故简报失败, exception:{}", JsonUtils.writeValueAsString(e));
                }
                log.info("发送每日事故简报成功");
            } finally {
                RedissonUtils.unLock(accidentBugSyncLock);
            }
        }
    }

    /**
     * 构建卡片数据
     * @param total
     * @param mediumHighRiskAccidentCount
     * @param notAnalyzedCount
     * @param accidentFollowErp
     * @return
     */
    public JSONObject buildCardData(int total, int mediumHighRiskAccidentCount, int notAnalyzedCount, String accidentFollowErp, AccidentJdmeConfig accidentJdmeConfig) {
        JSONObject root = new JSONObject();
        //构建标题头
        JSONObject header = buildHeader("【今日事故简报】");
        root.set("header", header);
        //构建元素
        JSONArray elements = JSONUtil.createArray();
        //构建分割线
        JSONObject hr = new JSONObject();
        hr.set("tag", "hr");
        elements.add(hr);
       //构建内容
        JSONObject meMd = buildMeMd(total, mediumHighRiskAccidentCount, notAnalyzedCount, accidentFollowErp);
        elements.add(meMd);
        //构建按钮
        JSONObject buttons = buildButtons(accidentJdmeConfig.getAccidentReportPin());
        elements.add(buttons);
        root.set("elements", elements);
        return root;
    }

    private JSONObject buildButtons(String erp) {
        JSONObject buttons = new JSONObject();
        buttons.set("tag", "buttons");
        buttons.set("layout", "row");
        JSONArray buttonArray = new JSONArray();
        JSONObject button = new JSONObject();
        button.set("enable", true);
        button.set("action", new JsonObject());
        JSONObject text = new JSONObject();
        text.set("content", "咨询人工");
        button.set("text", text);
        JSONObject href = new JSONObject();
        href.set("pc", "timline://chat/?topin=" + erp);
        href.set("mobile", "jdme://jm/biz/im/contact/details?mparam={\"erp\":\"" + erp + "\"}");
        href.set("pc_deeplink", "");
        button.set("href", href);
        button.set("type", "default");
        buttonArray.add(button);
        buttons.set("buttons", buttonArray);
        return buttons;
    }

    private JSONObject buildMeMd(int total, int mediumHighRiskAccidentCount, int notAnalyzedCount, String accidentFollowErp) {
        JSONObject meMd = new JSONObject();
        Date currentDate = new Date();
        // 格式化为 "M月d日"
        String formattedDate = DateUtil.format(currentDate, "M月d日");
        String content = String.format("%s(0点~22点)共发生<font color='red'>%s</font>起事故，<font color='red'>%s</font>起中高风险事故，", formattedDate, total, mediumHighRiskAccidentCount);
        //拼装不同情况文案
        if (notAnalyzedCount == 0) {
            content = content + "安全组均已进入事故分析。";
        } else {
            content = content + String.format("<font color='red'>%s</font>起安全组未做事故分析，请[%s](%s/ee)跟进。可点击[【事故管理】](%s)更新事故解决情况", notAnalyzedCount, accidentFollowErp, accidentFollowErp, accidentUrl);
        }
        meMd.set("tag", "me_md");
        meMd.set("content", content);
        JSONObject i18n = new JSONObject();
        meMd.set("i18n", i18n);
        i18n.set("en_us", content);
        i18n.set("zh_cn", content);
        return meMd;
    }

    private JSONObject buildHeader(String titleValue) {
        JSONObject header = new JSONObject();
        header.set("theme", "default");
        JSONObject title = new JSONObject();
        title.set("tag", "plain_text");
        title.set("content", titleValue);
        JSONObject i18n  = new JSONObject();
        title.set("i18n", i18n);
        i18n.set("en_us", titleValue);
        i18n.set("zh_cn", titleValue);
        header.set("title", title);
        return header;
    }

    public void accidentWeeklyReportJob() {
        log.info("开始发送每周事故简报");
        // 加锁避免并发执行数据不一致
        String accidentBugSyncLock = RedisKeyEnum.ACCIDENT_WEEKLY_REPORT_LOCK.getValue();
        if (RedissonUtils.tryLock(accidentBugSyncLock)) {
            try {
                Date today = new Date();
                Date lastFriday = DateUtil.offsetDay(DateUtil.beginOfWeek(today), -3); // 上周五是上周的第三天
                Date thisThursday = DateUtil.offsetDay(DateUtil.beginOfWeek(today), 3);
                Date thisFriday = DateUtil.offsetDay(DateUtil.beginOfWeek(today), 4); // 这周五是本周的第五天
                List<Accident> accidentList = accidentManager.lambdaQuery().isNotNull(Accident::getBugCode).between(Accident::getAccidentReportTime, lastFriday, thisFriday).list();
                List<AccidentJdmeConfig> accidentJdmeConfigList = accidentJdmeConfigMapper.selectList(new LambdaQueryWrapper<>());
                AccidentJdmeConfig accidentJdmeConfig = accidentJdmeConfigList.get(0);
                int total = accidentList.size();
                int mediumHighRiskAccidentCount = 0;
                for (Accident accident : accidentList) {
                    AccidentDetail accidentDetail = accidentDetailManager.lambdaQuery().eq(AccidentDetail::getAccidentNo, accident.getAccidentNo()).one();
                    if (PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(accidentDetail.getTechnicalSupportAccidentLevel()) || PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(accidentDetail.getTechnicalSupportAccidentLevel())) {
                        mediumHighRiskAccidentCount++;
                    }
                }
                //计算事故率以及里程数等数据
                HistoryMileageQueryJsfVO historyMileageQueryVo = new HistoryMileageQueryJsfVO();
                historyMileageQueryVo.setStartTime(lastFriday);
                historyMileageQueryVo.setEndTime(thisThursday);
                List<String> useCaseList =
                        Lists.newArrayList(VehicleOwnerUseCaseEnum.SOLUTION.getValue(), VehicleOwnerUseCaseEnum.OPEN.getValue(), VehicleOwnerUseCaseEnum.RD.getValue(), VehicleOwnerUseCaseEnum.TEST.getValue(), VehicleOwnerUseCaseEnum.FIX.getValue(), VehicleOwnerUseCaseEnum.HARDWARE.getValue());
                historyMileageQueryVo.setVehicleOwnerUseCaseList(useCaseList);
                List<HistoryMileageQueryJsfDTO> mileageQueryDTOList = dataCenterVehicleMileageApiManager.historyMileageQuery(historyMileageQueryVo);
                double totalMileage = mileageQueryDTOList.stream().mapToDouble(HistoryMileageQueryJsfDTO::getTotalMileage).sum() / 1000.0;
                double accidentRate = 0d;
                if (totalMileage > 0) {
                    accidentRate = mediumHighRiskAccidentCount * 10000 / totalMileage;
                }
                JSONObject cardData = buildWeeklyCardData(total, mediumHighRiskAccidentCount, totalMileage, accidentRate, accidentJdmeConfig);
                try {
                    accidentJdmePushManager.sendJUEMsg(cardData, "本周事故简报", accidentJdmeConfig.getWeeklyReportGroupId());
                } catch (Exception e) {
                    log.error("发送每周事故简报失败, exception:{}", JsonUtils.writeValueAsString(e));
                }
                log.info("发送每周事故简报完成");
            } finally {
                RedissonUtils.unLock(accidentBugSyncLock);
            }
        }
    }

    private JSONObject buildWeeklyCardData(int total, int mediumHighRiskAccidentCount, double totalMileage, double accidentRate, AccidentJdmeConfig accidentJdmeConfig) {
        JSONObject root = new JSONObject();
        //构建标题头
        JSONObject header = buildHeader("【本周事故简报】");
        root.set("header", header);
        //构建元素
        JSONArray elements = JSONUtil.createArray();
        //构建分割线
        JSONObject hr = new JSONObject();
        hr.set("tag", "hr");
        elements.add(hr);
        //构建内容
        JSONObject meMd = buildWeeklyMeMd(total,mediumHighRiskAccidentCount, String.format("%.2f", totalMileage), String.format("%.2f", accidentRate));
        elements.add(meMd);
        //构建按钮
        JSONObject buttons = buildButtons(accidentJdmeConfig.getAccidentReportPin());
        elements.add(buttons);
        root.set("elements", elements);
        return root;
    }

    private JSONObject buildWeeklyMeMd(int total, int mediumHighRiskAccidentCount, String mileage, String accidentRate) {
        JSONObject meMd = new JSONObject();
        Date today = new Date();
        Date lastFriday = DateUtil.offsetDay(DateUtil.beginOfWeek(today), -3); // 上周五是上周的第三天
        Date thisThursday = DateUtil.offsetDay(DateUtil.beginOfWeek(today), 3); // 这周四是本周的第四天
        // 格式化为 "M月d日至M月d日"
        String formattedDate = DateUtil.format(lastFriday, "M月d日") + "至" + DateUtil.format(thisThursday, "M月d日");
        String content = String.format("%s共发生<font color='red'>%s</font>起事故，<font color='red'>%s</font>起中高风险事故，行驶里程约%s公里，中高风险事故率%s起/万公里", formattedDate, total, mediumHighRiskAccidentCount, mileage, accidentRate);
        meMd.set("tag", "me_md");
        meMd.set("content", content);
        JSONObject i18n = new JSONObject();
        meMd.set("i18n", i18n);
        i18n.set("en_us", content);
        i18n.set("zh_cn", content);
        return meMd;
    }
}
