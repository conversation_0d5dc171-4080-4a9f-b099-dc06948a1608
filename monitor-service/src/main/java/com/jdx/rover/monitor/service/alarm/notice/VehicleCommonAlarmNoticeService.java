package com.jdx.rover.monitor.service.alarm.notice;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.infrastructure.api.domain.entity.bo.notice.jdme.JdMeNoticeMessage;
import com.jdx.rover.infrastructure.api.domain.entity.bo.notice.sms.SmsAudioMessage;
import com.jdx.rover.infrastructure.jsf.service.InfraNoticeJsfService;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.bo.vehicle.VehicleAlarmNoticeBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.UserAttentionEntity;
import com.jdx.rover.monitor.enums.UserAttentionTypeEnum;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.repository.redis.UserAttentionStationRepository;
import com.jdx.rover.monitor.service.config.MonitorNotifyUserProperties;
import com.jdx.rover.permission.domain.dto.basic.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleCommonAlarmNoticeService implements AlarmNotice {

  @Autowired
  private MetadataVehicleApiManager metadataVehicleApiManager;
  @Autowired
  private MetadataUserApiManager metadataUserApiManager;
  @Autowired
  private MonitorNotifyUserProperties monitorNotifyUserProperties;
  @Autowired
  private InfraNoticeJsfService infraNoticeJsfService;
  @Autowired
  private VehicleManager vehicleManager;
  @Autowired
  private UserAttentionStationRepository userAttentionStationRepository;
  private static final String dateFormat = "yyyy/MM/dd HH:mm:ss";
  private static final String bumpMsg = "无人车碰撞报警，车号%s，时间%s，请及时处理。";

  private VehicleAlarmNoticeBO buildVehicleAlarmNotice(AlarmNoticeProcessEnum alarmEnum, String vehicleName) {
    StationBasicDTO stationInfo = metadataVehicleApiManager.getStationInfoByVehicle(vehicleName);
    if (stationInfo == null || stationInfo.getAddressInfo() == null ||
            StringUtils.isAnyBlank(stationInfo.getAddressInfo().getCityName(), stationInfo.getStationName())) {
      return null;
    }
    VehicleAlarmNoticeBO vehicleAlarmNoticeBO = new VehicleAlarmNoticeBO();
    vehicleAlarmNoticeBO.setVehicleName(vehicleName);
    vehicleAlarmNoticeBO.setTitle(alarmEnum.getTitle());
    vehicleAlarmNoticeBO.setContent(alarmEnum.getContent());
    vehicleAlarmNoticeBO.setStationInfo(stationInfo);
    vehicleAlarmNoticeBO.setDate(new Date());
    vehicleAlarmNoticeBO.setUser(Arrays.asList(monitorNotifyUserProperties.getBaseUser().split(",")));
    if (alarmEnum == AlarmNoticeProcessEnum.VEHICLE_STOP_BUTTON) {
      UserInfoDTO userInfoDto = metadataUserApiManager.getUserInfoByPhone(stationInfo.getContact());
      if (!Objects.isNull(userInfoDto) && StringUtils.isNotBlank(userInfoDto.getJdErp())) {
        vehicleAlarmNoticeBO.setUser(Lists.newArrayList(userInfoDto.getJdErp()));
      } else {
        vehicleAlarmNoticeBO.setUser(Lists.newArrayList(stationInfo.getPersonName()));
      }
    }
    return vehicleAlarmNoticeBO;
  }

  /*
   * 发送告警通知
   */
  private void sendDDMsg(VehicleAlarmNoticeBO vehicleAlarmNoticeBO) {
    JdMeNoticeMessage msgVo = new JdMeNoticeMessage();
    msgVo.setTitle(vehicleAlarmNoticeBO.getTitle());
    String content = String.format(vehicleAlarmNoticeBO.getContent(), vehicleAlarmNoticeBO.getStationInfo().getAddressInfo().getCityName(),
            vehicleAlarmNoticeBO.getStationInfo().getStationName(), vehicleAlarmNoticeBO.getVehicleName(), DateUtil.format(vehicleAlarmNoticeBO.getDate(), dateFormat));
    msgVo.setContent(content);
    msgVo.setErps(vehicleAlarmNoticeBO.getUser());
    infraNoticeJsfService.sendJdMeNotice(msgVo);
  }

  /*
   * 发送语音通知
   */
  private void sendVoiceMsg(VehicleAlarmNoticeBO vehicleAlarmNoticeBO) {
    SmsAudioMessage smsSendAudioVo = new SmsAudioMessage();
    List<UserAttentionEntity> userAttention = userAttentionStationRepository.get(vehicleAlarmNoticeBO.getStationInfo().getStationId(), UserAttentionTypeEnum.BUMP.getType());
    Set<String> phoneList = userAttention.stream().map(user -> user.getPhone()).collect(Collectors.toSet());
    phoneList.add(vehicleAlarmNoticeBO.getStationInfo().getContact());
    smsSendAudioVo.setPhones(StringUtils.join(phoneList, ","));
    smsSendAudioVo.setContent(String.format(bumpMsg, vehicleAlarmNoticeBO.getVehicleName(), DateUtil.formatTime(vehicleAlarmNoticeBO.getDate())));
    infraNoticeJsfService.sendSmsAudio(smsSendAudioVo);
  }

  @Override
  public boolean process(AlarmNoticeProcessEnum alarmEvent, String vehicleName) {
    try {
      VehicleAlarmNoticeBO vehicleAlarmNoticeBO = buildVehicleAlarmNotice(alarmEvent, vehicleName);
      if (Objects.isNull(vehicleAlarmNoticeBO)) {
        return false;
      }
      sendDDMsg(vehicleAlarmNoticeBO);
      VehicleBasicDTO vehicleBasicDto = vehicleManager.getBasicByName(vehicleName);
      if (!StringUtils.equalsAny(vehicleBasicDto.getOwnerUseCase(), VehicleOwnerUseCaseEnum.OPEN.getValue(),
              VehicleOwnerUseCaseEnum.SOLUTION.getValue())) {
        return false;
      }
      if (Objects.equals(AlarmNoticeProcessEnum.VEHICLE_CRASH_BUMP, alarmEvent) || Objects.equals(AlarmNoticeProcessEnum.VEHICLE_CRASH_F_BUMPER, alarmEvent)
              || Objects.equals(AlarmNoticeProcessEnum.VEHICLE_CRASH_R_BUMPER, alarmEvent)) {
        sendVoiceMsg(vehicleAlarmNoticeBO);
      }
    } catch (Exception e) {
      log.info("发送咚咚提醒消息异常:{}", vehicleName, e);
    }
    return true;
  }

}
