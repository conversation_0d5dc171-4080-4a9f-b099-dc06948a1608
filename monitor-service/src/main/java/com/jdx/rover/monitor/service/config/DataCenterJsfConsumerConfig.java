/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.rover.datacenter.jsf.service.map.PointRangeQueryJsfService;
import com.jdx.rover.datacenter.jsf.service.order.OrderAppletService;
import com.jdx.rover.datacenter.jsf.service.warehouse.VehicleMileageJsfService;
import com.jdx.rover.datacenter.jsf.service.warehouse.VehicleRealtimeJsfService;
import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 数据服务jsf配置
 *
 * <AUTHOR>
 * @date 2025/2/20
 */
@Slf4j
@Configuration
@Component
public class DataCenterJsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册 OrderAppletService
     */
    @Bean
    public OrderAppletService orderAppletService() {
        return jsfConsumerRegister.createConsumerConfig(OrderAppletService.class).refer();
    }

    /**
     * 注册 VehicleMileageJsfService
     */
    @Bean
    public VehicleMileageJsfService vehicleMileageJsfService() {
        return jsfConsumerRegister.createConsumerConfig(VehicleMileageJsfService.class).refer();
    }

    /**
     * 注册 VehicleMileageJsfService
     */
    @Bean
    public VehicleRealtimeJsfService vehicleRealtimeJsfService() {
        return jsfConsumerRegister.createConsumerConfig(VehicleRealtimeJsfService.class).refer();
    }

    /**
     * 注册DataCenter点位范围查询服务接口
     */
    @Bean
    public PointRangeQueryJsfService pointRangeQueryJsfService() {
        return jsfConsumerRegister.createConsumerConfig(PointRangeQueryJsfService.class).refer();
    }

}