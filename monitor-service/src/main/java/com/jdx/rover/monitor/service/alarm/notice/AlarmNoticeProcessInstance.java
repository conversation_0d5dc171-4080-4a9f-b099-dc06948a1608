package com.jdx.rover.monitor.service.alarm.notice;

import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePncTaskAndTrafficLightDTO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * This is a alarm notice process enum.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@Getter
public enum AlarmNoticeProcessInstance {
  INSTANCE;


  /*
   * 处理车端上报告警
   */
  public void handleVehicleAlarm(String vehicleName, List<AlarmInfoDTO> alarmEventList) {
    alarmEventList.stream().map(alarmEvent -> getAlarmNoticeStrategy(alarmEvent, vehicleName)).filter(obj -> !Objects.isNull(obj)).
            forEach(alarmEvent -> alarmEvent.getProcessFunction().apply(alarmEvent, vehicleName));
  }

  private AlarmNoticeProcessEnum getAlarmNoticeStrategy(AlarmInfoDTO alarmEvent, String vehicleName) {
    VehicleAlarmEnum alarmTypeEnum = VehicleAlarmEnum.of(alarmEvent.getAlarmType());
    if (alarmTypeEnum == VehicleAlarmEnum.BOOT_FAIL || alarmTypeEnum == VehicleAlarmEnum.BOOT_TIMEOUT
            || alarmTypeEnum == VehicleAlarmEnum.BOOT_ABNORMAL_FAIL) {
      return AlarmNoticeProcessEnum.of(alarmTypeEnum.getValue(), null);
    }
    AlarmNoticeProcessEnum alarmEnum = AlarmNoticeProcessEnum.of(alarmEvent.getAlarmType(), alarmEvent.getErrorCode());
    if (alarmEnum == null) {
      return null;
    }
    if (alarmEnum == AlarmNoticeProcessEnum.VEHICLE_DRIVING_LANE) {
      return alarmEnum;
    }
    String redisKey = RedisKeyEnum.TRAFFIC_LIGHT_SET_VEHICLE.getValue() + vehicleName;
    SingleVehiclePncTaskAndTrafficLightDTO dto = RedissonUtils.getObject(redisKey);
    if (Objects.isNull(dto) || !StringUtils.equals("ACTIVE", dto.getPncTaskType())) {
      return null;
    }
    return alarmEnum;
  }


}
