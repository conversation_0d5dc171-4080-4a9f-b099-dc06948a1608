/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.service.listener.redis;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.notice.JdMeNoticeManager;
import com.jdx.rover.monitor.vo.datacollection.ws.DataCollectionTaskVehicleTopicMsg;
import com.jdx.rover.monitor.vo.datacollection.ws.DataCollectionWsVehicleLever;
import com.jdx.rover.monitor.vo.datacollection.ws.DataCollectionWsVehicleOccupied;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.listener.MessageListener;

import java.io.IOException;
import java.util.Objects;

/**
 * 数据采集任务车辆监听器
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Slf4j
public class DataCollectionTaskVehicleListener implements MessageListener<String> {

    /**
     * 会话
     */
    private Session session;

    /**
     * 用户名
     */
    private String username;

    /**
     * 车牌号
     */
    private String vehicleName;


    // 咚咚通知标题
    private static final String DD_NOTICE_TITLE = "数采车WS告警";

    // 咚咚通知人
    private static final String DD_NOTICE_ERP = "linbingbing6,cuihongjian1,gulin21";

    /**
     * 构造方法
     * @param session 会话
     * @param username 用户名
     */
    public DataCollectionTaskVehicleListener(Session session, String username, String vehicleName) {
        this.session = session;
        this.username = username;
        this.vehicleName = vehicleName;
    }

    @Override
    public void onMessage(CharSequence channel, String msg) {
        log.info("[数采车]接收到采集任务车辆RTopic消息，vehicleName:{}, channel:{}, msg:{}", vehicleName, channel, msg);
        DataCollectionTaskVehicleTopicMsg topicMsg = JsonUtils.readValue(msg, DataCollectionTaskVehicleTopicMsg.class);

        if (StringUtils.equals(topicMsg.getMessageType(), WebsocketEventTypeEnum.DATA_COLLECTION_OCCUPIED.getValue())
            && StringUtils.equals(topicMsg.getUsername(), username)) {
            log.info("[数采车]接收到采集任务车辆新用户进入采集模式，当前用户为新用户，忽略处理");
            return;
        }
        WebsocketEventTypeEnum eventTypeEnum = WebsocketEventTypeEnum.of(topicMsg.getMessageType());
        if (Objects.isNull(eventTypeEnum)) {
            log.info("[数采车]接收到采集任务车辆RTopic消息，消息类型不匹配，忽略处理");
            return;
        }
        WsResult wsResult = null;
        switch (eventTypeEnum) {
            case DATA_COLLECTION_OCCUPIED:
                wsResult = WsResult.success(eventTypeEnum.getValue(),
                    new DataCollectionWsVehicleOccupied(topicMsg.getUsername()));
                break;
            case DATA_COLLECTION_LEVER:
                wsResult = WsResult.success(eventTypeEnum.getValue(),
                    new DataCollectionWsVehicleLever(topicMsg.getSceneId(), topicMsg.getLeverTime()));
                break;
            case DATA_COLLECTION_FINISHED:
                wsResult = WsResult.success(eventTypeEnum.getValue());
                break;
            default:
        }
        if (Objects.isNull(wsResult)) {
            log.info("[数采车]接收到采集任务车辆RTopic消息，消息类型不匹配，忽略处理");
            return;
        }

        String jsonStr = JsonUtils.writeValueAsString(wsResult);
        synchronized (session) {
            try {
                if (session.isOpen()) {
                    this.session.getBasicRemote().sendText(jsonStr);
                    log.info("[数采车]发送采集任务车辆ws消息, vehicleName:{}, username={}, msg={}", vehicleName, username, jsonStr);
                }
            } catch (IOException e) {
                log.error("[数采车]发送采集任务车辆ws消息失败,vehicleName:{}, username={}, msg={}", vehicleName, username, jsonStr, e);
                SpringUtil.getBean(JdMeNoticeManager.class).sendNotice(DD_NOTICE_TITLE, String.format("[数采车]ws给用户%s发送车辆%s消息异常", username, vehicleName), DD_NOTICE_ERP);
                throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getMessage());
            }
        }
    }
}
