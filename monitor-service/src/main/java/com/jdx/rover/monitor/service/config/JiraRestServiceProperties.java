/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;

/**
 * <p>
 * This is static class that provides configuration properties for jira service service.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "jira-service")
@Data
@Validated
public class JiraRestServiceProperties {

  /**
   * <p>
   * The url.
   * </p>
   */
  @NotBlank
  private String url = "http://jira.jd.com/";

  /**
   * <p>
   * The user name.
   * </p>
   */
  @NotBlank
  private String userName = "org.jdx.jira";

  /**
   * <p>
   * The password.
   * </p>
   */
  @NotBlank
  private String passWord = "Zhenxinaini@521";

  /**
   * <p>
   * The projectKey.
   * </p>
   */
  @NotBlank
  private String projectKey = "JDXAD";

  /**
   * <p>
   * The issue url.
   * </p>
   */
  @NotBlank
  private String issuetUrl = "rest/api/2/issue/";

  /**
   * <p>
   * The component url.
   * </p>
   */
  @NotBlank
  private String componentUrl = "rest/api/2/component/";

  /**
   * <p>
   * The custom field url.
   * </p>
   */
  @NotBlank
  private String customFieldUrl = "secure/QuickCreateIssue!default.jspa";

  /**
   * <p>
   * The attachment bucket name.
   * </p>
   */
  @NotBlank
  private String attachmentBucketName = "rover-jira";
}
