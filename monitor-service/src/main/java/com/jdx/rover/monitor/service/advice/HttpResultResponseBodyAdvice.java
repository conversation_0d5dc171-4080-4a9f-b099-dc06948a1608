/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.advice;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.annotation.NoHttpResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.AnnotatedElement;

/**
 * 接口响应体处理器
 * 参考 https://blog.csdn.net/aiyaya_/article/details/78976759
 *
 * <AUTHOR>
 */
@ControllerAdvice
public class HttpResultResponseBodyAdvice implements ResponseBodyAdvice<Object> {
  @Override
  public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
    final Class<?> clazz = returnType.getContainingClass();
    final AnnotatedElement method = returnType.getAnnotatedElement();
    // 如果类或者方法上有@NoHttpResult注解,不进行HttpResult封装
    if (clazz.isAnnotationPresent(NoHttpResult.class) || method.isAnnotationPresent(NoHttpResult.class)) {
      return false;
    }
    return true;
  }

  @Override
  public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType
      , Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
    if (body instanceof String) {
      return JsonUtils.writeValueAsString(HttpResult.success(body));
    } else if (body instanceof MonitorErrorEnum) {
      MonitorErrorEnum monitorErrorEnum = (MonitorErrorEnum) body;
      return new HttpResult<>(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    } else if (!(body instanceof HttpResult)) {
      return HttpResult.success(body);
    }
    return body;
  }
}