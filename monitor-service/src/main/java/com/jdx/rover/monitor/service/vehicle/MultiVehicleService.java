package com.jdx.rover.monitor.service.vehicle;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.bo.vehicle.VehicleSortBO;
import com.jdx.rover.monitor.dto.vehicle.*;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleCategoryEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleGroupsSortEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleSortTypeEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleTabTypeEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.vehicle.MultiVehicleManager;
import com.jdx.rover.monitor.manager.vehicle.SystemStateManager;
import com.jdx.rover.monitor.repository.redis.*;
import com.jdx.rover.monitor.repository.redis.metadata.CockpitVehicleNameRepository;
import com.jdx.rover.monitor.vo.vehicle.MultiVehiclePageVO;
import com.jdx.rover.monitor.vo.vehicle.MultiVehicleRealtimeVO;
import com.jdx.rover.schedule.api.domain.enums.StopTravelStatus;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 多车页服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MultiVehicleService {
  @Autowired
  private VehicleSelectTypeRepository vehicleSelectTypeRepository;
  @Autowired
  private VehicleBasicRepository vehicleBasicRepository;
  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;
  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;
  @Autowired
  private UserAttentionRepository userAttentionRepository;
  @Autowired
  private CockpitVehicleNameRepository cockpitVehicleNameRepository;
  @Autowired
  private MultiVehicleManager multiVehicleManager;

  private static final Integer MAX_ERROR_SIZE = 20;

  /**
   * 通过车辆名称获取对象
   *
   * @param vo 多车页入参
   * @return
   */
  public WsResult<MultiVehicleRealtimeDTO> listMultiVehicleWs(MultiVehicleRealtimeVO vo) {
    MultiVehicleRealtimeDTO dto = listMultiVehicle(vo);
    WsResult<MultiVehicleRealtimeDTO> wsResult = WsResult.success(WebsocketEventTypeEnum.MULTI_VEHICLE.getValue(), dto);
    return wsResult;
  }

  /**
   * 通过车辆名称获取对象
   *
   * @param vo 多车页入参
   * @return
   */
  public MultiVehicleRealtimeDTO listMultiVehicle(MultiVehicleRealtimeVO vo) {
    MultiVehicleRealtimeDTO dto = initMultiVehicleRealtimeDTO(vo);

    // 第一步 通过筛选条件,获取所有符合(用户名,筛选,排序方式)的车辆名称
    Set<String> multiVehicleSet = StringUtils.isNotBlank(vo.getCockpitNumber())?
            cockpitVehicleNameRepository.get(vo.getCockpitNumber()) : userVehicleNameRepository.get(vo.getUsername());
    Set<String> userAttentionVehicleSet = userAttentionRepository.get(vo.getUsername());
    Collection<String> userAttentionVehicle = multiVehicleManager.getByCondition(vo, userAttentionVehicleSet);
    Collection<String> vehicleNameCollection = multiVehicleManager.getByCondition(vo, multiVehicleSet);
    // 第二步 获取所有车辆的分数
    List<String> vehicleNameOrderList = new ArrayList<>(vehicleNameCollection);
    List<String> userAttentionVehicleList = new ArrayList<>(userAttentionVehicle);
    List<Double> scoreList = multiVehicleManager.getScoredSortedSetScore(vo.getSortType(), vehicleNameOrderList);
    // 第三步 汇总 失联及异常,正常,离线,全部 条数
    setAllSize(dto, scoreList, userAttentionVehicleList.size());

    // 第四步 读取每个页面的数据,并设置分页
    if (StringUtils.equals(VehicleCategoryEnum.INTERESTED.getValue(), vo.getCurrentCategory())) {
      setMultiVehiclePageList(dto, userAttentionVehicleList, scoreList);
    } else {
      setMultiVehiclePageList(dto, vehicleNameOrderList, scoreList);
    }

    return dto;
  }

  /**
   * 从MultiVehicleRealtimeVO对象数据,初始化MultiVehicleRealtimeDTO对象数据
   *
   * @param vo
   * @return
   */
  private MultiVehicleRealtimeDTO initMultiVehicleRealtimeDTO(MultiVehicleRealtimeVO vo) {
    MultiVehicleRealtimeDTO dto = new MultiVehicleRealtimeDTO();
    dto.setVehicleBusinessType(vo.getVehicleBusinessType());
    dto.setVehicleUseCaseList(vo.getVehicleUseCaseList());
    dto.setTabType(vo.getTabType());
    dto.setCurrentCategory(vo.getCurrentCategory());
    dto.setSortType(vo.getSortType());

    List<MultiVehiclePageVO> multiVehiclePageVOList = vo.getMultiVehiclePageList();
    if (multiVehiclePageVOList == null) {
      multiVehiclePageVOList = Lists.newArrayList();
    }
    Map<String, MultiVehiclePageVO> pageMapVO = multiVehiclePageVOList.stream()
        .collect(Collectors.toMap(MultiVehiclePageVO::getCategory, Function.identity()));
    // 正常/离线/全部 只能有一个分页
    if (pageMapVO.get(VehicleCategoryEnum.NORMAL.getValue()) != null ||
        pageMapVO.get(VehicleCategoryEnum.OFFLINE.getValue()) != null ||
        pageMapVO.get(VehicleCategoryEnum.INTERESTED.getValue()) != null ||
        pageMapVO.get(VehicleCategoryEnum.ALL.getValue()) != null) {
      if (pageMapVO.size() > 1) {
        throw new AppException("A999999", "非异常tab分页参数不能多于一个!");
      }
    } else {
      // 失联及异常如果没有分页信息,默认取第1页,每页8个
      for (VehicleCategoryEnum itemEnum : VehicleCategoryEnum.values()) {
        if (VehicleCategoryEnum.NORMAL.equals(itemEnum) || VehicleCategoryEnum.OFFLINE.equals(itemEnum)
            || VehicleCategoryEnum.ALL.equals(itemEnum)) {
          continue;
        }
        if (pageMapVO.get(itemEnum.getValue()) != null) {
          continue;
        }
        MultiVehiclePageVO pageVO = new MultiVehiclePageVO();
        pageVO.setPageSize(8);
        pageMapVO.put(itemEnum.getValue(), pageVO);
      }
    }

    List<MultiVehiclePageDTO> multiVehiclePageDTOList = new ArrayList<>();
    for (Map.Entry<String, MultiVehiclePageVO> entry : pageMapVO.entrySet()) {
      String key = entry.getKey();
      MultiVehiclePageVO pageVO = pageMapVO.get(key);
      MultiVehiclePageDTO pageDTO = new MultiVehiclePageDTO();
      pageDTO.setCategory(key);
      pageDTO.setPageNum(pageVO.getPageNum());
      pageDTO.setPageSize(pageVO.getPageSize());
      multiVehiclePageDTOList.add(pageDTO);
    }
    dto.setMultiVehiclePageList(multiVehiclePageDTOList);

    // 初始加载没有选择tab,默认为失联及异常tab
    if (StringUtils.isBlank(dto.getTabType())) {
      dto.setTabType(VehicleTabTypeEnum.ERROR.getValue());
    }

    // 离线和全部情况下,只有按车号排序
    if (StringUtils.equalsAny(dto.getTabType(), VehicleTabTypeEnum.INTERESTED.getValue()
        , VehicleTabTypeEnum.ALL.getValue(), VehicleTabTypeEnum.OFFLINE.getValue())) {
      dto.setSortType(VehicleSortTypeEnum.VEHICLE_NAME.getValue());
    }

    // 没有分类,即全部数据,离线时只按照车号排序
    if (StringUtils.isBlank(dto.getCurrentCategory())
        || StringUtils.equals(dto.getCurrentCategory(), VehicleGroupsSortEnum.OFFLINE.name())) {
      vo.setSortType(VehicleSortTypeEnum.VEHICLE_NAME.getValue());
    }
    return dto;
  }

  /**
   * 获取失联及异常数据
   *
   * @param dto
   * @param vehicleNameOrderList
   * @param scoreList
   */
  private void setMultiVehiclePageList(MultiVehicleRealtimeDTO dto, List<String> vehicleNameOrderList, List<Double> scoreList) {
    for (VehicleCategoryEnum itemEnum : VehicleCategoryEnum.values()) {
      String category = itemEnum.getValue();
      // 没有分页请求则返回
      if (getCategoryPageDTO(category, dto.getMultiVehiclePageList()) == null) {
        continue;
      }
      VehicleSortBO vehicleSortBO = getScoreByCategory(category);
      double startSectionScore = vehicleSortBO.getStartScore();
      double endSectionScore = vehicleSortBO.getEndScore();
      List<String> tabPageVehicleNameList = new ArrayList<>();
      for (int i = 0; i < vehicleNameOrderList.size(); i++) {
        Double score = scoreList.get(i);
        if (!Objects.isNull(score) && score >= startSectionScore && score < endSectionScore) {
          tabPageVehicleNameList.add(vehicleNameOrderList.get(i));
        }
      }
      if (CollectionUtils.isEmpty(tabPageVehicleNameList)) {
        continue;
      }
      setPagesAndTotal(dto, category, tabPageVehicleNameList);
      setPageData(dto, category, tabPageVehicleNameList);
    }
  }

  /**
   * 获取指定分类页面对象
   *
   * @param category
   * @param multiVehiclePageList
   * @return
   */
  private static final MultiVehiclePageDTO getCategoryPageDTO(String category, List<MultiVehiclePageDTO> multiVehiclePageList) {
    for (MultiVehiclePageDTO multiVehiclePageDTO : multiVehiclePageList) {
      if (Objects.equals(multiVehiclePageDTO.getCategory(), category)) {
        return multiVehiclePageDTO;
      }
    }
    return null;
  }

  /**
   * 设置分页的总数,总页数
   *
   * @param dto
   * @param vehicleNameOrderList
   */
  private void setPagesAndTotal(MultiVehicleRealtimeDTO dto, String category, List<String> vehicleNameOrderList) {
    MultiVehiclePageDTO pageDTO = getCategoryPageDTO(category, dto.getMultiVehiclePageList());
    pageDTO.setCategory(category);
    Long pageTotal = Long.valueOf(vehicleNameOrderList.size());
    pageDTO.setTotal(pageTotal);

    Double pagesDouble = Math.ceil(pageDTO.getTotal().doubleValue() / pageDTO.getPageSize());
    Integer pages = pagesDouble.intValue();
    pageDTO.setPages(pages);
  }

  /**
   * 设置分页数据
   *
   * @param dto
   * @param vehicleNameOrderList
   */
  private void setPageData(MultiVehicleRealtimeDTO dto, String category, List<String> vehicleNameOrderList) {
    MultiVehiclePageDTO pageDTO = getCategoryPageDTO(category, dto.getMultiVehiclePageList());

    Integer startIndex = (pageDTO.getPageNum() - 1) * pageDTO.getPageSize();
    if (startIndex < 0) {
      startIndex = 0;
    }
    Integer endIndex = startIndex + pageDTO.getPageSize();
    if (endIndex > vehicleNameOrderList.size()) {
      endIndex = vehicleNameOrderList.size();
    }
    List<String> vehicleNamePageList = new ArrayList<>();
    if (endIndex > startIndex) {
      vehicleNamePageList.addAll(vehicleNameOrderList.subList(startIndex, endIndex));
    }
    if (CollectionUtils.isEmpty(vehicleNamePageList)) {
      return;
    }
    Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNamePageList);
    Map<String, VehicleAlarmDO> alarmMap = vehicleAlarmRepository.listMap(vehicleNamePageList);
    Map<String, MonitorScheduleEntity> monitorScheduleEntityMap = vehicleScheduleRepository.listMap(vehicleNamePageList);
    Map<String, VehicleBasicDTO> vehicleBasicDTOMap = vehicleBasicRepository.listMap(vehicleNamePageList);
    List<MultiVehicleDTO> list = new ArrayList<>();
    for (String vehicleName : vehicleNamePageList) {
      MultiVehicleDTO vehicle = new MultiVehicleDTO();
      vehicle.setVehicleName(vehicleName);
      vehicle.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
      VehicleBasicDTO vehicleBasicDTO = vehicleBasicDTOMap.get(vehicleName);
      if (vehicleBasicDTO != null) {
        vehicle.setVehicleBusinessType(vehicleBasicDTO.getBusinessType());
        vehicle.setSupplier(vehicleBasicDTO.getSupplier());
      }
      VehicleRealtimeInfoDTO realtime = realtimeMap.get(vehicleName);
      if (realtime != null) {
        String systemState = realtime.getSystemState();
        vehicle.setSystemState(systemState);
        if (SystemStateEnum.CONNECTION_LOST.getSystemState().equals(vehicle.getSystemState())) {
          vehicle.setRecordTime(realtime.getRecordTime());
        }
        if (!SystemStateManager.isOfflineOrLost(systemState)) {
          vehicle.setVehicleState(realtime.getVehicleState());
          vehicle.setSpeed(realtime.getSpeed());
          vehicle.setPower(realtime.getPower());

          VehicleAlarmDO alarm = alarmMap.get(vehicleName);
          if (alarm != null && CollectionUtils.isNotEmpty(alarm.getAlarmEventList())) {
            List<AlarmEventDTO> alarmEventList = alarm.getAlarmEventList().stream().map(tmp -> {
              AlarmEventDTO alarmEventDTO = new AlarmEventDTO();
              alarmEventDTO.setAlarmEvent(tmp.getType());
              alarmEventDTO.setReportTime(tmp.getReportTime());
              return alarmEventDTO;
            }).collect(Collectors.toList());
            vehicle.setAlarmEventList(alarmEventList);
          }
        }
      } else {
        vehicle.setSystemState(SystemStateEnum.OFFLINE.getSystemState());
      }

      MonitorScheduleEntity monitorScheduleEntity = monitorScheduleEntityMap.get(vehicleName);
      if (monitorScheduleEntity != null) {
        MultiVehicleScheduleDTO multiVehicleScheduleDTO = convertEntityToDTO(monitorScheduleEntity, realtime);
        vehicle.setSchedule(multiVehicleScheduleDTO);
        vehicle.setScheduleState(monitorScheduleEntity.getScheduleState());
        vehicle.setTaskType(monitorScheduleEntity.getTaskType());
      }
      list.add(vehicle);
    }
    pageDTO.setList(list);
  }

  private MultiVehicleScheduleDTO convertEntityToDTO(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
    MultiVehicleScheduleDTO dto = new MultiVehicleScheduleDTO();
    if (monitorScheduleEntity == null || monitorScheduleEntity.getScheduleNo() == null) {
      return dto;
    }
    dto.setTotalOrderNum(
            monitorScheduleEntity.getTotalCollectOrderNum() + monitorScheduleEntity.getTotalDeliveryOrderNum());
    dto.setFinishedOrderNum(
            monitorScheduleEntity.getFinishedCollectOrderNum() + monitorScheduleEntity.getFinishedDeliveryOrderNum());
    dto.setGlobalMileage(monitorScheduleEntity.getGlobalMileage());
    Double arrivedMileage = getArrivedMileage(monitorScheduleEntity, realtime);
    dto.setArrivedMileage(arrivedMileage);
    if (realtime != null && realtime.getCurrentStopFinishedMileage() != null) {
      dto.setCurrentStopFinishedMileage(realtime.getCurrentStopFinishedMileage());
    } else {
      dto.setCurrentStopFinishedMileage(0.0);
    }
    List<MonitorScheduleStopEntity> monitorScheduleStopEntityList = monitorScheduleEntity.getStop();
    if (CollectionUtils.isEmpty(monitorScheduleStopEntityList)) {
      return dto;
    }
    List<MultiVehicleScheduleStopDTO> stopList = new ArrayList<>();
    for (MonitorScheduleStopEntity stop : monitorScheduleStopEntityList) {
      MultiVehicleScheduleStopDTO multiVehicleScheduleStopDTO = new MultiVehicleScheduleStopDTO();
      multiVehicleScheduleStopDTO.setId(stop.getId());
      multiVehicleScheduleStopDTO.setGoalId(stop.getGoalId());
      multiVehicleScheduleStopDTO.setType(stop.getType());
      multiVehicleScheduleStopDTO.setStopAction(stop.getStopAction());
      multiVehicleScheduleStopDTO.setTravelStatus(stop.getTravelStatus());
      // 只有前往才需要里程算百分比
      if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.START.getTravelStatus())) {
        multiVehicleScheduleStopDTO.setGlobalMileage(stop.getGlobalMileage());
        multiVehicleScheduleStopDTO.setStartTime(stop.getStartTime());
        multiVehicleScheduleStopDTO.setName(stop.getName());
      }
      // 只有停留才需要站点名称,等待时间,到达时间
      if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.STAY.getTravelStatus())) {
        multiVehicleScheduleStopDTO.setName(stop.getName());
        multiVehicleScheduleStopDTO.setEstDepartTime(stop.getEstDepartTime());
        multiVehicleScheduleStopDTO.setArrivedTime(stop.getArrivedTime());
        multiVehicleScheduleStopDTO.setWaitingTime(stop.getWaitingTime());
      }
      stopList.add(multiVehicleScheduleStopDTO);
    }
    dto.setStopList(stopList);
    return dto;
  }

  /**
   * 当前调度完成里程
   *
   * @param monitorScheduleEntity
   * @param realtime
   * @return
   */
  private Double getArrivedMileage(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
    Double finishedMileage = Optional.ofNullable(monitorScheduleEntity.getFinishedMileage()).orElse(0.0);
    if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(),
            VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(),
            VehicleScheduleState.RETURN.getVehicleScheduleState())) {
      Double currentStopFinishedMileage;
      if (realtime == null || realtime.getCurrentStopFinishedMileage() == null) {
        currentStopFinishedMileage = 0.0;
      } else {
        currentStopFinishedMileage = realtime.getCurrentStopFinishedMileage();
      }
      finishedMileage += currentStopFinishedMileage;
    }
    return finishedMileage;
  }

  /**
   * 设置全部/异常/正常/离线 数量
   *
   * @param dto
   * @param scoreList
   */
  private void setAllSize(MultiVehicleRealtimeDTO dto, List<Double> scoreList, Integer attentionSize) {
    Integer allSize = scoreList.size();
    Integer errorSize = 0;
    Integer normalSize = 0;
    Integer offlineSize = 0;
    for (Double score : scoreList) {
      if (score == null) {
        offlineSize++;
        continue;
      }
      if (score < VehicleGroupsSortEnum.NORMAL.getValue()) {
        errorSize++;
      } else if (score < VehicleGroupsSortEnum.OFFLINE.getValue()) {
        normalSize++;
      } else {
        offlineSize++;
      }
    }
    dto.setAllSize(allSize);
    dto.setInterestedSize(attentionSize);
    dto.setErrorSize(errorSize);
    dto.setNormalSize(normalSize);
    dto.setOfflineSize(offlineSize);
  }

  /**
   * 通过标签也获取分数区间
   *
   * @param category
   * @return
   */
  private VehicleSortBO getScoreByCategory(String category) {
    VehicleCategoryEnum vehicleCategoryEnum = VehicleCategoryEnum.valueOf(category);
    VehicleSortBO bo = new VehicleSortBO();
    bo.setStartScore(vehicleCategoryEnum.getStartScore());
    bo.setEndScore(vehicleCategoryEnum.getEndScore());
    return bo;
  }
}
