package com.jdx.rover.monitor.service.mobile;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mobile.SystemStatusEnum;
import com.jdx.rover.monitor.manager.vehicle.VehicleLoginManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.vo.mobile.login.RemoteLoginVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/16 22:14
 * @description 远程登录车端Service
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RemoteLoginService {

    /**
     * 远程登录车端Manager
     */
    private final VehicleLoginManager vehicleLoginManager;

    /**
     * 车辆实时信息
     */
    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    /**
     * 远程登录车端
     *
     * @param remoteLoginVO remoteLoginVO
     */
    public void remoteLogin(RemoteLoginVO remoteLoginVO) {
        // 判断系统状态
        VehicleRealtimeInfoDTO realtimeDTO = vehicleRealtimeRepository.get(remoteLoginVO.getVehicleName());
        if (Objects.isNull(realtimeDTO) || SystemStatusEnum.OFFLINE.getCode().equals(realtimeDTO.getSystemState())) {
            throw new AppException(MonitorErrorEnum.ERROR_LOGIN_VEHICLE_OFFLINE.getCode(), MonitorErrorEnum.ERROR_LOGIN_VEHICLE_OFFLINE.getMessage());
        }

        // 调用远程登录接口
        vehicleLoginManager.remoteLogin(remoteLoginVO.getVehicleName(), remoteLoginVO.getContact());
    }
}
