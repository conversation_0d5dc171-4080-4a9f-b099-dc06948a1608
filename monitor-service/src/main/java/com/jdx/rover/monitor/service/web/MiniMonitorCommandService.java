/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Optional;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.exception.BusinessException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorRemoteCommandDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.entity.VehicleRemoteOperationStatusEnum;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.MonitorKafkaTopicConstant;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.vo.MiniMonitorAsArrivedRequestVO;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteCommandVO;
import com.jdx.rover.monitor.vo.MiniMonitorResetAbnormalControlCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteControlCommandVO;
import com.jdx.rover.server.api.domain.enums.RemoteCommandTypeEnum;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteControlCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteResetAbnormalCommandVO;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * This is a command service function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
@Slf4j
public class MiniMonitorCommandService {

  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private RemoteCommandService remoteCommandJsfService;

  @Autowired
  private JmqProducerService jmqProducerService;

  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;

  @Autowired
  private RemoteCommandService remoteCommandService;

  /**
   * <p>
   * This is a inner class use as private functions' return data struct.
   * </p>
   */
  @Data
  private class ResultInfo {

    /**
     * <p>
     * Represents whether params meet the post conditions. The default value is
     * false. It's changeable.
     * </p>
     */
    private Boolean ableToPost;

    /**
     * <p>
     * Represents whether db has the record. The default value is false. It's
     * changeable.
     * </p>
     */
    private Boolean hasRecord;

    /**
     * <p>
     * Represents the name of user. The default value is null. It's changeable.
     * </p>
     */
    private String recordName;

    /**
     * <p>
     * Represents the source of command. The default value is null. It's changeable.
     * </p>
     */
    private String commandSource;

  }

  /**
   * <p>
   * Post emergency stop request.
   * </p>
   *
   * @param emergencyStopCommandVO the supervisor remote request vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postEmergencyStopRequest(MiniMonitorRemoteCommandVO emergencyStopCommandVO) {
    ParameterCheckUtility.checkNotNull(emergencyStopCommandVO, "emergencyStopCommandVO");
    ParameterCheckUtility.checkNotNullNorEmpty(emergencyStopCommandVO.getVehicleName(),
            "emergencyStopCommandVO#vehicleName");
    ParameterCheckUtility.checkNotNull(emergencyStopCommandVO.getTimeStamp(), "emergencyStopCommandVO#timeStamp");
    log.info("Receive emergency command {}.", emergencyStopCommandVO);
    MiniMonitorRemoteCommandDTO remoteCommandDto = new MiniMonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue());
    String userName = UserUtils.getLoginUser();
    if (userName == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(), MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
    }
    remoteCommandDto.setUserName(userName);
    MiniMonitorCommandService.ResultInfo resultInfo = isAbleToPost(emergencyStopCommandVO.getVehicleName(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("%s 在接管中", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(emergencyStopCommandVO.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(emergencyStopCommandVO.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    remoteCommandVo.setCommandType(RemoteCommandTypeEnum.MOBILE);
    HttpResult result = remoteCommandJsfService.publishEmergencyStopCommand(remoteCommandVo);
    if (!HttpResult.isSuccess(result)) {
      log.info("急停失败!{},{}", result, emergencyStopCommandVO);
      return result;
    }
    VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
    vehicleTakeOverEntity.setOperationStatus(VehicleRemoteOperationStatusEnum.TAKEOVER.getOperationStatus());
    vehicleTakeOverEntity.setUserName(userName);
    vehicleTakeOverEntity.setCommandSource(RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    vehicleTakeOverRepository.save(emergencyStopCommandVO.getVehicleName(), vehicleTakeOverEntity);
    try {
      sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(emergencyStopCommandVO),
              WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    } catch (Exception e) {
      log.error("Store user remote command record exception", e);
    }
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * 接管
   */
  public void postEmergencyStopRequest(MiniMonitorRemoteCommandVO emergencyStopCommandVO, RemoteCommandSourceEnum sourceEnum) {
    // 获取当前用户
    String userName = JsfLoginUtil.getUsername();

    // 接管操作判断
    String vehicleName = emergencyStopCommandVO.getVehicleName();
    MiniMonitorCommandService.ResultInfo resultInfo = isAbleToPost(vehicleName, userName, sourceEnum.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("%s在%s接管中", resultInfo.getRecordName(), RemoteCommandSourceEnum.getCommandSourceName(resultInfo.getCommandSource()));
      throw new BusinessException(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }

    // 下发指令
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(vehicleName);
    remoteCommandVo.setReceiveTimeStamp(emergencyStopCommandVO.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    remoteCommandVo.setCommandType(RemoteCommandTypeEnum.MOBILE);

    HttpResult<Void> httpResult = remoteCommandService.publishEmergencyStopCommand(remoteCommandVo);
    if (!HttpResult.isSuccess(httpResult)) {
      throw new BusinessException(httpResult.getCode(), httpResult.getMessage());
    }

    // 缓存接管操作
    VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
    vehicleTakeOverEntity.setOperationStatus(VehicleRemoteOperationStatusEnum.TAKEOVER.getOperationStatus());
    vehicleTakeOverEntity.setUserName(userName);
    vehicleTakeOverEntity.setCommandSource(sourceEnum.getCommandSource());
    vehicleTakeOverRepository.save(vehicleName, vehicleTakeOverEntity);

    // 发送操作日志
    String log = JsonUtils.writeValueAsString(emergencyStopCommandVO);
    sendRemoteCommandRecordLog(log, WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue(), userName, sourceEnum.getCommandSource());
  }

  /**
   * <p>
   * 急刹
   * </p>
   *
   * @param emergencyBrakeCommandVo the supervisor remote request vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postEmergencyBrakeRequest(MiniMonitorRemoteCommandVO emergencyBrakeCommandVo) {
    ParameterCheckUtility.checkNotNull(emergencyBrakeCommandVo, "monitorEmergencyBrakeCommandVo");
    ParameterCheckUtility.checkNotNull(emergencyBrakeCommandVo.getVehicleName(),
            "monitorEmergencyBrakeCommandVo#vehicleName");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive emergency brake request {}, userName {}.", emergencyBrakeCommandVo, userName);

    MiniMonitorCommandService.ResultInfo resultInfo =
            isAbleToPost(emergencyBrakeCommandVo.getVehicleName(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    } else if (!resultInfo.getHasRecord()) {
      String message = String.format("The vehicle not being taken over.");
      return HttpResult.error(MonitorErrorEnum.OPERATOR_MISMATCH_TAKEOVER.getCode(), message);
    }
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(emergencyBrakeCommandVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(emergencyBrakeCommandVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = remoteCommandJsfService.publishEmergencyStopCommand(remoteCommandVo);
    if (!HttpResult.isSuccess(result)) {
      log.info("急刹失败!{},{}", result, emergencyBrakeCommandVo);
      return result;
    }
    try {
      sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(emergencyBrakeCommandVo),
              WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_BRAKE.getValue(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    } catch (Exception e) {
      log.error("Store user remote command record exception", e);
    }
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_BRAKE.getValue());
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * 急刹
   */
  public void postEmergencyBrakeRequest(MiniMonitorRemoteCommandVO brakeCommandVO, RemoteCommandSourceEnum sourceEnum) {
    // 获取当前用户
    String userName = JsfLoginUtil.getUsername();

    // 急刹操作判断
    String vehicleName = brakeCommandVO.getVehicleName();
    MiniMonitorCommandService.ResultInfo resultInfo = isAbleToPost(vehicleName, userName, sourceEnum.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("%s在%s接管中", resultInfo.getRecordName(), RemoteCommandSourceEnum.getCommandSourceName(resultInfo.getCommandSource()));
      throw new BusinessException(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }

    // 下发指令
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(vehicleName);
    remoteCommandVo.setReceiveTimeStamp(brakeCommandVO.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    remoteCommandVo.setCommandType(RemoteCommandTypeEnum.MOBILE);

    HttpResult<Void> httpResult = remoteCommandService.publishEmergencyStopCommand(remoteCommandVo);
    if (!HttpResult.isSuccess(httpResult)) {
      throw new BusinessException(httpResult.getCode(), httpResult.getMessage());
    }

    // 发送操作日志
    String log = JsonUtils.writeValueAsString(brakeCommandVO);
    sendRemoteCommandRecordLog(log, WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_BRAKE.getValue(), userName, sourceEnum.getCommandSource());
  }

  /**
   * <p>
   * Post recovery request.
   * </p>
   *
   * @param minimonitorRecoveryCommandVo the supervisor recovery request vo
   * @return The remote request response info includes its result and message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   * @throws AppException             If failed to find object.
   */
  public HttpResult postRecoveryRequest(MiniMonitorRemoteCommandVO minimonitorRecoveryCommandVo) {
    ParameterCheckUtility.checkNotNull(minimonitorRecoveryCommandVo, "minimonitorRecoveryCommandVo");
    ParameterCheckUtility.checkNotNull(minimonitorRecoveryCommandVo.getVehicleName(),
            "minimonitorRecoveryCommandVo#vehicleName");
    String userName = UserUtils.getLoginUser();
    if (userName == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(), MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
    }
    log.info("Receive recovery request {}, userName {}.", minimonitorRecoveryCommandVo, userName);
    MiniMonitorCommandService.ResultInfo resultInfo = isAbleToPost(minimonitorRecoveryCommandVo.getVehicleName(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("%s 在控制中", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue());
    vehicleTakeOverRepository.remove(minimonitorRecoveryCommandVo.getVehicleName());
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(minimonitorRecoveryCommandVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(minimonitorRecoveryCommandVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    remoteCommandVo.setCommandType(RemoteCommandTypeEnum.MOBILE);
    HttpResult result = remoteCommandJsfService.publishRecoveryCommand(remoteCommandVo);
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
            WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    if (!HttpResult.isSuccess(result)) {
      return result;
    }
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * 取消接管
   */
  public void postRecoveryRequest(MiniMonitorRemoteCommandVO recoveryCommandVO, RemoteCommandSourceEnum sourceEnum) {
    // 获取当前用户
    String userName = JsfLoginUtil.getUsername();

    // 取消接管操作判断
    String vehicleName = recoveryCommandVO.getVehicleName();
    MiniMonitorCommandService.ResultInfo resultInfo = isAbleToPost(vehicleName, userName, sourceEnum.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("%s在%s接管中", resultInfo.getRecordName(), RemoteCommandSourceEnum.getCommandSourceName(resultInfo.getCommandSource()));
      throw new BusinessException(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }

    // 下发指令
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(vehicleName);
    remoteCommandVo.setReceiveTimeStamp(recoveryCommandVO.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    remoteCommandVo.setCommandType(RemoteCommandTypeEnum.MOBILE);

    HttpResult<Void> httpResult = remoteCommandService.publishRecoveryCommand(remoteCommandVo);
    if (!HttpResult.isSuccess(httpResult)) {
      throw new BusinessException(httpResult.getCode(), httpResult.getMessage());
    }

    // 清除接管操作缓存
    vehicleTakeOverRepository.remove(vehicleName);

    // 发送操作日志
    String log = JsonUtils.writeValueAsString(recoveryCommandVO);
    sendRemoteCommandRecordLog(log, WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue(), userName, sourceEnum.getCommandSource());
  }

  /**
   * <p>
   * 自动监测触发恢复
   * </p>
   */
  public void manualRecoveryRequest(String vehicleName, String commandSource) {
    VehicleTakeOverEntity vehicleTakeOverEntity = vehicleTakeOverRepository.get(vehicleName);
    if (Objects.isNull(vehicleTakeOverEntity) || !StringUtils.equalsAny(vehicleTakeOverEntity.getCommandSource(), commandSource)) {
      // 无接管或者非本端接管，无需处理
      return;
    }
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue());
    vehicleTakeOverRepository.remove(vehicleName);
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(vehicleName);
    remoteCommandVo.setReceiveTimeStamp(new Date());
    remoteCommandVo.setTransitTimeStamp(new Date());
    // 代表指令类型
    remoteCommandVo.setCommandType(RemoteCommandTypeEnum.MOBILE);
    HttpResult result = remoteCommandJsfService.publishRecoveryCommand(remoteCommandVo);
    log.info("车辆{}发送自动恢复指令结果{}", vehicleName, JsonUtils.writeValueAsString(result));
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
            WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue(), vehicleTakeOverEntity.getUserName(), commandSource);
  }

  /**
   * <p>
   * Post remote control request.
   * </p>
   *
   * @param monitorRemoteControlCommandVo the supervisor remote control request
   *                                         vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  @Transactional
  public WsResult postRemoteControlRequest(
          MonitorRemoteControlCommandVO monitorRemoteControlCommandVo) {
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo, "monitorRemoteControlCommandVo");
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo.getVehicleName(),
            "monitorRemoteControlCommandVo#vehicleName");
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo.getUserName(),
            "monitorRemoteControlCommandVo#userName");
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo.getTargetVelocity(),
            "monitorRemoteControlCommandVo#targetVelocity");
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo.getTargetAngle(),
            "monitorRemoteControlCommandVo#targetAngle");
    log.info("Receive remote control request {}.", monitorRemoteControlCommandVo);
    RemoteControlCommandVO remoteControlCommandVo = new RemoteControlCommandVO();
    remoteControlCommandVo.setVehicleName(monitorRemoteControlCommandVo.getVehicleName());
    remoteControlCommandVo.setId(monitorRemoteControlCommandVo.getId());
    remoteControlCommandVo.setModuleName(monitorRemoteControlCommandVo.getModuleName());
    remoteControlCommandVo.setTargetAngle(monitorRemoteControlCommandVo.getTargetAngle());
    remoteControlCommandVo.setTargetVelocity(monitorRemoteControlCommandVo.getTargetVelocity());
    remoteControlCommandVo
            .setCommandType(RemoteCommandTypeEnum.valueOf(monitorRemoteControlCommandVo.getCommandType()));
    remoteControlCommandVo.setReceiveTimeStamp(new Date());
    remoteControlCommandVo.setTransitTimeStamp(monitorRemoteControlCommandVo.getTimeStamp());
    remoteCommandJsfService.publishRemoteControlCommand(remoteControlCommandVo);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_REMOTE_CONTROL_COMMAND.getTopic(), monitorRemoteControlCommandVo.getVehicleName(), remoteControlCommandVo);
    return WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_CONTROL.getValue(),new MonitorRemoteCommandDTO());
  }

  /**
   * <p>
   * Post reset abnomal control request.
   * </p>
   *
   * @param minimonitorResetAbnormalCommandVO the supervisor remote request vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public WsResult postResetAbnormalControlRequest(
          MiniMonitorResetAbnormalControlCommandVO minimonitorResetAbnormalCommandVO, String userName) {
    ParameterCheckUtility.checkNotNull(minimonitorResetAbnormalCommandVO, "minimonitorResetAbnormalCommandVO");
    ParameterCheckUtility.checkNotNullNorEmpty(userName,
            "minimonitorResetAbnormalCommandVO#userName");
    log.info("Receive resetAbnormal command {}.", minimonitorResetAbnormalCommandVO);
    RemoteResetAbnormalCommandVO remoteResetAbnormalCommandVo = new RemoteResetAbnormalCommandVO();
    remoteResetAbnormalCommandVo
            .setCommandType(RemoteCommandTypeEnum.valueOf(minimonitorResetAbnormalCommandVO.getCommandType()));
    remoteResetAbnormalCommandVo.setId(minimonitorResetAbnormalCommandVO.getId());
    remoteResetAbnormalCommandVo.setModuleName(minimonitorResetAbnormalCommandVO.getModuleName());
    remoteResetAbnormalCommandVo.setReceiveTimeStamp(minimonitorResetAbnormalCommandVO.getTimeStamp());
    remoteResetAbnormalCommandVo.setTransitTimeStamp(new Date());
    remoteResetAbnormalCommandVo.setVehicleName(minimonitorResetAbnormalCommandVO.getVehicleName());
    remoteCommandJsfService.publishResetAbnormalCommand(remoteResetAbnormalCommandVo);
    return WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_RESET_ABNORMAL_CONTROL.getValue(),new MonitorRemoteCommandDTO());
  }

  /**
   * <p>
   * Post user control request.
   * </p>
   *
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public WsResult postUserControlRequest(MonitorRemoteControlCommandVO remoteCommandVo, String userName) {
    ParameterCheckUtility.checkNotNull(remoteCommandVo, "userControlCommandVO");
    log.info("Receive user operate request {}.", remoteCommandVo);
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL.getValue(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    return WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_USER_CONTROL.getValue(),new MonitorRemoteCommandDTO());
  }

  /**
   * <p>
   * Post as arrived request.
   * </p>
   *
   * @param miniMonitorAsArrivedRequestVo the as arrived request vo
   * @return The remote request response info includes its result and message.
   * @throws IllegalArgumentException If the argument does not meet the requirement.
   */
  public HttpResult postAsArrivedRequest(MiniMonitorAsArrivedRequestVO miniMonitorAsArrivedRequestVo) {
    String userName = UserUtils.getLoginUser();
    if (userName == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    log.info("Receive asArrived request {}, userName {}.", miniMonitorAsArrivedRequestVo, userName);
    MiniMonitorCommandService.ResultInfo resultInfo = isAbleToPost(miniMonitorAsArrivedRequestVo.getVehicleName(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("%s 在控制中", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_AS_ARRIVED.getValue());
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(miniMonitorAsArrivedRequestVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(miniMonitorAsArrivedRequestVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = remoteCommandJsfService.publishAsArrivedCommand(remoteCommandVo);
    try {
      sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(miniMonitorAsArrivedRequestVo),
              WebsocketEventTypeEnum.REMOTE_REQUEST_AS_ARRIVED.getValue(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    } catch (Exception e) {
      log.error("Store user remote command record exception", e);
    }
    if (!HttpResult.isSuccess(result)) {
      return result;
    }
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * <p>
   * Post control lamp request.
   * </p>
   *
   * @param miniMonitorRemoteCommandVO the remote command request vo
   * @return The remote request response info includes its result and message.
   * @throws IllegalArgumentException If the argument does not meet the requirement.
   */
  public HttpResult postLampRequest(MiniMonitorRemoteCommandVO miniMonitorRemoteCommandVO) {
    String userName = UserUtils.getLoginUser();
    if (userName == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    log.info("Receive control lamp request {}, userName {}.", miniMonitorRemoteCommandVO, userName);
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_LAMP_CONTROL.getValue());
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(miniMonitorRemoteCommandVO.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(miniMonitorRemoteCommandVO.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    remoteCommandVo.setCommandType(RemoteCommandTypeEnum.MOBILE);
    HttpResult result = remoteCommandJsfService.publishControlLampCommand(remoteCommandVo);
    try {
      sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(miniMonitorRemoteCommandVO),
              WebsocketEventTypeEnum.REMOTE_REQUEST_LAMP_CONTROL.getValue(), userName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
    } catch (Exception e) {
      log.error("Store user control lamp command record exception", e);
    }
    if (!HttpResult.isSuccess(result)) {
      return result;
    }
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * <p>
   * Is able to post.
   * </p>
   *
   * @param vehicleName the vehicle name
   * @param userName    the user name
   * @return ResultInfo
   */
  private MiniMonitorCommandService.ResultInfo isAbleToPost(String vehicleName, String userName, String commandSource) {
    MiniMonitorCommandService.ResultInfo resultInfo = new MiniMonitorCommandService.ResultInfo();
    Optional<VehicleTakeOverEntity> op = vehicleTakeOverRepository.getByKey(vehicleName);
    if (op.isPresent() && userName.equals(op.get().getUserName()) && StringUtils.equals(commandSource, op.get().getCommandSource())) {
      resultInfo.setAbleToPost(true);
      resultInfo.setHasRecord(true);
      resultInfo.setRecordName(op.get().getUserName());
      resultInfo.setCommandSource(op.get().getCommandSource());
    }  else if (op.isPresent() && StringUtils.equals(VehicleRemoteOperationStatusEnum.TEMPORARY.getOperationStatus(), op.get().getOperationStatus())) {
      resultInfo.setAbleToPost(true);
      resultInfo.setHasRecord(false);
    } else if (!op.isPresent()) {
      resultInfo.setAbleToPost(true);
      resultInfo.setHasRecord(false);
    } else {
      resultInfo.setAbleToPost(false);
      resultInfo.setHasRecord(true);
      resultInfo.setRecordName(op.get().getUserName());
      resultInfo.setCommandSource(op.get().getCommandSource());
    }
    return resultInfo;
  }

  /**
   * <p>
   * Send record log to kafka.
   * </p>
   *
   * @return ResultInfo
   */
  private void sendRemoteCommandRecordLog(String data,String eventType, String userName, String commandSource) {
    MonitorUserOperationEntity monitorUserOperationLog = new MonitorUserOperationEntity();
    monitorUserOperationLog.setUserName(userName);
    monitorUserOperationLog.setOperationType(eventType);
    monitorUserOperationLog.setTimestamp(new Date());
    monitorUserOperationLog.setOperationSource(commandSource);
    TypeReference<RemoteCommandVO> typeReference = new TypeReference<RemoteCommandVO>() {
    };
    RemoteCommandVO commandVo = JsonUtils.readValue(data, typeReference);
    monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
    monitorUserOperationLog.setOperationMessage(commandVo.toString());
   if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_AS_ARRIVED.getValue())) {
      monitorUserOperationLog.setOperationMessage( "One-time Command: As Arrived.");
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue())) {
      monitorUserOperationLog.setOperationMessage("Takeover: enable emergency stop");
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue())) {
      monitorUserOperationLog.setOperationMessage("Takeover: recovery. Disable takeover.");
    } else if (StringUtils.equalsAny(eventType, WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL.getValue())) {
     TypeReference<MonitorRemoteControlCommandVO> controlTypeReference = new TypeReference<MonitorRemoteControlCommandVO>() {
     };
     MonitorRemoteControlCommandVO remoteControlCommandVo = JsonUtils.readValue(data, controlTypeReference);
     monitorUserOperationLog.setOperationMessage("Takeover: enable remote control with " + remoteControlCommandVo.toString());
   } else if (StringUtils.equals(eventType,
           WebsocketEventTypeEnum.REMOTE_REQUEST_LAMP_CONTROL.getValue())) {
     monitorUserOperationLog.setOperationMessage("LampControl: enable control");
   } else if (StringUtils.equals(eventType,
           WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_BRAKE.getValue())) {
     monitorUserOperationLog.setOperationMessage("运营端遥控操作急刹");
   }
    String kafkaData = JsonUtils.writeValueAsString(monitorUserOperationLog);
    kafkaTemplate.send(MonitorKafkaTopicConstant.MONITER_REMOTE_COMMAND_LOG, kafkaData)
            .whenComplete((result, ex) -> {
              if (Objects.isNull(ex)) {
                log.info("Send {} remote command record log {} success", MonitorKafkaTopicConstant.MONITER_REMOTE_COMMAND_LOG, kafkaData);
              } else {
                log.error("Send remote command record log fail {}", ex.getStackTrace());
              }
            });
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_REMOTE_COMMAND_LOG.getTopic(), monitorUserOperationLog.getVehicleName(), monitorUserOperationLog);
  }
}
