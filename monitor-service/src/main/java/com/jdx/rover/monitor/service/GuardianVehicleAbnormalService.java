/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service;

import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.VehicleExceptionInfoDTO;
import com.jdx.rover.monitor.manager.abnormal.GuardianVehicleAbnormalManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.po.GuardianVehicleAbnormal;
import com.jdx.rover.monitor.repository.mapper.GuardianVehicleAbnormalMapper;
import com.jdx.rover.monitor.service.base.BaseService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAbnormalDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAbnormalDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 车辆异常service类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class GuardianVehicleAbnormalService extends BaseService<GuardianVehicleAbnormalMapper, GuardianVehicleAbnormal> {
  @Autowired
  private GuardianVehicleAbnormalManager guardianVehicleAbnormalManager;

  @Autowired
  private SingleVehicleManager singleVehicleManager;

  /**
   * 处理kafka消息
   *
   * @param messageList
   */
  public void handleMessage(List<String> messageList) {
    List<GuardianVehicleAbnormal> poList = new ArrayList<>();
    List<String> vehicleNameList = new ArrayList<>();
    for (String message : messageList) {
      VehicleAbnormalDTO vehicleAbnormalDTO = JsonUtils.readValue(message, VehicleAbnormalDTO.class);
      if (vehicleAbnormalDTO == null || vehicleAbnormalDTO.getVehicleName() == null) {
        log.error("读取异常信息为空!{}", message);
        continue;
      }
      List<GuardianVehicleAbnormal> list = convertDTOToPOList(vehicleAbnormalDTO);
      poList.addAll(list);
      vehicleNameList.add(vehicleAbnormalDTO.getVehicleName());
    }
    guardianVehicleAbnormalManager.saveOrUpdateBatchList(poList.stream().distinct().collect(Collectors.toList()));

    List<String> vehicleNameDistinctList = vehicleNameList.stream().distinct().collect(Collectors.toList());
    for (String vehicleName : vehicleNameDistinctList) {
      // 推送异常增量消息
      singleVehicleManager.pushSingleVehicleException(vehicleName);
    }
  }

  /**
   * 获取List
   *
   */
  public List<VehicleExceptionInfoDTO> listVehicleAbnoraml(String vehicleName, Date startDate, Date endDate) {
    log.info("读取异常信息请求{}，{}-{}", vehicleName, startDate, endDate);
    List<GuardianVehicleAbnormal> abnormalList = guardianVehicleAbnormalManager.
            listAbnormalBeforeTime(vehicleName,startDate,endDate);
    log.info("读取异常信息结果{}", JsonUtils.writeValueAsString(abnormalList));
    List<VehicleExceptionInfoDTO> exceptionInfoList = new ArrayList<>();
    if (CollectionUtil.isNotEmpty(abnormalList)) {
      exceptionInfoList = abnormalList.stream().
              filter(abnormal -> Objects.isNull(abnormal.getEndTime()) || abnormal.getEndTime().after(endDate)).
              map(abnormal -> {
        VehicleExceptionInfoDTO exceptionInfo = new VehicleExceptionInfoDTO();
        exceptionInfo.setErrorCode(abnormal.getErrorCode());
        exceptionInfo.setErrorLevel(abnormal.getErrorLevel());
        exceptionInfo.setErrorMessage(abnormal.getErrorMessage());
        exceptionInfo.setReportTime(abnormal.getStartTime());
        exceptionInfo.setTranslateMessage(abnormal.getTranslateMessage());
        return exceptionInfo;
      }).collect(Collectors.toList());
    }
    return exceptionInfoList;

  }

  /**
   * DTO转换为PO
   *
   * @param dto
   * @return
   */
  private List<GuardianVehicleAbnormal> convertDTOToPOList(VehicleAbnormalDTO dto) {
    List<GuardianVehicleAbnormal> result = new ArrayList<>();
    String vehicleName = dto.getVehicleName();
    Map<String, VehicleAbnormalDetailDTO> abnormalMap = dto.getAbnormalMap();
    for (VehicleAbnormalDetailDTO detailDTO : abnormalMap.values()) {
      GuardianVehicleAbnormal po = new GuardianVehicleAbnormal();
      po.setVehicleName(vehicleName);
      po.setModuleName(detailDTO.getModuleName());
      po.setErrorCode(detailDTO.getErrorCode());
      po.setErrorLevel(detailDTO.getErrorLevel());
      po.setErrorMessage(detailDTO.getErrorMsg());
      po.setStartTime(detailDTO.getStartTime());
      po.setEndTime(detailDTO.getEndTime());
      result.add(po);
    }
    return result;
  }
}