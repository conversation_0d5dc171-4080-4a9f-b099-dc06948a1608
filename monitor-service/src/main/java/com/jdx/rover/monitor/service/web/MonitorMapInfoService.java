/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.stop.StopBasicDTO;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.dto.MonitorMapVehicleRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.MonitorMetadataPositionDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleDTO;
import com.jdx.rover.monitor.dto.MonitorStationAndVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.MonitorStationMapInfoDTO;
import com.jdx.rover.monitor.dto.MonitorStationVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.MonitorStopMapInfoDTO;
import com.jdx.rover.monitor.dto.MonitorVehicleMapInfoDTO;
import com.jdx.rover.monitor.enums.MonitorPositionTypeEnum;
import com.jdx.rover.monitor.enums.UserMetaDataEnum;
import com.jdx.rover.monitor.manager.schedule.VehicleScheduleManager;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.stop.MetadataStopApiManager;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSchedulePncRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRealRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSelectTypeRepository;
import com.jdx.rover.monitor.vo.MonitorStationVehicleMapInfoRequestVO;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a controller for monitor vehicle map info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class MonitorMapInfoService {

  @Autowired
  private VehicleManager vehicleManager;

  @Autowired
  private VehicleScheduleRealRouteRepository realRouteRepository;

  @Autowired
  private VehicleSchedulePncRouteRepository pncRouteRepository;

  @Autowired
  private VehicleScheduleManager vehicleScheduleManager;

  @Autowired
  private MetadataStopApiManager stopApiManager;

  @Autowired
  private MetadataStationApiManager metadataStationApiManager;

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  
  @Autowired
  private VehicleSelectTypeRepository vehicleSelectTypeRepository;

  private static final double kHeadingOffset = Math.PI / 2;
  private static final double kCircleOffset = 2 * Math.PI;
  private static final double kConvertAngle = 180.0 / Math.PI;
  /**
   * <p>
   * Search station and vehicle map realtime info.
   * </p>
   */
  public HttpResult<MonitorStationAndVehicleMapInfoDTO> getStationAndVehicleMapInfo(MonitorStationVehicleMapInfoRequestVO stationVehicleMapInfoRequestVo) {
    ParameterCheckUtility.checkNotNull(stationVehicleMapInfoRequestVo, "stationVehicleMapInfoRequestVo");
    ParameterCheckUtility.checkNotNullNorEmpty(stationVehicleMapInfoRequestVo.getVehicleName(), "stationVehicleMapInfoRequestVo#vehicleName");
    MonitorStationAndVehicleMapInfoDTO stationAndVehicleMapInfDto = new MonitorStationAndVehicleMapInfoDTO();
    boolean isSingleVehicleRequest = stationVehicleMapInfoRequestVo.getVehicleName().size() == 1;
    Map<String, VehicleRealtimeInfoDTO> realtimeInfoDtoMap = vehicleRealtimeRepository.listMap(stationVehicleMapInfoRequestVo.getVehicleName());
    Integer stationId = isSingleVehicleRequest? vehicleManager.getBasicByName(
            stationVehicleMapInfoRequestVo.getVehicleName().get(0)).getStationId() : stationVehicleMapInfoRequestVo.getStationId();
    stationAndVehicleMapInfDto.setStation(buildStationMapInfo(stationId));
    Map<String, MonitorStationVehicleMapInfoDTO> vehicleMapInfoDtoMap = new HashMap<>();
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(stationVehicleMapInfoRequestVo.getVehicleName());
    Set<String> vedingVehicleList = vehicleSelectTypeRepository.get(VehicleBusinessTypeEnum.VENDING.getValue(), null);
    for (String vehicleName : stationVehicleMapInfoRequestVo.getVehicleName()) {
      VehicleRealtimeInfoDTO realtimeInfoDto = realtimeInfoDtoMap.get(vehicleName);
      if (realtimeInfoDto == null || StringUtils.equals(realtimeInfoDto.getSystemState(), SystemStateEnum.OFFLINE.getSystemState())) {
        continue;
      }
      MonitorStationVehicleMapInfoDTO vehicleMapInfoDto = new MonitorStationVehicleMapInfoDTO();
      vehicleMapInfoDto.setName(vehicleName);
      vehicleMapInfoDto.setBusinessType(vedingVehicleList.contains(vehicleName)? VehicleBusinessTypeEnum.VENDING.getValue(): 
        VehicleBusinessTypeEnum.DISPATCH.getValue());
      MapPointBO mapPoint = TransformUtility.toGCJ02Point(realtimeInfoDto.getLat(), realtimeInfoDto.getLon());
      if (mapPoint != null) {
        vehicleMapInfoDto.setLon(mapPoint.getLongitude());
        vehicleMapInfoDto.setLat(mapPoint.getLatitude());
      }
      MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(vehicleName);
      if (scheduleDto == null || StringUtils.equals(scheduleDto.getScheduleState(), VehicleScheduleState.WAITING.getVehicleScheduleState())) {
        vehicleMapInfoDtoMap.put(vehicleName, vehicleMapInfoDto);
        continue;
      }
      List pncRoutingPoint = pncRouteRepository.getByKey(vehicleName);
      vehicleMapInfoDto.setPlanningRoutingPoint(pncRoutingPoint);
      vehicleMapInfoDto.setStop(buildScheduleStopRouting(scheduleDtoMap.get(vehicleName)));
      if (isSingleVehicleRequest) {
        vehicleMapInfoDto.setFinishedRoutingPoint(realRouteRepository.getByKey(vehicleName));
      }
      vehicleMapInfoDtoMap.put(vehicleName, vehicleMapInfoDto);
    }
    stationAndVehicleMapInfDto.setVehicle(vehicleMapInfoDtoMap);
    return HttpResult.success(stationAndVehicleMapInfDto);
  }

  /**
   * <p>
   * Get vehicle realtime position info.
   * </p>
   */
  public Map<String, MonitorVehicleMapInfoDTO> getVehicleRealtimePositionInfo(MonitorStationVehicleMapInfoRequestVO stationVehicleMapInfoRequestVo) {
    ParameterCheckUtility.checkNotNull(stationVehicleMapInfoRequestVo, "stationVehicleMapInfoRequestVo");
    ParameterCheckUtility.checkNotNull(stationVehicleMapInfoRequestVo.getVehicleName(), "stationVehicleMapInfoRequestVo#vehicleName");
    List<VehicleRealtimeInfoDTO> resultList = vehicleRealtimeRepository.list(stationVehicleMapInfoRequestVo.getVehicleName());
    Map<String, MonitorVehicleMapInfoDTO> vehiclePositionMap = new HashMap<>();
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(stationVehicleMapInfoRequestVo.getVehicleName());
    resultList.stream().forEach(result -> {
      if (StringUtils.equals(SystemStateEnum.OFFLINE.getSystemState(), result.getSystemState())) {
        return;
      }
      MonitorVehicleMapInfoDTO mapInfoDTO = new MonitorVehicleMapInfoDTO();
      MapPointBO stopMapPoint = TransformUtility.toGCJ02Point(result.getLat(), result.getLon());
      if (stopMapPoint != null) {
        mapInfoDTO.setLat(stopMapPoint.getLatitude());
        mapInfoDTO.setLon(stopMapPoint.getLongitude());
      }
      mapInfoDTO.setName(result.getVehicleName());
      mapInfoDTO.setSystemState(result.getSystemState());
      mapInfoDTO.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
      mapInfoDTO.setTaskType(TaskType.NOTASK.getTaskType());
      MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(result.getVehicleName());
      if (scheduleDto != null) {
        mapInfoDTO.setScheduleState(scheduleDto.getScheduleState());
        mapInfoDTO.setTaskType(scheduleDto.getTaskType());
      }
      vehiclePositionMap.put(result.getVehicleName(), mapInfoDTO);
    });
    return vehiclePositionMap;
  }

  /**
   * <p>
   * Get vehicle realtime position info.
   * </p>
   */
  public MonitorVehicleMapInfoDTO getVehicleRealtimePositionInfo(String vehicleName) {
    ParameterCheckUtility.checkNotNull(vehicleName, "stationVehicleMapInfoRequestVo#vehicleName");
    VehicleRealtimeInfoDTO result = vehicleRealtimeRepository.get(vehicleName);
    MonitorVehicleMapInfoDTO mapInfoDTO = new MonitorVehicleMapInfoDTO();
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(Arrays.asList(vehicleName));
    if (Objects.isNull(result)) {
      return mapInfoDTO;
    }
    MapPointBO stopMapPoint = TransformUtility.toGCJ02Point(result.getLat(), result.getLon());
    if (stopMapPoint != null) {
      mapInfoDTO.setLat(stopMapPoint.getLatitude());
      mapInfoDTO.setLon(stopMapPoint.getLongitude());
    }
    mapInfoDTO.setName(result.getVehicleName());
    mapInfoDTO.setSystemState(result.getSystemState());
    mapInfoDTO.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
    mapInfoDTO.setTaskType(TaskType.NOTASK.getTaskType());
    MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(result.getVehicleName());
    if (scheduleDto != null) {
      mapInfoDTO.setScheduleState(scheduleDto.getScheduleState());
      mapInfoDTO.setTaskType(scheduleDto.getTaskType());
    }
    return mapInfoDTO;
  }

  /**
   * <p>
   * 获取车辆实时系统状态和调度状态信息。
   * </p>
   *
   * @param
   */
  public HttpResult getVehicleMapRealtimeInfo(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    MonitorMapVehicleRealtimeInfoDTO realtimeInfoDto = new MonitorMapVehicleRealtimeInfoDTO();
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (vehicleRealtimeInfoDto != null) {
      realtimeInfoDto.setSystemState(vehicleRealtimeInfoDto.getSystemState());
    }
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(Lists.newArrayList(vehicleName));
    MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(vehicleName);
    realtimeInfoDto.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
    realtimeInfoDto.setTaskType(TaskType.NOTASK.getTaskType());
    if (scheduleDto != null) {
      realtimeInfoDto.setScheduleState(scheduleDto.getScheduleState());
      realtimeInfoDto.setTaskType(scheduleDto.getTaskType());
    }

    return HttpResult.success(realtimeInfoDto);
  }

  /**
   * <p>
   * 获取车辆实时系统状态和调度状态信息。
   * </p>
   *
   * @param
   */
  public List getVehiclePncRoutingInfo(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    List pncRoutingPoint = pncRouteRepository.getByKey(vehicleName);
    if (CollectionUtils.isEmpty(pncRoutingPoint)) {
      return new ArrayList<>();
    }
    return pncRoutingPoint;
  }

  private MonitorStationMapInfoDTO buildStationMapInfo(Integer stationId) {
    Map<Integer, StationBasicDTO> stationMonitorBasicDtoMap = metadataStationApiManager.getStationById(stationId);
    StationBasicDTO stationMonitorBasicDto = stationMonitorBasicDtoMap.get(stationId);
    MonitorStationMapInfoDTO station = new MonitorStationMapInfoDTO();
    if (stationMonitorBasicDto != null) {
      station.setId(stationMonitorBasicDto.getStationId());
      station.setName(stationMonitorBasicDto.getStationName());
      if(stationMonitorBasicDto.getLatitude() != null && stationMonitorBasicDto.getLongitude() != null) {
        MapPointBO mapPoint = TransformUtility.toGCJ02Point(stationMonitorBasicDto.getLatitude(),
                stationMonitorBasicDto.getLongitude());
        if (mapPoint != null) {
          station.setLat(mapPoint.getLatitude());
          station.setLon(mapPoint.getLongitude());
        }
      }
    }
    return station;
  }

  public MonitorStationVehicleMapInfoDTO getSingleVehicleRouting(String vehicleName) {
    MonitorStationVehicleMapInfoDTO vehicleMapInfoDto = new MonitorStationVehicleMapInfoDTO();
    vehicleMapInfoDto.setName(vehicleName);
    VehicleRealtimeInfoDTO realtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (!Objects.isNull(realtimeInfoDto)) {
      MapPointBO mapPoint = TransformUtility.toGCJ02Point(realtimeInfoDto.getLat(), realtimeInfoDto.getLon());
      if (mapPoint != null) {
        vehicleMapInfoDto.setLon(mapPoint.getLongitude());
        vehicleMapInfoDto.setLat(mapPoint.getLatitude());
      }
    }
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(Arrays.asList(vehicleName));
    vehicleMapInfoDto.setPlanningRoutingPoint(pncRouteRepository.getByKey(vehicleName));
    vehicleMapInfoDto.setStop(buildScheduleStopRouting(scheduleDtoMap.get(vehicleName)));
    vehicleMapInfoDto.setFinishedRoutingPoint(realRouteRepository.getByKey(vehicleName));
    return vehicleMapInfoDto;
  }

  private List<MonitorStopMapInfoDTO> buildScheduleStopRouting(MonitorScheduleDTO scheduleDto) {
    if (Objects.isNull(scheduleDto) || StringUtils.equals(scheduleDto.getScheduleState(), VehicleScheduleState.WAITING.getVehicleScheduleState())) {
      return new ArrayList<>();
    }
    return scheduleDto.getStop().stream().map(stop -> {
      MonitorStopMapInfoDTO stopMapInfoDTO = new MonitorStopMapInfoDTO();
      stopMapInfoDTO.setId(stop.getId());
      stopMapInfoDTO.setName(stop.getStopName());
      stopMapInfoDTO.setGoalId(stop.getGoalId());
      stopMapInfoDTO.setStopAction(stop.getStopAction());
      if (stop.getLat() == null || stop.getLon() == null) {
        return stopMapInfoDTO;
      }
      MapPointBO stopMapPoint = TransformUtility.toGCJ02Point(stop.getLat(), stop.getLon());
      if (stopMapPoint != null) {
        stopMapInfoDTO.setLon(stopMapPoint.getLongitude());
        stopMapInfoDTO.setLat(stopMapPoint.getLatitude());
      }
      return stopMapInfoDTO;
    }).collect(Collectors.toList());
  }

  public MonitorMetadataPositionDTO getMetaDataPositionInfo(String metaType, String key, String positionType) {
    if (StringUtils.equals(metaType, UserMetaDataEnum.STATION.getType())) {
      Integer stationId = Integer.valueOf(key);
      Map<Integer, StationBasicDTO> stationMonitorBasicDtoMap = metadataStationApiManager.getStationById(stationId);
      StationBasicDTO stationMonitorBasicDto = stationMonitorBasicDtoMap.get(stationId);
      if (!Objects.isNull(stationMonitorBasicDto)) {
        return buildMetadataPosition(positionType,
                stationMonitorBasicDto.getLatitude(), stationMonitorBasicDto.getLongitude(), null);
      }
    } else if (StringUtils.equals(metaType, UserMetaDataEnum.VEHICLE.getType())) {
      VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(key);
      if (!Objects.isNull(vehicleRealtimeInfoDto)) {
        return buildMetadataPosition(positionType, vehicleRealtimeInfoDto.getLat(),
                vehicleRealtimeInfoDto.getLon(), vehicleRealtimeInfoDto.getHeading());
      }
    } else if (StringUtils.equals(metaType, UserMetaDataEnum.STOP.getType())) {
      Integer stopId = Integer.valueOf(key);
      StopBasicDTO stopBasicDto = stopApiManager.getStopInfoById(stopId);
      if (!Objects.isNull(stopBasicDto)) {
        return buildMetadataPosition(positionType, stopBasicDto.getLatitude(),
                stopBasicDto.getLongitude(), stopBasicDto.getHeading());
      }
    }
    return new MonitorMetadataPositionDTO();
  }

  private MonitorMetadataPositionDTO buildMetadataPosition(String positionType, Double latitude, Double longitude, Double heading) {
    MonitorMetadataPositionDTO positionDto = new MonitorMetadataPositionDTO();
    if (!Objects.isNull(heading)) {
      positionDto.setRadian(heading);
      double headingTf = kCircleOffset + kHeadingOffset - heading;
      double headingAngle = BigDecimal.valueOf(headingTf % kCircleOffset).doubleValue();
      positionDto.setHeading(headingAngle * kConvertAngle);
    }
    if (StringUtils.equalsIgnoreCase(positionType, MonitorPositionTypeEnum.GCJ02.getValue())) {
      MapPointBO stopMapPoint = TransformUtility.toGCJ02Point(latitude, longitude);
      if (stopMapPoint != null) {
        positionDto.setLongitude(stopMapPoint.getLongitude());
        positionDto.setLatitude(stopMapPoint.getLatitude());
      }
    } else {
      positionDto.setLatitude(latitude);
      positionDto.setLongitude(longitude);
    }
    return positionDto;
  }

}