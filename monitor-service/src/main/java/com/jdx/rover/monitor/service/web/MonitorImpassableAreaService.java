/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorImpassableAreaDTO;
import com.jdx.rover.monitor.dto.MonitorVehiclePositionDTO;
import com.jdx.rover.monitor.dto.tnta.ImpassableAreaAddDTO;
import com.jdx.rover.monitor.dto.tnta.ImpassableAreaResponseDTO;
import com.jdx.rover.monitor.dto.tnta.ImpassableAreaUpdateDTO;
import com.jdx.rover.monitor.dto.tnta.ImpassableAreaUtmDTO;
import com.jdx.rover.monitor.enums.CoordinateZoneEnum;
import com.jdx.rover.monitor.enums.ImpassableAreaEffectTypeEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.search.MonitorImpassableAreaSearch;
import com.jdx.rover.monitor.service.config.ImpassableAreaProperties;
import com.jdx.rover.monitor.vo.MonitorImpassableAreaAddVO;
import com.jdx.rover.monitor.vo.MonitorImpassableAreaUpdateVO;
import com.jdx.rover.monitor.vo.MonitorImpassableAreaVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a monitor impassable area service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class MonitorImpassableAreaService {

  /**
   * <p>
   * Represents the rest template.
   * </p>
   */
  @Autowired
  private RestTemplate restTemplate;

  /**
   * <p>
   * The configuration of impassable area.
   * </p>
   */
  @Autowired
  private ImpassableAreaProperties impassableAreaProperties;

  /**
   * <p>
   * The vehicle realtime repo.
   * </p>
   */
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  /**
   * <p>
   * Create impassable area.
   * </p>
   * 
   * @param monitorImpassableAreaAddVO the supervisorImpassableArea view object
   *                                   which contains all the information needed
   *                                   to update or create for impassable area.
   * @return operation result.
   * @throws IllegalArgumentException if the argument doesn't meet the
   *                                  requirement.
   */
  public HttpResult<MonitorImpassableAreaDTO> add(final MonitorImpassableAreaAddVO monitorImpassableAreaAddVO) {

    ParameterCheckUtility.checkNotNull(monitorImpassableAreaAddVO, "monitorImpassableAreaAddVO");

    ParameterCheckUtility.checkNotNullNorEmpty(monitorImpassableAreaAddVO.getCoords(),
            "monitorImpassableAreaAddVO#coords");
    ParameterCheckUtility.checkNotNull(monitorImpassableAreaAddVO.getCoordType(),
            "monitorImpassableAreaAddVO#coordType");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorImpassableAreaAddVO.getType(), "monitorImpassableAreaAddVO#type");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorImpassableAreaAddVO.getUtmZone(),
            "monitorImpassableAreaAddVO#utmZone");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorImpassableAreaAddVO.getUserName(),
            "monitorImpassableAreaAddVO#userName");

    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

    MultiValueMap<String, String> postData = new LinkedMultiValueMap<>();
    postData.add("coords", monitorImpassableAreaAddVO.getCoords());
    postData.add("coordType", String.valueOf(monitorImpassableAreaAddVO.getCoordType()));
    postData.add("utm", monitorImpassableAreaAddVO.getUtmZone());
    postData.add("type", monitorImpassableAreaAddVO.getType());
    postData.add("operator", monitorImpassableAreaAddVO.getUserName());
    postData.add("effectDate", String.valueOf(monitorImpassableAreaAddVO.getEffectType()));
    postData.add("effectStatus", String.valueOf(monitorImpassableAreaAddVO.getEffectState()));
    List<String> timeLimit = monitorImpassableAreaAddVO.getTimeLimit();
    if (!CollectionUtils.isEmpty(timeLimit)) {
      postData.add("timeLimit", StringUtils.join(timeLimit.toArray(), "|"));
    }
    postData.add("remark", monitorImpassableAreaAddVO.getRemark());
    HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<MultiValueMap<String, String>>(postData,
            httpHeaders);
    ResponseEntity<String> result = restTemplate.exchange(impassableAreaProperties.getAddUrl(), HttpMethod.POST,
            httpEntity, String.class);
    MonitorImpassableAreaDTO impassableAreaDto = new MonitorImpassableAreaDTO();
    if (result.getStatusCode().equals(HttpStatus.OK)) {
      ImpassableAreaAddDTO addDto = JsonUtils.readValue(result.getBody(), ImpassableAreaAddDTO.class);
      String successCode = "100";
      if (addDto != null && successCode.equalsIgnoreCase(addDto.getStateCode())) {
        impassableAreaDto.setId(Integer.valueOf(addDto.getStateCode()));
        return HttpResult.success(impassableAreaDto);
      }
    }
    return HttpResult.error(MonitorErrorEnum.ERROR_CALL_IMPASSABLE_RESOLVE_SERVICE.getCode(),
            "result resolve error");
  }

  /**
   * <p>
   * Delete impassable area.
   * </p>
   * 
   * @param monitorImpassableAreaVo the supervisorImpassableArea view object which
   *                                contains all the information needed to delete
   *                                for impassable area.
   * @return operation result.
   * @throws IllegalArgumentException if the argument doesn't meet the
   *                                  requirement.
   */
  public HttpResult delete(final MonitorImpassableAreaVO monitorImpassableAreaVo) {

    ParameterCheckUtility.checkNotNull(monitorImpassableAreaVo, "monitorImpassableAreaVo");
    ParameterCheckUtility.checkNotNull(monitorImpassableAreaVo.getId(), "monitorImpassableAreaVo#id");

    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    MultiValueMap<String, String> postData = new LinkedMultiValueMap<>();

    postData.add("objids", String.valueOf(monitorImpassableAreaVo.getId()));
    postData.add("operator", monitorImpassableAreaVo.getUserName());

    HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<MultiValueMap<String, String>>(postData,
        httpHeaders);
    ResponseEntity<String> result = restTemplate.exchange(impassableAreaProperties.getDelUrl(), HttpMethod.POST,
        httpEntity, String.class);
    if (result.getStatusCode().equals(HttpStatus.OK)) {
      return HttpResult.success();
    }
    return HttpResult.error(MonitorErrorEnum.ERROR_CALL_IMPASSABLE_REST_SERVICE.getCode(), "Rest service fail");
  }

  /**
   * <p>
   * Search impassable area to record data transform object.
   * </p>
   * 
   * @param pageableVo           The pageable view object.
   * @param impassableAreaSearch the search entity of impassable area.
   * @return The impassable area data transform object.
   */
  public HttpResult<PageDTO<MonitorImpassableAreaDTO>> search(PageVO pageableVo,
      MonitorImpassableAreaSearch impassableAreaSearch) {
    ParameterCheckUtility.checkNotNull(pageableVo, "pageableVo");
    ParameterCheckUtility.checkNotNegative(pageableVo.getPageSize(), "pageableVo#pageSize");
    ParameterCheckUtility.checkNotNegative(pageableVo.getPageNum(), "pageableVo#pageNumber");

    ParameterCheckUtility.checkNotNull(impassableAreaSearch, "impassableAreaSearch");
    ParameterCheckUtility.checkNotNullNorEmpty(impassableAreaSearch.getUtmLat(),
        "impassableAreaSearch#utmLat");
    ParameterCheckUtility.checkNotNullNorEmpty(impassableAreaSearch.getUtmLng(),
        "impassableAreaSearch#utmLng");
    ParameterCheckUtility.checkNotNullNorEmpty(impassableAreaSearch.getUtmZone(),
        "impassableAreaSearch#utmZone");
    ParameterCheckUtility.checkNotNull(impassableAreaSearch.getCoordType(),
        "impassableAreaSearch#coordType");
    ParameterCheckUtility.checkNotNull(impassableAreaSearch.getSortType(),
        "impassableAreaSearch#sortType");

    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    MultiValueMap<String, String> paraMap = new LinkedMultiValueMap<String, String>();
    String coord = new StringBuilder(impassableAreaSearch.getUtmLat()).append(",")
            .append(impassableAreaSearch.getUtmLng()).toString();
    paraMap.add("origin", "1");
    paraMap.add("coord", coord);
    paraMap.add("coordType", String.valueOf(impassableAreaSearch.getCoordType()));
    paraMap.add("utm", impassableAreaSearch.getUtmZone());
    if (impassableAreaSearch.getType() != null) {
      paraMap.add("type", StringUtils.join(impassableAreaSearch.getType().toArray(), ";"));
    }
    if (impassableAreaSearch.getCoordStatus() != null) {
      paraMap.add("status", String.valueOf(impassableAreaSearch.getCoordStatus()));
    }
    if (StringUtils.isNotBlank(impassableAreaSearch.getUserName())) {
      paraMap.add("operator", impassableAreaSearch.getUserName());
    }
    if (!CollectionUtils.isEmpty(impassableAreaSearch.getEffectState())) {
      paraMap.add("effectStatus", StringUtils.join(impassableAreaSearch.getEffectState().toArray(), ";"));
    }
    if (!CollectionUtils.isEmpty(impassableAreaSearch.getEffectType())
            && impassableAreaSearch.getEffectType().size() == 1) {
      paraMap.add("effectDate", String.valueOf(impassableAreaSearch.getEffectType().get(0)));
    }
    paraMap.add("pageNo", String.valueOf(pageableVo.getPageNum() - 1));
    paraMap.add("pageSize", String.valueOf(pageableVo.getPageSize()));

    if (StringUtils.isNotEmpty(impassableAreaSearch.getSidx())) {
      paraMap.add("orderField", impassableAreaSearch.getSidx());
    }
    paraMap.add("orderDesc", String.valueOf(impassableAreaSearch.getSortType()));
    HttpEntity<MultiValueMap<String, String>> httpEntity =
            new HttpEntity<MultiValueMap<String, String>>(paraMap, httpHeaders);
    ResponseEntity<String> result = restTemplate.exchange(impassableAreaProperties.getSearchUrl(),
            HttpMethod.POST, httpEntity, String.class);
    log.info(String.format("The impassable area search result: %s", result.getBody()));
    List<MonitorImpassableAreaDTO> impassableAreaList = new ArrayList<>();
    ImpassableAreaResponseDTO impassableAreaResponseDto = JsonUtils.readValue(result.getBody(), ImpassableAreaResponseDTO.class);
    if (HttpStatus.OK.equals(result.getStatusCode()) && impassableAreaResponseDto != null) {
      PageDTO<MonitorImpassableAreaDTO> pageInfo = new PageDTO();
      pageInfo.setList(impassableAreaList);
      pageInfo.setTotal(Optional.ofNullable(impassableAreaResponseDto.getRecordCount()).map(count -> count.longValue()).orElse(0L));
      pageInfo.setPageNum(Optional.ofNullable(impassableAreaResponseDto.getPageNo()).map(count -> count + 1).orElse(null));
      pageInfo.setPageSize(impassableAreaResponseDto.getPageSize());
      pageInfo.setPages(impassableAreaResponseDto.getPageCount());
      if (!CollectionUtils.isEmpty(impassableAreaResponseDto.getObjects())) {
        impassableAreaList.addAll(impassableAreaResponseDto.getObjects().stream().map(item -> item.convertMonitorDto()).collect(Collectors.toList()));
      }
      return HttpResult.success(pageInfo);
    }
    return HttpResult.error(MonitorErrorEnum.ERROR_CALL_IMPASSABLE_REST_SERVICE.getCode(), "Rest service fail");
  }

  /**
   * <p>
   * Update impassable area object.
   * </p>
   *
   * @param impassableAreaUpdateVo the update entity of impassable area.
   * @return The impassable area data transform object.
   */
  public HttpResult update(MonitorImpassableAreaUpdateVO impassableAreaUpdateVo) {
    ParameterCheckUtility.checkNotNull(impassableAreaUpdateVo, "impassableAreaUpdateVo");
    ParameterCheckUtility.checkNotNull(impassableAreaUpdateVo.getId(), "impassableAreaUpdateVo#id");
    ParameterCheckUtility.checkNotNull(impassableAreaUpdateVo.getUserName(), "impassableAreaUpdateVo#userName");
    if (ImpassableAreaEffectTypeEnum.SEASONAL.getValue().equals(impassableAreaUpdateVo.getEffectType())) {
      ParameterCheckUtility.checkNotNullNorEmpty(impassableAreaUpdateVo.getTimeLimit(), "impassableAreaUpdateVo#timeLimit");
    }
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    MultiValueMap<String, String> paraMap = new LinkedMultiValueMap<String, String>();
    paraMap.add("objid", String.valueOf(impassableAreaUpdateVo.getId()));
    List<String> timeLimit = impassableAreaUpdateVo.getTimeLimit();
    if (!CollectionUtils.isEmpty(timeLimit)) {
      paraMap.add("timelimit", StringUtils.join(timeLimit.toArray(), "|"));
    }
    paraMap.add("effectDate", String.valueOf(impassableAreaUpdateVo.getEffectType()));
    paraMap.add("remark", impassableAreaUpdateVo.getRemark());
    paraMap.add("operator", String.valueOf(impassableAreaUpdateVo.getUserName()));
    if (impassableAreaUpdateVo.getEffectState() != null) {
      paraMap.add("effectStatus", String.valueOf(impassableAreaUpdateVo.getEffectState()));
    }
    HttpEntity<MultiValueMap<String, String>> httpEntity =
            new HttpEntity<MultiValueMap<String, String>>(paraMap, httpHeaders);
    ResponseEntity<String> result = restTemplate.exchange(impassableAreaProperties.getUpdateUrl(),
            HttpMethod.POST, httpEntity, String.class);
    log.info(String.format("The impassable area update result: %s", result.getBody()));
    if (result.getStatusCode().equals(HttpStatus.OK)) {
      ImpassableAreaUpdateDTO updateDto = JsonUtils.readValue(result.getBody(), ImpassableAreaUpdateDTO.class);
      String successCode = "100";
      if (updateDto != null && successCode.equalsIgnoreCase(updateDto.getStateCode())) {
        return HttpResult.success();
      }
    }
    return HttpResult.error(MonitorErrorEnum.ERROR_CALL_IMPASSABLE_REST_SERVICE.getCode(),
            "Rest service fail");
  }

  /**
   * <p>
   * Search vehicle real position.
   * </p>
   * 
   * @param vehicleName The vehicle search entity.
   * @return The information of vehicle postion data transform objects.
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  public HttpResult getVehiclePosition(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmpty(vehicleName, "vehicleName");

    MonitorVehiclePositionDTO vehiclePostionDto = new MonitorVehiclePositionDTO();
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (vehicleRealtimeInfoDto == null || vehicleRealtimeInfoDto.getLat() == null || vehicleRealtimeInfoDto.getLon() == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_MAP_VEHICLE_ANSENT.getCode(), MonitorErrorEnum.ERROR_CALL_MAP_VEHICLE_ANSENT.getMessage());
    }
    vehiclePostionDto.setHeading(vehicleRealtimeInfoDto.getHeading());
    String coords = new StringBuilder().append(vehicleRealtimeInfoDto.getLon()).append(",")
        .append(vehicleRealtimeInfoDto.getLat()).toString();
    try {
      ResponseEntity<String> result =
          convertCoordinate(coords, CoordinateZoneEnum.WGS84, CoordinateZoneEnum.XCS);
      String utmCoords = null;
      if (!result.getStatusCode().equals(HttpStatus.OK)) {
        return HttpResult.error(MonitorErrorEnum.ERROR_CALL_IMPASSABLE_REST_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_IMPASSABLE_REST_SERVICE.getMessage());
      }
      log.info(String.format("The wgs84 coordinate convert xcs result: %s", result.getBody()));
      ImpassableAreaUtmDTO xcsUtm = JsonUtils.readValue(result.getBody(), ImpassableAreaUtmDTO.class);
      String successCode = "100";
      if (xcsUtm != null && successCode.equalsIgnoreCase(xcsUtm.getStateCode())) {
        ResponseEntity<String> utmResult = convertCoordinate(xcsUtm.getLocations(), CoordinateZoneEnum.XCS,
            CoordinateZoneEnum.UTM);
        if (utmResult.getStatusCode().equals(HttpStatus.OK)) {
          log.info(String.format("The xcs coordinate convert utm result: %s", utmResult.getBody()));
          ImpassableAreaUtmDTO utmDto = JsonUtils.readValue(utmResult.getBody(), ImpassableAreaUtmDTO.class);
          if (utmDto != null && utmDto.getLocations() != null) {
              utmCoords = utmDto.getLocations();
              vehiclePostionDto.setUtmZone(utmDto.getUtmZone());
          }
        }
      }
      if (StringUtils.isNotBlank(utmCoords) && utmCoords.contains(",")) {
        String[] lcs = utmCoords.split(",");
        vehiclePostionDto.setLon(Double.valueOf(lcs[0]));
        vehiclePostionDto.setLat(Double.valueOf(lcs[1]));
      }
    } catch (RestClientException e) {
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_IMPASSABLE_RESOLVE_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_IMPASSABLE_RESOLVE_SERVICE.getMessage());
    }
    return HttpResult.success(vehiclePostionDto);
  }

  /**
   * <p>
   * convert coordinate position.
   * </p>
   * 
   * @param coords The coords entity.
   * @param fromType The origin coordinate.
   * @param toType The need coordinate.
   * @return The responseEntity data transform objects.
   * @throws RestClientException if the rest fail.
   */
  private ResponseEntity<String> convertCoordinate(String coords, 
      CoordinateZoneEnum fromType, CoordinateZoneEnum toType) throws RestClientException {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    MultiValueMap<String, String> postData = new LinkedMultiValueMap<>();
    postData.add("coords", coords);
    postData.add("from", String.valueOf(fromType.getCoordinateZoneType()));
    postData.add("to", String.valueOf(toType.getCoordinateZoneType()));
    HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(postData, httpHeaders);
    return restTemplate.exchange(impassableAreaProperties.getUtmUrl(),
        HttpMethod.POST, httpEntity, String.class);
  }

}
