package com.jdx.rover.monitor.service.mobile;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.MqttMsgTypeEnum;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.manager.mobile.CommandManager;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteShoutVO;
import com.jdx.rover.monitor.vo.mobile.command.SendVO;
import com.jdx.rover.server.api.domain.enums.light.SwitchLightEnum;
import com.jdx.rover.server.api.domain.enums.light.SwitchTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * @description: CommandService
 * @author: wangguotai
 * @create: 2024-07-18 13:42
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class CommandService {

    private final CommandManager commandManager;

    /**
     * mqtt消息发送服务
     */
    private final MqttManager mqttManager;

    /**
     * 指令操作
     *
     * @param sendVO sendVO
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum send(SendVO sendVO) {
        String vehicleName = sendVO.getVehicleName();
        Date operateTime = sendVO.getOperateTime();
        CommandTypeEnum commandTypeEnum = CommandTypeEnum.of(sendVO.getCommandType());
        if (Objects.isNull(commandTypeEnum)) {
            return MonitorErrorEnum.ERROR_CALL_CHECK_PARAM;
        }
        switch (commandTypeEnum) {
            case REMOTE_POWER_ON -> {
                return commandManager.sendPdu(vehicleName, CommandTypeEnum.REMOTE_POWER_ON);
            }
            case REMOTE_SHUTDOWN -> {
                MonitorErrorEnum powerEnum = commandManager.remoteShutdown(vehicleName);
                MonitorErrorEnum pduEnum = commandManager.sendPdu(vehicleName, CommandTypeEnum.REMOTE_SHUTDOWN);
                if (MonitorErrorEnum.OK.equals(powerEnum) || MonitorErrorEnum.OK.equals(pduEnum)) {
                    return MonitorErrorEnum.OK;
                } else {
                    return MonitorErrorEnum.ERROR_PDU_CONNECT;
                }
            }
            case VEHICLE_REBOOT -> {
                MonitorErrorEnum powerEnum = commandManager.vehicleReboot(vehicleName);
                MonitorErrorEnum pduEnum = commandManager.sendPdu(vehicleName, CommandTypeEnum.VEHICLE_REBOOT);
                if (MonitorErrorEnum.OK.equals(powerEnum) || MonitorErrorEnum.OK.equals(pduEnum)) {
                    return MonitorErrorEnum.OK;
                } else {
                    return MonitorErrorEnum.ERROR_PDU_CONNECT;
                }
            }
            case ROVER_REBOOT -> {
                return commandManager.roverReboot(vehicleName, operateTime);
            }
            case LOW_LIGHT_OPEN -> {
                return commandManager.switchLight(vehicleName, SwitchTypeEnum.HEAD_LIGHT, SwitchLightEnum.TURN_ON, operateTime);
            }
            case LOW_LIGHT_CLOSE -> {
                return commandManager.switchLight(vehicleName, SwitchTypeEnum.HEAD_LIGHT, SwitchLightEnum.TURN_OFF, operateTime);
            }
            case LEFT_TURN_LIGHT_OPEN -> {
                return commandManager.switchLight(vehicleName, SwitchTypeEnum.TURN_LEFT, SwitchLightEnum.TURN_ON, operateTime);
            }
            case LEFT_TURN_LIGHT_CLOSE -> {
                return commandManager.switchLight(vehicleName, SwitchTypeEnum.TURN_LEFT, SwitchLightEnum.TURN_OFF, operateTime);
            }
            case RIGHT_TURN_LIGHT_OPEN -> {
                return commandManager.switchLight(vehicleName, SwitchTypeEnum.TURN_RIGHT, SwitchLightEnum.TURN_ON, operateTime);
            }
            case RIGHT_TURN_LIGHT_CLOSE -> {
                return commandManager.switchLight(vehicleName, SwitchTypeEnum.TURN_RIGHT, SwitchLightEnum.TURN_OFF, operateTime);
            }
            case FLASH_LIGHT_OPEN -> {
                return commandManager.switchLight(vehicleName, SwitchTypeEnum.WARNING_FLASH, SwitchLightEnum.TURN_ON, operateTime);
            }
            case FLASH_LIGHT_CLOSE -> {
                return commandManager.switchLight(vehicleName, SwitchTypeEnum.WARNING_FLASH, SwitchLightEnum.TURN_OFF, operateTime);
            }
            default -> {
                return MonitorErrorEnum.OK;
            }
        }
    }

    /**
     * <p>
     * 声音播报
     * </p>
     */
    public HttpResult remoteBroadCastWord(String vehicleName, MiniMonitorRemoteShoutVO remoteShoutVo) {
        String topic = "android/command/" + vehicleName;
        mqttManager.sendQos1NoRetainNoResponse(vehicleName, topic, MqttMsgTypeEnum.VEHICLE_MSG_BROADCAST.getValue(), remoteShoutVo);
        return HttpResult.success();
    }

    /**
     * <p>
     * 声音鸣笛
     * </p>
     */
    public HttpResult remoteVoiceWhistle(String vehicleName) {
        String topic = "android/command/" + vehicleName;
        mqttManager.sendQos1NoRetainNoResponse(vehicleName, topic, MqttMsgTypeEnum.VEHICLE_VOICE_WHISTLE.getValue(), null);
        return HttpResult.success();
    }
}