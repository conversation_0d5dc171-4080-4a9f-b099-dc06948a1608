/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.integrate.api.domain.message.IntegrateTaskInfoMessage;
import com.jdx.rover.monitor.dto.robot.RobotMapRouteInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.device.RobotScheduleDO;
import com.jdx.rover.monitor.entity.device.RobotScheduleDO.ParkStop;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.robot.RobotScheduleRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotTaskRouteRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 机器人调度服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotScheduleService {

    /**
     * 机器人调度信息服务。
     */
    private final RobotScheduleRepository robotScheduleRepository;

    /**
     * 机器人任务路由服务接口。
     */
    private final RobotTaskRouteRepository robotTaskRouteRepository;

    /**
     * 处理任务信息消息。
     * @param taskInfoMessage 集成任务信息消息对象。
     * @throws Exception 如果处理过程中出现异常。
     */
    public void addSchedule(IntegrateTaskInfoMessage taskInfoMessage) {
        if (StringUtils.equalsAny(taskInfoMessage.getTaskStatus(), "COMPLETE", "CANCEL")) {
            // 删除调度信息
            robotScheduleRepository.remove(taskInfoMessage.getRobotSn());
            // 删除路径规划信息
            robotTaskRouteRepository.remove(taskInfoMessage.getRobotSn());
            // 推送前端调度结束
            String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + taskInfoMessage.getRobotSn();
            RTopic rTopic = RedissonUtils.getRTopic(topicName);
            WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_UPDATE.getValue(), new RobotMapRouteInfoDTO());
            long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
            log.info("发送调度结束消息给页面端{}, 结果{}", taskInfoMessage.getRobotSn(), result);
            return;
        }
        RobotScheduleDO scheduleDo = new RobotScheduleDO();
        scheduleDo.setDeviceName(taskInfoMessage.getRobotName());
        scheduleDo.setTaskId(taskInfoMessage.getSubTaskId());
        scheduleDo.setBusinessNo(taskInfoMessage.getBusinessNo());
        scheduleDo.setShelfNo(taskInfoMessage.getShelfNo());
        scheduleDo.setTimestamp(new Date(taskInfoMessage.getTimestamp()));
        scheduleDo.setReminderTime(taskInfoMessage.getReminderTime());
        scheduleDo.setLatestOutTime(taskInfoMessage.getLatestOutTime());
        scheduleDo.setRecordTime(new Date());
        scheduleDo.setTaskNo(taskInfoMessage.getTaskNo());
        scheduleDo.setTaskType(taskInfoMessage.getTaskType());
        scheduleDo.setTaskTypeName(taskInfoMessage.getTaskTypeName());
        scheduleDo.setShelfTypeName(taskInfoMessage.getShelfTypeName());
        scheduleDo.setTaskStatus(taskInfoMessage.getTaskStatus());
        RobotScheduleDO.ParkStop parkStop = new RobotScheduleDO.ParkStop();
        parkStop.setStopId(taskInfoMessage.getParkPointId());
        parkStop.setStopName(taskInfoMessage.getParkPointName());
        parkStop.setX(taskInfoMessage.getParkPointX());
        parkStop.setY(taskInfoMessage.getParkPointY());
        scheduleDo.setParkStop(parkStop);
        robotScheduleRepository.set(taskInfoMessage.getRobotSn(), scheduleDo);
    }
}
