/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.vehicle;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 车辆位置服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/7
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleLocationService {

    public static Cache<String, VehicleLocation> CACHE = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.SECONDS)
            .maximumSize(10_000)
            .build();

    /**
     * 缓存运动中车辆
     */
    public void cacheInMotionVehicle(List<VehicleRealtimeInfoDTO> dtoList) {
        for (VehicleRealtimeInfoDTO dto : dtoList) {
            if (Objects.isNull(dto.getSpeed())) {
                continue;
            }
            if (dto.getSpeed() == 0) {
                continue;
            }
            VehicleLocation vehicleLocation = new VehicleLocation(dto.getLat(), dto.getLon(), new Date());
            CACHE.put(dto.getVehicleName(), vehicleLocation);
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VehicleLocation {

        /**
         * 纬度
         */
        private Double lat;

        /**
         * 经度
         */
        private Double lon;

        /**
         * 记录时间
         */
        private Date recordTime;
    }
}
