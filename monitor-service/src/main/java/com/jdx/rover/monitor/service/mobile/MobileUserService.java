/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.mobile;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.mobile.MobileUserConfigDTO;
import com.jdx.rover.monitor.dto.mobile.MobileUserMaskPhoneDTO;
import com.jdx.rover.monitor.dto.mobile.MobileUserStationNoticeConfigDTO;
import com.jdx.rover.monitor.dto.mobile.call.MobileAttentionPhoneDTO;
import com.jdx.rover.monitor.dto.mobile.call.MobileSafetyOfficerDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.UserAttentionEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.UserAttentionTypeEnum;
import com.jdx.rover.monitor.enums.mobile.MobileUserConfigEnum;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.repository.redis.UserAttentionEventRepository;
import com.jdx.rover.monitor.repository.redis.UserAttentionStationRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSearchRecordRepository;
import com.jdx.rover.monitor.vo.mobile.MobileUserConfigVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import com.jdx.rover.permission.domain.dto.basic.UserInfoDTO;
import com.jdx.rover.permission.domain.dto.basic.UserMaskPhoneDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 运营端用户配置服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MobileUserService {

    /**
     * 站点服务
     */
    private final MetadataStationApiManager metadataStationApiManager;

    /**
     * 车辆基础数据服务
     */
    private final VehicleBasicRepository vehicleBasicRepository;

    /**
     * 运管用户服务
     */
    private final MetadataUserApiManager metadataUserApiManager;

    /**
     * 用户搜索记录缓存
     */
    private final VehicleSearchRecordRepository vehicleSearchRecordRepository;

    /**
     * 用户关注缓存
     */
    private final UserAttentionEventRepository userAttentionEventRepository;

    /**
     * 用户关注站点缓存
     */
    private final UserAttentionStationRepository userAttentionStationRepository;

    /**
     * 添加车辆搜索记录
     *
     * @param vehicleName vehicleName
     */
    public void addSearchRecord(String vehicleName) {
        String userName = UserUtils.getAndCheckLoginUser();
        vehicleSearchRecordRepository.add(userName, vehicleName);
    }

    /**
     * 获取车辆搜索记录
     *
     * @return List<String>
     */
    public List<String> getVehicleSearchRecord() {
        String userName = UserUtils.getAndCheckLoginUser();
        return vehicleSearchRecordRepository.get(userName).stream().limit(10).collect(Collectors.toList());
    }

    /**
     * 清空用户车辆搜索记录
     */
    public void clearVehicleSearchRecord() {
        String userName = UserUtils.getAndCheckLoginUser();
        vehicleSearchRecordRepository.removeAll(userName);
    }

    /**
     * 获取用户信息
     *
     * @return UserExtendInfoDTO
     */
    public HttpResult<UserExtendInfoDTO> getUserInfo(String userName) {
        UserExtendInfoDTO extendInfoDto = metadataUserApiManager.getUserExtendInfoByName(userName);
        if (Objects.isNull(extendInfoDto)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
        }
        return HttpResult.success(extendInfoDto);
    }

    /**
     * 获取用户配置
     *
     * @return List<MobileUserConfigDTO>
     */
    public List<MobileUserConfigDTO> getUserConfig() {
        String userName = UserUtils.getAndCheckLoginUser();
        List<MobileUserConfigDTO> configList = new LinkedList<>();
        List<String> userAttentionList = userAttentionEventRepository.get(UserAttentionTypeEnum.BUMP.getType(), userName);
        MobileUserConfigDTO config = new MobileUserConfigDTO();
        config.setKey(MobileUserConfigEnum.CONFIG_ACCIDENT_VOICE_NOTIFY.getValue());
        config.setValue(String.valueOf(userAttentionList.size()));
        configList.add(config);
        return configList;
    }

    /**
     * 获取用户关注站点碰撞列表
     *
     * @return List<MobileUserStationNoticeConfigDTO>
     */
    public List<MobileUserStationNoticeConfigDTO> getConfigStationCrashList() {
        String userName = UserUtils.getAndCheckLoginUser();
        List<MobileUserStationNoticeConfigDTO> configStationList = new LinkedList<>();
        List<StationBasicDTO> stationList = metadataStationApiManager.getStationListByUser(userName);
        if (CollectionUtils.isEmpty(stationList)) {
            return configStationList;
        }
        List<String> userAttentionList = userAttentionEventRepository.get(UserAttentionTypeEnum.BUMP.getType(), userName);
        UserExtendInfoDTO userExtendInfo = metadataUserApiManager.getUserExtendInfoByName(userName);
        Map<Integer, Integer> attentionMap = userAttentionList.stream().collect(Collectors.toMap(Integer::valueOf, Integer::valueOf));
        configStationList = stationList.stream().map(station -> {
            MobileUserStationNoticeConfigDTO configDto = new MobileUserStationNoticeConfigDTO();
            configDto.setStationId(station.getStationId());
            configDto.setStationName(station.getStationName());
            boolean isOwnerStation = StringUtils.equalsIgnoreCase(station.getContact(), Optional.ofNullable(userExtendInfo).map(UserExtendInfoDTO::getPhone).orElse(null));
            configDto.setOwner(isOwnerStation);
            configDto.setOpened(isOwnerStation || Objects.equals(attentionMap.get(station.getStationId()), station.getStationId()));
            return configDto;
        }).collect(Collectors.toList());
        return configStationList;
    }

    /**
     * 配置用户项
     */
    public HttpResult<Void> updateConfigStation(MobileUserConfigVO userConfigVo) {
        String userName = UserUtils.getAndCheckLoginUser();
        if (StringUtils.equals(userConfigVo.getConfig(), MobileUserConfigEnum.CONFIG_ACCIDENT_VOICE_NOTIFY.getOperate())) {
            ParameterCheckUtility.checkNotNull(userConfigVo.getKey(), "userConfig#key");
            ParameterCheckUtility.checkNotNull(userConfigVo.getValue(), "userConfig#value");
            Integer stationId = (Integer) userConfigVo.getKey();
            String event = UserAttentionTypeEnum.BUMP.getType();
            StationBasicDTO basicStationInfo = metadataStationApiManager.getById(stationId);
            if (Objects.isNull(basicStationInfo)) {
                return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getMessage());
            }
            if ((boolean) userConfigVo.getValue()) {
                userAttentionEventRepository.set(event, userName, String.valueOf(stationId));
                UserExtendInfoDTO extendInfoDto = metadataUserApiManager.getUserExtendInfoByName(userName);
                if (Objects.isNull(extendInfoDto)) {
                    return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
                }
                UserAttentionEntity userAttentionEntity = new UserAttentionEntity();
                userAttentionEntity.setUserName(userName);
                userAttentionEntity.setPhone(extendInfoDto.getPhone());
                userAttentionStationRepository.set(stationId, event, userAttentionEntity);
            } else {
                // 如果是站点负责人，不支持取消
                UserInfoDTO userInfo = metadataUserApiManager.getUserInfoByPhone(basicStationInfo.getContact());
                if (!Objects.isNull(userInfo) && StringUtils.equals(userName, userInfo.getUserName())) {
                    return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_USER_ATTENTION.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_USER_ATTENTION.getMessage());
                }
                userAttentionEventRepository.delete(event, userName, String.valueOf(stationId));
                userAttentionStationRepository.delete(stationId, event, Lists.newArrayList(userName));
            }
        }
        return HttpResult.success();
    }

    /**
     * 获取车辆一线安全员
     *
     * @return List<MobileSafetyOfficerDTO>
     */
    public HttpResult<List<MobileSafetyOfficerDTO>> getSiteUserList(String vehicleName) {
        VehicleBasicDTO vehicleBasicDto = vehicleBasicRepository.get(vehicleName);
        if (Objects.isNull(vehicleBasicDto)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_VEHICLE_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_VEHICLE_ABSENT.getMessage());
        }
        StationBasicDTO stationBasicDto = metadataStationApiManager.getById(vehicleBasicDto.getStationId());
        if (Objects.isNull(stationBasicDto)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getMessage());
        }

        MobileSafetyOfficerDTO userOfficer = new MobileSafetyOfficerDTO();
        userOfficer.setUserName(stationBasicDto.getPersonName());
        userOfficer.setErp(stationBasicDto.getPersonErp());

        // 获取用户中文名
        UserExtendInfoDTO userExtendInfoDTO = metadataUserApiManager.getUserExtendInfoByName(stationBasicDto.getPersonName());
        if (ObjectUtil.isNotNull(userExtendInfoDTO)) {
            userOfficer.setRealName(userExtendInfoDTO.getRealName());
        }
        return HttpResult.success(Lists.newArrayList(userOfficer));
    }

    /**
     * 根据userName获取掩码手机号
     *
     * @param userName userName
     * @return MobileUserMaskPhoneDTO
     */
    public MobileUserMaskPhoneDTO getMaskPhone(String userName) {
        List<String> userNameList = new ArrayList<>();
        userNameList.add(userName);
        List<UserMaskPhoneDTO> userMaskPhoneDTOList = metadataUserApiManager.getUserPhoneInfoByName(userNameList);
        MobileUserMaskPhoneDTO mobileUserMaskPhoneDTO = new MobileUserMaskPhoneDTO();
        if (CollectionUtils.isNotEmpty(userMaskPhoneDTOList)) {
            mobileUserMaskPhoneDTO.setUserName(userMaskPhoneDTOList.get(0).getUserName());
            mobileUserMaskPhoneDTO.setMaskPhone(userMaskPhoneDTOList.get(0).getMaskPhone());
        }
        return mobileUserMaskPhoneDTO;
    }

    /**
     * 用户新增站点权限，如是站点负责人，则默认添加碰撞提醒
     */
    public void addStationOwnerAttention(List<String> increaseUserNameList, List<Integer> stationIdList) {
        Map<Integer, com.jdx.rover.metadata.domain.dto.station.StationBasicDTO> stationMap = metadataStationApiManager.getStationById(stationIdList.toArray(new Integer[0]));
        for (Map.Entry<Integer, StationBasicDTO> entry : stationMap.entrySet()) {
            UserInfoDTO userInfoDTO = metadataUserApiManager.getUserInfoByPhone(entry.getValue().getContact());
            if (Objects.isNull(userInfoDTO) || StringUtils.isBlank(userInfoDTO.getUserName())) {
                continue;
            }
            if (increaseUserNameList.contains(userInfoDTO.getUserName())) {
                userAttentionEventRepository.set(UserAttentionTypeEnum.BUMP.getType(), userInfoDTO.getUserName(), String.valueOf(entry.getKey()));
            }
        }
    }

    /**
     * 根据车号查询对应站点负责人手机号
     *
     * @param vehicleBaseVO vehicleBaseVO
     * @return MobileAttentionPhoneDTO
     */
    public HttpResult<MobileAttentionPhoneDTO> getStationPersonPhone(VehicleBaseVO vehicleBaseVO) {
        VehicleBasicDTO vehicleBasicDTO = vehicleBasicRepository.get(vehicleBaseVO.getVehicleName());
        if (Objects.isNull(vehicleBasicDTO)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_VEHICLE_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_VEHICLE_ABSENT.getMessage());
        }
        StationBasicDTO stationBasicDTO = metadataStationApiManager.getById(vehicleBasicDTO.getStationId());
        if (Objects.isNull(stationBasicDTO)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getMessage());
        }

        MobileAttentionPhoneDTO mobileAttentionPhoneDTO = new MobileAttentionPhoneDTO();
        mobileAttentionPhoneDTO.setPhone(stationBasicDTO.getContact());
        return HttpResult.success(mobileAttentionPhoneDTO);
    }
}