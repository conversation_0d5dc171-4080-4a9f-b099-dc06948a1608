package com.jdx.rover.monitor.service.listener.redis;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.dto.message.MessageDTO;
import com.jdx.rover.monitor.dto.vehicle.AlarmEventRealtimeDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleConnectDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 改变告警信息推送
 *
 * <AUTHOR>
 */
@Slf4j
public class ChangeAlarmEventMessageListener implements MessageListener<String> {
  private Session session;
  private WebsocketClientBO client;
  private final static TypeReference<WsResult<List<AlarmEventRealtimeDTO>>> typeReference = new TypeReference<WsResult<List<AlarmEventRealtimeDTO>>>() {
  };

  public ChangeAlarmEventMessageListener(Session session, WebsocketClientBO client) {
    this.session = session;
    this.client = client;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    WsResult input = JsonUtils.readValue(msg, WsResult.class);
    String vehicleName;
    if (Objects.equals(input.getEventType(), WebsocketEventTypeEnum.CHANGE_VEHICLE_CONNECT.getValue())) {
      // 只有多车页才推送连接变化消息
      String eventType = this.client.getEventType();
      if (StringUtils.isNotBlank(eventType) && !StringUtils.equals(eventType, WebsocketEventTypeEnum.MULTI_VEHICLE.getValue())) {
        return;
      }
      TypeReference<WsResult<VehicleConnectDTO>> typeReference = new TypeReference<WsResult<VehicleConnectDTO>>() {
      };
      WsResult<VehicleConnectDTO> inputConnect = JsonUtils.readValue(msg, typeReference);
      vehicleName = inputConnect.getData().getVehicleName();
    } else if (Objects.equals(input.getEventType(), WebsocketEventTypeEnum.COMMON_MESSAGE.getValue())) {
      TypeReference<WsResult<MessageDTO>> typeReference = new TypeReference<WsResult<MessageDTO>>() {
      };
      WsResult<MessageDTO> inputAlarm = JsonUtils.readValue(msg, typeReference);
      vehicleName = inputAlarm.getData().getVehicleName();
    } else {
      TypeReference<WsResult<List<AlarmEventRealtimeDTO>>> typeReference = new TypeReference<WsResult<List<AlarmEventRealtimeDTO>>>() {
      };
      WsResult<List<AlarmEventRealtimeDTO>> inputAlarm = JsonUtils.readValue(msg, typeReference);
      vehicleName = inputAlarm.getData().get(0).getVehicleName();
    }
    // 只有用户拥有此车才推送
    UserVehicleNameRepository userVehicleNameRepository = SpringUtil.getBean(UserVehicleNameRepository.class);
    boolean isContain = userVehicleNameRepository.isContainVehicleName(this.client.getUsername(), vehicleName);
    if (!isContain) {
      return;
    }
    String jsonStr = JsonUtils.writeValueAsString(input);
    synchronized (this.session) {
      try {
        if (session.isOpen()) {
          this.session.getBasicRemote().sendText(jsonStr);
        }
      } catch (IOException e) {
        throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getMessage());
      }
    }
    log.info("发送ws告警变化username={}, {}", this.client.getUsername(), jsonStr);
  }
}
