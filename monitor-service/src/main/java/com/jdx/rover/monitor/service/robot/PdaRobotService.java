/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.device.jsfapi.domain.enums.OperationTypeEnum;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceGroupMessageData;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceMessageData;
import com.jdx.rover.monitor.enums.device.DeviceNetworkTypeEnum;
import com.jdx.rover.monitor.enums.device.DevicePropertyCodeEnum;
import com.jdx.rover.monitor.enums.device.DeviceRealtimeStateEnum;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.pda.PdaRealtimeInfoManager;
import com.jdx.rover.monitor.po.pda.PdaRealtimeInfo;
import com.jdx.rover.transport.api.domain.dto.status.PropertyChangeDTO;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物联网PDA服务
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PdaRobotService implements IRobotDeviceService {

    /**
     * 机器人实时信息服务。
     */
    private final PdaRealtimeInfoManager pdaRealtimeInfoManager;

    /**
     * 设备实时服务接口
     */
    public final TransportDeviceApiManager realtimeDeviceApiManager;

    @Override
    public void batchUpdateDeviceGroup(Map<String, List<DeviceMessageData>> mapResult) {
        if (CollectionUtil.isEmpty(mapResult)) {
            return;
        }
        log.info("批量更新PDA基础分组信息数据量{}, 详情{}", mapResult.size(), JsonUtils.writeValueAsString(mapResult));
        List<DeviceMessageData> addDeviceList = mapResult.get(OperationTypeEnum.ADD.getValue());
        if (CollectionUtils.isNotEmpty(addDeviceList)) {
            List<PdaRealtimeInfo> pdaList = buildUpdateRealtimeInfo(addDeviceList);
            pdaRealtimeInfoManager.saveBatchData(pdaList.stream().map(pda -> {
                pda.setRealtimeStatus(DeviceRealtimeStateEnum.OFFLINE.getValue());
                return pda;
            }).collect(Collectors.toList()));
        }
        List<DeviceMessageData> updateDeviceList = mapResult.get(OperationTypeEnum.EDIT.getValue());
        if (CollectionUtils.isNotEmpty(updateDeviceList)) {
            pdaRealtimeInfoManager.updateBatch(buildUpdateRealtimeInfo(updateDeviceList));
        }
        List<DeviceMessageData> deleteDeviceList = mapResult.get(OperationTypeEnum.DELETE.getValue());
        if (CollectionUtils.isNotEmpty(deleteDeviceList)) {
            pdaRealtimeInfoManager.deleteBatch(buildUpdateRealtimeInfo(deleteDeviceList));
        }
    }

    /**
     * 批量更新设备状态。
     * @param deviceNameList 设备名称列表。
     */
    @Override
    public void batchUpdateDeviceState(List<String> deviceNameList) {
        if (CollectionUtils.isEmpty(deviceNameList)) {
            return;
        }
        List<Map<String, Object>> result = realtimeDeviceApiManager.getDeviceStatusList(deviceNameList,DevicePropertyCodeEnum.MONITOR_PDA_STATUS.getPropertyList());
        if (CollectionUtils.isEmpty(result)) {
            log.error("查询设备列表{}实时信息不存在", deviceNameList.toArray());
            return;
        }
        List<PdaRealtimeInfo> deviceList = result.stream().map(mapResult -> {
            PdaRealtimeInfo pdaRealtimeInfo = BeanUtil.mapToBean(mapResult, PdaRealtimeInfo.class, true);
            pdaRealtimeInfo.setRealtimeStatus(DeviceRealtimeStateEnum.OFFLINE.getValue());
            Object onlineStatus = mapResult.get("onlineStatus");
            if (Objects.nonNull(onlineStatus)) {
                pdaRealtimeInfo.setRealtimeStatus((Integer) onlineStatus == 1 ? DeviceRealtimeStateEnum.ONLINE.getValue() : DeviceRealtimeStateEnum.OFFLINE.getValue());
            }
            Object networkType = mapResult.get("networkType");
            if (Objects.nonNull(networkType)) {
                // 1:wifi 2:2G 3:3G 4:4G 5 蜂窝在线
                pdaRealtimeInfo.setNetworkType((Integer) networkType == 1 ? DeviceNetworkTypeEnum.WIFI.getValue() : DeviceNetworkTypeEnum.MOBILE.getValue());
            }
            return pdaRealtimeInfo;
        }).collect(Collectors.toList());
        try {
            pdaRealtimeInfoManager.batchUpdateState(deviceList);
        } catch (Exception e) {
            log.error("批量更新设备实时状态更新入库异常", e);
            // TODO: 失败发送异步处理，再次添加到下次的更新缓存数据中
        }
    }

    /**
     * 处理设备消息上报事件
     */
    @Override
    public void eventsReport(TransportDeviceMessage transportDeviceMessage) {

    }

    /**
     * 处理设备属性心跳消息
     * @param transportDeviceMessage 传输设备消息的内容
     */
    @Override
    public void propertyReport(String transportDeviceMessage) {

    }

    /**
     * 处理设备消息回复事件。
     * @param transportDeviceMessage 设备消息对象，包含消息内容和发送者信息。
     */
    @Override
    public void eventsReply(TransportDeviceMessage transportDeviceMessage) {

    }

    /**
     * 处理属性更改事件
     * @param propertyChangeDto 属性更改DTO对象，包含更改的属性信息
     */
    @Override
    public void statusChange(PropertyChangeDTO propertyChangeDto) {

    }

    /**
     * 构建PDA实时信息列表。
     */
    private List<PdaRealtimeInfo> buildUpdateRealtimeInfo(List<DeviceMessageData> deviceList) {
        List<PdaRealtimeInfo> pdaList =
                deviceList.stream().map(device -> {
                    PdaRealtimeInfo pdaRealtimeInfo = new PdaRealtimeInfo();
                    pdaRealtimeInfo.setDeviceName(device.getDeviceName());
                    pdaRealtimeInfo.setProductModelNo(device.getProductModelNo());
                    pdaRealtimeInfo.setProductModelName(device.getProductModelName());
                    pdaRealtimeInfo.setProductKey(device.getProductKey());
                    DeviceGroupMessageData deviceGroupMessageData = device.getDeviceGroupMessageData();
                    if (!Objects.isNull(deviceGroupMessageData)) {
                        if (Objects.isNull(deviceGroupMessageData.getLevel()) || deviceGroupMessageData.getLevel() == 1) {
                            pdaRealtimeInfo.setGroupOne(deviceGroupMessageData.getGroupNo());
                            pdaRealtimeInfo.setGroupName(deviceGroupMessageData.getGroupName());
                        } else if (deviceGroupMessageData.getLevel() == 2 && !Objects.isNull(deviceGroupMessageData.getParent())) {
                            pdaRealtimeInfo.setGroupOne(deviceGroupMessageData.getParent().getGroupNo());
                            pdaRealtimeInfo.setGroupTwo(deviceGroupMessageData.getGroupNo());
                            pdaRealtimeInfo.setGroupName(deviceGroupMessageData.getLevelName());
                        } else if (deviceGroupMessageData.getLevel() == 3 && !Objects.isNull(deviceGroupMessageData.getParent()) &&
                                !Objects.isNull(deviceGroupMessageData.getParent().getParent())) {
                            pdaRealtimeInfo.setGroupOne(deviceGroupMessageData.getParent().getParent().getGroupNo());
                            pdaRealtimeInfo.setGroupTwo(deviceGroupMessageData.getParent().getGroupNo());
                            pdaRealtimeInfo.setGroupThree(deviceGroupMessageData.getGroupNo());
                            pdaRealtimeInfo.setGroupName(deviceGroupMessageData.getLevelName());
                        }
                    }
                    return pdaRealtimeInfo;
                }).collect(Collectors.toList());
        return pdaList;
    }

}
