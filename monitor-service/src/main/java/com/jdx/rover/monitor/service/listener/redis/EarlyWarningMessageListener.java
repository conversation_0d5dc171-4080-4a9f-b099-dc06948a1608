/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.listener.redis;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.map.api.domain.dto.MapVariableUpgradeDTO;
import com.jdx.rover.monitor.service.area.EarlyWarningAreaService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

/**
 * 预警区域消息变化监听
 *
 * <AUTHOR>
 */
@Slf4j
public class EarlyWarningMessageListener implements MessageListener<String> {

    @Override
    public void onMessage(CharSequence channel, String msg) {
        try {
            EarlyWarningAreaService earlyWarningAreaService = SpringUtil.getBean(EarlyWarningAreaService.class);
            MapVariableUpgradeDTO mapVariable = JsonUtils.readValue(msg, MapVariableUpgradeDTO.class);
            earlyWarningAreaService.updateMapVariable(Integer.valueOf(mapVariable.getKey()));
        } catch (Exception e) {
            log.error("更新动态地图重点关注区域失败,msg={}", msg, e);
        }
    }
}
