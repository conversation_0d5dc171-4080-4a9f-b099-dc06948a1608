package com.jdx.rover.monitor.service.mobile;

import cn.hutool.core.util.CoordinateUtil;
import com.jdx.rover.metadata.api.domain.dto.vehicle.mobile.VehicleStopDTO;
import com.jdx.rover.metadata.api.domain.vo.vehicle.mobile.VehicleStopVO;
import com.jdx.rover.monitor.common.utils.jts.GeoUtils;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.mobile.freeDrive.ArbitraryNavigationDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetMapVehicleDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetStopListDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.NavigationDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mobile.SystemStatusEnum;
import com.jdx.rover.monitor.manager.mobile.MetadataVehicleMobileApiManager;
import com.jdx.rover.monitor.manager.mobile.ScheduleMiniMonitorFreeApiManager;
import com.jdx.rover.monitor.manager.mobile.VehiclePositionManager;
import com.jdx.rover.monitor.manager.utils.vehicle.SupplierUtils;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.vo.mobile.freeDrive.ArbitraryNavigationVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetMapVehicleVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetStopListVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetVehicleListVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.NavigationVO;
import com.jdx.rover.schedule.api.domain.vo.schedule.MiniMonitorFreeNavigationVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * @description: FreeDriveService
 * @author: wangguotai
 * @create: 2024-07-15 19:36
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class FreeDriveService {

    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    private final UserVehicleNameRepository userVehicleNameRepository;

    private final VehicleBasicRepository vehicleBasicRepository;

    private final VehicleScheduleRepository vehicleScheduleRepository;

    private final VehiclePositionManager vehiclePositionManager;

    private final MetadataVehicleMobileApiManager metadataVehicleMobileApiManager;

    private final ScheduleMiniMonitorFreeApiManager scheduleMiniMonitorFreeApiManager;

    /**
     * 获取车辆列表
     * TODO 考虑接口性能
     *
     * @return List<GetVehicleListDTO>
     */
    public List<GetVehicleListDTO> getVehicleList(GetVehicleListVO getVehicleListVO) {
        String username = UserUtils.getAndCheckLoginUser();
        // 获取用户权限下车辆
        Set<String> vehicleNameSet = userVehicleNameRepository.get(username);
        if (CollectionUtils.isEmpty(vehicleNameSet)) {
            log.info("User vehicle list empty.");
            return Collections.emptyList();
        }
        List<String> vehicleNameList = vehicleNameSet.stream().toList();
        // 获取车辆基础信息
        Map<String, VehicleBasicDTO> basicMap = vehicleBasicRepository.listMap(vehicleNameList);
        // 获取车辆实时信息
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
        // 获取车辆调度信息
        Map<String, MonitorScheduleEntity> scheduleMap = vehicleScheduleRepository.listMap(vehicleNameList);
        // 组装
        List<GetVehicleListDTO> dtoList = new ArrayList<>(vehicleNameList.size());
        for (String vehicleName : vehicleNameList) {
            VehicleBasicDTO basicDTO = basicMap.get(vehicleName);
            if (Objects.isNull(basicDTO) || !SupplierUtils.isJdVehicle(basicDTO.getSupplier())) {
                continue;
            }
            VehicleRealtimeInfoDTO realtimeDTO = realtimeMap.get(vehicleName);
            MonitorScheduleEntity scheduleDTO = scheduleMap.get(vehicleName);
            GetVehicleListDTO listDTO = new GetVehicleListDTO();
            listDTO.setVehicleName(vehicleName);
            Optional.ofNullable(basicDTO).ifPresent(v -> listDTO.setStationName(v.getStationName()));
            Optional.ofNullable(realtimeDTO).ifPresentOrElse(v -> {
                CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(v.getLon(), v.getLat());
                SystemStatusEnum systemStatusEnum = SystemStatusEnum.getCodeMapping(v.getSystemState());
                listDTO.setSystemStatus(systemStatusEnum.getCode());
                listDTO.setDistance(GeoUtils.haversineDistance(getVehicleListVO.getLatitude(), getVehicleListVO.getLongitude(), coordinate.getLat(), coordinate.getLng()));
            }, () -> {
                listDTO.setSystemStatus(SystemStatusEnum.OFFLINE.getCode());
            });
            listDTO.setBusinessStatus(!Objects.isNull(scheduleDTO));
            dtoList.add(listDTO);
        }
        // 排序（距离）
        dtoList.sort(Comparator.comparing(GetVehicleListDTO::getDistance, Comparator.nullsLast(Double::compareTo)));
        return dtoList;
    }

    /**
     * 获取地图上车辆
     *
     * @return List<GetMapVehicleDTO>
     */
    public List<GetMapVehicleDTO> getMapVehicle(GetMapVehicleVO getMapVehicleVO) {
        String username = UserUtils.getAndCheckLoginUser();
        // 获取用户权限下车辆
        Set<String> vehicleNameSet = userVehicleNameRepository.get(username);
        if (CollectionUtils.isEmpty(vehicleNameSet)) {
            log.info("User vehicle list empty.");
            return Collections.emptyList();
        }
        // 获取5km范围内车辆
        CoordinateUtil.Coordinate coordinate = CoordinateUtil.gcj02ToWgs84(getMapVehicleVO.getLongitude(), getMapVehicleVO.getLatitude());
        List<String> distanceVehicleList = vehiclePositionManager.getBaseMapper().getVehicleByDistance(coordinate.getLng(), coordinate.getLat(), 5000);
        if (CollectionUtils.isEmpty(distanceVehicleList)) {
            log.info("Distance vehicle list empty.");
            return Collections.emptyList();
        }
        // 获取车辆交集
        List<String> vehicleList = CollectionUtils.intersection(vehicleNameSet, distanceVehicleList).stream().toList();
        if (CollectionUtils.isEmpty(vehicleList)) {
            log.info("Intersection vehicle list empty.");
            return Collections.emptyList();
        }
        // 获取车辆实时信息
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleList);
        Map<String, VehicleBasicDTO> basicMap = vehicleBasicRepository.listMap(vehicleList);
        // 组装
        List<GetMapVehicleDTO> dtoList = new ArrayList<>(vehicleList.size());
        for (String vehicleName : vehicleList) {
            VehicleBasicDTO basicDTO = basicMap.get(vehicleName);
            if (Objects.isNull(basicDTO) || !SupplierUtils.isJdVehicle(basicDTO.getSupplier())) {
                continue;
            }
            VehicleRealtimeInfoDTO realtimeDTO = realtimeMap.get(vehicleName);
            GetMapVehicleDTO vehicleDTO = new GetMapVehicleDTO();
            vehicleDTO.setVehicleName(vehicleName);
            Optional.ofNullable(realtimeDTO).ifPresentOrElse(v -> {
                CoordinateUtil.Coordinate coo = CoordinateUtil.wgs84ToGcj02(v.getLon(), v.getLat());
                SystemStatusEnum systemStatusEnum = SystemStatusEnum.getCodeMapping(v.getSystemState());
                vehicleDTO.setSystemStatus(systemStatusEnum.getCode());
                vehicleDTO.setLongitude(coo.getLng());
                vehicleDTO.setLatitude(coo.getLat());
            }, () -> {
                vehicleDTO.setSystemStatus(SystemStatusEnum.OFFLINE.getCode());
            });
            dtoList.add(vehicleDTO);
        }
        return dtoList;
    }

    /**
     * 车辆选择校验
     *
     * @param vehicleName vehicleName
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum checkVehicle(String vehicleName) {
        // 判断系统状态
        VehicleRealtimeInfoDTO realtimeDTO = vehicleRealtimeRepository.get(vehicleName);
        if (Objects.isNull(realtimeDTO) || SystemStatusEnum.OFFLINE.getCode().equals(realtimeDTO.getSystemState())) {
            return MonitorErrorEnum.ERROR_FREE_DRIVE_SYSTEM_STATUS;
        }

        // 判断业务状态 2025-07-18 移除业务状态校验，改为调度系统自行校验，因为单点调度已改为正常调度，可能会影响重发
//        MonitorScheduleEntity scheduleDTO = vehicleScheduleRepository.get(vehicleName);
//        if (!Objects.isNull(scheduleDTO)) {
//            return MonitorErrorEnum.ERROR_FREE_DRIVE_BUSINESS_STATUS;
//        }
        // 判断是否存在影响运营维修单
        boolean require = metadataVehicleMobileApiManager.getRequire(vehicleName);
        if (require) {
            return MonitorErrorEnum.ERROR_FREE_DRIVE_REPAIR_STATUS;
        }
        return MonitorErrorEnum.OK;
    }

    /**
     * 获取去往停靠点列表
     *
     * @param getStopListVO getStopListVO
     * @return List<GetStopListDTO>
     */
    public List<GetStopListDTO> getStopList(GetStopListVO getStopListVO) {
        VehicleStopVO stopVO = new VehicleStopVO();
        stopVO.setVehicleNameList(getStopListVO.getVehicleNameList());
        List<VehicleStopDTO> stopDTOList = metadataVehicleMobileApiManager.getCommonStop(stopVO);
        // 组装
        List<GetStopListDTO> dtoList = new ArrayList<>(stopDTOList.size());
        stopDTOList.forEach(v -> {
            GetStopListDTO listDTO = new GetStopListDTO();
            listDTO.setStopId(v.getStopId());
            listDTO.setStopName(v.getStopName());
            dtoList.add(listDTO);
        });
        return dtoList;
    }

    /**
     * 立即出发
     *
     * @param navigationVO navigationVO
     * @return NavigationDTO
     */
    public NavigationDTO navigation(NavigationVO navigationVO) {
        MiniMonitorFreeNavigationVO freeNavigationVO = new MiniMonitorFreeNavigationVO();
        freeNavigationVO.setStopId(navigationVO.getStopId());
        freeNavigationVO.setVehicleNameList(navigationVO.getVehicleNameList());
        List<String> failVehicleList = scheduleMiniMonitorFreeApiManager.navigation(freeNavigationVO);
        // 组装
        NavigationDTO navigationDTO = new NavigationDTO();
        navigationDTO.setStopId(navigationVO.getStopId());
        navigationDTO.setStopName(navigationVO.getStopName());
        navigationDTO.setVehicleNameList(failVehicleList);
        return navigationDTO;
    }

    /**
     * 立即出发前往任意点
     *
     * @param arbitraryNavigationVO arbitraryNavigationVO
     * @return ArbitraryNavigationDTO
     */
    public ArbitraryNavigationDTO arbitraryNavigation(ArbitraryNavigationVO arbitraryNavigationVO) {
        MiniMonitorFreeNavigationVO freeNavigationVO = new MiniMonitorFreeNavigationVO();
        freeNavigationVO.setStopName(arbitraryNavigationVO.getStopName());
        freeNavigationVO.setLongitude(arbitraryNavigationVO.getLongitude());
        freeNavigationVO.setLatitude(arbitraryNavigationVO.getLatitude());
        freeNavigationVO.setHeading(arbitraryNavigationVO.getHeading());
        freeNavigationVO.setVehicleNameList(arbitraryNavigationVO.getVehicleNameList());
        List<String> failVehicleList = scheduleMiniMonitorFreeApiManager.navigation(freeNavigationVO);
        // 组装
        ArbitraryNavigationDTO arbitraryNavigationDTO = new ArbitraryNavigationDTO();
        arbitraryNavigationDTO.setStopName(arbitraryNavigationVO.getStopName());
        arbitraryNavigationDTO.setLongitude(arbitraryNavigationVO.getLongitude());
        arbitraryNavigationDTO.setLatitude(arbitraryNavigationVO.getLatitude());
        arbitraryNavigationDTO.setHeading(arbitraryNavigationVO.getHeading());
        arbitraryNavigationDTO.setVehicleNameList(failVehicleList);
        return arbitraryNavigationDTO;
    }
}