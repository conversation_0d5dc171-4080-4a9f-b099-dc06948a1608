/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot.alarm;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.robot.RobotReportAbnormalDTO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO.DeviceAlarmEventDO;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCodeEnum;
import com.jdx.rover.monitor.manager.robot.RobotAbnormalInfoManager;
import com.jdx.rover.monitor.po.robot.RobotAbnormalInfo;
import com.jdx.rover.monitor.repository.redis.robot.RobotAlarmRepository;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机器人主动上报告警服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotReportAlarmService {

    /**
     * 机器人异常信息服务。
     */
    private final RobotAbnormalInfoManager robotAbnormalInfoManager;

    /**
     * 机器人告警缓存服务。
     */
    private final RobotAlarmRepository robotAlarmRepository;

    /**
     * 机器人告警服务。
     */
    private final RobotAlarmService robotAlarmService;

    /**
     * 处理设备报警信息。
     */
    public void processReportAlarmInfo(TransportDeviceMessage transportDeviceMessage, String deviceName, String productType) {
        Map<String, RobotAlarmDO.DeviceAlarmEventDO> alarmMap = robotAlarmRepository.get(deviceName);
        RobotReportAbnormalDTO abnormalDto = JsonUtils.readValue(JsonUtils.writeValueAsString(transportDeviceMessage.getData()), RobotReportAbnormalDTO.class);
        String errorCode = StringUtils.equals(abnormalDto.getModuleName(), "BUMPER_EMERGENCY")?
                DeviceAlarmCodeEnum.VEHICLE_CRASH.getAlarmCode() : String.valueOf(abnormalDto.getCode());
        if (Objects.isNull(abnormalDto.getActive()) || abnormalDto.getActive() == Boolean.FALSE) {
            // 告警消失
            alarmMap.remove(errorCode);
            robotAlarmRepository.fastRemoveMapKey(deviceName, Arrays.asList(errorCode));
            robotAlarmService.endDeviceAbnormal(deviceName, productType, errorCode);
        } else if (Objects.isNull(alarmMap.get(errorCode))){
            DeviceAlarmEventDO deviceAlarmEventDo = new DeviceAlarmEventDO();
            deviceAlarmEventDo.setReportTime(new Date());
            deviceAlarmEventDo.setAbnormalModule(abnormalDto.getModuleName());
            deviceAlarmEventDo.setErrorCode(errorCode);
            deviceAlarmEventDo.setErrorLevel(abnormalDto.getAlarmLevel());
            deviceAlarmEventDo.setErrorMsg(abnormalDto.getMessage());
            alarmMap.put(deviceAlarmEventDo.getErrorCode(), deviceAlarmEventDo);
            robotAlarmRepository.add(deviceName, deviceAlarmEventDo.getErrorCode(), deviceAlarmEventDo);
            robotAlarmService.addDeviceAbnormal(deviceName, productType, deviceAlarmEventDo);
        }
        robotAlarmService.sendJmqMessage(deviceName, alarmMap.values());
  }

    /**
     * 处理机器人重启后结束未关闭告警
     */
    public void processRobotReboot(String productType, String deviceName) {
        Map<String, RobotAlarmDO.DeviceAlarmEventDO> currentAbnormalList = robotAlarmRepository.get(deviceName);
        if (MapUtils.isEmpty(currentAbnormalList)) {
            return;
        }
        robotAlarmRepository.remove(deviceName);
        LambdaUpdateWrapper<RobotAbnormalInfo> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(RobotAbnormalInfo::getProductKey, productType);
        updateWrapper.eq(RobotAbnormalInfo::getDeviceName, deviceName);
        updateWrapper.isNull(RobotAbnormalInfo::getEndTime);
        updateWrapper.set(RobotAbnormalInfo::getEndTime, new Date());
        robotAbnormalInfoManager.update(updateWrapper);
        //发送机器人告警变化通知
        robotAlarmService.sendJmqMessage(deviceName, new ArrayList<>());
    }
}
