package com.jdx.rover.monitor.service.listener.redis;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;

/**
 * 地图页车辆信息更新推送
 *
 * <AUTHOR>
 */
@Slf4j
public class WebterminalResponseListener implements MessageListener<String> {
  private Session session;

  public WebterminalResponseListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    synchronized (this.session) {
      try {
        this.session.getBasicRemote().sendText(msg);
      } catch (Exception e) {
        log.error("Send web terminal info exception", e);
      }
    }
  }
}
