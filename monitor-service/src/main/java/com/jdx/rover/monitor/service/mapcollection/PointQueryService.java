/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.mapcollection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.datacenter.api.enums.PointTypeEnum;
import com.jdx.rover.datacenter.domain.dto.map.PointQueryDTO;
import com.jdx.rover.datacenter.domain.dto.map.PointQueryDTO.PointDTO;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.stop.StopBasicDTO;
import com.jdx.rover.monitor.manager.datacenter.DataCenterCommonPointApiManager;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.stop.MetadataStopApiManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:52
 * @description 范围搜索
 */
@Service
@RequiredArgsConstructor
public class PointQueryService {

    /**
     * DataCenterCommonPointApiManager
     */
    private final DataCenterCommonPointApiManager dataCenterCommonPointApiManager;

    /**
     * MetadataStationApiManager
     */
    private final MetadataStationApiManager metadataStationApiManager;

    /**
     * MetadataStopApiManager
     */
    private final MetadataStopApiManager metadataStopApiManager;

    /**
     * 根据半径搜索用户权限下的站点和停靠点
     *
     * @param latitude latitude
     * @param longitude longitude
     * @param username username
     * @param radius radius
     * @return PointQueryDTO
     */
    public PointQueryDTO queryStationAndStopByRadiusAndUser(Double latitude, Double longitude, int radius, String username) {
        if (latitude == null || longitude == null || radius < 0) {
            PointQueryDTO pointQueryDTO = new PointQueryDTO();
            pointQueryDTO.setPointList(Collections.emptyList());
            return pointQueryDTO;
        }

        PointQueryDTO pointQueryDTO = dataCenterCommonPointApiManager.queryPointByRadius(latitude, longitude, radius, PointTypeEnum.STATION);
        if (Objects.isNull(pointQueryDTO) || CollUtil.isEmpty(pointQueryDTO.getPointList())) {
            pointQueryDTO = new PointQueryDTO();
            pointQueryDTO.setPointList(Collections.emptyList());
            return pointQueryDTO;
        }

        // 无需过滤
        if (StrUtil.isBlank(username)) {
            return pointQueryDTO;
        }

        // 按照用户权限站点过滤点位
        List<PointQueryDTO.PointDTO> filteredPoints = Lists.newArrayList();
        List<StationBasicDTO> stationList = metadataStationApiManager.getStationListByUser(username);
        if (CollUtil.isNotEmpty(stationList)) {
            Set<Integer> stationIds = stationList.stream().map(StationBasicDTO::getStationId).collect(Collectors.toSet());
            filteredPoints.addAll(pointQueryDTO.getPointList().stream().filter(pointDTO ->
                PointTypeEnum.STATION.getCode().equals(pointDTO.getPointType()) && stationIds.contains(pointDTO.getPointId())).collect(Collectors.toList()));

            // 查询用户权限各站点下的停靠点数据
            if (CollUtil.isNotEmpty(filteredPoints)) {
                Set<Integer> userStationIds = filteredPoints.stream().map(PointDTO::getPointId).collect(Collectors.toSet());
                userStationIds.forEach(stationId -> {
                    List<StopBasicDTO> stopByStationId = metadataStopApiManager.getStopByStationId(stationId);
                    if (CollUtil.isNotEmpty(stopByStationId)) {
                        stopByStationId.forEach(stopBasicDTO -> {
                            PointQueryDTO.PointDTO stop = new PointQueryDTO.PointDTO();
                            stop.setPointId(stopBasicDTO.getStopId());
                            stop.setPointName(stopBasicDTO.getStopName());
                            stop.setPointNumber(stopBasicDTO.getStopNumber());
                            stop.setPointType(PointTypeEnum.STOP.getCode());
                            stop.setLongitude(stopBasicDTO.getLongitude());
                            stop.setLatitude(stopBasicDTO.getLatitude());
                            stop.setEnabled(stopBasicDTO.getEnable());
                            filteredPoints.add(stop);
                        });
                    }
                });
            }
        }
        pointQueryDTO.setPointList(filteredPoints);
        return pointQueryDTO;
    }

    /**
     * 根据半径及用户权限查询站点
     *
     * @param latitude latitude
     * @param longitude longitude
     * @param radius radius
     * @param username username
     * @return filtered stationIds
     */
    public List<Integer> queryStationIdsByRadiusAndUser(Double latitude, Double longitude, int radius, String username) {
        if (latitude == null || longitude == null || radius < 0) {
            return Collections.emptyList();
        }

        // 搜索点位
        PointQueryDTO pointQueryDTO = dataCenterCommonPointApiManager.queryPointByRadius(latitude, longitude, radius, PointTypeEnum.STATION);
        if (Objects.isNull(pointQueryDTO) || CollUtil.isEmpty(pointQueryDTO.getPointList())) {
            return Collections.emptyList();
        }

        // 未提供用户名无需过滤
        if (StrUtil.isBlank(username)) {
            return pointQueryDTO.getPointList().stream().map(PointDTO::getPointId).collect(Collectors.toList());
        }

        // 按照用户权限站点过滤点位
        List<com.jdx.rover.metadata.domain.dto.station.StationBasicDTO> stationList = metadataStationApiManager.getStationListByUser(username);
        if (CollUtil.isEmpty(stationList)) {
            return Collections.emptyList();
        }
        Set<Integer> stationIds = stationList.stream().map(StationBasicDTO::getStationId).collect(Collectors.toSet());
        List<Integer> result = Lists.newArrayList();
        pointQueryDTO.getPointList().forEach(pointDTO -> {
            if (stationIds.contains(pointDTO.getPointId())) {
                result.add(pointDTO.getPointId());
            }
        });
        return result;
    }
}
