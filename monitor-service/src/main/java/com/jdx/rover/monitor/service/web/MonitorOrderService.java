/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.MonitorSecurityLogDTO;
import com.jdx.rover.monitor.dto.MonitorOrderDTO;
import com.jdx.rover.monitor.dto.MonitorOrderInfoDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.vo.MonitorOrderRequestVO;
import com.jdx.rover.monitor.vo.MonitorVerifyCodeRequestVO;
import com.jdx.rover.monitor.vo.MonitorViewOrderRequestVO;
import com.jdx.rover.schedule.api.domain.dto.schedule.ScheduleOrderDto;
import com.jdx.rover.schedule.api.domain.enums.DeliveryMode;
import com.jdx.rover.schedule.api.domain.enums.DeliveryStatus;
import com.jdx.rover.schedule.api.domain.enums.OrderSourceType;
import com.jdx.rover.schedule.api.domain.enums.StopAction;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import com.jdx.rover.schedule.api.domain.vo.schedule.MonitorVerifyCodeVo;
import com.jdx.rover.schedule.api.domain.vo.schedule.ScheduleOrderQueryVo;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMonitorJsfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a order record service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class MonitorOrderService {

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private ScheduleMonitorJsfService scheduleMonitorJsfService;

  @Autowired
  private JmqProducerService jmqProducerService;

  /**
   * <p>
   * Search order record.
   * </p>
   * 
   * @param monitorOrderRequestVo the order query object
   * @throws IllegalArgumentException if the argument doesn't meet the
   *                                  requirement.
   */
  public HttpResult search(MonitorOrderRequestVO monitorOrderRequestVo) {
    ParameterCheckUtility.checkNotNull(monitorOrderRequestVo, "monitorOrderRequestVo");
    ParameterCheckUtility.checkNotNull(monitorOrderRequestVo.getVehicleName(), "monitorOrderRequestVo#vehicleName");
    if (monitorOrderRequestVo.getGoalId() != null) {
      ParameterCheckUtility.checkNotNull(monitorOrderRequestVo.getStopAction(), "monitorOrderRequestVo#stopAction");
    }
    MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.get(monitorOrderRequestVo.getVehicleName());
    if (scheduleEntity == null || scheduleEntity.getScheduleNo() == null) {
      return HttpResult.success();
    }
    String stopAction = monitorOrderRequestVo.getStopAction();
    MonitorOrderDTO orderDto = new MonitorOrderDTO();
    orderDto.setScheduleStartTime(scheduleEntity.getStartDateTime());
    orderDto.setVehicleName(scheduleEntity.getVehicleName());
    String requestTaskType = scheduleEntity.getTaskType();
    if (StringUtils.isNotBlank(stopAction)) {
      if (StringUtils.equals(stopAction, StopAction.PICKUP.getStopAction())) {
        requestTaskType = TaskType.DELIVERY.getTaskType();
      } else if (StringUtils.equals(stopAction, StopAction.LOAD.getStopAction())) {
        requestTaskType =  TaskType.LOADTASK.getTaskType();
      }
      if (StringUtils.equals(stopAction, "START")) {
        orderDto.setStopName("起点");
      } else if (StringUtils.equals(stopAction, StopAction.RETURN.getStopAction())) {
        orderDto.setStopName(scheduleEntity.getStop().getLast().getName());
      } else {
        Optional<MonitorScheduleStopEntity> op = scheduleEntity.getStop().stream().filter(
                stop -> stop.getGoalId().equals(monitorOrderRequestVo.getGoalId())).findFirst();
        if (op.isPresent()) {
          orderDto.setStopName(op.get().getName());
        }
      }
    }
    orderDto.setTaskType(requestTaskType);
    orderDto.setOrder(getOrderInfoDtoList(scheduleEntity.getScheduleNo(),requestTaskType,
          monitorOrderRequestVo.getGoalId()));
    return HttpResult.success(orderDto);
  }

  /*
   * 重发订单短信验证法
   */
  public HttpResult sendVerifyCode(MonitorVerifyCodeRequestVO verifyCodeVo) {
    ParameterCheckUtility.checkNotNull(verifyCodeVo, "verifyCodeVo");
    ParameterCheckUtility.checkNotNull(verifyCodeVo.getVehicleName(), "verifyCodeVo#vehicleName");
    String vehicleName = verifyCodeVo.getVehicleName();
    MonitorVerifyCodeVo monitorVerifyCodeVo = new MonitorVerifyCodeVo();
    monitorVerifyCodeVo.setVehicleName(vehicleName);
    if (verifyCodeVo.getOrderId() != null) {
      monitorVerifyCodeVo.setOrderId(verifyCodeVo.getOrderId());
      return scheduleMonitorJsfService.getVerifyCode(monitorVerifyCodeVo);
    }
    ParameterCheckUtility.checkNotNullNorEmpty(verifyCodeVo.getOrderInfo(), "verifyCodeVo#orderInfo");
    verifyCodeVo.getOrderInfo().stream().collect(Collectors.collectingAndThen(
            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(s -> s.getContact()+ s.getDeliveryModel()))), ArrayList :: new))
            .forEach(info -> {
      monitorVerifyCodeVo.setOrderId(info.getOrderId());
              scheduleMonitorJsfService.getVerifyCode(monitorVerifyCodeVo);
    });
    return HttpResult.success();
  }

  /*
   * 查看订单详情记录
   */
  public HttpResult viewOrderInfo(String userName, MonitorViewOrderRequestVO viewOrderRequestVo) {
    MonitorSecurityLogDTO securityLogDto = new MonitorSecurityLogDTO();
    securityLogDto.setOrderNo(viewOrderRequestVo.getOrderNo());
    securityLogDto.setClientIp("*********");
    securityLogDto.setInterfaceName("com.jdx.rover.monitor.service.web.MonitorOrderService.videOrderInfo");
    securityLogDto.setPhone(viewOrderRequestVo.getPhone());
    securityLogDto.setReceiverAddress(viewOrderRequestVo.getStopName());
    securityLogDto.setReceiverUser(viewOrderRequestVo.getUser());
    securityLogDto.setUser(userName);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITER_VIEW_ORDER_DETAIL.getTopic(), securityLogDto);
    return HttpResult.success();
  }

  /*
   * 获取配送订单数据
   */
  private List<MonitorOrderInfoDTO> getOrderInfoDtoList(String scheduleNo, String scheduleTask, Integer goalId) {
    ScheduleOrderQueryVo deliveryOrderQueryVo = new ScheduleOrderQueryVo();
    deliveryOrderQueryVo.setScheduleNo(scheduleNo);
    deliveryOrderQueryVo.setGoalId(goalId);
    HttpResult<List<ScheduleOrderDto>> deliveryOrderInfos = scheduleMonitorJsfService.queryScheduleOrders(deliveryOrderQueryVo);
    if (!HttpResult.isSuccess(deliveryOrderInfos) || CollectionUtils.isEmpty(deliveryOrderInfos.getData())) {
      return new ArrayList<>();
    }
    return deliveryOrderInfos.getData().stream().filter(orderInfoDto -> filterOrderByDeliveryState(scheduleTask, orderInfoDto)).
            map(orderInfoDto -> builderOrderInfoFromDeliveryOrder(orderInfoDto)).collect(Collectors.toList());
  }

  private boolean filterOrderByDeliveryState(String taskType, ScheduleOrderDto orderDto) {
    String deliveryStatus = orderDto.getDeliveryStatus();
    if (StringUtils.equals(DeliveryStatus.DELETE.getDeliveryStatus(),deliveryStatus)) {
      return false;
    }
    if (StringUtils.equals(taskType, TaskType.UNLOADTASK.getTaskType())) {
      if (StringUtils.equalsAny(deliveryStatus, DeliveryStatus.CANCEL.getDeliveryStatus(), DeliveryStatus.INITIAL.getDeliveryStatus(),
              DeliveryStatus.USERCANCEL.getDeliveryStatus())) {
        return  false;
      }
    }
    return true;
  }

  private MonitorOrderInfoDTO builderOrderInfoFromDeliveryOrder(ScheduleOrderDto orderDto) {
    MonitorOrderInfoDTO orderInfoDto = new MonitorOrderInfoDTO();
    orderInfoDto.setId(orderDto.getOrderId());
    orderInfoDto.setOrderId(orderDto.getOriginalId());
    orderInfoDto.setName(orderDto.getName());
    if (StringUtils.isBlank(orderDto.getOrderSourceCode())) {
      orderInfoDto.setOrderSourceCode(OrderSourceType.VIRTUAL.getName());
    } else {
      orderInfoDto.setOrderSourceCode(orderDto.getOrderSourceCode());
    }
    orderInfoDto.setDeliveryModel(orderDto.getDeliveryModel());
    orderInfoDto.setDeliveryState(orderDto.getDeliveryStatus());
    orderInfoDto.setGridNoList(orderDto.getGridNo());
    orderInfoDto.setStopId(orderDto.getStopId());
    orderInfoDto.setPackageTotal(orderDto.getPackageTotal());
    if (StringUtils.equals(DeliveryMode.TRANSPORT.getDeliveryMode(), orderDto.getDeliveryModel())) {
      orderInfoDto.setPackageTotal(orderDto.getSubpackageCount());
    }
    orderInfoDto.setStopName(orderDto.getStopName());
    orderInfoDto.setContact(orderDto.getContact());
    orderInfoDto.setLoadStopId(orderDto.getLoadStopId());
    orderInfoDto.setLoadMethodType(orderDto.getLoadMethod());
    orderInfoDto.setLoadMethodName(orderDto.getLoadMethodName());
    orderInfoDto.setVerifyCode(orderDto.getVerifyCode());
    return orderInfoDto;
  }

}
