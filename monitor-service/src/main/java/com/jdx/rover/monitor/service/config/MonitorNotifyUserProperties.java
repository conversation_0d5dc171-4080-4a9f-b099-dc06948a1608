package com.jdx.rover.monitor.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <p>
 * This is static class that provides configuration properties for vehicle crash.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "monitor-notify")
@Data
@Validated
public class MonitorNotifyUserProperties {
  /**
   * <p>
   * Represents the base user.
   * </p>
   */
  @NotNull
  @NotBlank
  private String baseUser = "gulin21";

  /**
   * <p>
   * Represents single vehicle monitor url.
   * </p>
   */
  @NotNull
  @NotBlank
  private String monitorUrl = "https://jdxmonitor-beta.jdl.cn/app/single?license=";

}
