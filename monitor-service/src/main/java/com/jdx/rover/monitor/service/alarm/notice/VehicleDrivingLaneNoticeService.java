package com.jdx.rover.monitor.service.alarm.notice;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.infrastructure.api.domain.entity.bo.notice.jdme.JdMeNoticeMessage;
import com.jdx.rover.infrastructure.jsf.service.InfraNoticeJsfService;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.bo.vehicle.VehicleAlarmNoticeBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.message.NotifyMessageTypeEnum;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.repository.redis.user.UserNotifyRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 车辆行驶机动车道通知
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleDrivingLaneNoticeService implements AlarmNotice {

  @Autowired
  private VehicleManager vehicleManager;
  @Autowired
  private InfraNoticeJsfService infraNoticeJsfService;
  @Autowired
  private UserNotifyRepository userNotifyRepository;

  private VehicleAlarmNoticeBO buildVehicleAlarmNotice(AlarmNoticeProcessEnum alarmEnum, String vehicleName) {
    VehicleBasicDTO vehicleBasicDto = vehicleManager.getBasicByName(vehicleName);
    if (Objects.isNull(vehicleBasicDto)) {
      return null;
    }
    VehicleAlarmNoticeBO vehicleAlarmNoticeBO = new VehicleAlarmNoticeBO();
    vehicleAlarmNoticeBO.setVehicleName(vehicleName);
    vehicleAlarmNoticeBO.setTitle(alarmEnum.getTitle());
    vehicleAlarmNoticeBO.setContent(alarmEnum.getContent());
    StationBasicDTO stationBasicDto = new StationBasicDTO();
    stationBasicDto.setStationId(vehicleBasicDto.getStationId());
    stationBasicDto.setStationName(vehicleBasicDto.getStationName());
    vehicleAlarmNoticeBO.setStationInfo(stationBasicDto);
    vehicleAlarmNoticeBO.setDate(new Date());
    List<String> userAttention = userNotifyRepository.get(NotifyMessageTypeEnum.DRIVE_LANE.getValue());
    if (CollectionUtils.isEmpty(userAttention)) {
      return null;
    }
    vehicleAlarmNoticeBO.setUser(userAttention);
    return vehicleAlarmNoticeBO;
  }

  /*
   * 发送告警通知
   */
  private void sendDDMsg(VehicleAlarmNoticeBO vehicleAlarmNoticeBO) {
    JdMeNoticeMessage msgVo = new JdMeNoticeMessage();
    msgVo.setTitle(vehicleAlarmNoticeBO.getTitle());
    String content = String.format(vehicleAlarmNoticeBO.getContent(),
            vehicleAlarmNoticeBO.getStationInfo().getStationName(), vehicleAlarmNoticeBO.getVehicleName(), DateUtil.formatDateTime(new Date()));
    msgVo.setContent(content);
    msgVo.setErps(vehicleAlarmNoticeBO.getUser());
    infraNoticeJsfService.sendJdMeNotice(msgVo);
  }

  @Override
  public boolean process(AlarmNoticeProcessEnum alarmEvent, String vehicleName) {
    try {
      VehicleAlarmNoticeBO vehicleAlarmNoticeBO = buildVehicleAlarmNotice(alarmEvent, vehicleName);
      if (Objects.isNull(vehicleAlarmNoticeBO)) {
        return false;
      }
      sendDDMsg(vehicleAlarmNoticeBO);
    } catch (Exception e) {
      log.info("发送咚咚提醒消息异常:{}", vehicleName, e);
    }
    return true;
  }
}
