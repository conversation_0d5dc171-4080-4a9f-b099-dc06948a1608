/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.service.datacollection;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.device.jsfapi.domain.vo.devicecommand.ImmediateTaskCreateVO;
import com.jdx.rover.device.jsfapi.service.server.devicecommand.IntelligentDeviceServerDeviceCommandTaskInfoService;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionHmiAbnormalDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehicleExceptionDTO;
import com.jdx.rover.monitor.entity.datacollection.DataCollectionErrorInfoDO;
import com.jdx.rover.monitor.enums.device.DeviceCommandTaskEnum;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 数采车质检接口
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionHmiService {

    /**
     * 管理数据采集场景的增删改查等操作
     */
    private final DuccConfigProperties duccConfigProperties;

    /**
     * 指令服务
     */
    private final IntelligentDeviceServerDeviceCommandTaskInfoService commandTaskInfoService;

    /**
     * 管理单个车辆的运行状态及相关操作
     */
    private final SingleVehicleManager singleVehicleManager;

    /**
     * 推送车辆告警列表
     */
    public void pushVehicleAlarmList(String vehicleName) {
        // 获取开机或关机时刻车辆实时位置
        if (!duccConfigProperties.getDataCollectionVehicleList().contains(vehicleName)) {
            return;
        }
        log.info("推送车辆告警列表{}", vehicleName);
        List<SingleVehicleExceptionDTO> dtoList = singleVehicleManager.getSingleVehicleExceptionList(vehicleName);
        if (CollectionUtils.isEmpty(dtoList)) {
            // 推送HMI异常消除事件
            sendHmiAlarmMessage(vehicleName, new ArrayList<>());
            return;
        }
        Map<String, DataCollectionErrorInfoDO> dataCollectionErrorInfoMap = duccConfigProperties.getDataCollectionErrorMap();
        List<DataCollectionHmiAbnormalDTO.AbnormalInfo> abnormalInfoList = dtoList.stream().filter(abnormal -> Objects.nonNull(dataCollectionErrorInfoMap.get(abnormal.getErrorCode()))).map(abnormal -> {
            DataCollectionErrorInfoDO dataCollectionErrorInfoDO = dataCollectionErrorInfoMap.get(abnormal.getErrorCode());
            DataCollectionHmiAbnormalDTO.AbnormalInfo alarmInfo = new DataCollectionHmiAbnormalDTO.AbnormalInfo();
            alarmInfo.setAlarmNumber(abnormal.getErrorCode());
            alarmInfo.setErrorCode(abnormal.getErrorMessage());
            alarmInfo.setTimestamp(DateUtil.formatDateTime(abnormal.getReportTime()));
            alarmInfo.setCategory(dataCollectionErrorInfoDO.getCategory());
            alarmInfo.setDescription(dataCollectionErrorInfoDO.getDescription());
            alarmInfo.setAlarmTypeName(dataCollectionErrorInfoDO.getErrorCodeName());
            // 推送HMI异常事件
            return alarmInfo;
        }).collect(Collectors.toList());
        sendHmiAlarmMessage(vehicleName, abnormalInfoList);
    }

    /**
     * 发送HMI报警消息。
     * @param vehicleName 车辆报警变化对象。
     * @param currentAlarm 当前报警信息列表。
     */
    private void sendHmiAlarmMessage(String vehicleName, List<DataCollectionHmiAbnormalDTO.AbnormalInfo> currentAlarm) {
        ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(vehicleName);
        createVo.setProductKey(ProductTypeEnum.ROVER.getValue());
        createVo.setBlockNo("hmi");
        createVo.setIdentifier(DeviceCommandTaskEnum.VEHICLE_ERROR_UPDATE.getValue());
        DataCollectionHmiAbnormalDTO hmiAlarmDto = new DataCollectionHmiAbnormalDTO();
        hmiAlarmDto.setCurrentAlarm(currentAlarm);
        createVo.setCommandArgs(JsonUtils.writeValueAsString(hmiAlarmDto));
        log.info("推送车辆HMI端告警列表{}", JsonUtils.writeValueAsString(hmiAlarmDto));
        commandTaskInfoService.createImmediateTask(createVo);
    }

}