/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import com.jdx.rover.ota.jsf.OtaApplicationIssueInfoJsfService;
import com.jdx.rover.ota.jsf.OtaApplicationVersionJsfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * ota服务jsf配置
 *
 * <AUTHOR>
 * @date 2025/2/20
 */
@Slf4j
@Configuration
@Component
public class OtaJsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册 OtaApplicationIssueInfoJsfService
     */
    @Bean
    public OtaApplicationIssueInfoJsfService otaApplicationIssueInfoJsfService() {
        return jsfConsumerRegister.createConsumerConfig(OtaApplicationIssueInfoJsfService.class).refer();
    }

    /**
     * 注册 OtaApplicationVersionJsfService
     */
    @Bean
    public OtaApplicationVersionJsfService otaApplicationVersionJsfService() {
        return jsfConsumerRegister.createConsumerConfig(OtaApplicationVersionJsfService.class).refer();
    }



}