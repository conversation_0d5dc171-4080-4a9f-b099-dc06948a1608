package com.jdx.rover.monitor.service.mobile;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.CoordinateUtil;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.metadata.api.domain.enums.RoverBucketEnum;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.common.utils.fileUtil.FileUtil;
import com.jdx.rover.monitor.common.utils.jts.GeoUtils;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.mobile.common.CommonVehicleInfoDTO;
import com.jdx.rover.monitor.dto.mobile.common.FileDTO;
import com.jdx.rover.monitor.dto.mobile.common.GetSelectVehicleListDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mobile.SystemStatusEnum;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.manager.utils.vehicle.SupplierUtils;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.vo.mobile.common.GetSelectVehicleListVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * @description: CommonService
 * @author: wangguotai
 * @create: 2024-07-15 09:35
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class CommonService {

    private final S3Properties s3Properties;

    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    private final UserVehicleNameRepository userVehicleNameRepository;

    private final VehicleBasicRepository vehicleBasicRepository;

    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    private final MetadataVehicleApiManager metadataVehicleApiManager;

    /**
     * 文件上传
     *
     * @param file MultipartFile
     * @return FileDTO
     */
    public FileDTO fileUpload(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (!FileUtil.jdPathCheck(fileName)) {
            throw new AppException("文件名含有非法字符，不允许上传!");
        }
        String fileKey = DateUtil.format(new Date(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "_" + fileName;
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byte[] contentBytes = IoUtil.readBytes(file.getInputStream());
            byteArrayInputStream = new ByteArrayInputStream(contentBytes);
            S3Utils.uploadFile(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getEndpoint(), RoverBucketEnum.ROVER_OPERATION.getName(), fileKey, byteArrayInputStream);
        } catch (Exception e) {
            log.error("Failed to upload file to s3 {}.", fileKey, e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_UPLOAD.getCode(), MonitorErrorEnum.ERROR_CALL_UPLOAD.getMessage());
        } finally {
            IoUtil.close(byteArrayInputStream);
        }
        FileDTO fileDTO = new FileDTO();
        fileDTO.setFileKey(fileKey);
        return fileDTO;
    }

    /**
     * 获取选择车辆列表
     * TODO 考虑接口性能
     *
     * @param getSelectVehicleListVO getSelectVehicleListVO
     * @return List<GetSelectVehicleListDTO>
     */
    public List<GetSelectVehicleListDTO> getSelectVehicleList(GetSelectVehicleListVO getSelectVehicleListVO) {
        String username = UserUtils.getAndCheckLoginUser();
        // 获取用户权限下车辆
        Set<String> vehicleNameSet = userVehicleNameRepository.get(username);
        if (CollectionUtils.isEmpty(vehicleNameSet)) {
            log.info("User vehicle list empty.");
            return Collections.emptyList();
        }
        List<String> vehicleNameList = vehicleNameSet.stream().toList();
        // 获取车辆基础信息
        Map<String, VehicleBasicDTO> basicMap = vehicleBasicRepository.listMap(vehicleNameList);
        // 获取车辆实时信息
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
        // 获取车辆接管信息
        Map<String, VehicleTakeOverEntity> takeOverMap = vehicleTakeOverRepository.listMap(vehicleNameList);
        // 组装
        List<GetSelectVehicleListDTO> dtoList = new ArrayList<>(vehicleNameList.size());
        for (String vehicleName : vehicleNameList) {
            VehicleBasicDTO basicDTO = basicMap.get(vehicleName);
            if (Objects.isNull(basicDTO) || !SupplierUtils.isJdVehicle(basicDTO.getSupplier())) {
                continue;
            }
            VehicleRealtimeInfoDTO realtimeDTO = realtimeMap.get(vehicleName);
            VehicleTakeOverEntity takeOverDTO = takeOverMap.get(vehicleName);
            GetSelectVehicleListDTO listDTO = new GetSelectVehicleListDTO();
            listDTO.setVehicleName(vehicleName);
            Optional.ofNullable(basicDTO).ifPresent(v -> listDTO.setStationName(v.getStationName()));
            Optional.ofNullable(realtimeDTO).ifPresentOrElse(v -> {
                CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(v.getLon(), v.getLat());
                SystemStatusEnum systemStatusEnum = SystemStatusEnum.getCodeMapping(v.getSystemState());
                listDTO.setSystemStatus(systemStatusEnum.getCode());
                listDTO.setSystemStatusSort(systemStatusEnum.getSort());
                listDTO.setLongitude(coordinate.getLng());
                listDTO.setLatitude(coordinate.getLat());
                listDTO.setDistance(GeoUtils.haversineDistance(getSelectVehicleListVO.getLatitude(), getSelectVehicleListVO.getLongitude(), coordinate.getLat(), coordinate.getLng()));
            }, () -> {
                listDTO.setSystemStatus(SystemStatusEnum.OFFLINE.getCode());
                listDTO.setSystemStatusSort(SystemStatusEnum.OFFLINE.getSort());
            });
            Optional.ofNullable(takeOverDTO).ifPresent(v -> {
                listDTO.setTakeoverStatus(v.getOperationStatus());
                listDTO.setTakeoverSource(v.getCommandSource());
                listDTO.setTakeoverUserName(v.getUserName());
            });
            dtoList.add(listDTO);
        }
        // 排序（系统状态+距离）
        dtoList.sort(Comparator.comparing(GetSelectVehicleListDTO::getSystemStatusSort, Comparator.nullsLast(Integer::compareTo)).thenComparing(GetSelectVehicleListDTO::getDistance, Comparator.nullsLast(Double::compare)));
        return dtoList;
    }

    /**
     * 判断车辆能够提报维修单
     *
     * @param vehicleName vehicleName
     * @return Boolean
     */
    public Boolean requireCheck(String vehicleName) {
        return metadataVehicleApiManager.vehicleRequireCheck(vehicleName);
    }

    /**
     * 获取车辆基本信息（经纬度、系统状态、接管信息）
     *
     * @param vehicleName vehicleName
     * @return CommonVehicleInfoDTO
     */
    public CommonVehicleInfoDTO getVehicleInfo(String vehicleName) {
        CommonVehicleInfoDTO commonVehicleInfoDTO = new CommonVehicleInfoDTO();
        String userName = UserUtils.getAndCheckLoginUser();
        if (StringUtils.isNotBlank(userName)) {
            Set<String> userVehicleSet = userVehicleNameRepository.get(userName);
            commonVehicleInfoDTO.setCoverPermission(userVehicleSet.contains(vehicleName));
        }
        // 获取车辆实时信息
        VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
        if (null != vehicleRealtimeInfoDTO) {
            commonVehicleInfoDTO.setSystemStatus(vehicleRealtimeInfoDTO.getSystemState());
            MapPointBO mapPoint = TransformUtility.toGCJ02Point(vehicleRealtimeInfoDTO.getLat(), vehicleRealtimeInfoDTO.getLon());
            if (mapPoint != null) {
                commonVehicleInfoDTO.setLongitude(mapPoint.getLongitude());
                commonVehicleInfoDTO.setLatitude(mapPoint.getLatitude());
            }
        }

        // 车辆接管/临时停车状态
        VehicleTakeOverEntity vehicleTakeOverEntity = vehicleTakeOverRepository.get(vehicleName);
        if (null != vehicleTakeOverEntity) {
            commonVehicleInfoDTO.setTakeoverUserName(vehicleTakeOverEntity.getUserName());
            commonVehicleInfoDTO.setTakeoverSource(vehicleTakeOverEntity.getCommandSource());
            commonVehicleInfoDTO.setTakeoverStatus(vehicleTakeOverEntity.getOperationStatus());
        }

        return commonVehicleInfoDTO;
    }
}