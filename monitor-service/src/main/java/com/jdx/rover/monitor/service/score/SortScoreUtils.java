package com.jdx.rover.monitor.service.score;

import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleGroupsSortEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleScheduleStateSortEnum;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * 排序分数服务
 *
 * <AUTHOR>
 */
@Slf4j
public class SortScoreUtils {
  public static double getScore(VehicleScoreBO bo) {
    //调用对应的策略方法
    UpdateScoreTypeEnum em = UpdateScoreTypeEnum.valueOf(bo.getScoreType());
    Function<VehicleScoreBO, Double> function = em.getFunction();
    if (Objects.isNull(function)) {
      log.error("车辆分数类型不存在={}", bo.getVehicleName());
      return 0;
    }
    Double score = function.apply(bo);
    return score.doubleValue();
  }

  /**
   * 通过电量获取分数
   *
   * @param power
   * @return
   */
  private static double getPowerScore(double power) {
    // 只有2位,防止溢出
    double score = power;
    if (score > 99) {
      score = 99;
    }
    return score;
  }

  /**
   * 获取站点分数
   */
  private static int getStationScore(String stationName, List<String> stationSortList) {
    Assert.notNull(stationName, "站点名称不能为空!");
    Assert.notEmpty(stationSortList, "站点名称列表不能为空!");
    return stationSortList.indexOf(stationName);
  }

  /**
   * 通过业务类型获取分数
   *
   * @param businessType
   * @return
   */
  private static double getBusinessTypeScore(String businessType) {
    double score = Objects.equals(businessType, VehicleBusinessTypeEnum.DISPATCH.getValue()) ? 10 : 20;
    return score;
  }

  /**
   * 通过过车号获取分数
   */
  public static double getVehicleNameScore(String vehicleName, List<String> vehicleNameSortList) {
    Assert.notNull(vehicleName, "车辆名称不能为空!");
    Assert.notEmpty(vehicleNameSortList, "车辆名称列表不能为空!");
    double scale = 1.0 / vehicleNameSortList.size();
    int vehicleNameIndex = vehicleNameSortList.indexOf(vehicleName);
    if (vehicleNameIndex < 0) {
      // -1导致分数相减,会有跳变
      vehicleNameIndex = 0;
    }
    double score = scale * vehicleNameIndex;
    return score;
  }

  /**
   * 通过告警获取分数
   * 根据场景分组展示。顺序：失联、碰撞、传感器异常、运营异常、停车异常、电量异常、胎压异常
   *
   * @return
   */
  protected static double getAlarmEventScore(String systemState, String alarmEvent) {
    Assert.notNull(systemState, "系统状态不能为空!");
    if (Objects.equals(systemState, SystemStateEnum.OFFLINE.getSystemState())
        || Objects.equals(systemState, SystemStateEnum.CONNECTION_LOST.getSystemState())) {
      alarmEvent = systemState;
    } else if (StringUtils.isBlank(alarmEvent)) {
      // 以告警为准,告警空的时候归属到正常(处理系统故障MALFUNCTION和自修复中SELF_REPAIR,后续有告警则取告警分数)
      alarmEvent = SystemStateEnum.NORMAL.getSystemState();
      if (!Objects.equals(systemState, SystemStateEnum.NORMAL.getSystemState())) {
        log.info("更新分数系,统状态={},告警为空", systemState);
      }
    }
    for (VehicleGroupsSortEnum em : VehicleGroupsSortEnum.values()) {
      if (em.name().equals(alarmEvent)) {
        return em.getValue();
      }
    }
    return VehicleGroupsSortEnum.OFFLINE.getValue();
  }

  /**
   * 通过调度状态取分数
   * 失联组车辆按照“配送状态”排序。（去程->返程->去装载->去卸载->投递中->装载中->卸载中->已装载->已卸载->已到达->待装载）没有配送状态车辆在最后
   *
   * @return
   */
  public static double getScheduleStateScore(String scheduleState) {
    for (VehicleScheduleStateSortEnum em : VehicleScheduleStateSortEnum.values()) {
      if (em.name().equals(scheduleState)) {
        return em.getValue();
      }
    }
    return VehicleScheduleStateSortEnum.WAITING.getValue();
  }

  /**
   * 获取电量分数
   */
  protected static double getPowerScore(VehicleScoreBO bo) {
    double vehicleNameScore = getVehicleNameScore(bo.getVehicleName(), bo.getVehicleNameSortList());
    double powerScore = getPowerScore(bo.getPower());
    double alarmEventScore = getAlarmEventScore(bo.getSystemState(), bo.getAlarmEvent());
    double score = vehicleNameScore + powerScore + alarmEventScore;
    return score;
  }

  /**
   * 获取车号分数
   */
  protected static double getVehicleNameScore(VehicleScoreBO bo) {
    double vehicleNameScore = getVehicleNameScore(bo.getVehicleName(), bo.getVehicleNameSortList());
    double alarmEventScore = getAlarmEventScore(bo.getSystemState(), bo.getAlarmEvent());
    double score = vehicleNameScore + alarmEventScore;
    return score;
  }

  /**
   * 获取业务类型分数
   */
  protected static double getBusinessTypeScore(VehicleScoreBO bo) {
    double vehicleNameScore = getVehicleNameScore(bo.getVehicleName(), bo.getVehicleNameSortList());
    double alarmEventScore = getAlarmEventScore(bo.getSystemState(), bo.getAlarmEvent());
    double businessTypeScore = getBusinessTypeScore(bo.getBusinessType());
    double scheduleStateScore = getScheduleStateScore(bo.getScheduleState());
    double score = vehicleNameScore + alarmEventScore + businessTypeScore + scheduleStateScore;
    return score;
  }

  /**
   * 获取站点分数
   */
  protected static double getStationScore(VehicleScoreBO bo) {
    double vehicleNameScore = getVehicleNameScore(bo.getVehicleName(), bo.getVehicleNameSortList());
    double alarmEventScore = getAlarmEventScore(bo.getSystemState(), bo.getAlarmEvent());
    double stationScore = getStationScore(bo.getStationName(), bo.getStationSortList());
    double score = vehicleNameScore + alarmEventScore + stationScore;
    return score;
  }
}

