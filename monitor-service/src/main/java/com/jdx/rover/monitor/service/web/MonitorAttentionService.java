package com.jdx.rover.monitor.service.web;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorAttentionPhoneDTO;
import com.jdx.rover.monitor.dto.MonitorUserInfoDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.message.NotifyMessageTypeEnum;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.repository.redis.UserAttentionRepository;
import com.jdx.rover.monitor.repository.redis.VehicleAttentionRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.user.UserNotifyRepository;
import com.jdx.rover.monitor.vo.attention.MonitorAttentionVehicleVO;
import com.jdx.rover.monitor.vo.attention.MonitorEventAttentionAddVO;
import com.jdx.rover.monitor.vo.attention.MonitorEventAttentionCancelVO;
import com.jdx.rover.monitor.vo.attention.MonitorVehicleAttentionAddVO;
import com.jdx.rover.monitor.vo.attention.MonitorVehicleAttentionCancelVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import com.jdx.rover.permission.domain.dto.basic.UserMaskPhoneDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 关注相关 Controller
 * </p>
 *
 * <AUTHOR>
 * @date 2023/09/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MonitorAttentionService {

    private final UserAttentionRepository userAttentionRepository;

    private final VehicleAttentionRepository vehicleAttentionRepository;

    private final UserNotifyRepository userNotifyRepository;

    private final VehicleManager vehicleManager;

    private final MetadataStationApiManager stationApiManager;

    private final MetadataUserApiManager userBasicApiManager;

    private final VehicleBasicRepository vehicleBasicRepository;

    private final MetadataStationApiManager metadataStationApiManager;

    /**
     * 添加车辆关注列表
     * @param monitorVehicleAttentionAddVO
     */
    public HttpResult addVehicle(MonitorVehicleAttentionAddVO monitorVehicleAttentionAddVO) {
        String username = UserUtils.getAndCheckLoginUser();
        //获取用户车辆列表
        Set<String> vehicleNameSet = userAttentionRepository.get(username);
        vehicleNameSet.addAll(monitorVehicleAttentionAddVO.getVehicleNameList());
        List<String> vehicleNameList = vehicleNameSet.stream().sorted().collect(Collectors.toList());
        userAttentionRepository.initSet(username, vehicleNameList);
        return HttpResult.success();
    }

    /**
     * 取消车辆关注列表
     * @param monitorVehicleAttentionCancelVO
     * @return
     */
    public HttpResult cancelVehicle(MonitorVehicleAttentionCancelVO monitorVehicleAttentionCancelVO) {
        String username = UserUtils.getAndCheckLoginUser();
        //获取用户车辆列表
        Set<String> vehicleNameSet = userAttentionRepository.get(username);
        vehicleNameSet.removeAll(monitorVehicleAttentionCancelVO.getVehicleNameList());
        List<String> vehicleNameList = vehicleNameSet.stream().sorted().collect(Collectors.toList());
        userAttentionRepository.initSet(username, vehicleNameList);
        return HttpResult.success();
    }

    /**
     * 取消用户订阅所有车辆
     * @return
     */
    public HttpResult cancelAll() {
        String username = UserUtils.getAndCheckLoginUser();
        userAttentionRepository.delete(username);
        return HttpResult.success();
    }

    public HttpResult<List<MonitorUserInfoDTO>> getVehicleAttentionUser(String vehicleName) {
        ParameterCheckUtility.checkNotNullNorEmpty(vehicleName, "vehicleName");
        List<MonitorUserInfoDTO> monitorUserInfoList = new LinkedList<>();
        VehicleBasicDTO vehicleBasicDto = vehicleManager.getBasicByName(vehicleName);
        String stationUserName = null;
        if (!Objects.isNull(vehicleBasicDto)) {
            StationBasicDTO stationBasicInfo = stationApiManager.getById(vehicleBasicDto.getStationId());
            if (!Objects.isNull(stationBasicInfo)) {
                MonitorUserInfoDTO stationUser = new MonitorUserInfoDTO();
                stationUser.setUserName(stationBasicInfo.getPersonName());
                stationUser.setErp(stationBasicInfo.getPersonErp());
                stationUser.setPhone(String.valueOf(PhoneUtil.hideBetween(stationBasicInfo.getContact())));
                UserExtendInfoDTO userExtendInfoDTO = userBasicApiManager.getUserExtendInfoByName(stationBasicInfo.getPersonName());
                if (ObjectUtil.isNotNull(userExtendInfoDTO)) {
                    stationUser.setRealName(userExtendInfoDTO.getRealName());
                }
                stationUserName = stationBasicInfo.getPersonName();
                monitorUserInfoList.add(stationUser);
            }
        }
        // TODO 增加当前车在小程序关注车辆
        Set<String> userList = vehicleAttentionRepository.hget(vehicleName);
        if (CollectionUtils.isNotEmpty(userList)) {
            List<UserMaskPhoneDTO> userMaskPhoneDtoList = userBasicApiManager.getUserPhoneInfoByName(Lists.newArrayList(userList));
            // TODO 增加当前车在用户关注区域内
            for (UserMaskPhoneDTO user : userMaskPhoneDtoList) {
                if (StringUtils.equals(stationUserName, user.getUserName())) {
                    continue;
                }
                MonitorUserInfoDTO attentionUser = new MonitorUserInfoDTO();
                attentionUser.setUserName(user.getUserName());
                attentionUser.setRealName(user.getRealName());
                attentionUser.setPhone(user.getMaskPhone());
                monitorUserInfoList.add(attentionUser);
            }
        }
        return HttpResult.success(monitorUserInfoList);
    }

    /**
     * 添加用户关注事件
     * @param attentionAddVO 关注事件的详细信息
     * @return 添加结果
     */
    public HttpResult<List<String>> addAttentionEvent(MonitorEventAttentionAddVO attentionAddVO) {
        NotifyMessageTypeEnum messageTypeEnum = NotifyMessageTypeEnum.of(attentionAddVO.getAttentionEvent());
        if (Objects.isNull(messageTypeEnum)) {
            return HttpResult.error("非法关注事件类型");
        }
        userNotifyRepository.putAllMapObject(attentionAddVO.getAttentionEvent(), attentionAddVO.getUserErpList());
        List<String> userList = userNotifyRepository.get(attentionAddVO.getAttentionEvent());
        return HttpResult.success(userList);
    }


    /**
     * 取消关注事件
     * @param attentionCancelVO 包含要取消关注的事件信息的VO对象
     * @return 操作结果
     */
    public HttpResult<List<String>> deleteAttentionEvent(MonitorEventAttentionCancelVO attentionCancelVO) {
        NotifyMessageTypeEnum messageTypeEnum = NotifyMessageTypeEnum.of(attentionCancelVO.getAttentionEvent());
        if (Objects.isNull(messageTypeEnum)) {
            return HttpResult.error("非法关注事件类型");
        }
        if (CollectionUtils.isEmpty(attentionCancelVO.getUserErpList())) {
            return HttpResult.error("非法关注事件用户列表");
        }
        attentionCancelVO.getUserErpList().stream().forEach(erpUser -> {
            userNotifyRepository.fastRemoveMapKey(attentionCancelVO.getAttentionEvent(), erpUser);
        });
        List<String> userList = userNotifyRepository.get(attentionCancelVO.getAttentionEvent());
        return HttpResult.success(userList);
    }

    /**
     * 获取指定关注事件的详细信息。
     * @param attentionEvent 关注事件的名称或ID。
     * @return 包含关注事件详细信息的列表。
     */
    public HttpResult<List<String>> getAttentionEvent(String attentionEvent) {
        List<String> userList = userNotifyRepository.get(attentionEvent);
        return HttpResult.success(userList);
    }

    /**
     * 根据车号查询对应站点负责人手机号
     *
     * @param monitorAttentionVehicleVO monitorAttentionVehicleVO
     * @return MonitorAttentionPhoneDTO
     */
    public HttpResult<MonitorAttentionPhoneDTO> getStationPersonPhone(MonitorAttentionVehicleVO monitorAttentionVehicleVO) {
        VehicleBasicDTO vehicleBasicDTO = vehicleBasicRepository.get(monitorAttentionVehicleVO.getVehicleName());
        if (Objects.isNull(vehicleBasicDTO)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_VEHICLE_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_VEHICLE_ABSENT.getMessage());
        }
        StationBasicDTO stationBasicDTO = metadataStationApiManager.getById(vehicleBasicDTO.getStationId());
        if (Objects.isNull(stationBasicDTO)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getMessage());
        }

        MonitorAttentionPhoneDTO monitorAttentionPhoneDTO = new MonitorAttentionPhoneDTO();
        monitorAttentionPhoneDTO.setPhone(stationBasicDTO.getContact());
        return HttpResult.success(monitorAttentionPhoneDTO);
    }
}
