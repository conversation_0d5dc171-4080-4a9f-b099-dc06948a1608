/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.issue;

import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.cockpit.CockpitTeamStatusDO;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamMqttManager;
import com.jdx.rover.monitor.manager.user.UserInfoMqttManager;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitTeamStatusRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import com.jdx.rover.ticket.api.kafka.IssueTodayStatisticDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 工单统计信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IssueTodayStatisticService {

    private final UserInfoMqttManager userInfoMqttManager;

    private final CockpitTeamMqttManager cockpitTeamMqttManager;

    private final CockpitTeamStatusRepository cockpitTeamStatusRepository;

    private final UserStatusRepository userStatusRepository;

    public void handleOneMessage(IssueTodayStatisticDTO dto) {
        handleStatisticCockpitTeam(dto);
        handleStatisticUser(dto);
    }

    private void handleStatisticCockpitTeam(IssueTodayStatisticDTO dto) {
        if (Objects.isNull(dto.getStatisticCockpitTeamDTO())) {
            return;
        }

        //1、更新座席团队完成工单数，待处理工单数
        Map<String, Object> paramMap = new ParamMap<CockpitTeamStatusDO>()
                .addProperty(CockpitTeamStatusDO::getCompleteIssueCount, dto.getStatisticCockpitTeamDTO().getCompleteIssueCount())
                .addProperty(CockpitTeamStatusDO::getWaitAcceptIssueCount, dto.getStatisticCockpitTeamDTO().getWaitAcceptIssueCount())
                .addProperty(CockpitTeamStatusDO::getIssueStatisticDate, dto.getStatisticDate())
                .toMap();
        cockpitTeamStatusRepository.putAllMapObject(dto.getCockpitTeamNumber(), paramMap);

        //2、推送工单抽屉mqtt消息
        cockpitTeamMqttManager.pushCockpitTeam(dto.getCockpitTeamNumber());
    }

    private void handleStatisticUser(IssueTodayStatisticDTO dto) {
        if (Objects.isNull(dto.getStatisticUserDTO())) {
            return;
        }
        //1、更新用户完成工单数
        Map<String, Object> paramMap = new ParamMap<UserStatusDO>()
                .addProperty(UserStatusDO::getCompleteIssueCount, dto.getStatisticUserDTO().getCompleteIssueCount())
                .addProperty(UserStatusDO::getIssueStatisticDate, dto.getStatisticDate())
                .toMap();
        userStatusRepository.putAllMapObject(dto.getUsername(), paramMap);

        //2、推送工单抽屉mqtt消息
        userInfoMqttManager.pushUserInfo(dto.getUsername());
    }
}

