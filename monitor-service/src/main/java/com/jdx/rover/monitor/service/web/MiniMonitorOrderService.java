/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.order.OrderAppletSearchDto;
import com.jdx.rover.datacenter.domain.vo.order.OrderAppletSearchVo;
import com.jdx.rover.datacenter.jsf.service.order.OrderAppletService;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.dto.mini.MiniMonitorOrderInfoDTO;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.vo.MiniMonitorOrderListRequestVO;
import com.jdx.rover.schedule.api.domain.dto.schedule.ScheduleOrderDto;
import com.jdx.rover.schedule.api.domain.enums.DeliveryMode;
import com.jdx.rover.schedule.api.domain.enums.DeliveryStatus;
import com.jdx.rover.schedule.api.domain.enums.LoadMethodEnum;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import com.jdx.rover.schedule.api.domain.vo.schedule.ScheduleOrderQueryVo;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMonitorJsfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a mini monitor order function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
@Slf4j
public class MiniMonitorOrderService {

  @Autowired
  private ScheduleMonitorJsfService scheduleMonitorJsfService;
  @Autowired
  private OrderAppletService orderAppletService;
  @Autowired
  private MetadataStationApiManager metadataStationApiManager;

  /*
   * 获取订单数据
   */
  public List<MiniMonitorOrderInfoDTO> getOrderInfoDtoList(String scheduleNo, String taskType) {
    ScheduleOrderQueryVo deliveryOrderQueryVo = new ScheduleOrderQueryVo();
    deliveryOrderQueryVo.setScheduleNo(scheduleNo);
    HttpResult<List<ScheduleOrderDto>> deliveryOrderInfos = scheduleMonitorJsfService.queryScheduleOrders(deliveryOrderQueryVo);
    if (!HttpResult.isSuccess(deliveryOrderInfos) || CollectionUtils.isEmpty(deliveryOrderInfos.getData())) {
      return new ArrayList<>();
    }
    return deliveryOrderInfos.getData().stream().filter(orderInfoDto -> filterOrderByState(taskType, orderInfoDto)).
            map(orderInfoDto -> builderOrderInfo(orderInfoDto)).collect(Collectors.toList());
  }

  /*
   * 获取订单数据
   */
  public List<MiniMonitorOrderInfoDTO> getHistoryOrderInfoDtoList(MiniMonitorOrderListRequestVO orderListVo) {
    List<StationBasicDTO> result = metadataStationApiManager.getStationListByUser(LoginUtils.getUsername());
    if (CollectionUtils.isEmpty(result)) {
      return Lists.newArrayList();
    }
    List<String> stationDtos = result.stream().map(station -> station.getStationName()).collect(Collectors.toList());
    OrderAppletSearchVo deliveryOrderQueryVo = new OrderAppletSearchVo();
    deliveryOrderQueryVo.setPhone(orderListVo.getOrderPhone());
    deliveryOrderQueryVo.setStationNameList(stationDtos);
    deliveryOrderQueryVo.setDay(DateUtil.today());
    HttpResult<List<OrderAppletSearchDto>> httpResult = orderAppletService.queryOrder(deliveryOrderQueryVo);
    log.info("根据订单手机号模糊搜索当天订单结果{}", JsonUtils.writeValueAsString(httpResult));
    if (HttpResult.isSuccess(httpResult) && !ObjectUtils.isEmpty(httpResult.getData())) {
      List<OrderAppletSearchDto> orderDtoList = httpResult.getData();
      return orderDtoList.stream().filter(orderInfoDto ->
              !StringUtils.equals(orderInfoDto.getOrderStatus(),DeliveryStatus.DELETE.getDeliveryStatus())).
              map(orderInfoDto -> builderOrderInfoFromApplet(orderInfoDto)).collect(Collectors.toList());
    }
    return new ArrayList<>();
  }

  /*
   * 订单转换
   */
  private MiniMonitorOrderInfoDTO builderOrderInfoFromApplet(OrderAppletSearchDto orderDto) {
    MiniMonitorOrderInfoDTO orderInfoDto = new MiniMonitorOrderInfoDTO();
    orderInfoDto.setName(orderDto.getName());
    orderInfoDto.setVehicleName(orderDto.getVehicleName());
    orderInfoDto.setOrderId(orderDto.getOriginalOrderId());
    orderInfoDto.setDeliveryState(orderDto.getOrderStatus());
    orderInfoDto.setGridNoList(StrUtil.split(orderDto.getGridNo(), ",").stream().map(Integer ::valueOf).collect(Collectors.toList()));
    orderInfoDto.setPackageTotal(orderDto.getPackageTotal());
    orderInfoDto.setStopName(orderDto.getStopName());
    if (StringUtils.equals(DeliveryMode.TRANSPORT.getDeliveryMode(), orderDto.getDeliveryMode())) {
      orderInfoDto.setPackageTotal(orderDto.getSubpackageCount());
    }
    orderInfoDto.setContact(orderDto.getContact());
    orderInfoDto.setVerifyCode(orderDto.getVerifyCode());
    if (!StringUtils.equals(DeliveryMode.DROP_OFF.getDeliveryMode(), orderDto.getDeliveryMode())) {
      orderInfoDto.setLoadMethod(Optional.ofNullable(orderDto.getLoadMethod()).map(
              loadMethod -> LoadMethodEnum.getByType(loadMethod).getDesc()).orElse(null));
    }
    return orderInfoDto;
  }

  /*
   * 订单转换
   */
  private MiniMonitorOrderInfoDTO builderOrderInfo(ScheduleOrderDto orderDto) {
    MiniMonitorOrderInfoDTO orderInfoDto = new MiniMonitorOrderInfoDTO();
    orderInfoDto.setId(orderDto.getOrderId());
    orderInfoDto.setName(orderDto.getName());
    orderInfoDto.setOrderId(orderDto.getOriginalId());
    orderInfoDto.setDeliveryState(orderDto.getDeliveryStatus());
    orderInfoDto.setGridNoList(orderDto.getGridNo());
    orderInfoDto.setStopId(orderDto.getStopId());
    orderInfoDto.setLoadStopId(orderDto.getLoadStopId());
    orderInfoDto.setPackageTotal(orderDto.getPackageTotal());
    if (StringUtils.equals(DeliveryMode.TRANSPORT.getDeliveryMode(), orderDto.getDeliveryModel())) {
      orderInfoDto.setPackageTotal(orderDto.getSubpackageCount());
    }
    orderInfoDto.setStopName(orderDto.getStopName());
    orderInfoDto.setContact(orderDto.getContact());
    orderInfoDto.setVerifyCode(orderDto.getVerifyCode());
    orderInfoDto.setDeliveryModel(orderDto.getDeliveryModel());
    if (!StringUtils.equals(orderDto.getDeliveryModel(), DeliveryMode.DROP_OFF.getDeliveryMode())) {
      orderInfoDto.setLoadMethod(orderDto.getLoadMethodName());
    }
    return orderInfoDto;
  }

  private boolean filterOrderByState(String taskType, ScheduleOrderDto orderDto) {
    String deliveryStatus = orderDto.getDeliveryStatus();
    if (DeliveryStatus.DELETE.getDeliveryStatus().equals(deliveryStatus)) {
      return false;
    }
    if (StringUtils.equals(taskType, TaskType.UNLOADTASK.getTaskType())) {
      if (StringUtils.equalsAny(deliveryStatus, DeliveryStatus.CANCEL.getDeliveryStatus(), DeliveryStatus.INITIAL.getDeliveryStatus(), DeliveryStatus.USERCANCEL.getDeliveryStatus())) {
        return  false;
      }
  }
    return true;
  }
}
