/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.drawer;

import com.jdx.rover.monitor.manager.drawer.DrawerMqttManager;
import com.jdx.rover.monitor.repository.redis.user.UserOnlineSetRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;


/**
 * 抽屉服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DrawerService {
    private final UserOnlineSetRepository userOnlineSetRepository;
    private final DrawerMqttManager drawerMqttManager;

    /**
     * 推送全部用户抽屉信息,5s一次
     */
    public void pushAllDrawer() {
        Set<String> userNameSet = userOnlineSetRepository.get();
        userNameSet.forEach(drawerMqttManager::pushDrawer);
    }
}
