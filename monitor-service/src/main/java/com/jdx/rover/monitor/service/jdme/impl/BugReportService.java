package com.jdx.rover.monitor.service.jdme.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.amazonaws.HttpMethod;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.constant.AccidentConstant;
import com.jdx.rover.monitor.dto.jdme.*;
import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import com.jdx.rover.monitor.manager.accident.AccidentAttachmentManager;
import com.jdx.rover.monitor.manager.accident.AccidentDetailManager;
import com.jdx.rover.monitor.manager.accident.AccidentFlowLogManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.config.RoverVideoProperties;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfig;
import com.jdx.rover.monitor.manager.video.VideoManager;
import com.jdx.rover.monitor.po.AccidentAttachment;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.service.jdme.IAccidentFlowEventService;
import com.jdx.rover.shadow.api.domain.dto.ShadowSubscribeEventTaskDTO;
import lombok.extern.slf4j.Slf4j;

import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentDetail;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.po.AccidentJdmePush;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 提报缺陷处理
 */
@Slf4j
public class BugReportService extends AbstractJdmeMessageService implements IAccidentFlowEventService {
    private AccidentJdmePushManager accidentJdmePushManager;
    private AccidentManager accidentManager;
   private AccidentDetailManager accidentDetailManager;
    private AccidentFlowLogManager accidentFlowLogManager;

    private AccidentAttachmentManager accidentAttachmentManager;

    private VideoManager videoManager;

    private RoverVideoProperties roverVideoProperties;

    private S3Properties s3Properties;

    public BugReportService() {
        accidentJdmePushManager = SpringUtil.getBean(AccidentJdmePushManager.class);
        accidentManager = SpringUtil.getBean(AccidentManager.class);
        accidentDetailManager = SpringUtil.getBean(AccidentDetailManager.class);
        accidentFlowLogManager = SpringUtil.getBean(AccidentFlowLogManager.class);
        accidentAttachmentManager = SpringUtil.getBean(AccidentAttachmentManager.class);
        videoManager = SpringUtil.getBean(VideoManager.class);
        roverVideoProperties = SpringUtil.getBean(RoverVideoProperties.class);
        s3Properties = SpringUtil.getBean(S3Properties.class);
    }
    /**
     * 处理消息
     * @param accidentNo 事故号
     * @param accidentFlowType 事故处理环节
     * @param operator 操作员
     * @param repeated 重试消息
     */
    @Override
    public void handleMessage(String accidentNo, String accidentFlowType, String operator, boolean repeated) throws Exception {
        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if(null == accident) {
            log.warn("区域安全员提报事故处理时未找到事故编号[{}]对应的事故信息,忽略处理！", accidentNo);
            return;
        }
        AccidentDetail accidentDetail = accidentDetailManager.selectByAccidentNo(accidentNo);
        if(null == accidentDetail) {
            log.warn("区域安全员提报事故处理时未找到事故编号[{}]对应的事故详情,忽略处理！", accidentNo);
            return;
        }
        List<AccidentFlowLog> flowLogList = accidentFlowLogManager.listByAccidentNo(accidentNo);
        AccidentJdmePush accidentJdmePush = accidentJdmePushManager.getByEventId(accident.getShadowEventId());
        if(null == accidentJdmePush) {
            log.warn("区域安全员提报事故处理时未找到事故推送消息，影子事件编号：【{}】", accident.getShadowEventId());
            return;
        }
        accidentJdmePush.setLevel(accidentDetail.getTechnicalSupportAccidentLevel());
        accidentJdmePush.setTitle(accidentDetail.getTechnicalSupportDescription());

        //影子事件订阅
        try {
            ShadowSubscribeEventTaskDTO shadowEventTask = subscribeShadowEvent(accident);
            //持久化异常提报的需要发送的卡片消息
            if (null != shadowEventTask) {
                accidentJdmePush.setStatus(shadowEventTask.getStatus());
                accidentJdmePush.setVideoUrl(shadowEventTask.getVideoUrl());
                accidentJdmePush.setEventPlayUrl(shadowEventTask.getEventPlayUrl());
            }
        } catch (Exception e) {
            log.info("订阅影子事件[{}]订阅失败: {}", accident.getShadowEventId(), e);
        }
        accidentJdmePush.setAccidentFlowType(accidentFlowType);
        String newGroupId = accidentJdmePush.getAccidentGroupId();
        if(StrUtil.isBlank(newGroupId)) {
            //根据当前事故编码创建新京ME群，从而获取到新群号，作为卡片消息的参数之一发送出去
            JdmeGroup jdmeGroup = buildJdmeGroup(accidentJdmePush, accident);
            newGroupId = accidentJdmePushManager.createGroup(jdmeGroup);
            accidentJdmePush.setAccidentGroupId(newGroupId);
        }
        this.accidentJdmePushManager.updateById(accidentJdmePush);

        // 获取站点负责人信息
        StationBasicDTO stationBasicDTO = metadataVehicleApiManager.getStationInfoByVehicle(accident.getVehicleName());
        addMemberIntoGroup(newGroupId, accidentJdmePush.getFollower(), operator, stationBasicDTO.getPersonName());

        JdmeConfig jdmeConfig = accidentJdmePushManager.getLatestConfig();
        JueCardData jueCardData = buildCardData(jdmeConfig, accident, accidentJdmePush, flowLogList, newGroupId, true);

        //给统一的领导群推送固定格式的卡片消息，卡片上的交互按钮的逻辑在此提前生成，按钮交互由前端JS完成
        jueCardData.setGroupId(jdmeConfig.getRobot().getFixedGroupId());
        jueCardData.setSummary(buildSummary(jueCardData, accident));
        if(!repeated) {
            this.accidentJdmePushManager.sendJUEMsg(jueCardData);
        }

        //给新群推送卡片消息，卡片内容相同，取消加入处理群
        jueCardData.setGroupId(newGroupId);
        jueCardData.getButtons().getButtons().remove(0);
        this.accidentJdmePushManager.sendJUEMsg(jueCardData);
    }

    /**
     * 构建卡片数据
     * @param jdmePush 推送消息
     * @param newGroupId 处理群号
     * @param showProcessButton 是否显示处理按钮
     * @return
     */
    public JueCardData buildCardData(JdmeConfig jdmeConfig, Accident accident, AccidentJdmePush jdmePush,
                                     List<AccidentFlowLog> flowLogList, String newGroupId, boolean showProcessButton) {

        //组装卡片消息头
        StringBuilder titleSb = new StringBuilder("【事故分析】碰撞-");
        titleSb.append(jdmePush.getVehicleName());

        if(StrUtil.isNotBlank(jdmePush.getLevel())) {
            String level = PreliminaryAccidentLevelEnum.getNameByValue(jdmePush.getLevel());
            if(null != level) {
                titleSb.append("-");
                titleSb.append(level);
            } else {
                log.warn("区域安全员提报事故处理时，事故等级[{}]无法识别！", jdmePush.getLevel());
            }
        }

        JueCardDataHeaderTitle headerTitle = new JueCardDataHeaderTitle();
        headerTitle.setContent(titleSb.toString());

        JueCardDataHeader header = new JueCardDataHeader();
        if(StrUtil.isBlank(jdmePush.getLevel())){
            header.setTheme(JueCardDataEnums.LabelColorType.GRAY.getCode());
        } else if( PreliminaryAccidentLevelEnum.HIGH_RISK.getValue().equals(jdmePush.getLevel())
                || PreliminaryAccidentLevelEnum.MEDIUM_RISK.getValue().equals(jdmePush.getLevel())) {
            header.setTheme(JueCardDataEnums.LabelColorType.RED.getCode());
        } else {
            header.setTheme(JueCardDataEnums.LabelColorType.YELLOW.getCode());
        }
        header.setTitle(headerTitle);

        //组装卡片消息体
        List<JueCardDataElementText> elementList = new ArrayList<>();
        //问题描述
        if(StrUtil.isNotBlank(jdmePush.getTitle())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("问题描述", jdmePush.getTitle(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //缺陷编号
        if(StrUtil.isNotBlank(jdmePush.getBugCode())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("缺陷编号", "http://xingyun.jd.com/test-manage/jbug/" + jdmePush.getBugId(), jdmePush.getBugCode(), JueCardDataEnums.ElementValueType.LINK));
            elementList.add(elementText);
        }
        //发生时间
        if(StrUtil.isNotBlank(jdmePush.getDebugTime())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("发生时间", jdmePush.getDebugTime(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //发生位置
        if(StrUtil.isNotBlank(jdmePush.getAccidentAddress())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("发生位置", jdmePush.getAccidentAddress(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //事故快照
        List<AccidentAttachment> accidentAttachments = accidentAttachmentManager.selectOrderedListByAccidentNoAndSource(accident.getAccidentNo(), AccidentAttachmentSourceEnum.VIDEO_SNAPSHOT.getValue());
        List<String> snapshotUrlList = new ArrayList<>();
        for (AccidentAttachment accidentAttachment : accidentAttachments) {
            final Date expiration = DateUtil.offsetDay(new Date(), AccidentConstant.VIDEO_EXPIRATION);
            String videoUrl = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), accidentAttachment.getBucket(), accidentAttachment.getFileKey(), HttpMethod.GET, expiration).toString();
            snapshotUrlList.add(videoUrl);
        }
        //站点名称
        if(StrUtil.isNotBlank(accident.getStationName())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("站点名称", accident.getStationName(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //rover版本
        if(StrUtil.isNotBlank(accident.getRoverVersion())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("Rover版本", accident.getRoverVersion(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //事故视频
        if(StrUtil.isNotBlank(jdmePush.getVideoUrl())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("事故视频", jdmePush.getVideoUrl(), "查看", JueCardDataEnums.ElementValueType.LINK));
            elementList.add(elementText);
        }
        //整车回放
        if(StrUtil.isNotBlank(jdmePush.getEventPlayUrl())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("整车回放", jdmePush.getEventPlayUrl(), "PC端查看", JueCardDataEnums.ElementValueType.LINK));
            elementList.add(elementText);
        }
        //事故进度
        if(CollectionUtil.isNotEmpty(flowLogList)) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            StringBuilder sb = new StringBuilder("\n");
            for(int i = 0; i < flowLogList.size(); i++) {
                AccidentFlowLog log = flowLogList.get(i);
                sb.append(i + 1);
                sb.append("、");
                sb.append(log.getContent());
                if(i < flowLogList.size() - 1) {
                    sb.append("\n");
                }
            }

            elementText.setContent(new JueCardDataElementTextItem("事故进度", sb.toString(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //事故负责人
        if(StrUtil.isNotBlank(jdmePush.getFollower())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("事故负责人", jdmePush.getFollower() + "/ee", jdmePush.getFollower(),  JueCardDataEnums.ElementValueType.LINK));
            elementList.add(elementText);
        }

        //组装卡片消息按钮
        List<JueCardDataButton> buttonList = new ArrayList<>();
        if(showProcessButton) {
            //加入处理群按钮
            JueCardDataElementTextItem processButtonTextItem = new JueCardDataElementTextItem();
            processButtonTextItem.setLabel("加入处理群");

            JueCardDataButtonText processButtonText = new JueCardDataButtonText();
            processButtonText.setContent(processButtonTextItem);

            JueCardDataButtonHref processButtonHref = new JueCardDataButtonHref();
            processButtonHref.setPc("");
            processButtonHref.setMobile("");

            JueCardDataButtonBehavior processButtonBehavior = new JueCardDataButtonBehavior();
            processButtonBehavior.setMethod(JueCardDataEnums.ButtonBehaviorMethod.JOIN_GROUP_CHAT);
            processButtonBehavior.setParams(newGroupId);

            JueCardDataButton processButton = new JueCardDataButton();
            processButton.setType(JueCardDataEnums.ButtonType.PRIMARY.getCode());
            processButton.setEnable(true);
            processButton.setText(processButtonText);
            processButton.setHref(processButtonHref);
            processButton.setBehavior(processButtonBehavior);
            buttonList.add(processButton);
        }

        //咨询人工按钮
        JueCardDataElementTextItem manualButtonTextItem = new JueCardDataElementTextItem();
        manualButtonTextItem.setLabel("咨询人工");

        JueCardDataButtonText manualButtonText = new JueCardDataButtonText();
        manualButtonText.setContent(manualButtonTextItem);

        JueCardDataButtonHref manualButtonHref = new JueCardDataButtonHref();
        manualButtonHref.setPc("timline://chat/?topin=" + jdmeConfig.getRobot().getManualPin());
        manualButtonHref.setMobile("jdme://jm/biz/im/contact/details?mparam={\"erp\":\"" + jdmeConfig.getRobot().getManualPin() + "\"}");

        JueCardDataButton manualButton = new JueCardDataButton();
        manualButton.setType(JueCardDataEnums.ButtonType.DEFAULT.getCode());
        manualButton.setEnable(true);
        manualButton.setText(manualButtonText);
        manualButton.setHref(manualButtonHref);
        buttonList.add(manualButton);

        JueCardDataButtons buttons = new JueCardDataButtons();
        buttons.setLayout(JueCardDataEnums.ButtonLayout.ROW);
        buttons.setButtons(buttonList);

        JueCardData jueCardData = new JueCardData();
        jueCardData.setHeader(header);
        jueCardData.setElements(elementList);
        jueCardData.setSnapshotUrlList(snapshotUrlList);
        jueCardData.setButtons(buttons);

        return jueCardData;
    }
}
