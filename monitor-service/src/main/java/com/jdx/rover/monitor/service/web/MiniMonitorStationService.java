/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.stop.StopBasicDTO;
import com.jdx.rover.monitor.dto.minimonitor.MiniMonitorStationDTO;
import com.jdx.rover.monitor.dto.minimonitor.MiniMonitorStationDetailDTO;
import com.jdx.rover.monitor.dto.minimonitor.MiniMonitorStationStopDTO;
import com.jdx.rover.monitor.dto.station.StationVehicleSearchDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.UserAttentionEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.ToggleStateEnum;
import com.jdx.rover.monitor.enums.UserAttentionTypeEnum;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.stop.MetadataStopApiManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.manager.vehicle.BusinessTypeManager;
import com.jdx.rover.monitor.repository.redis.UserAttentionStationRepository;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.vo.MiniMonitorUserInfoRequestVO;
import com.jdx.rover.monitor.vo.station.StationVehicleSearchVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.Collator;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a station service function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
@Slf4j
public class MiniMonitorStationService {
  /**
   * 查询返回最大数量为100
   */
  private static final int MAX_SEARCH_RESULT_SIZE = 100;

  @Autowired
  private MetadataStationApiManager metadataStationApiManager;

  @Autowired
  private MetadataStopApiManager metadataStopApiManager;
  @Autowired
  private MetadataUserApiManager metadataUserApiManager;
  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;

  @Autowired
  private VehicleBasicRepository vehicleBasicRepository;

  @Autowired
  private BusinessTypeManager businessTypeManager;

  @Autowired
  private UserAttentionStationRepository userAttentionStationRepository;

  /**
   * <p>
   * Get station list from user and business type.
   * </p>
   */
  public HttpResult getUserStationList(MiniMonitorUserInfoRequestVO userInfo) {
    String userName = LoginUtils.getUsername();
    if (userName == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    List<StationBasicDTO> result = metadataStationApiManager.getStationListByUser(userName);
    if (CollectionUtils.isEmpty(result)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getMessage());
    }
    List<MiniMonitorStationDTO> stationDtos = result.stream().filter(station -> {
      if (StringUtils.isNotBlank(userInfo.getVehicleBusinessType())) {
        return station.getBusinessCodeList().contains(userInfo.getVehicleBusinessType());
      }
      return true;
    }).map(station -> {
      MiniMonitorStationDTO stationDto = new MiniMonitorStationDTO();
      stationDto.setId(station.getStationId());
      stationDto.setName(station.getStationName());
      stationDto.setLat(station.getLatitude());
      stationDto.setLon(station.getLongitude());
      return stationDto;
    }).collect(Collectors.toList());
    return HttpResult.success(stationDtos);
  }

  /**
   * <p>
   * Get station detail by id.
   * </p>
   */
  public HttpResult getStationDetailById(Integer id) {
    Map<Integer, StationBasicDTO> stationMap = metadataStationApiManager.getStationById(id);
    if (CollectionUtil.isEmpty(stationMap)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STATION_ABSENT.getMessage());
    }
    StationBasicDTO stationDetail = stationMap.get(id);
    MiniMonitorStationDetailDTO stationDto = new MiniMonitorStationDetailDTO();
    stationDto.setId(stationDetail.getStationId());
    stationDto.setStationUser(stationDetail.getPersonName());
    stationDto.setUserAttentionState(ToggleStateEnum.OFF.getValue());
    String userName = LoginUtils.getUsername();
    UserExtendInfoDTO extendInfoDto = metadataUserApiManager.getUserExtendInfoByName(userName);
    if (Objects.isNull(extendInfoDto)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(), MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
    }
    if (StringUtils.equals(stationDetail.getPersonName(), extendInfoDto.getJdErp())) {
      stationDto.setUserAttentionState(ToggleStateEnum.ON.getValue());
    } else {
      List<UserAttentionEntity> attentionUser =
              userAttentionStationRepository.get(stationDetail.getStationId(), UserAttentionTypeEnum.BUMP.getType());
      stationDto.setUserAttentionState(attentionUser.stream().anyMatch(user ->
              StringUtils.equals(user.getUserName(), userName))? ToggleStateEnum.ON.getValue() : ToggleStateEnum.OFF.getValue());
    }
    log.info("Return user Station info {}", JsonUtils.writeValueAsString(stationDto));
    return HttpResult.success(stationDto);
  }

  /**
   * <p>
   * Get stop list from station.
   * </p>
   */
  public HttpResult getStationStopList(Integer stationId) {
    List<StopBasicDTO> stopList = metadataStopApiManager.getStopByStationId(stationId);
    if (CollectionUtils.isEmpty(stopList)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_STOP_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_STOP_ABSENT.getMessage());
    }
    return HttpResult.success(stopList.stream().map(stop -> {
      MiniMonitorStationStopDTO stopDto = new MiniMonitorStationStopDTO();
      stopDto.setId(stop.getStopId());
      stopDto.setName(stop.getStopName());
      return stopDto;
    }).collect(Collectors.toList()));
  }

  /**
   * 根据输入项模糊查找站点车辆列表
   */
  public HttpResult<?> getStationVehicleList(StationVehicleSearchVO stationVehicleSearchVO, String userName) {
    List<StationVehicleSearchDTO> resultList = getStationSearchList(stationVehicleSearchVO, userName);
    List<StationVehicleSearchDTO> vehicleSearchList = getVehicleSearchList(stationVehicleSearchVO, userName);
    resultList.addAll(vehicleSearchList);
    resultList.sort((o1, o2) -> {
      Collator collator = Collator.getInstance(Locale.CHINA);
      return collator.compare(o1.getResult(), o2.getResult());
    });
    if (resultList.size() < MAX_SEARCH_RESULT_SIZE) {
      return HttpResult.success(resultList);
    }
    return HttpResult.success(resultList.subList(0, MAX_SEARCH_RESULT_SIZE));
  }

  /**
   * 获取站点查询结果
   *
   * @param stationVehicleSearchVO
   * @return
   */
  private List<StationVehicleSearchDTO> getStationSearchList(StationVehicleSearchVO stationVehicleSearchVO, String userName) {
    List<StationVehicleSearchDTO> resultList = new ArrayList<>();
    List<StationBasicDTO> stationResult = metadataStationApiManager.getStationListByUser(userName);
    if (CollectionUtils.isEmpty(stationResult)) {
      return resultList;
    }
    for (StationBasicDTO station : stationResult) {
      if (station.getStationName().indexOf(stationVehicleSearchVO.getName()) == -1) {
        continue;
      }
      StationVehicleSearchDTO dto = new StationVehicleSearchDTO();
      dto.setStationId(station.getStationId());
      dto.setStationName(station.getStationName());
      dto.setResult(station.getStationName());
      dto.setResultType(SearchResultTypeEnum.STATION.name());
      resultList.add(dto);
      if (resultList.size() > MAX_SEARCH_RESULT_SIZE) {
        break;
      }
    }
    return resultList;
  }

  /**
   * 获取车辆查询结果
   *
   * @param stationVehicleSearchVO
   * @return
   */
  private List<StationVehicleSearchDTO> getVehicleSearchList(StationVehicleSearchVO stationVehicleSearchVO, String userName) {
    List<StationVehicleSearchDTO> resultList = new ArrayList<>();
    List<String> businessTypeList = businessTypeManager.getVehicleNameByBusinessType(stationVehicleSearchVO.getVehicleBusinessType());
    List vehicleNameList = new ArrayList();
    Set<String> vehicleNameSet = userVehicleNameRepository.get(userName);
    if (CollectionUtils.isNotEmpty(vehicleNameSet)) {
      for (String vehicleName : vehicleNameSet) {
        //转换为小写，模糊搜索
        if (vehicleName.toLowerCase().indexOf(stationVehicleSearchVO.getName().toLowerCase()) == -1) {
          continue;
        }
        if (businessTypeList.indexOf(vehicleName) == -1) {
          continue;
        }
        vehicleNameList.add(vehicleName);
        if (vehicleNameList.size() > MAX_SEARCH_RESULT_SIZE) {
          break;
        }
      }
    }
    List<VehicleBasicDTO> vehicleBasicDTOList = vehicleBasicRepository.list(vehicleNameList);
    for (VehicleBasicDTO vehicleBasicDTO : vehicleBasicDTOList) {
      StationVehicleSearchDTO dto = new StationVehicleSearchDTO();
      dto.setStationId(vehicleBasicDTO.getStationId());
      dto.setStationName(vehicleBasicDTO.getStationName());
      dto.setResult(vehicleBasicDTO.getName());
      dto.setResultType(SearchResultTypeEnum.VEHICLE.name());
      resultList.add(dto);
    }
    return resultList;
  }

  /**
   * 查询结果类型
   */
  private enum SearchResultTypeEnum {
    /**
     * 车辆
     */
    VEHICLE,
    /**
     * 站点
     */
    STATION;
  }
}
