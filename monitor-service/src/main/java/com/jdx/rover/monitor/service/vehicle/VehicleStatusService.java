/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.vehicle;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.OnlineStatusEnum;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.service.cockpit.CockpitScoreService;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.jdx.rover.server.api.domain.dto.guardian.GuardianConnectDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttOnline;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import com.jdx.rover.server.api.domain.enums.guardian.GuardianConnectStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 车辆状态变化服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/7
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleStatusService {

    /**
     * 车辆状态
     */
    private final VehicleStatusRepository vehicleStatusRepository;
    private final CockpitScoreService cockpitScoreService;

    /**
     * 通过实时信息处理
     */
    public void handByRealtime(VehicleRealtimeInfoDTO dto) {
        VehicleStatusDO vehicleStatus = vehicleStatusRepository.get(dto.getVehicleName());
        if (Objects.equals(dto.getVehicleState(), vehicleStatus.getVehicleState())
                && Objects.equals(dto.getSystemState(), vehicleStatus.getSystemState())) {
            return;
        }
        ParamMap<VehicleStatusDO> paramMap = new ParamMap<VehicleStatusDO>()
                .addNonNullProperty(VehicleStatusDO::getVehicleState, dto.getVehicleState());
        if (!Objects.equals(dto.getSystemState(), vehicleStatus.getSystemState())) {
            paramMap.addNonNullProperty(VehicleStatusDO::getSystemState, dto.getSystemState());
        }
        vehicleStatusRepository.putAllMapObject(dto.getVehicleName(), paramMap.toMap());

        cockpitScoreService.updateScore(dto.getVehicleName());
//        singleVehicleService.handleSingleVehicleChange(dto.getVehicleName());
    }

    /**
     * 通过guardian连接状态处理
     */
    public void handByGuardianConnect(GuardianConnectDTO guardianConnectDTO) {
        String guardianOnline;
        if (Objects.equals(guardianConnectDTO.getGuardianConnectStatus(), GuardianConnectStatusEnum.CONNECT.getValue())) {
            guardianOnline = OnlineStatusEnum.ONLINE.name();
        } else {
            guardianOnline = OnlineStatusEnum.OFFLINE.name();
        }
        vehicleStatusRepository.putMapValue(guardianConnectDTO.getVehicleName(), VehicleStatusDO::getGuardianOnline, guardianOnline);
    }

    /**
     * 通过PNC信息处理
     */
    public void handByPncInfo(VehiclePncInfoDTO dto) {
        Map<String, Object> paramMap = new ParamMap<VehicleStatusDO>()
                .addProperty(VehicleStatusDO::getIntersectionStatus, dto.getIntersectionStatus())
                .addProperty(VehicleStatusDO::getPlanningStatusType, dto.getType())
                .toMap();
        vehicleStatusRepository.putAllMapObject(dto.getVehicleName(), paramMap);
    }

    /**
     * 通过调度信息处理
     */
    public void handBySchedule(ScheduleTask scheduleTask) {
        Map<String, Object> paramMap = new ParamMap<VehicleStatusDO>()
                .addProperty(VehicleStatusDO::getScheduleState, scheduleTask.getVehicleScheduleState())
                .addProperty(VehicleStatusDO::getScheduleStartTime, DatePattern.NORM_DATETIME_MS_FORMAT.format(scheduleTask.getStartDatetime().getTime()))
                .toMap();
        vehicleStatusRepository.putAllMapObject(scheduleTask.getVehicleName(), paramMap);
    }

    /**
     * 通过PDU遗嘱消息处理
     */
    public void handleByPduWill(String pduWillMsg) {
        MqttRequestDTO<MqttOnline> mqttRequestDTO = JsonUtils.readValue(pduWillMsg, new TypeReference<>() {
        });
        if (mqttRequestDTO == null) {
            return;
        }
        Boolean isOnline = mqttRequestDTO.getData().getOnline();
        String vehicleName = mqttRequestDTO.getHeader().getSendName();
        String pduOnline = BooleanUtils.isTrue(isOnline) ? OnlineStatusEnum.ONLINE.name() : OnlineStatusEnum.OFFLINE.name();
        vehicleStatusRepository.putMapValue(vehicleName, VehicleStatusDO::getPduOnline, pduOnline);
    }
}
