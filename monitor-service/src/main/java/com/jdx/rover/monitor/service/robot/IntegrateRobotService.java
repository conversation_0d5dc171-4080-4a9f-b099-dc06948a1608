/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jdx.k2.indoor.map.jsf.domain.dto.mapInfo.PointInfoDTO;
import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.device.jsfapi.domain.enums.OperationTypeEnum;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceGroupMessageData;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceMessageData;
import com.jdx.rover.device.jsfapi.domain.vo.devicecommand.ImmediateTaskCreateVO;
import com.jdx.rover.device.jsfapi.service.server.devicecommand.IntelligentDeviceServerDeviceCommandTaskInfoService;
import com.jdx.rover.monitor.common.utils.jts.GeometryUtils;
import com.jdx.rover.monitor.dto.robot.RobotControlCommandReplyDTO;
import com.jdx.rover.monitor.dto.robot.RobotDeviceRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRouteInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotOperationFollowDTO;
import com.jdx.rover.monitor.dto.robot.RobotReportBootTaskDTO;
import com.jdx.rover.monitor.dto.robot.RobotReportTaskGoalDTO;
import com.jdx.rover.monitor.dto.robot.RobotReportTaskLocationDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.Tuple2;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO.DeviceAlarmEventDO;
import com.jdx.rover.monitor.entity.device.RobotIssueDO;
import com.jdx.rover.monitor.entity.device.RobotScheduleDO;
import com.jdx.rover.monitor.entity.device.RobotTaskRouteInfoDO;
import com.jdx.rover.monitor.enums.AlarmLevelEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.MqttCommandTypeEnum;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCategoryEnum;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCodeEnum;
import com.jdx.rover.monitor.enums.device.DeviceBootStatusEnum;
import com.jdx.rover.monitor.enums.device.DeviceCommandTaskEnum;
import com.jdx.rover.monitor.enums.device.DeviceEventTypeEnum;
import com.jdx.rover.monitor.enums.device.DevicePropertyCodeEnum;
import com.jdx.rover.monitor.enums.device.DeviceRealtimeStateEnum;
import com.jdx.rover.monitor.enums.device.DeviceTaskActionEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.device.indoormap.IndoorMapApiManager;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotAlarmRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotIssueRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotScheduleRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotTaskRouteRepository;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.robot.alarm.RobotRealtimeAlarmService;
import com.jdx.rover.monitor.service.robot.alarm.RobotReportAlarmService;
import com.jdx.rover.monitor.service.robot.alarm.RobotScheduleAlarmService;
import com.jdx.rover.server.api.domain.dto.hermes.HermesChassisCommandModuleDelayInfoDTO;
import com.jdx.rover.server.api.domain.dto.hermes.HermesVehicleControlCommandDelayInfoDTO;
import com.jdx.rover.transport.api.domain.dto.status.PropertyChangeDTO;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceHeader;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.geotools.geojson.geom.GeometryJSON;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LineString;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物联网多合一机器人服务
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IntegrateRobotService implements IRobotDeviceService {

    /**
     * 机器人实时信息服务。
     */
    private final RobotRealtimeInfoManager robotRealtimeInfoManager;

    /**
     * 设备实时服务接口
     */
    private final TransportDeviceApiManager realtimeDeviceApiManager;

    /**
     * 设备告警服务接口
     */
    private final RobotRealtimeAlarmService robotRealtimeAlarmService;

    /**
     * 告警缓存
     */
    private final RobotAlarmRepository robotAlarmRepository;

    /**
     * 工单缓存
     */
    private final RobotIssueRepository robotIssueRepository;

    /**
     * 接管缓存
     */
    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    /**
     * 机器人任务路由服务接口。
     */
    private final RobotTaskRouteRepository robotTaskRouteRepository;

    /**
     * 机器人调度信息服务。
     */
    private final RobotScheduleRepository robotScheduleRepository;

    /**
     * 室内地图服务
     */
    private final IndoorMapApiManager indoorMapApiManager;

    /**
     * 日志收集服务
     */
    private final TrackingEventCollectService trackingEventCollectService;

    /**
     * 调度检测服务
     */
    private final RobotScheduleAlarmService robotScheduleAlarmService;

    /**
     * 设备上报告警检测服务
     */
    private final RobotReportAlarmService robotReportAlarmService;

    /**
     * 设备工单服务
     */
    private final RobotIssueService robotIssueService;

    /**
     * 设备指令服务
     */
    private final IntelligentDeviceServerDeviceCommandTaskInfoService deviceCommandService;

    /*
     * 本地过滤Map
     */
    private static ConcurrentMap<String, Long> vehicleHandleTimeMap = new ConcurrentHashMap<>(100);

    private static final long TWO_SECOND_NANOSECOND = TimeUnit.SECONDS.toNanos(2);

    @Override
    public void batchUpdateDeviceGroup(Map<String, List<DeviceMessageData>> mapResult) {
        if (CollectionUtil.isEmpty(mapResult)) {
            return;
        }
        log.info("批量更新机器人基础分组信息数据量{}, 详情{}", mapResult.size(), JsonUtils.writeValueAsString(mapResult));
        // 处理新增设备
        List<DeviceMessageData> addDeviceList = mapResult.get(OperationTypeEnum.ADD.getValue());
        if (CollectionUtils.isNotEmpty(addDeviceList)) {
            List<RobotRealtimeInfo> robotList = buildUpdateRealtimeInfo(addDeviceList);
            robotRealtimeInfoManager.saveBatchData(robotList.stream().map(robot -> {
                robot.setRealtimeStatus(DeviceRealtimeStateEnum.OFFLINE.getValue());
                return robot;
            }).collect(Collectors.toList()));
        }
        // 处理更新设备
        List<DeviceMessageData> updateDeviceList = mapResult.get(OperationTypeEnum.EDIT.getValue());
        if (CollectionUtils.isNotEmpty(updateDeviceList)) {
            robotRealtimeInfoManager.updateBatch(buildUpdateRealtimeInfo(updateDeviceList));
        }
        // 处理删除设备
        List<DeviceMessageData> deleteDeviceList = mapResult.get(OperationTypeEnum.DELETE.getValue());
        if (CollectionUtils.isNotEmpty(deleteDeviceList)) {
            robotRealtimeInfoManager.deleteBatch(buildUpdateRealtimeInfo(deleteDeviceList));
        }
    }

    /**
     * 批量更新设备状态。
     * @param deviceNameList 设备名称列表。
     */
    @Override
    public void batchUpdateDeviceState(List<String> deviceNameList) {
        if (CollectionUtils.isEmpty(deviceNameList)) {
            return;
        }
        List<Map<String, Object>> result = realtimeDeviceApiManager.getDeviceStatusList(deviceNameList, DevicePropertyCodeEnum.MONITOR_ROBOT_STATUS.getPropertyList());

        if (CollectionUtils.isEmpty(result)) {
            log.error("查询设备列表{}实时信息不存在", deviceNameList.toArray());
            return;
        }
        List<String> onLineDeviceList = new ArrayList<>();
        // 查询设备实时状态并更新
        Map<String, RobotRealtimeInfo> resultMap = result.stream()
                .map(mapData -> {
                    RobotRealtimeInfo realtimeInfo = new RobotRealtimeInfo();
                    try {
                        realtimeInfo = BeanUtil.mapToBean(mapData, RobotRealtimeInfo.class, true);
                        realtimeInfo.setRealtimeStatus(DeviceRealtimeStateEnum.OFFLINE.getValue());
                        Object onlineStatus = mapData.get("onlineStatus");
                        if (Objects.nonNull(onlineStatus) && (Integer) onlineStatus == 1) {
                            onLineDeviceList.add(realtimeInfo.getDeviceName());
                            realtimeInfo.setRealtimeStatus(DeviceRealtimeStateEnum.ONLINE.getValue());
                        } else {
                            processReportOfflineDevice(realtimeInfo);
                        }
                    } catch (Exception e) {
                        log.error("设备{}实时信息转换异常", JsonUtils.writeValueAsString(mapData), e);
                    }
                    return realtimeInfo;
                })
                .collect(Collectors.toMap(
                        RobotRealtimeInfo::getDeviceName,
                        Function.identity(),
                        (existing, replacement) -> existing // 处理可能的键冲突
                ));

        // 更新异常状态设备
        Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> abnormalMap = robotAlarmRepository.listMap(deviceNameList);
        onLineDeviceList.forEach(deviceName -> {
            RobotRealtimeInfo realtimeInfo = resultMap.get(deviceName);
            List<RobotAlarmDO.DeviceAlarmEventDO> robotAlarmDOList = abnormalMap.get(deviceName);
            if (CollectionUtils.isNotEmpty(robotAlarmDOList)) {
                boolean hasUrgentAlarm = robotAlarmDOList.stream()
                        .anyMatch(alarm -> StringUtils.equalsAny(
                                alarm.getErrorLevel(),
                                AlarmLevelEnum.ALARM_URGENT.getValue(),
                                AlarmLevelEnum.ALARM_MAJOR.getValue()
                        ));
                realtimeInfo.setRealtimeStatus(
                        hasUrgentAlarm ? DeviceRealtimeStateEnum.ABNORMAL.getValue() : DeviceRealtimeStateEnum.ONLINE.getValue()
                );
            }
        });
        robotRealtimeInfoManager.batchUpdateState(new ArrayList<>(resultMap.values()));
    }

    /**
     * 处理设备上报事件
     */
    @Override
    public void eventsReport(TransportDeviceMessage transportDeviceMessage) {
        String productType = transportDeviceMessage.getHeader().getProductKey();
        String messageType = transportDeviceMessage.getHeader().getMessageType();
        String deviceName = transportDeviceMessage.getHeader().getDeviceName();
        DeviceEventTypeEnum deviceEventTypeEnum = DeviceEventTypeEnum.getByValue(messageType);
        if (Objects.isNull(deviceEventTypeEnum)) {
            return;
        }
        switch (deviceEventTypeEnum) {
            case REPORT_ROBOT_ERROR -> {
                // 处理告警信息
                processReportAlarmInfo(transportDeviceMessage, deviceName, productType);
            }
            case REPORT_TASK_STATUS -> {
                // 处理行走日志
                processReportTaskStatus(transportDeviceMessage, productType, deviceName);
            }
            case REPORT_BOOT_TASK -> {
                // 处理启动数据
                processReportBootTask(transportDeviceMessage, deviceName, productType);
            }
            case EVENT_OPERATION_REMOTE_CALL -> {
                // 处理HMI远程呼叫
                processReportIssue(transportDeviceMessage, deviceName, productType);
            }
            case REPORT_V2V_INFO -> {
                // 处理V2V信息
                processReportV2VInfo(transportDeviceMessage, deviceName, productType);
            }

            default -> {}
        }
    }

    /**
     * 设备心跳消息
     */
    @Override
    public void propertyReport(String transportDeviceMessage) {
        RobotDeviceRealtimeInfoDTO realtimeInfoDto = JsonUtils.readValue(transportDeviceMessage, RobotDeviceRealtimeInfoDTO.class);
        if (Objects.isNull(realtimeInfoDto)) {
            return;
        }
        long lastValue = vehicleHandleTimeMap.getOrDefault(realtimeInfoDto.getDeviceName(), 0L);
        // 两条数据间隔小于2s,不满足最小间隔
        if (realtimeInfoDto.getTimestamp() - lastValue < TWO_SECOND_NANOSECOND) {
            return;
        }
        vehicleHandleTimeMap.put(realtimeInfoDto.getDeviceName(),realtimeInfoDto.getTimestamp());
        // 检测车辆长时间停车和低电量
        robotRealtimeAlarmService.processRobotPropertyAlarm(realtimeInfoDto);
        RobotMapRealtimeInfoDTO vehicleMapInfoDto = new RobotMapRealtimeInfoDTO();
        vehicleMapInfoDto.setProductKey(realtimeInfoDto.getProductKey());
        vehicleMapInfoDto.setDeviceName(realtimeInfoDto.getDeviceName());
        vehicleMapInfoDto.setX(realtimeInfoDto.getX());
        vehicleMapInfoDto.setY(realtimeInfoDto.getY());
        vehicleMapInfoDto.setOnlineStatus(DeviceRealtimeStateEnum.ONLINE.getProperty());
        vehicleMapInfoDto.setYaw(realtimeInfoDto.getYaw());
        vehicleMapInfoDto.setAlarmState(DeviceAlarmCategoryEnum.NORMAL.getValue());
        Map<String, RobotAlarmDO.DeviceAlarmEventDO> alarmList = robotAlarmRepository.get(realtimeInfoDto.getDeviceName());
        Optional<DeviceAlarmCategoryEnum> op = alarmList.values().stream().map(alarmEvent ->
                DeviceAlarmCategoryEnum.getByValue(DeviceAlarmCodeEnum.of(alarmEvent.getErrorCode()).getCategory()))
                .filter(Objects::nonNull).sorted(Comparator.comparingInt(Enum::ordinal)).findFirst();
        op.ifPresent(alarmCategory -> vehicleMapInfoDto.setAlarmState(alarmCategory.getValue()));
        sendRobotMapPosition(vehicleMapInfoDto);
    }

    /**
     * 处理设备消息事件回复
     * @param transportDeviceMessage 设备消息对象
     */
    @Override
    public void eventsReply(TransportDeviceMessage transportDeviceMessage) {
        TransportDeviceHeader headerMessage = transportDeviceMessage.getHeader();
        if (StringUtils.equals(headerMessage.getMessageType(), MqttCommandTypeEnum.CMD_REMOTE_COMMAND_REPLY.getValue())) {
            RobotControlCommandReplyDTO commandReplyDto = JsonUtils.readValue(JsonUtils.writeValueAsString(transportDeviceMessage.getData()), RobotControlCommandReplyDTO.class);
            if (Objects.isNull(commandReplyDto)) {
                return;
            }
            String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + headerMessage.getDeviceName();
            RTopic rTopic = RedissonUtils.getRTopic(topicName);
            if (rTopic.countSubscribers() > 0) {
                HermesVehicleControlCommandDelayInfoDTO delayInfoDto = buildControlCommandDelayInfo(headerMessage.getDeviceName(), commandReplyDto);
                WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_VEHICLE_RESPONSE.getValue(), delayInfoDto);
                long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
                log.info("Success result {} send vehicle delay info to topic {}", result, topicName);
            } else {
                log.error("Error find robot control topic {} listener", topicName);
            }
        }
    }

    /**
     * 处理属性更改事件
     * @param propertyChangeDto 属性更改数据传输对象
     */
    @Override
    public void statusChange(PropertyChangeDTO propertyChangeDto) {

    }

    /**
     * 处理设备报警信息并更新设备状态。
     */
    private void processReportAlarmInfo(TransportDeviceMessage transportDeviceMessage, String deviceName, String productType) {
        try {
            robotReportAlarmService.processReportAlarmInfo(transportDeviceMessage, deviceName, productType);
        } catch (Exception e) {
            log.error("处理报警信息异常", e);
        }
    }

    /**
     * 处理设备上报的启动任务信息，并更新相关的设备状态和异常信息。
     */
    private void processReportBootTask(TransportDeviceMessage transportDeviceMessage, String deviceName, String productType) {
        RobotReportBootTaskDTO bootTask = JsonUtils.readValue(JsonUtils.writeValueAsString(transportDeviceMessage.getData()), RobotReportBootTaskDTO.class);
        if (Objects.nonNull(bootTask) && StringUtils.equals(bootTask.getBootStatus(), DeviceBootStatusEnum.BOOT_STATUS_SUCCESS.getValue())) {
            robotReportAlarmService.processRobotReboot(productType, deviceName);
            robotIssueService.endRobotIssue(productType, deviceName);
        }
    }

    /**
     * 处理机器人任务行走日志
     */
    private void processReportTaskStatus(TransportDeviceMessage transportDeviceMessage, String productType, String deviceName) {
        RobotReportTaskGoalDTO taskGoalDto = JsonUtils.readValue(JsonUtils.writeValueAsString(transportDeviceMessage.getData()), RobotReportTaskGoalDTO.class);
        if (Objects.nonNull(taskGoalDto) && Objects.nonNull(taskGoalDto.getTargetPoint())) {
            String pointId = taskGoalDto.getTargetPoint().getPointId();
            if (StringUtils.isNotBlank(pointId)) {
                PointInfoDTO pointInfoDto = indoorMapApiManager.getPointInfoByPointNo(pointId);
                if (Objects.nonNull(pointInfoDto) && StringUtils.isNotBlank(pointInfoDto.getName())) {
                    DeviceTaskActionEnum taskActionEnum = DeviceTaskActionEnum.getByValue(taskGoalDto.getTaskStatus());
                    String taskStatus = Objects.nonNull(taskActionEnum)? taskActionEnum.getName() : "未知动作";
                    String action = String.format("机器人%s【%s】",  taskStatus, pointInfoDto.getName());
                    trackingEventCollectService.pushDeviceDrivingEvent(productType, deviceName, new Date(), action);
                    robotScheduleAlarmService.checkRobotScheduleAlarm(deviceName, taskGoalDto.getTaskStatus(), taskGoalDto.getTaskId(), pointInfoDto);
                }
            }
        }
    }

    /**
     * 处理机器人工单信息
     */
    private void processReportIssue(TransportDeviceMessage transportDeviceMessage, String deviceName, String productType) {
        RobotIssueDO issueDo = robotIssueRepository.get(deviceName);
        if (Objects.isNull(issueDo)) {
            log.info("机器人{}工单不存在", deviceName);
            return;
        }
        issueDo.setRemoteCall(Boolean.TRUE);
        robotIssueRepository.set(deviceName, issueDo);
        ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(deviceName);
        createVo.setProductKey(productType);
        createVo.setBlockNo("hmi");
        createVo.setIdentifier(DeviceCommandTaskEnum.CMD_OPERATION_FOLLOW.getValue());
        RobotOperationFollowDTO followDto = new RobotOperationFollowDTO();
        followDto.setFollowUser(MapUtils.isNotEmpty(issueDo.getFollowUserMap())?
                issueDo.getFollowUserMap().entrySet().iterator().next().getValue() : "");
        createVo.setCommandArgs(JsonUtils.writeValueAsString(followDto));
        deviceCommandService.createImmediateTask(createVo);
    }

    /**
     * 处理V2V信息的报告
     */
    private void processReportV2VInfo(TransportDeviceMessage transportDeviceMessage, String deviceName, String productType) {
        RobotReportTaskLocationDTO taskLocationDto = JsonUtils.readValue(JsonUtils.writeValueAsString(transportDeviceMessage.getData()), RobotReportTaskLocationDTO.class);
        if (Objects.isNull(taskLocationDto) || !Objects.equals(taskLocationDto.getFunctionCode(), 1)
                || CollectionUtils.isEmpty(taskLocationDto.getPath())) {
            return;
        }
        RobotTaskRouteInfoDO taskRouteInfoDo = new RobotTaskRouteInfoDO();
        taskRouteInfoDo.setMapId(taskLocationDto.getMapId());
        RobotTaskRouteInfoDO.Point goal = new RobotTaskRouteInfoDO.Point();
        goal.setX(taskLocationDto.getGoalPose().getX());
        goal.setY(taskLocationDto.getGoalPose().getY());
        taskRouteInfoDo.setGoal(goal);
        List<RobotTaskRouteInfoDO.Point> pointList = new ArrayList<>();
        taskLocationDto.getPath().forEach(point -> {
            RobotTaskRouteInfoDO.Point pointInfo = new RobotTaskRouteInfoDO.Point();
            pointInfo.setX(point.getX());
            pointInfo.setY(point.getY());
            pointInfo.setYaw(point.getYaw());
            pointList.add(pointInfo);
        });
        taskRouteInfoDo.setRoutePoint(pointList);
        RobotScheduleDO robotScheduleDo = robotScheduleRepository.get(deviceName);
        if (Objects.isNull(robotScheduleDo)) {
            return;
        }
        robotTaskRouteRepository.add(deviceName, taskRouteInfoDo);
        RobotMapRouteInfoDTO robotMapRouteInfo = new RobotMapRouteInfoDTO();
        if (Objects.nonNull(robotScheduleDo.getParkStop())) {
            robotMapRouteInfo.setStopId(robotScheduleDo.getParkStop().getStopId());
            robotMapRouteInfo.setStopName(robotScheduleDo.getParkStop().getStopName());
            robotMapRouteInfo.setX(robotScheduleDo.getParkStop().getX());
            robotMapRouteInfo.setY(robotScheduleDo.getParkStop().getY());
            if (CollectionUtils.isNotEmpty(taskRouteInfoDo.getRoutePoint())) {
                LineString lineString = GeometryUtils.createLineString(taskRouteInfoDo.getRoutePoint().stream().map(point -> {
                    Coordinate coordinate = new Coordinate();
                    coordinate.setX(point.getX());
                    coordinate.setY(point.getY());
                    return coordinate;
                }).collect(Collectors.toList()));
                try {
                    // 使用GeoTools的GeometryJSON将Point转换为GeoJSON
                    StringWriter writer = new StringWriter();
                    GeometryJSON geometryJSON = new GeometryJSON(8); // 8位小数精度
                    geometryJSON.write(lineString, writer);
                    robotMapRouteInfo.setPlanRoute(writer.toString());
                } catch (Exception e) {
                    log.error("获取机器人设备的路径信息失败", e);
                }
            }
        }
        String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + deviceName;
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_UPDATE.getValue(), robotMapRouteInfo);
        rTopic.publish(JsonUtils.writeValueAsString(wsResult));
    }

    /**
     * 处理设备离线。
     * @param realtimeInfo 实时信息对象，包含设备名称等信息。
     */
    private void processReportOfflineDevice(RobotRealtimeInfo realtimeInfo) {
        String deviceName = realtimeInfo.getDeviceName();
        // 1、通知前端关闭遥控页面
        String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + deviceName;
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        if (rTopic.countSubscribers() > 0) {
            WsResult wsResult = WsResult.error(WebsocketEventTypeEnum.REMOTE_CONTROL_SESSION_CLOSE.getValue(),
                    HttpCodeEnum.FORBIDDEN.getValue(), MonitorErrorEnum.ERROR_VEHICLE_CONTROL_OFFLINE.getMessage());
            rTopic.publish(JsonUtils.writeValueAsString(wsResult));
        }
        vehicleTakeOverRepository.remove(deviceName);
        // 2、地图侧通知更新设备状态
        RobotMapRealtimeInfoDTO vehicleMapInfoDto = new RobotMapRealtimeInfoDTO();
        vehicleMapInfoDto.setProductKey(realtimeInfo.getProductKey());
        vehicleMapInfoDto.setDeviceName(realtimeInfo.getDeviceName());
        vehicleMapInfoDto.setOnlineStatus(DeviceRealtimeStateEnum.OFFLINE.getProperty());
        sendRobotMapPosition(vehicleMapInfoDto);

    }

    /**
     * 构建控制命令延迟信息DTO对象。
     * @param deviceName 设备名称。
     * @param commandReplyDto 控制命令回复DTO对象。
     * @return 控制命令延迟信息DTO对象。
     */
    private HermesVehicleControlCommandDelayInfoDTO buildControlCommandDelayInfo(String deviceName, RobotControlCommandReplyDTO commandReplyDto) {
        HermesVehicleControlCommandDelayInfoDTO delayInfoDto = new HermesVehicleControlCommandDelayInfoDTO();
        delayInfoDto.setVehicleName(deviceName);
        delayInfoDto.setId(commandReplyDto.getSequenceNum());
        delayInfoDto.setHeading(commandReplyDto.getAngular());
        delayInfoDto.setSpeed(commandReplyDto.getLinear());
        if (Objects.nonNull(commandReplyDto.getModuleDelayInfo())) {
            delayInfoDto.setDelayInfo(commandReplyDto.getModuleDelayInfo().stream().map(delayInfo -> {
                HermesChassisCommandModuleDelayInfoDTO delayInfoDTO = new HermesChassisCommandModuleDelayInfoDTO();
                delayInfoDTO.setModuleName(delayInfo.getModuleName());
                delayInfoDTO.setReceiveTime(delayInfo.getReceiveTime());
                delayInfoDTO.setTransitTime(delayInfo.getTransitTime());
                return delayInfoDTO;
            }).collect(Collectors.toList()));
        }
        return delayInfoDto;
    }

    /**
     * 构建机器人实时信息列表。
     * @param deviceList 设备消息数据列表。
     * @return 机器人实时信息列表。
     */
    private List<RobotRealtimeInfo> buildUpdateRealtimeInfo(List<DeviceMessageData> deviceList) {
        List<RobotRealtimeInfo> robotList =
                deviceList.stream().map(device -> {
                    RobotRealtimeInfo deviceRealtimeInfo = new RobotRealtimeInfo();
                    deviceRealtimeInfo.setDeviceName(device.getDeviceName());
                    deviceRealtimeInfo.setProductModelNo(device.getProductModelNo());
                    deviceRealtimeInfo.setProductModelName(device.getProductModelName());
                    deviceRealtimeInfo.setProductKey(device.getProductKey());
                    deviceRealtimeInfo.setRemarkName(device.getRemarkName());
                    DeviceGroupMessageData deviceGroupMessageData = device.getDeviceGroupMessageData();
                    if (Objects.nonNull(deviceGroupMessageData)) {
                        deviceRealtimeInfo.setGroupName(deviceGroupMessageData.getGroupName());
                        deviceRealtimeInfo.setGroupLevelName(deviceGroupMessageData.getLevelName());
                        if (Objects.isNull(deviceGroupMessageData.getParent())) {
                            deviceRealtimeInfo.setGroupLevelName(deviceGroupMessageData.getGroupName());
                        }
                        if (Objects.isNull(deviceGroupMessageData.getLevel()) || deviceGroupMessageData.getLevel() == 1) {
                            deviceRealtimeInfo.setGroupOne(deviceGroupMessageData.getGroupNo());
                        } else if (deviceGroupMessageData.getLevel() == 2 && !Objects.isNull(deviceGroupMessageData.getParent())) {
                            deviceRealtimeInfo.setGroupOne(deviceGroupMessageData.getParent().getGroupNo());
                            deviceRealtimeInfo.setGroupTwo(deviceGroupMessageData.getGroupNo());
                        } else if (deviceGroupMessageData.getLevel() == 3 && !Objects.isNull(deviceGroupMessageData.getParent()) &&
                                !Objects.isNull(deviceGroupMessageData.getParent().getParent())) {
                            deviceRealtimeInfo.setGroupOne(deviceGroupMessageData.getParent().getParent().getGroupNo());
                            deviceRealtimeInfo.setGroupTwo(deviceGroupMessageData.getParent().getGroupNo());
                            deviceRealtimeInfo.setGroupThree(deviceGroupMessageData.getGroupNo());
                        }
                    }
                    return deviceRealtimeInfo;
                }).collect(Collectors.toList());
        return robotList;
    }

}
