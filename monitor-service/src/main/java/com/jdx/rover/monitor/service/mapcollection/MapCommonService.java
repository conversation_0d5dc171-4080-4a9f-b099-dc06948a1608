/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.mapcollection;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.metadata.domain.dto.station.CityStationInfoDTO;
import com.jdx.rover.monitor.dto.mapcollection.CityStationDTO;
import com.jdx.rover.monitor.dto.mapcollection.CityStationDTO.CityDTO;
import com.jdx.rover.monitor.dto.mapcollection.CityStationDTO.StationDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskFilterDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskFilterDTO.CityFilterDTO;
import com.jdx.rover.monitor.vo.mapcollection.TaskFilterVO;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskManager;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTask;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/12 16:48
 * @description 采集公共服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MapCommonService {

    /**
     * MapCollectionTaskManager
     */
    private final MapCollectionTaskManager mapCollectionTaskManager;

    /**
     * MetadataStationApiManager
     */
    private final MetadataStationApiManager metadataStationApiManager;

    /**
     * 获取全部城市及站点
     *
     * @return CityStationDTO
     */
    public CityStationDTO getAllCityAndStation() {
        CityStationDTO cityStationDTO = new CityStationDTO();
        CityStationInfoDTO allCityAndStation = metadataStationApiManager.getAllCityAndStation();
        if (CollUtil.isEmpty(allCityAndStation.getCityList())) {
            return cityStationDTO;
        }

        // 组装数据
        List<CityDTO> cityList = Lists.newArrayListWithCapacity(allCityAndStation.getCityList().size());
        cityStationDTO.setCityList(cityList);
        allCityAndStation.getCityList().forEach(cityInfoDTO -> {
            CityDTO cityDTO = new CityDTO();
            cityDTO.setCityId(cityInfoDTO.getCityId());
            cityDTO.setCityName(cityInfoDTO.getCityName());

            List<StationDTO> stationList = Lists.newArrayListWithCapacity(cityInfoDTO.getStationList().size());
            cityDTO.setStationList(stationList);
            cityInfoDTO.getStationList().forEach(stationInfoDTO -> {
                StationDTO stationDTO = new StationDTO();
                stationDTO.setStationId(stationInfoDTO.getStationId());
                stationDTO.setStationName(stationInfoDTO.getStationName());
                stationList.add(stationDTO);
            });
            cityList.add(cityDTO);
        });
        return cityStationDTO;
    }

    /**
     * 获取站点及任务创建人列表
     *
     * @param taskFilterVO taskFilterVO
     * @return TaskFilterDTO
     */
    public TaskFilterDTO getTaskFilter(TaskFilterVO taskFilterVO) {
        TaskFilterDTO taskFilterDTO = new TaskFilterDTO();
        List<MapCollectionTask> mapCollectionTasks = mapCollectionTaskManager.queryFilterItem(taskFilterVO == null ? null : taskFilterVO.getStationId());
        if (CollUtil.isEmpty(mapCollectionTasks)) {
            return taskFilterDTO;
        }

        // 处理城市站点
        Map<Integer, List<MapCollectionTask>> groupedByCityId = mapCollectionTasks.stream()
            .collect(Collectors.groupingBy(MapCollectionTask::getCityId));
        List<CityFilterDTO> cityList = groupedByCityId.entrySet().stream()
            .map(entry -> {
                CityFilterDTO cityFilterDTO = new CityFilterDTO();
                cityFilterDTO.setCityId(entry.getKey());
                List<Integer> stations = entry.getValue().stream()
                    .map(MapCollectionTask::getStationId)
                    .distinct()
                    .collect(Collectors.toList());
                cityFilterDTO.setStationList(stations);
                return cityFilterDTO;
            })
            .collect(Collectors.toList());
        taskFilterDTO.setCityList(cityList);

        // 处理创建人
        List<String> creatorList = mapCollectionTasks.stream()
            .map(MapCollectionTask::getTaskCreator)
            .distinct()
            .collect(Collectors.toList());
        taskFilterDTO.setCreatorList(creatorList);

        return taskFilterDTO;
    }
}
