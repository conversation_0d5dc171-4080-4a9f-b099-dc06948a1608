/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.MonitorChangeVideoModeVO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorVideoModeDTO;
import com.jdx.rover.monitor.dto.RoverVideoUrlDTO;
import com.jdx.rover.monitor.dto.accident.VehicleSnapshotDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.MqttCommandTypeEnum;
import com.jdx.rover.monitor.enums.MqttMsgTypeEnum;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.VideoCaptureStatus;
import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttMessageTypeEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.accident.AccidentAttachmentManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.config.RoverVideoProperties;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentAttachment;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.vo.drive.MonitorHistoryVideoVO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestHeader;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Random;

/**
 * <p>
 * This is a basic infomation service with vehicle, user-vehicle and jira
 * function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
@Slf4j
public class MonitorVideoService {

  /**
   * <p>
   * Represents the rest template.
   * </p>
   */
  @Autowired
  private RestTemplate restTemplate;

  @Autowired
  private RoverVideoProperties roverVideoProperties;

  @Autowired
  private AccidentManager accidentManager;

  @Autowired
  private AccidentAttachmentManager accidentAttachmentManager;

  @Autowired
  private SingleVehicleManager singleVehicleManager;

  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;

  @Value("${spring.profiles.active}")
  private String profileActive;

  @Autowired
  private MqttSendJsfService mqttSendJsfService;

  @Autowired
  private MqttManager mattManager;

  @Autowired
  private TrackingEventCollectService trackingEventCollectService;

  /**
   * <p>
   * find all video address.
   * </p>
   *
   */
  public HttpResult getVehicleVideoInfo(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    long expire_time = System.currentTimeMillis()/1000 + 10;
    Integer rand = new Random().nextInt(10000);
    String uid = roverVideoProperties.getUid();
    StringBuilder stringBuilder = new StringBuilder(String.valueOf(expire_time)).append("-").append(rand).append("-").append(uid).append("-")
            .append(vehicleName).append("-").append(roverVideoProperties.getKey());
    String sha = DigestUtil.sha256Hex(stringBuilder.toString(), "UTF-8");
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(roverVideoProperties.getUrl() + vehicleName).
            queryParam("uid", uid).queryParam("expire_time", expire_time).
            queryParam("rand", rand).queryParam("token", sha);
    RoverVideoUrlDTO dto = new RoverVideoUrlDTO();
    try {
      String url = builder.toUriString();
      log.info("Load video url {}", url);
      String result = restTemplate.getForObject(url, String.class);
      log.info("Load video url result: {}", result);
      dto = JsonUtils.readValue(result, RoverVideoUrlDTO.class);
    } catch (Exception e) {
      log.error("Load video url exception", e);
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
    }
    return HttpResult.success(Objects.isNull(dto)? new Object():dto.getData());
  }

  /**
   * <p>
   * find multi vehicle video address.
   * </p>
   *
   */
  public HttpResult getVehicleVideoList(List<String> vehicleNameList) {
    ParameterCheckUtility.checkNotNullNorEmpty(vehicleNameList, "vehicleNameList");
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(roverVideoProperties.getMultiUrl());
    RoverVideoUrlDTO dto = new RoverVideoUrlDTO();
    try {
      String url = builder.toUriString();
      log.info("Load video url {}", url);
      MultiVideoUrlVO requestBody = MultiVideoUrlVO.builder().front("true").carIdList(vehicleNameList).build();
      String result = HttpUtil.post(url, JsonUtils.writeValueAsString(requestBody));
      log.info("Load video url result: {}", result);
      dto = JsonUtils.readValue(result, RoverVideoUrlDTO.class);
    } catch (Exception e) {
      log.error("Load multi video url exception", e);
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
    }
    return HttpResult.success(Objects.isNull(dto)? new Object():dto.getData());
  }

  /**
   * <p>
   * find multi vehicle video address.
   * </p>
   *
   */
  public HttpResult getHistoryVideo(MonitorHistoryVideoVO historyVideoVo) {
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(roverVideoProperties.getHistorySearchUrl());
    RoverVideoUrlDTO dto = new RoverVideoUrlDTO();
    try {
      String url = builder.toUriString();
      log.info("Search video url {}", url);
      String result = HttpUtil.post(url, JsonUtils.writeValueAsString(historyVideoVo));
      log.info("Search video url result: {}", result);
      dto = JsonUtils.readValue(result, RoverVideoUrlDTO.class);
    } catch (Exception e) {
      log.error("Load multi video url exception", e);
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
    }
    return HttpResult.success(Objects.isNull(dto)? new Object():dto.getData());
  }

  /**
   * 修改视频模式
   * @param changeVideoModeVo 包含要修改的视频模式信息的对象
   * @return 操作结果
   */
  public HttpResult<Void> changeVideoMode(MonitorChangeVideoModeVO changeVideoModeVo) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(changeVideoModeVo.getVehicleName(), "vehicleName");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(changeVideoModeVo.getVideoMode(), "videoMode");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(changeVideoModeVo.getSource(), "source");
    String topic = String.format(MqttMsgTypeEnum.VEHICLE_CHANGE_VIDEO_MODE.getValue(), profileActive, changeVideoModeVo.getVehicleName());
    String userName = UserUtils.getLoginUser();
    MonitorVideoModeDTO videoModeDto = new MonitorVideoModeDTO();
    videoModeDto.setUser(userName);
    videoModeDto.setDevice(changeVideoModeVo.getSource());
    videoModeDto.setMode(changeVideoModeVo.getVideoMode());
    MqttMessageVO<String> mqttMessageVO = new MqttMessageVO<>();
    mqttMessageVO.setTopic(topic);
    MqttRequestDTO requestDto = new MqttRequestDTO();
    MqttRequestHeader header = new MqttRequestHeader();
    header.setRequestId(DateUtil.currentSeconds());
    header.setVehicleName(changeVideoModeVo.getVehicleName());
    header.setMessageType(MqttCommandTypeEnum.CHANGE_VIDEO_QUALITY.getValue());
    header.setNeedResponse(Boolean.TRUE);
    header.setReceiveName(changeVideoModeVo.getVehicleName());
    header.setRequestTime(DateUtil.current());
    requestDto.setHeader(header);
    requestDto.setData(videoModeDto);
    mqttMessageVO.setMessage(JsonUtils.writeValueAsString(requestDto));
    mqttSendJsfService.sendString(mqttMessageVO);
    trackingEventCollectService.pushVideoChangeMode(changeVideoModeVo, userName);
    return HttpResult.success();
  }

  @Async("asyncThreadPool")
  @Transactional(rollbackFor=Exception.class)
  public void getVehicleSnapshot(String vehicleName, Date date, String location, String userName, String accidentNo) {

    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(roverVideoProperties.getSnapshotUrl());
    try {
      String url = builder.toUriString();
      log.info("Load vehicle snapshot url : {}", url);
      VehicleSnapshotVO vehicleSnapshotVO = VehicleSnapshotVO.builder().terminalId(vehicleName).direction(location).picTimeStr(DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"))
              .originator(userName).usageType("DEV").build();
      String result = HttpUtil.post(url, JsonUtils.writeValueAsString(vehicleSnapshotVO));
      log.info("Load vehicle snapshot result: {}", result);
      RoverVideoUrlDTO<List<VehicleSnapshotDTO>> dto = JsonUtils.readValue(result, new TypeReference<RoverVideoUrlDTO<List<VehicleSnapshotDTO>>>() {
      });
      boolean hasPicture = false;
      List<VehicleSnapshotDTO> vehicleSnapshotDTOS = dto.getData();
      for (VehicleSnapshotDTO vehicleSnapshotDTO : vehicleSnapshotDTOS) {
        if (vehicleSnapshotDTO.getPicOssUrl() != null) {
          hasPicture = true;
          AccidentAttachment accidentAttachment = new AccidentAttachment();
          accidentAttachment.setAccidentNo(accidentNo);
          accidentAttachment.setType("image");
          accidentAttachment.setSource(AccidentAttachmentSourceEnum.VIDEO_SNAPSHOT.getValue());
          accidentAttachment.setLocation(vehicleSnapshotDTO.getDirection());
          accidentAttachment.setBucket("video-stream");
          accidentAttachment.setFileKey(vehicleSnapshotDTO.getPicOssUrl());
          accidentAttachment.setCreateUser("DEFAULT");
          accidentAttachment.setModifyUser("DEFAULT");
          accidentAttachmentManager.save(accidentAttachment);
        }
      }
      setAccidentVideoCaptureStatus(accidentNo, "0".equals(dto.getErrorCode()) && hasPicture);
    } catch (Exception e) {
      log.error("Load multi video url exception", e);
    }
    //消息推送
    singleVehicleManager.pushSingleVehicleAccident(vehicleName);
  }

  @Async
  @Transactional(rollbackFor=Exception.class)
  public void getVehicleHistorySnapshot(String vehicleName, Date date, String location, String userName, String accidentNo) {

    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(roverVideoProperties.getHistoryPicUrl());
    try {
      String url = builder.toUriString();
      log.info("Load vehicle history snapshot url : {}", url);
      VehicleSnapshotVO vehicleSnapshotVO = VehicleSnapshotVO.builder().terminalId(vehicleName).direction(location).picTimeStr(DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"))
              .originator(userName).usageType("DEV").build();
      String result = HttpUtil.post(url, JsonUtils.writeValueAsString(vehicleSnapshotVO));
      log.info("Load vehicle history snapshot result: {}", result);
      RoverVideoUrlDTO<List<VehicleSnapshotDTO>> dto = JsonUtils.readValue(result, new TypeReference<RoverVideoUrlDTO<List<VehicleSnapshotDTO>>>() {
      });
      boolean hasPicture = false;
      List<VehicleSnapshotDTO> vehicleSnapshotDTOS = dto.getData();
      for (VehicleSnapshotDTO vehicleSnapshotDTO : vehicleSnapshotDTOS) {
        if (vehicleSnapshotDTO.getPicOssUrl() != null) {
          hasPicture = true;
          AccidentAttachment accidentAttachment = new AccidentAttachment();
          accidentAttachment.setAccidentNo(accidentNo);
          accidentAttachment.setType("image");
          accidentAttachment.setSource(AccidentAttachmentSourceEnum.VIDEO_SNAPSHOT.getValue());
          accidentAttachment.setLocation(vehicleSnapshotDTO.getDirection());
          accidentAttachment.setBucket("video-stream");
          accidentAttachment.setFileKey(vehicleSnapshotDTO.getPicOssUrl());
          accidentAttachment.setCreateUser("DEFAULT");
          accidentAttachment.setModifyUser("DEFAULT");
          accidentAttachmentManager.save(accidentAttachment);
        }
      }
      setAccidentVideoCaptureStatus(accidentNo, "0".equals(dto.getErrorCode()) && hasPicture);
    } catch (Exception e) {
      log.error("Load multi video url exception", e);
    }
    //消息推送
    singleVehicleManager.pushSingleVehicleAccident(vehicleName);
    return;
  }

  /**
   * 设置事故快照截取状态
   * @param accidentNo
   * @param responseStatus
   */
  private void setAccidentVideoCaptureStatus(String accidentNo, boolean responseStatus) {
    Accident accident = this.accidentManager.selectByAccidentNo(accidentNo);
    if(null == accident) {
      String key = RedisKeyEnum.VIDEO_CAPTURE_STATUS.getValue() + accidentNo;
      if(responseStatus) {
        RedissonUtils.setObject(key, VideoCaptureStatus.SUCCESS.getCode(), 30);
      } else {
        RedissonUtils.setObject(key, VideoCaptureStatus.FAIL.getCode(), 30);
      }
    } else {
      if(responseStatus) {
        accident.setVideoCaptureStatus(VideoCaptureStatus.SUCCESS.getCode());
        this.accidentManager.updateById(accident);
      } else {
        accident.setVideoCaptureStatus(VideoCaptureStatus.FAIL.getCode());
        this.accidentManager.updateById(accident);
      }
    }
  }

  /**
   * 通知视频模式变更响应。
   * @param videoModeDto 视频模式DTO对象，包含视频模式信息。
   */
  public void notifyVideoModeChangeResponse(String deviceName, MonitorVideoModeDTO videoModeDto) {
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + deviceName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (StringUtils.equals(deviceName, RemoteCommandSourceEnum.MONITOR.getCommandSource()) && rTopic.countSubscribers() > 0) {
        // 推送监控端
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.VEHICLE_VIDEO_MODE_INFO.getValue(), deviceName);
        long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
        log.info("Success result {} send vehicle monitor control to topic {}", result, topicName);
    } else if (StringUtils.equals(videoModeDto.getDevice(), RemoteCommandSourceEnum.REMOTE_JOYSTICK.getCommandSource())) {
      // 推送平行驾驶端
      VehicleTakeOverEntity vehicleTakeOverEntity = vehicleTakeOverRepository.get(deviceName);
      if (Objects.isNull(vehicleTakeOverEntity) || StringUtils.isBlank(vehicleTakeOverEntity.getUserName())) {
        return;
      }
      String userName = vehicleTakeOverEntity.getUserName();
      String topic = String.format(MqttTopicEnum.USER_INFO.getTopic(), profileActive) + vehicleTakeOverEntity.getUserName();
      log.info("通知座席下{}用户{}车辆{}视频推流模式", vehicleTakeOverEntity.getCockpitNumber(), userName, deviceName);
      mattManager.sendQos1NoRetainNoResponse(deviceName, topic, MqttMessageTypeEnum.VEHICLE_VIDEO_MODE_INFO.name(), videoModeDto);
    }
  }

  @Data
  @Builder
  static class MultiVideoUrlVO {
    /**
     * <p>
     * 请求标识
     * </p>
     */
     private String uid = "jdx";

    /**
     * <p>
     * 请求视频
     * </p>
     */
    private String front = "false";


    /**
     * <p>
     * 请求视频
     * </p>
     */
    private String right = "false";


    /**
     * <p>
     * 请求视频
     * </p>
     */
    private String back = "false";

    /**
     * <p>
     * 请求视频
     * </p>
     */
    private String left = "false";

    /**
     * <p>
     * 请求车辆列表
     * </p>
     */
    private List<String> carIdList;
  }

  @Data
  @Builder
  static class VehicleSnapshotVO {
    /**
     * <p>
     * 车牌号
     * </p>
     */
    private String terminalId;

    /**
     * <p>
     * 视频方向
     * </p>
     */
    private String direction = "ALL";


    /**
     * <p>
     * 截图时间
     * </p>
     */
    private String picTimeStr;


    /**
     * <p>
     * 用户名
     * </p>
     */
    private String originator;

    /**
     * <p>
     * 截图用途
     * </p>
     */
    private String usageType = "dev";
  }
}
