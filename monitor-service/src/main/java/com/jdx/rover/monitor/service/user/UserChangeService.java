package com.jdx.rover.monitor.service.user;

import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamBasicInfoDTO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamBasicUserVO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.user.GetCockpitStatusDTO;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamMqttManager;
import com.jdx.rover.monitor.manager.user.UserInfoMqttManager;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @description: 用户页面切换Service
 * @author: wangguotai
 * @create: 2024-06-12 17:27
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class UserChangeService {

    private final CockpitTeamManager cockpitTeamManager;

    private final UserStatusRepository userStatusRepository;

    private final CockpitStatusRepository cockpitStatusRepository;

    private final CockpitTeamMqttManager cockpitTeamMqttManager;

    private final UserInfoMqttManager userInfoMqttManager;

    /**
     * 1、获取用户座席状态
     *
     * @return GetCockpitStatusDTO
     */
    public GetCockpitStatusDTO getCockpitStatus() {
        String username = UserUtils.getAndCheckLoginUser();
        GetCockpitStatusDTO statusDTO = new GetCockpitStatusDTO();
        // 1、获取座席团队
        CockpitTeamBasicUserVO userVO = new CockpitTeamBasicUserVO();
        userVO.setUserName(username);
        CockpitTeamBasicInfoDTO teamInfoOfUserName = cockpitTeamManager.getTeamBasicInfoOfUserName(userVO);
        if (teamInfoOfUserName != null) {
            statusDTO.setCockpitTeamNumber(teamInfoOfUserName.getCockpitTeamNumber());
        }
        // 2、获取座席
        UserStatusDO userStatusDO = userStatusRepository.get(username);
        if (userStatusDO != null) {
            String cockpitNumber = userStatusDO.getCockpitNumber();
            if (StringUtils.isNotBlank(cockpitNumber)) {
                CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(cockpitNumber);
                statusDTO.setCockpitNumber(cockpitStatusDO.getCockpitNumber());
                statusDTO.setCockpitType(cockpitStatusDO.getCockpitType());
                statusDTO.setCockpitStatus(cockpitStatusDO.getCockpitStatus());
                statusDTO.setCockpitMode(userStatusDO.getCockpitMode());
            }
        }
        // 3、推送初始化mqtt
        if (!Objects.isNull(teamInfoOfUserName)) {
            cockpitTeamMqttManager.pushInitCockpitTeam(teamInfoOfUserName.getCockpitTeamNumber());
            userInfoMqttManager.pushInitUserInfo(username);
        }
        return statusDTO;
    }
}