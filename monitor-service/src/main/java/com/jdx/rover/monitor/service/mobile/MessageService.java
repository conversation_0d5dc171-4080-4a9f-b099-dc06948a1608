package com.jdx.rover.monitor.service.mobile;

import cn.hutool.core.date.DateUtil;
import com.amazonaws.HttpMethod;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.metadata.api.domain.enums.RequireLogTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.RoverBucketEnum;
import com.jdx.rover.metadata.api.domain.enums.common.YesOrNoEnum;
import com.jdx.rover.metadata.domain.dto.require.RequireDetailDTO;
import com.jdx.rover.metadata.domain.vo.require.CheckRequireVO;
import com.jdx.rover.monitor.bo.vehicle.VehicleSortBO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.mobile.message.AccidentAttachmentDTO;
import com.jdx.rover.monitor.dto.mobile.message.AccidentOperateDTO;
import com.jdx.rover.monitor.dto.mobile.message.AccidentRecordDTO;
import com.jdx.rover.monitor.dto.mobile.message.ErrorVehicleAlarmEventDTO;
import com.jdx.rover.monitor.dto.mobile.message.ErrorVehicleInfoDTO;
import com.jdx.rover.monitor.dto.mobile.message.ErrorVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.message.ErrorVehicleScheduleDTO;
import com.jdx.rover.monitor.dto.mobile.message.ErrorVehicleScheduleStopDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAbnormalVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAccidentDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAccidentReportDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetRepairDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetUserTodoTaskListDTO;
import com.jdx.rover.monitor.dto.mobile.message.RequireAttachmentDTO;
import com.jdx.rover.monitor.dto.mobile.message.RequireRecordDTO;
import com.jdx.rover.monitor.dto.mobile.message.TodoListDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentFlowEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentGenerateEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentHandleMethodEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentLogEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperateEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperationJudgeEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperationStatusEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperationTypeEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentSupervisionEnum;
import com.jdx.rover.monitor.enums.mobile.CompensateTypeEnum;
import com.jdx.rover.monitor.enums.mobile.MessageModuleEnum;
import com.jdx.rover.monitor.enums.mobile.MessageStatusEnum;
import com.jdx.rover.monitor.enums.mobile.MessageTypeEnum;
import com.jdx.rover.monitor.enums.mobile.RepairOperateEnum;
import com.jdx.rover.monitor.enums.mobile.RequireResourceTypeConvertEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleCategoryEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleGroupsSortEnum;
import com.jdx.rover.monitor.enums.video.VideoDirectionEnum;
import com.jdx.rover.monitor.manager.accident.AccidentAttachmentManager;
import com.jdx.rover.monitor.manager.accident.AccidentDetailManager;
import com.jdx.rover.monitor.manager.accident.AccidentFlowLogManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.accident.AccidentRecordLogManager;
import com.jdx.rover.monitor.manager.require.RequireInfoManager;
import com.jdx.rover.monitor.manager.ticket.IssueQueryApiManager;
import com.jdx.rover.monitor.manager.todo.UserTodoTaskManager;
import com.jdx.rover.monitor.manager.vehicle.SystemStateManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentAttachment;
import com.jdx.rover.monitor.po.AccidentDetail;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.po.AccidentRecordLog;
import com.jdx.rover.monitor.po.UserTodoTask;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.sort.VehicleNameScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.service.web.AccidentFlowService;
import com.jdx.rover.monitor.vo.mobile.message.AccidentAttachmentVO;
import com.jdx.rover.monitor.vo.mobile.message.AccidentGenerateVO;
import com.jdx.rover.monitor.vo.mobile.message.RepairOperateVO;
import com.jdx.rover.schedule.api.domain.enums.StopTravelStatus;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import com.jdx.rover.ticket.domain.dto.query.AlarmIssueDTO;
import com.jdx.rover.ticket.api.enums.IssueStatusEnum;
import com.jdx.rover.ticket.domain.vo.query.QueryAlarmIssueVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 消息相关service
 */
@Service
@Slf4j
public class MessageService {

    @Autowired
    private UserVehicleNameRepository userVehicleNameRepository;

    @Autowired
    private VehicleNameScoreSortedSetRepository vehicleNameScoreSortedSetRepository;

    @Autowired
    private VehicleRealtimeRepository vehicleRealtimeRepository;

    @Autowired
    private VehicleAlarmRepository vehicleAlarmRepository;

    @Autowired
    private VehicleScheduleRepository vehicleScheduleRepository;

    @Autowired
    private VehicleBasicRepository vehicleBasicRepository;

    @Autowired
    private UserTodoTaskManager userTodoTaskManager;

    @Autowired
    private AccidentManager accidentManager;

    @Autowired
    private AccidentRecordLogManager accidentRecordLogManager;

    @Autowired
    private AccidentDetailManager accidentDetailManager;

    @Autowired
    private AccidentAttachmentManager accidentAttachmentManager;

    @Autowired
    private S3Properties s3Properties;

    @Autowired
    private RequireInfoManager requireInfoManager;

    @Autowired
    private IssueQueryApiManager issueQueryApiManager;

    @Autowired
    private AccidentFlowService accidentFlowService;

    @Autowired
    private AccidentFlowLogManager accidentFlowLogManager;

    private static Set<String> errorVehicleCategorySet = new HashSet<>(Arrays.asList("CONNECTION_LOST", "VEHICLE_CRASH", "MANUAL_REPORT", "SENSOR_ERROR", "OPERATION_ALARM", "BOOT_ALARM", "STOP", "CLOUD_WARN", "LOW_BATTERY", "LOW_PRESSURE", "INTERSECTION_ABNORMAL"));

    /**
     * 获取用户代办事项列表
     *
     * @return GetUserTodoTaskListDTO
     */
    public GetUserTodoTaskListDTO getUserTodoTaskList() {
        String loginUserName = UserUtils.getAndCheckLoginUser();
        Set<String> vehicleNameSet = userVehicleNameRepository.get(loginUserName);
        List<UserTodoTask> accidentList = userTodoTaskManager.lambdaQuery().eq(UserTodoTask::getModule, MessageModuleEnum.ACCIDENT.getValue()).eq(UserTodoTask::getStatus, MessageStatusEnum.HANDLE.getValue())
                .and(wrapper -> wrapper.eq(UserTodoTask::getOwner, loginUserName).or().in(!CollectionUtils.isEmpty(vehicleNameSet), UserTodoTask::getVehicleName, vehicleNameSet))
                .orderByDesc(UserTodoTask::getCreateTime).list();
        List<UserTodoTask> repairList = userTodoTaskManager.lambdaQuery().eq(UserTodoTask::getModule, MessageModuleEnum.REPAIR.getValue()).eq(UserTodoTask::getStatus, MessageStatusEnum.HANDLE.getValue())
                .eq(UserTodoTask::getOwner, loginUserName).orderByDesc(UserTodoTask::getCreateTime).list();
        GetUserTodoTaskListDTO result = new GetUserTodoTaskListDTO();
        List<TodoListDTO> todoList = new ArrayList<>();

        List<UserTodoTask> userTodoTaskList = new ArrayList<>(accidentList);
        userTodoTaskList.addAll(repairList);
        userTodoTaskList.sort(Comparator.comparing(UserTodoTask::getCreateTime).reversed());

        List<String> accidentNoList = accidentList.stream().map(UserTodoTask::getBusinessKey).toList();
        List<Accident> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accidentNoList)) {
            list = accidentManager.lambdaQuery().in(Accident::getAccidentNo, accidentNoList).select(Accident::getAccidentNo, Accident::getAlarmNumber).list();
        }
        //构造告警编号MAP
        Map<String, AlarmIssueDTO> accidentIssueMap = buildAccidentIssueMap(list);
        for (UserTodoTask userTodoTask : userTodoTaskList) {
            TodoListDTO todoListDTO = new TodoListDTO();
            buildTodoListDTO(todoListDTO, userTodoTask, accidentIssueMap);
            todoList.add(todoListDTO);
        }
        result.setTodoList(todoList);
        result.setTodoSize(todoList.size());
        result.setErrorSize(getErrorVehicleSize(vehicleNameSet));
        return result;
    }

    /**
     * 构造告警编号-> 工单 MAP
     *
     * @param accidentList
     * @return
     */
    private Map<String, AlarmIssueDTO> buildAccidentIssueMap(List<Accident> accidentList) {
        Map<String, AlarmIssueDTO> accidentIssueMap = new HashMap<>();
        if (CollectionUtils.isEmpty(accidentList)) {
            return accidentIssueMap;
        }
        //根据告警编号获取工单信息
        QueryAlarmIssueVO queryAlarmIssueVO = new QueryAlarmIssueVO();
        List<String> alarmNoList = accidentList.stream().map(Accident::getAlarmNumber).collect(Collectors.toList());
        queryAlarmIssueVO.setAlarmNoList(alarmNoList);
        List<AlarmIssueDTO> issueInfoByAlarmList = issueQueryApiManager.getIssueInfoByAlarm(queryAlarmIssueVO);
        Map<String, AlarmIssueDTO> alarmIssueDTOMap = issueInfoByAlarmList.stream().collect(Collectors.toMap(AlarmIssueDTO::getAlarmNo, Function.identity()));
        //构造告警编号MAP
        for (Accident accident : accidentList) {
            if (alarmIssueDTOMap.containsKey(accident.getAlarmNumber())) {
                accidentIssueMap.put(accident.getAccidentNo(), alarmIssueDTOMap.get(accident.getAlarmNumber()));
            }
        }
        return accidentIssueMap;
    }

    private void buildTodoListDTO(TodoListDTO todoListDTO, UserTodoTask userTodoTask, Map<String, AlarmIssueDTO> accidentIssueMap) {
        todoListDTO.setMessageId(userTodoTask.getId());
        todoListDTO.setVehicleName(userTodoTask.getVehicleName());
        todoListDTO.setModule(userTodoTask.getModule());
        todoListDTO.setType(userTodoTask.getType());
        todoListDTO.setTypeName(MessageTypeEnum.getNameByValue(userTodoTask.getType()));
        todoListDTO.setPushTime(userTodoTask.getPushTime());
        todoListDTO.setStationName(userTodoTask.getStationName());
        todoListDTO.setDescription(userTodoTask.getDescription());
        // 获取事故额外信息
        if (MessageModuleEnum.ACCIDENT.getValue().equals(userTodoTask.getModule())) {
            Map<String, Object> attributes = new HashMap<>();
            todoListDTO.setAttributes(attributes);
            attributes.put("siteUser", userTodoTask.getOwner());
            attributes.put("address", userTodoTask.getAddress());
            if (accidentIssueMap.containsKey(userTodoTask.getBusinessKey())) {
                attributes.put("remoteUser", accidentIssueMap.get(userTodoTask.getBusinessKey()).getAcceptUsername());
            }
        }
    }

    /**
     * 获取异常车辆信息
     *
     * @param vehicleNameSet
     * @return
     */
    private Integer getErrorVehicleSize(Set<String> vehicleNameSet) {
        // 获取所有车辆分数
        Integer errorSize = 0;
        Map<String, Double> map = vehicleNameScoreSortedSetRepository.getValueAndScoreMap();
        List<Double> scoreList = new ArrayList<>();
        for (String vehicleName : vehicleNameSet) {
            Double score = map.get(vehicleName);
            scoreList.add(score);
            if (score == null) {
                log.info("getScoredSortedSetScore vehicleName={},sortType={},分数不存在", vehicleName, score);
                continue;
            }
            if (score < VehicleGroupsSortEnum.NORMAL.getValue()) {
                errorSize++;
            }
        }
        return errorSize;
    }

    /**
     * 获取异常车辆信息
     *
     * @return GetAbnormalVehicleListDTO
     */
    public GetAbnormalVehicleListDTO GetAbnormalVehicleListDTO() {
        GetAbnormalVehicleListDTO result = new GetAbnormalVehicleListDTO();
        Integer errorSize = 0;
        List<ErrorVehicleListDTO> errorVehicleList = new ArrayList<>();

        String loginUserName = UserUtils.getLoginUser();
        if (loginUserName == null) {
            log.info("获取小程序异常车辆信息，未获取到用户名直接返回");
            return result;
        }

        Set<String> multiVehicleSet = userVehicleNameRepository.get(loginUserName);
        List<String> vehicleNameOrderList = new ArrayList<>(multiVehicleSet);
        // 获取所有车辆分数
        Map<String, Double> map = vehicleNameScoreSortedSetRepository.getValueAndScoreMap();
        List<Double> scoreList = new ArrayList<>();
        for (String vehicleName : multiVehicleSet) {
            Double score = map.get(vehicleName);
            scoreList.add(score);
            if (score == null) {
                log.info("getScoredSortedSetScore vehicleName={},sortType={},分数不存在", vehicleName, score);
                continue;
            }
            if (score < VehicleGroupsSortEnum.NORMAL.getValue()) {
                errorSize++;
            }
        }
        // 初始化
        for (VehicleCategoryEnum itemEnum : VehicleCategoryEnum.values()) {
            String category = itemEnum.getValue();
            // 没有分页请求则返回
            if (!errorVehicleCategorySet.contains(category)) {
                continue;
            }
            ErrorVehicleListDTO errorVehicleListDTO = new ErrorVehicleListDTO();
            errorVehicleListDTO.setCategory(category);
            List<ErrorVehicleInfoDTO> list = new ArrayList<>();
            errorVehicleListDTO.setList(list);

            VehicleSortBO vehicleSortBO = getScoreByCategory(category);
            double startSectionScore = vehicleSortBO.getStartScore();
            double endSectionScore = vehicleSortBO.getEndScore();
            List<String> tabPageVehicleNameList = new ArrayList<>();
            for (int i = 0; i < vehicleNameOrderList.size(); i++) {
                Double score = scoreList.get(i);
                if (!Objects.isNull(score) && score >= startSectionScore && score < endSectionScore) {
                    tabPageVehicleNameList.add(vehicleNameOrderList.get(i));
                }
            }
            if (CollectionUtils.isEmpty(tabPageVehicleNameList)) {
                continue;
            }
            setErrorVehicleList(list, tabPageVehicleNameList);
            errorVehicleListDTO.setTotal(tabPageVehicleNameList.size());
            errorVehicleList.add(errorVehicleListDTO);
        }
        result.setErrorSize(errorSize);
        result.setErrorVehicleList(errorVehicleList);
        return result;
    }

    private void setErrorVehicleList(List<ErrorVehicleInfoDTO> errorVehicleInfoDTOList, List<String> vehicleNameList) {
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
        Map<String, VehicleAlarmDO> alarmMap = vehicleAlarmRepository.listMap(vehicleNameList);
        Map<String, MonitorScheduleEntity> monitorScheduleEntityMap = vehicleScheduleRepository.listMap(vehicleNameList);
        Map<String, VehicleBasicDTO> vehicleBasicDTOMap = vehicleBasicRepository.listMap(vehicleNameList);
        for (String vehicleName : vehicleNameList) {
            ErrorVehicleInfoDTO errorVehicleInfoDTO = new ErrorVehicleInfoDTO();
            errorVehicleInfoDTO.setVehicleName(vehicleName);
            errorVehicleInfoDTO.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
            VehicleBasicDTO vehicleBasicDTO = vehicleBasicDTOMap.get(vehicleName);
            if (vehicleBasicDTO != null) {
                errorVehicleInfoDTO.setVehicleBusinessType(vehicleBasicDTO.getBusinessType());
                errorVehicleInfoDTO.setStationName(vehicleBasicDTO.getStationName());
            }
            VehicleRealtimeInfoDTO realtime = realtimeMap.get(vehicleName);
            if (realtime != null) {
                String systemState = realtime.getSystemState();
                errorVehicleInfoDTO.setSystemState(systemState);
                if (SystemStateEnum.CONNECTION_LOST.getSystemState().equals(errorVehicleInfoDTO.getSystemState())) {
                    errorVehicleInfoDTO.setRecordTime(realtime.getRecordTime());
                }
                if (!SystemStateManager.isOfflineOrLost(systemState)) {
                    errorVehicleInfoDTO.setVehicleState(realtime.getVehicleState());
                    errorVehicleInfoDTO.setSpeed(realtime.getSpeed());
                    errorVehicleInfoDTO.setPower(realtime.getPower());

                    VehicleAlarmDO alarm = alarmMap.get(vehicleName);
                    if (alarm != null && CollectionUtils.isNotEmpty(alarm.getAlarmEventList())) {
                        List<ErrorVehicleAlarmEventDTO> alarmEventList = alarm.getAlarmEventList().stream().map(tmp -> {
                            ErrorVehicleAlarmEventDTO alarmEventDTO = new ErrorVehicleAlarmEventDTO();
                            alarmEventDTO.setAlarmEvent(tmp.getType());
                            alarmEventDTO.setReportTime(tmp.getReportTime());
                            return alarmEventDTO;
                        }).collect(Collectors.toList());
                        errorVehicleInfoDTO.setAlarmEventList(alarmEventList);
                    }
                }
            } else {
                errorVehicleInfoDTO.setSystemState(SystemStateEnum.OFFLINE.getSystemState());
            }

            MonitorScheduleEntity monitorScheduleEntity = monitorScheduleEntityMap.get(vehicleName);
            if (monitorScheduleEntity != null) {
                ErrorVehicleScheduleDTO errorVehicleScheduleDTO = convertEntityToDTO(monitorScheduleEntity, realtime);
                errorVehicleInfoDTO.setSchedule(errorVehicleScheduleDTO);
                errorVehicleInfoDTO.setScheduleState(monitorScheduleEntity.getScheduleState());
                errorVehicleInfoDTO.setTaskType(monitorScheduleEntity.getTaskType());
            }
            errorVehicleInfoDTOList.add(errorVehicleInfoDTO);
        }
    }


    /**
     * 通过标签也获取分数区间
     *
     * @param category
     * @return
     */
    private VehicleSortBO getScoreByCategory(String category) {
        VehicleCategoryEnum vehicleCategoryEnum = VehicleCategoryEnum.valueOf(category);
        VehicleSortBO bo = new VehicleSortBO();
        bo.setStartScore(vehicleCategoryEnum.getStartScore());
        bo.setEndScore(vehicleCategoryEnum.getEndScore());
        return bo;
    }

    private ErrorVehicleScheduleDTO convertEntityToDTO(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
        ErrorVehicleScheduleDTO dto = new ErrorVehicleScheduleDTO();
        if (monitorScheduleEntity == null || monitorScheduleEntity.getScheduleNo() == null) {
            return dto;
        }
        dto.setTotalOrderNum(
                monitorScheduleEntity.getTotalCollectOrderNum() + monitorScheduleEntity.getTotalDeliveryOrderNum());
        dto.setFinishedOrderNum(
                monitorScheduleEntity.getFinishedCollectOrderNum() + monitorScheduleEntity.getFinishedDeliveryOrderNum());
        dto.setGlobalMileage(monitorScheduleEntity.getGlobalMileage());
        Double arrivedMileage = getArrivedMileage(monitorScheduleEntity, realtime);
        dto.setArrivedMileage(arrivedMileage);
        if (realtime != null && realtime.getCurrentStopFinishedMileage() != null) {
            dto.setCurrentStopFinishedMileage(realtime.getCurrentStopFinishedMileage());
        } else {
            dto.setCurrentStopFinishedMileage(0.0);
        }
        List<MonitorScheduleStopEntity> monitorScheduleStopEntityList = monitorScheduleEntity.getStop();
        if (CollectionUtils.isEmpty(monitorScheduleStopEntityList)) {
            return dto;
        }
        List<ErrorVehicleScheduleStopDTO> stopList = new ArrayList<>();
        for (MonitorScheduleStopEntity stop : monitorScheduleStopEntityList) {
            ErrorVehicleScheduleStopDTO errorVehicleScheduleStopDTO = new ErrorVehicleScheduleStopDTO();
            errorVehicleScheduleStopDTO.setId(stop.getId());
            errorVehicleScheduleStopDTO.setGoalId(stop.getGoalId());
            errorVehicleScheduleStopDTO.setType(stop.getType());
            errorVehicleScheduleStopDTO.setStopAction(stop.getStopAction());
            errorVehicleScheduleStopDTO.setTravelStatus(stop.getTravelStatus());
            // 只有前往才需要里程算百分比
            if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.START.getTravelStatus())) {
                errorVehicleScheduleStopDTO.setGlobalMileage(stop.getGlobalMileage());
                errorVehicleScheduleStopDTO.setStartTime(stop.getStartTime());
                errorVehicleScheduleStopDTO.setName(stop.getName());
            }
            // 只有停留才需要站点名称,等待时间,到达时间
            if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.STAY.getTravelStatus())) {
                errorVehicleScheduleStopDTO.setName(stop.getName());
                errorVehicleScheduleStopDTO.setEstDepartTime(stop.getEstDepartTime());
                errorVehicleScheduleStopDTO.setArrivedTime(stop.getArrivedTime());
                errorVehicleScheduleStopDTO.setWaitingTime(stop.getWaitingTime());
            }
            stopList.add(errorVehicleScheduleStopDTO);
        }
        dto.setStopList(stopList);
        return dto;
    }

    /**
     * 当前调度完成里程
     *
     * @param monitorScheduleEntity
     * @param realtime
     * @return
     */
    private Double getArrivedMileage(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
        Double finishedMileage = Optional.ofNullable(monitorScheduleEntity.getFinishedMileage()).orElse(0.0);
        if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(),
                VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(),
                VehicleScheduleState.RETURN.getVehicleScheduleState())) {
            Double currentStopFinishedMileage;
            if (realtime == null || realtime.getCurrentStopFinishedMileage() == null) {
                currentStopFinishedMileage = 0.0;
            } else {
                currentStopFinishedMileage = realtime.getCurrentStopFinishedMileage();
            }
            finishedMileage += currentStopFinishedMileage;
        }
        return finishedMileage;
    }

    /**
     * 获取事故卡片详情
     *
     * @param messageId messageId
     * @return GetAccidentDetailDTO
     */
    public GetAccidentDetailDTO getAccidentDetail(Integer messageId) {
        UserTodoTask userTodoTask = userTodoTaskManager.getById(messageId);
        if (userTodoTask == null) {
            log.error("获取事故卡片，卡片不存在，messageId:{}", messageId);
            return null;
        }
        Accident accident = accidentManager.selectByAccidentNo(userTodoTask.getBusinessKey());
        if (accident == null) {
            log.error("获取事故卡片，事故不存在，accidentNo:{}", userTodoTask.getBusinessKey());
            return null;
        }
        GetAccidentDetailDTO result = new GetAccidentDetailDTO();
        result.setMessageId(messageId);
        result.setAccidentNo(userTodoTask.getBusinessKey());
        result.setVehicleName(userTodoTask.getVehicleName());
        result.setDescription(userTodoTask.getDescription());
        //获取事故相关信息
        result.setShadowEventId(accident.getShadowEventId());
        result.setIsHaveTemporaryIncident(accident.getIsHaveTemporaryIncident() == 1);
        result.setAddress(accident.getAccidentAddress());
        result.setOperationUser(accident.getOperationUser());
        //获取事故日志
        List<AccidentRecordLog> accidentRecordLogs = accidentRecordLogManager.selectListByAccidentNo(userTodoTask.getBusinessKey());
        List<AccidentRecordDTO> accidentRecordDTOList = new ArrayList<>();
        accidentRecordLogs.forEach(accidentRecordLog -> {
            AccidentRecordDTO accidentRecordDTO = new AccidentRecordDTO();
            accidentRecordDTO.setTitle(accidentRecordLog.getTitle());
            accidentRecordDTO.setContent(accidentRecordLog.getContent());
            accidentRecordDTOList.add(accidentRecordDTO);
        });
        result.setRecordList(accidentRecordDTOList);
        List<AccidentAttachment> accidentAttachmentList = accidentAttachmentManager.selectOrderedListByAccidentNoAndSource(userTodoTask.getBusinessKey(), AccidentAttachmentSourceEnum.VIDEO_SNAPSHOT.getValue());

        //事故快照
        List<AccidentAttachmentDTO> attachmentList = new ArrayList<>();
        for (AccidentAttachment accidentAttachment : accidentAttachmentList) {
            AccidentAttachmentDTO accidentAttachmentDTO = new AccidentAttachmentDTO();
            accidentAttachmentDTO.setFileKey(accidentAttachment.getFileKey());
            accidentAttachmentDTO.setType(accidentAttachment.getType());
            final Date expiration = DateUtil.offsetHour(new Date(), 24);
            String videoUrl = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), accidentAttachment.getBucket(), accidentAttachment.getFileKey(), HttpMethod.GET, expiration).toString();
            accidentAttachmentDTO.setUrl(videoUrl);
            accidentAttachmentDTO.setLocation(accidentAttachment.getLocation());
            accidentAttachmentDTO.setLocationName(VideoDirectionEnum.getNameByValue(accidentAttachment.getLocation()));
            attachmentList.add(accidentAttachmentDTO);
        }
        result.setAttachmentList(attachmentList);
        //获取工单相关信息
        Map<String, AlarmIssueDTO> alarmIssueDTOMap = buildAccidentIssueMap(List.of(accident));
        AlarmIssueDTO alarmIssueDTO = alarmIssueDTOMap.get(accident.getAccidentNo());
        if (alarmIssueDTO != null) {
            result.setRemoteUser(alarmIssueDTO.getAcceptUsername());
            result.setIssueNumber(alarmIssueDTO.getIssueNumber());
            result.setIssueStatus(alarmIssueDTO.getIssueStatus());
            result.setIssueStatusName(IssueStatusEnum.getNameByCode(alarmIssueDTO.getIssueStatus()));
        }
        return result;
    }

    /**
     * 获取维修卡片详情
     *
     * @param messageId messageId
     * @return GetRepairDetailDTO
     */
    public GetRepairDetailDTO getRepairDetail(Integer messageId) {
        UserTodoTask userTodoTask = userTodoTaskManager.getById(messageId);
        if (userTodoTask == null) {
            log.error("获取维修卡片详情,卡片不存在,messageId:{}", messageId);
            return null;
        }
        RequireDetailDTO requireDetail = requireInfoManager.getRequireDetail(userTodoTask.getBusinessKey());
        if (requireDetail == null) {
            log.error("获取维修卡片详情,维修单不存在,messageId:{}", messageId);
            return null;
        }
        return createRepairDetailDTO(userTodoTask, requireDetail);
    }

    private GetRepairDetailDTO createRepairDetailDTO(UserTodoTask userTodoTask, RequireDetailDTO requireDetail) {
        GetRepairDetailDTO result = new GetRepairDetailDTO();
        result.setMessageId(userTodoTask.getId());
        result.setRepairNumber(userTodoTask.getBusinessKey());
        result.setVehicleName(userTodoTask.getVehicleName());
        result.setType(userTodoTask.getType());
        result.setReportTime(requireDetail.getReportTime());
        result.setIsInfluenceOperation(requireDetail.getIsInfluenceOperation());
        result.setIsInfluenceOperationName(requireDetail.getIsInfluenceOperation() == 1 ? "影响运营" : "不影响运营");
        result.setReportDesc(requireDetail.getDescription());
        result.setRequireHardwareTypeNames(requireDetail.getRequireHardwareTypeNames());
        //附件
        List<RequireAttachmentDTO> attachmentList = new ArrayList<>();
        result.setAttachmentList(attachmentList);
        requireDetail.getAttachmentDTOList().forEach(attachmentDTO -> {
            RequireAttachmentDTO requireAttachmentDTO = new RequireAttachmentDTO();
            String type = RequireResourceTypeConvertEnum.getNameByValue(attachmentDTO.getType());
            requireAttachmentDTO.setType(type);
            requireAttachmentDTO.setFileKey(attachmentDTO.getFileKey());
            requireAttachmentDTO.setUrl(attachmentDTO.getUrl());
            attachmentList.add(requireAttachmentDTO);
        });
        //记录
        List<RequireRecordDTO> recordList = new ArrayList<>();
        result.setRecordList(recordList);
        requireDetail.getRequireLog().forEach(requireLogDTO -> {
            RequireRecordDTO requireRecordDTO = new RequireRecordDTO();
            requireRecordDTO.setTitle(RequireLogTypeEnum.getNameByValue(requireLogDTO.getType()));
            requireRecordDTO.setContent(requireLogDTO.getDescription());
            if (RequireLogTypeEnum.CAN_NOT_ACCEPT.getValue().equals(requireLogDTO.getType())) {
                result.setRejectTime(requireLogDTO.getCreateTime());
                result.setRejectDesc(requireLogDTO.getRemark());
            } else if (RequireLogTypeEnum.COMPLETE_REQUIRE.getValue().equals(requireLogDTO.getType())) {
                result.setFinishTime(requireLogDTO.getCreateTime());
                result.setCompleteRequireDesc(requireLogDTO.getRemark());
            }
            recordList.add(requireRecordDTO);
        });
        return result;
    }

    /**
     * 操作维修卡片
     *
     * @param repairOperateVO repairOperateDTO
     * @return MonitorErrorEnum
     */
    @Transactional(rollbackFor = Exception.class)
    public MonitorErrorEnum repairOperate(RepairOperateVO repairOperateVO) {
        String username = UserUtils.getLoginUser();
        //判断卡片是否存在
        UserTodoTask userTodoTask = userTodoTaskManager.getById(repairOperateVO.getMessageId());
        if (userTodoTask == null) {
            log.error("操作维修卡片,卡片不存在,messageId:{}", repairOperateVO.getMessageId());
            return MonitorErrorEnum.ERROR_MESSAGE_NOT_EXIST;
        }
        userTodoTaskManager.updateStatusById(repairOperateVO.getMessageId(), MessageStatusEnum.FINISH.getValue());
        if (RepairOperateEnum.CONFIRM.getValue().equals(repairOperateVO.getOperateType())) {
            //更改维修单状态
            CheckRequireVO checkRequireVO = new CheckRequireVO();
            checkRequireVO.setRequireNumber(repairOperateVO.getRepairNumber());
            checkRequireVO.setOperationUser(username);
            Boolean result = requireInfoManager.checkRequire(checkRequireVO);
            if (!result) {
                throw new AppException(MonitorErrorEnum.ERROR_MOBILE_REPEAT_OPERATION.getCode(), MonitorErrorEnum.ERROR_MOBILE_REPEAT_OPERATION.getMessage());
            }
        }
        return MonitorErrorEnum.OK;
    }

    /**
     * 操作事故卡片（“无需处理”和“生成事故单”两个操作）
     *
     * @param accidentOperateDTO accidentOperateDTO
     * @return MonitorErrorEnum
     */
    @Transactional(rollbackFor = Exception.class)
    public MonitorErrorEnum accidentOperate(AccidentOperateDTO accidentOperateDTO) {

        //处理事故卡片
        String accidentOperationLock = RedisKeyEnum.ACCIDENT_OPERATION_LOCK.getValue() + accidentOperateDTO.getAccidentNo();
        String accidentFlowEnum = null;
        if (RedissonUtils.tryLock(accidentOperationLock)) {
            try {
                String loginUserName = UserUtils.getAndCheckLoginUser();
                //判断卡片是否存在
                UserTodoTask userTodoTask = userTodoTaskManager.getById(accidentOperateDTO.getMessageId());
                if (userTodoTask == null) {
                    log.error("操作事故卡片,卡片不存在,messageId:{}", accidentOperateDTO.getMessageId());
                    return MonitorErrorEnum.ERROR_MESSAGE_NOT_EXIST;
                }
                if (AccidentOperateEnum.REPORT.getValue().equals(accidentOperateDTO.getOperateType())) {
                    if (userTodoTask.getOwner() != null && !userTodoTask.getOwner().equals(loginUserName)) {
                        log.error("事故卡片已经被跟进, messageId:{}, userName:{}", accidentOperateDTO.getMessageId(), loginUserName);
                        return MonitorErrorEnum.ERROR_ACCIDENT_ACCEPTED;
                    }
                    accidentFlowEnum = AccidentFlowEnum.OPERATION_ACCEPT.getValue();
                    //更新事故状态以及一线操作人
                    userTodoTaskManager.updateOwner(userTodoTask.getId(), loginUserName);
                    accidentManager.lambdaUpdate()
                            .eq(Accident::getAccidentNo, userTodoTask.getBusinessKey())
                            .set(Accident::getOperationUser, loginUserName)
                            .set(Accident::getOperationStatus, AccidentOperationStatusEnum.IN_PROGRESS.getValue())
                            .update();
                    accidentDetailManager.lambdaUpdate()
                            .eq(AccidentDetail::getAccidentNo, userTodoTask.getBusinessKey())
                            .set(AccidentDetail::getOperationUser, loginUserName)
                            .update();
                    //保存日志
                    accidentRecordLogManager.save(createAccidentLog(userTodoTask, loginUserName, AccidentLogEnum.OPERATION_ACCEPT));
                } else if (AccidentOperateEnum.REJECT.getValue().equals(accidentOperateDTO.getOperateType())) {
                    if (MessageStatusEnum.FINISH.getValue().equals(userTodoTask.getStatus())) {
                        log.error("事故已操作无需处理, loginUserName:{}, userTodoTaskId:{}", loginUserName, userTodoTask.getId());
                        return MonitorErrorEnum.ERROR_ACCIDENT_FINISH;
                    }
                    accidentFlowEnum = AccidentFlowEnum.OPERATION_REJECT.getValue();
                    //更新卡片状态以及事故状态
                    userTodoTaskManager.updateOwnerAndStatus(userTodoTask.getId(), loginUserName, MessageStatusEnum.FINISH.getValue());
                    accidentManager.lambdaUpdate()
                            .eq(Accident::getAccidentNo, userTodoTask.getBusinessKey())
                            .set(Accident::getOperationUser, loginUserName)
                            .set(Accident::getOperationStatus, AccidentOperationStatusEnum.COMPLETED.getValue())
                            .update();
                    //清除之前暂存的信息
                    accidentDetailManager.lambdaUpdate()
                            .eq(AccidentDetail::getAccidentNo, userTodoTask.getBusinessKey())
                            .set(AccidentDetail::getOperationUser, loginUserName)
                            .set(AccidentDetail::getOperationHandleMethod, null)
                            .set(AccidentDetail::getOperationCompensated, null)
                            .set(AccidentDetail::getOperationAmount, null)
                            .set(AccidentDetail::getOperationAccidentType, null)
                            .set(AccidentDetail::getOperationAccidentJudge, null)
                            .set(AccidentDetail::getOperationAccidentReason, null)
                            .set(AccidentDetail::getOperationIsReportVehicleNet, null)
                            .update();
                    accidentAttachmentManager.lambdaUpdate()
                            .eq(AccidentAttachment::getAccidentNo, userTodoTask.getBusinessKey())
                            .eq(AccidentAttachment::getSource, AccidentAttachmentSourceEnum.OPERATION.getValue())
                            .remove();
                    //保存日志
                    accidentRecordLogManager.save(createAccidentLog(userTodoTask, loginUserName, AccidentLogEnum.OPERATION_REJECT));
                    //保存事故流程日志
                    //生成事故流程
                    AccidentFlowLog accidentFlowLog = new AccidentFlowLog();
                    accidentFlowLog.setAccidentNo(userTodoTask.getBusinessKey());
                    accidentFlowLog.setUserName(loginUserName);
                    accidentFlowLog.setSource(AccidentFlowEnum.OPERATION_REJECT.getValue());
                    accidentFlowLog.setCreateUser(loginUserName);
                    accidentFlowLog.setModifyUser(loginUserName);
                    String content = String.format("区域安全员，%s-%s，现场处理:无需处理", loginUserName, DateUtil.now());
                    accidentFlowLog.setContent(content);
                    accidentFlowLogManager.save(accidentFlowLog);
                }
                //入库成功事务提交后发送Kafka
                String finalAccidentFlowEnum = accidentFlowEnum;
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        accidentFlowService.pushAccidentFlowEvent(userTodoTask.getBusinessKey(), finalAccidentFlowEnum, loginUserName);
                    }
                });
            } finally {
                RedissonUtils.unLock(accidentOperationLock);
            }
            return MonitorErrorEnum.OK;
        }
        return MonitorErrorEnum.ERROR_MOBILE_REPEAT_OPERATION;
    }

    /**
     * 生成事故单
     *
     * @param accidentGenerateVO accidentGenerateVO
     * @return MonitorErrorEnum
     */
    @Transactional(rollbackFor = Exception.class)
    public MonitorErrorEnum accidentGenerate(AccidentGenerateVO accidentGenerateVO) {
        String loginUserName = UserUtils.getAndCheckLoginUser();
        UserTodoTask userTodoTask = userTodoTaskManager.getById(accidentGenerateVO.getMessageId());
        if (userTodoTask == null) {
            log.error("生成事故单,卡片不存在,messageId:{}", accidentGenerateVO.getMessageId());
            return MonitorErrorEnum.ERROR_MESSAGE_NOT_EXIST;
        }
        if (!Objects.equals(userTodoTask.getBusinessKey(), accidentGenerateVO.getAccidentNo())) {
            log.error("生成事故单,卡片编号与事故编号不符");
            return MonitorErrorEnum.ERROR_MESSAGE_ID_ACCIDENT_NO_MATCH;
        }

        //更新运营填写内容
        accidentDetailManager.lambdaUpdate()
                .eq(AccidentDetail::getAccidentNo, accidentGenerateVO.getAccidentNo())
                .set(AccidentDetail::getOperationUser, loginUserName)
                .set(AccidentDetail::getOperationHandleMethod, accidentGenerateVO.getHandleMethod())
                .set(AccidentDetail::getOperationCompensated, booleanToInt(accidentGenerateVO.getCompensated()))
                .set(AccidentDetail::getOperationAmount, accidentGenerateVO.getAmount())
                .set(AccidentDetail::getOperationAccidentType, accidentGenerateVO.getAccidentType())
                .set(AccidentDetail::getOperationIsReportVehicleNet, BooleanToInt(accidentGenerateVO.getIsReportVehicleNet()))
                .set(AccidentDetail::getOperationAccidentJudge, accidentGenerateVO.getAccidentJudge())
                .set(AccidentDetail::getOperationAccidentReason, accidentGenerateVO.getReason())
                .update();
        if (AccidentGenerateEnum.SAVE.getValue().equals(accidentGenerateVO.getOperateType())) {
            // 更新状态为暂存
            accidentManager.lambdaUpdate()
                    .eq(Accident::getAccidentNo, accidentGenerateVO.getAccidentNo())
                    .set(Accident::getIsHaveTemporaryIncident, YesOrNoEnum.YES.getValue())
                    .update();
        } else if (AccidentGenerateEnum.SUBMIT.getValue().equals(accidentGenerateVO.getOperateType())) {
            accidentManager.lambdaUpdate()
                    .eq(Accident::getAccidentNo, accidentGenerateVO.getAccidentNo())
                    .set(Accident::getIsHaveTemporaryIncident, YesOrNoEnum.NO.getValue())
                    .set(Accident::getOperationUser, loginUserName)
                    .set(Accident::getOperationStatus, AccidentOperationStatusEnum.COMPLETED.getValue())
                    .update();
            userTodoTaskManager.lambdaUpdate()
                    .eq(UserTodoTask::getId, accidentGenerateVO.getMessageId())
                    .set(UserTodoTask::getStatus, MessageStatusEnum.FINISH.getValue())
                    .set(UserTodoTask::getModifyUser, loginUserName)
                    .update();
        }
        //插入事故附件
        accidentAttachmentManager.lambdaUpdate()
                .eq(AccidentAttachment::getAccidentNo, accidentGenerateVO.getAccidentNo())
                .eq(AccidentAttachment::getSource, AccidentAttachmentSourceEnum.OPERATION.getValue())
                .remove();
        for (AccidentAttachmentVO accidentAttachmentVO : accidentGenerateVO.getAttachmentList()) {
            AccidentAttachment accidentAttachment = new AccidentAttachment();
            accidentAttachment.setAccidentNo(accidentGenerateVO.getAccidentNo());
            accidentAttachment.setType(accidentAttachmentVO.getType());
            accidentAttachment.setFileKey(accidentAttachmentVO.getFileKey());
            accidentAttachment.setBucket(RoverBucketEnum.ROVER_OPERATION.getValue());
            accidentAttachment.setSource(AccidentAttachmentSourceEnum.OPERATION.getValue());
            accidentAttachment.setCreateUser(loginUserName);
            accidentAttachment.setModifyUser(loginUserName);
            accidentAttachmentManager.getBaseMapper().insert(accidentAttachment);
        }
        //生成日志
        if (AccidentGenerateEnum.SAVE.getValue().equals(accidentGenerateVO.getOperateType())) {
            accidentRecordLogManager.save(createAccidentLog(userTodoTask, loginUserName, AccidentLogEnum.OPERATION_SAVE));
        } else if (AccidentGenerateEnum.SUBMIT.getValue().equals(accidentGenerateVO.getOperateType())) {
            accidentRecordLogManager.save(createAccidentLog(userTodoTask, loginUserName, AccidentLogEnum.OPERATION_SUBMIT));
            //生成事故流程
            AccidentFlowLog accidentFlowLog = new AccidentFlowLog();
            accidentFlowLog.setAccidentNo(accidentGenerateVO.getAccidentNo());
            accidentFlowLog.setUserName(loginUserName);
            accidentFlowLog.setSource(AccidentFlowEnum.OPERATION_SUBMIT.getValue());
            String editContent = buildOperationLog(accidentGenerateVO);
            String content = String.format("区域安全员，%s-%s，现场处理:%s", loginUserName, DateUtil.now(), editContent);
            accidentFlowLog.setContent(content);
            accidentFlowLog.setCreateUser(loginUserName);
            accidentFlowLog.setModifyUser(loginUserName);
            accidentFlowLogManager.save(accidentFlowLog);
        }
        //入库成功事务提交后进行单车页数据推送
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                if (AccidentGenerateEnum.SUBMIT.getValue().equals(accidentGenerateVO.getOperateType())) {
                    accidentFlowService.pushAccidentFlowEvent(accidentGenerateVO.getAccidentNo(), AccidentFlowEnum.OPERATION_SUBMIT.getValue(), loginUserName);
                }
            }
        });
        return MonitorErrorEnum.OK;
    }

    private String buildOperationLog(AccidentGenerateVO accidentGenerateVO) {
        String content = "";
        content += AccidentHandleMethodEnum.getNameByValue(accidentGenerateVO.getHandleMethod()) + "/";
        if (accidentGenerateVO.getCompensated()) {
            content += CompensateTypeEnum.YES.getName() + " " + accidentGenerateVO.getAmount() + "/";
        } else {
            content += CompensateTypeEnum.NO.getName() + "/";
        }
        if (accidentGenerateVO.getIsReportVehicleNet() != null && accidentGenerateVO.getIsReportVehicleNet()) {
            content += AccidentSupervisionEnum.YES.getName() + "/";
        } else if (accidentGenerateVO.getIsReportVehicleNet() != null) {
            content += AccidentSupervisionEnum.NO.getName() + "/";
        }
        if (accidentGenerateVO.getAccidentType() != null) {
            content += AccidentOperationTypeEnum.getNameByValue(accidentGenerateVO.getAccidentType()) + "/";
        }

        if (accidentGenerateVO.getAccidentJudge() != null) {
            content += AccidentOperationJudgeEnum.getNameByValue(accidentGenerateVO.getAccidentJudge()) + "/";
        }
        if (accidentGenerateVO.getReason() != null) {
            content += accidentGenerateVO.getReason() + "/";
        }
        content = StringUtils.chop(content);
        return content;
    }

    /**
     * 布尔值转化为整数类型
     *
     * @param isReportVehicleNet
     * @return
     */
    private Integer BooleanToInt(Boolean isReportVehicleNet) {
        if (isReportVehicleNet == null) {
            return null;
        }
        return isReportVehicleNet ? 1 : 0;
    }

    /**
     * 生成事故操作日志
     *
     * @param userTodoTask
     * @param loginUserName
     * @param accidentLogEnum
     * @return
     */
    private AccidentRecordLog createAccidentLog(UserTodoTask userTodoTask, String loginUserName, AccidentLogEnum accidentLogEnum) {
        AccidentRecordLog accidentRecordLog = new AccidentRecordLog();
        accidentRecordLog.setAccidentNo(userTodoTask.getBusinessKey());
        accidentRecordLog.setUserName(UserUtils.getLoginUser());
        accidentRecordLog.setSource(accidentLogEnum.getValue());
        accidentRecordLog.setTitle(accidentLogEnum.getTitle());
        accidentRecordLog.setContent(String.format(accidentLogEnum.getContent(), loginUserName, DateUtil.now()));
        accidentRecordLog.setCreateUser(loginUserName);
        accidentRecordLog.setModifyUser(loginUserName);
        return accidentRecordLog;
    }

    /**
     * 获取事故单详情
     *
     * @param accidentNo accidentNo
     * @return GetAccidentReportDetailDTO
     */
    public GetAccidentReportDetailDTO getAccidentReportDetail(String accidentNo) {

        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if (accident == null) {
            log.error("获取事故单详情,事故单不存在,accidentNo:{}", accidentNo);
            return null;
        }
        GetAccidentReportDetailDTO result = new GetAccidentReportDetailDTO();
        AccidentDetail accidentDetail = accidentDetailManager.selectByAccidentNo(accidentNo);
        result.setAccidentNo(accidentNo);
        result.setHandleMethod(accidentDetail.getOperationHandleMethod());
        result.setCompensated(intToBoolean(accidentDetail.getOperationCompensated()));
        result.setAmount(accidentDetail.getOperationAmount());
        result.setIsReportVehicleNet(intToBoolean(accidentDetail.getOperationIsReportVehicleNet()));
        result.setAccidentType(accidentDetail.getOperationAccidentType());
        result.setAccidentJudge(accidentDetail.getOperationAccidentJudge());
        result.setReason(accidentDetail.getOperationAccidentReason());
        //获取附件信息
        List<AccidentAttachmentDTO> accidentAttachmentDTOList = new ArrayList<>();
        List<AccidentAttachment> accidentAttachments = accidentAttachmentManager.selectListByAccidentNoAndSource(accidentNo, AccidentAttachmentSourceEnum.OPERATION.getValue());
        for (AccidentAttachment accidentAttachment : accidentAttachments) {
            AccidentAttachmentDTO accidentAttachmentDTO = new AccidentAttachmentDTO();
            accidentAttachmentDTO.setType(accidentAttachment.getType());
            accidentAttachmentDTO.setFileKey(accidentAttachment.getFileKey());
            final Date expiration = DateUtil.offsetHour(new Date(), 24);
            accidentAttachmentDTO.setUrl(S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), accidentAttachment.getBucket(), accidentAttachment.getFileKey(), HttpMethod.GET, expiration).toString());
            accidentAttachmentDTOList.add(accidentAttachmentDTO);
        }
        result.setAttachmentList(accidentAttachmentDTOList);
        return result;
    }

    /**
     * 布尔值转换为0,1
     *
     * @param value
     * @return
     */
    public static Integer booleanToInt(Boolean value) {
        if (value == null) {
            return null;
        }
        return value ? 1 : 0;
    }

    /**
     * Integer 转换为 布尔值
     *
     * @param value
     * @return
     */
    public static Boolean intToBoolean(Integer value) {
        if (value == null) {
            return null;
        }
        return value == 1 ? true : false;
    }
}
