package com.jdx.rover.monitor.service.transport;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CoordinateUtil;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.exception.BusinessException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.enums.SupplierEnum;
import com.jdx.rover.metadata.domain.dto.stop.StopBasicDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.StationVehicleBasicDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleBasicDto;
import com.jdx.rover.metadata.jsf.service.station.MetadataStationBasicService;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.dto.MonitorScheduleDTO;
import com.jdx.rover.monitor.dto.TransportStopMapInfoDTO;
import com.jdx.rover.monitor.dto.transport.GetCallCockpitVehicleListDTO;
import com.jdx.rover.monitor.dto.transport.GetSelectVehicleListDTO;
import com.jdx.rover.monitor.dto.transport.GetVehicleInfoDTO;
import com.jdx.rover.monitor.dto.transport.VehicleRoutingDTO;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.enums.mobile.H5RemotePowerEnum;
import com.jdx.rover.monitor.manager.mobile.CommandManager;
import com.jdx.rover.monitor.manager.mobile.NeolixManager;
import com.jdx.rover.monitor.manager.schedule.VehicleScheduleManager;
import com.jdx.rover.monitor.manager.stop.MetadataStopApiManager;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.manager.utils.vehicle.SupplierUtils;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.repository.redis.VehicleSchedulePncRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRealRouteRepository;
import com.jdx.rover.monitor.vo.transport.RemotePowerOnOffVO;
import com.jdx.rover.monitor.vo.transport.VehicleRoutingVO;
import com.jdx.rover.monitor.vo.transport.CallCockpitVO;
import com.jdx.rover.monitor.vo.transport.CommandVO;
import com.jdx.rover.monitor.vo.transport.GetSelectVehicleListVO;
import com.jdx.rover.monitor.vo.transport.GetVehicleInfoVO;
import com.jdx.rover.monitor.vo.transport.RemoteControlCommandVO;
import com.jdx.rover.monitor.common.utils.jts.GeoUtils;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.MqttMsgTypeEnum;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.mobile.SystemStatusEnum;
import com.jdx.rover.monitor.jsf.JsfUtil;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.service.config.ducc.DuccMobileProperties;
import com.jdx.rover.monitor.service.mobile.CommandService;
import com.jdx.rover.monitor.service.web.MiniMonitorCommandService;
import com.jdx.rover.monitor.service.web.MonitorAlarmService;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteCommandVO;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import com.jdx.rover.monitor.vo.mobile.command.SendVO;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: 接驳H5控车工具服务
 * @author: wangguotai
 * @create: 2025-02-06 15:47
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class MonitorTransportService {

    private final DuccMobileProperties duccMobileProperties;

    private final MetadataStationBasicService metadataStationBasicService;

    private final MonitorAlarmService monitorAlarmService;

    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    private final MiniMonitorCommandService miniMonitorCommandService;

    private final MqttManager mqttManager;

    private final CommandService commandService;

    private final CommandManager commandManager;

    private final VehicleScheduleManager vehicleScheduleManager;

    private final VehicleSchedulePncRouteRepository pncRouteRepository;

    private final VehicleScheduleRealRouteRepository realRouteRepository;

    private final MetadataStopApiManager metadataStopApiManager;

    private final MetadataVehicleApiManager metadataVehicleApiManager;

    private final NeolixManager neolixManager;

    /**
     * 呼叫远驾-获取车辆列表
     *
     * @return GetCallCockpitVehicleListDTO
     */
    public GetCallCockpitVehicleListDTO getCallCockpitVehicleList() {
        // 获取站点下车辆列表
        List<String> vehicleNameList = getSiteVehicleList();

        // 获取ducc配置快捷短语
        List<String> phraseList = duccMobileProperties.getPhraseList();

        // 组装
        GetCallCockpitVehicleListDTO listDTO = new GetCallCockpitVehicleListDTO();
        listDTO.setVehicleNameList(vehicleNameList);
        listDTO.setPhraseList(phraseList);
        return listDTO;
    }

    /**
     * 获取站点下车辆列表
     */
    private List<String> getSiteVehicleList() {
        // 上下文获取siteId
        Integer siteId = JsfUtil.getH5SiteId();

        // 获取站点下车辆列表
        HttpResult<List<StationVehicleBasicDTO>> httpResult = metadataStationBasicService.getLocalVehicleListByStationNumber(String.valueOf(siteId));
        if (!HttpResult.isSuccess(httpResult)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_H5_GET_SITE_VEHICLE_LIST.getCode(), MonitorErrorEnum.ERROR_H5_GET_SITE_VEHICLE_LIST.getMessage());
        }

        List<StationVehicleBasicDTO> vehicleBasicDTOList = httpResult.getData();
        if (CollUtil.isEmpty(vehicleBasicDTOList)) {
            return Collections.emptyList();
        }
        return vehicleBasicDTOList.stream().filter(basic -> SupplierUtils.isJdVehicle(basic.getVehicleSupplier())).map(StationVehicleBasicDTO::getVehicleName).toList();
    }

    /**
     * 呼叫远驾
     *
     * @param callCockpitVO callCockpitVO
     */
    public void callCockpit(CallCockpitVO callCockpitVO) {
        MonitorManualAlarmReportVO reportVO = new MonitorManualAlarmReportVO();
        reportVO.setVehicleName(callCockpitVO.getVehicleName());
        reportVO.setRemark(callCockpitVO.getRemark());
        monitorAlarmService.callCockpit(reportVO, ManualAlarmSourceEnum.WORKBENCH_MONITOR);
    }

    /**
     * 获取车辆选择列表
     *
     * @param getSelectVehicleListVO getSelectVehicleListVO
     * @return List<GetSelectVehicleListDTO>
     */
    public List<GetSelectVehicleListDTO> getSelectVehicleList(GetSelectVehicleListVO getSelectVehicleListVO) {
        // 获取站点下车辆列表
        List<String> vehicleNameList = getSiteVehicleList();
        if (CollUtil.isEmpty(vehicleNameList)) {
            return Collections.emptyList();
        }

        // 获取车辆实时信息
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
        // 获取车辆接管信息
        Map<String, VehicleTakeOverEntity> takeOverMap = vehicleTakeOverRepository.listMap(vehicleNameList);

        // 组装
        List<GetSelectVehicleListDTO> dtoList = new ArrayList<>(vehicleNameList.size());
        for (String vehicleName : vehicleNameList) {
            VehicleRealtimeInfoDTO realtimeDTO = realtimeMap.get(vehicleName);
            VehicleTakeOverEntity takeOverDTO = takeOverMap.get(vehicleName);

            GetSelectVehicleListDTO listDTO = new GetSelectVehicleListDTO();
            listDTO.setVehicleName(vehicleName);
            Optional.ofNullable(realtimeDTO).ifPresentOrElse(v -> {
                CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(v.getLon(), v.getLat());
                SystemStatusEnum systemStatusEnum = SystemStatusEnum.getCodeMapping(v.getSystemState());
                listDTO.setSystemStatus(systemStatusEnum.getCode());
                listDTO.setSystemStatusSort(systemStatusEnum.getSort());
                listDTO.setDistance(GeoUtils.haversineDistance(getSelectVehicleListVO.getLatitude(), getSelectVehicleListVO.getLongitude(), coordinate.getLat(), coordinate.getLng()));
            }, () -> {
                listDTO.setSystemStatus(SystemStatusEnum.OFFLINE.getCode());
                listDTO.setSystemStatusSort(SystemStatusEnum.OFFLINE.getSort());
            });
            Optional.ofNullable(takeOverDTO).ifPresent(v -> {
                listDTO.setTakeoverStatus(v.getOperationStatus());
                listDTO.setTakeoverSource(v.getCommandSource());
                listDTO.setTakeoverUserName(v.getUserName());
            });
            dtoList.add(listDTO);
        }
        // 排序（系统状态+距离）
        dtoList.sort(Comparator.comparing(GetSelectVehicleListDTO::getSystemStatusSort, Comparator.nullsLast(Integer::compareTo)).thenComparing(GetSelectVehicleListDTO::getDistance, Comparator.nullsLast(Double::compare)));
        return dtoList;
    }

    /**
     * 获取车辆基本信息
     *
     * @param getVehicleInfoVO getVehicleInfoVO
     * @return GetVehicleInfoDTO
     */
    public GetVehicleInfoDTO getVehicleInfo(GetVehicleInfoVO getVehicleInfoVO) {
        // 获取车辆实时信息 + 获取车辆接管信息
        String vehicleName = getVehicleInfoVO.getVehicleName();
        VehicleRealtimeInfoDTO realtimeDTO = vehicleRealtimeRepository.get(vehicleName);
        VehicleTakeOverEntity takeOverDTO = vehicleTakeOverRepository.get(vehicleName);

        // 组装
        GetVehicleInfoDTO infoDTO = new GetVehicleInfoDTO();
        Optional.ofNullable(realtimeDTO).ifPresent(v -> {
            CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(v.getLon(), v.getLat());
            SystemStatusEnum systemStatusEnum = SystemStatusEnum.getCodeMapping(v.getSystemState());
            infoDTO.setLongitude(coordinate.getLng());
            infoDTO.setLatitude(coordinate.getLat());
            infoDTO.setSystemStatus(systemStatusEnum.getCode());
            infoDTO.setVehicleStatus(v.getVehicleState());
        });
        Optional.ofNullable(takeOverDTO).ifPresent(v -> {
            infoDTO.setTakeoverStatus(v.getOperationStatus());
            infoDTO.setTakeoverSource(v.getCommandSource());
            infoDTO.setTakeoverUserName(v.getUserName());
        });
        return infoDTO;
    }

    /**
     * 开启遥控器
     *
     * @param remoteControlCommandVO remoteControlCommandVO
     */
    public void openRemoteControl(RemoteControlCommandVO remoteControlCommandVO) {
        MiniMonitorRemoteCommandVO commandVO = new MiniMonitorRemoteCommandVO();
        commandVO.setVehicleName(remoteControlCommandVO.getVehicleName());
        commandVO.setTimeStamp(remoteControlCommandVO.getTimeStamp());
        miniMonitorCommandService.postEmergencyStopRequest(commandVO, RemoteCommandSourceEnum.WORKBENCH_MONITOR);
    }

    /**
     * 关闭遥控器
     *
     * @param remoteControlCommandVO remoteControlCommandVO
     */
    public void quitRemoteControl(RemoteControlCommandVO remoteControlCommandVO) {
        MiniMonitorRemoteCommandVO commandVO = new MiniMonitorRemoteCommandVO();
        commandVO.setVehicleName(remoteControlCommandVO.getVehicleName());
        commandVO.setTimeStamp(remoteControlCommandVO.getTimeStamp());
        miniMonitorCommandService.postRecoveryRequest(commandVO, RemoteCommandSourceEnum.WORKBENCH_MONITOR);
    }

    /**
     * 急刹
     *
     * @param remoteControlCommandVO remoteControlCommandVO
     */
    public void brakeRemoteControl(RemoteControlCommandVO remoteControlCommandVO) {
        MiniMonitorRemoteCommandVO commandVO = new MiniMonitorRemoteCommandVO();
        commandVO.setVehicleName(remoteControlCommandVO.getVehicleName());
        commandVO.setTimeStamp(remoteControlCommandVO.getTimeStamp());
        miniMonitorCommandService.postEmergencyBrakeRequest(commandVO, RemoteCommandSourceEnum.WORKBENCH_MONITOR);
    }

    /**
     * 鸣笛
     *
     * @param remoteControlCommandVO remoteControlCommandVO
     */
    public void remoteVoiceWhistle(RemoteControlCommandVO remoteControlCommandVO) {
        String vehicleName = remoteControlCommandVO.getVehicleName();
        String topic = "android/command/" + vehicleName;
        mqttManager.sendQos1NoRetainNoResponse(vehicleName, topic, MqttMsgTypeEnum.VEHICLE_VOICE_WHISTLE.getValue(), null);
    }

    /**
     * 指令操作
     *
     * @param commandVO commandVO
     */
    public void sendCommand(CommandVO commandVO) {
        SendVO sendVO = new SendVO();
        sendVO.setVehicleName(commandVO.getVehicleName());
        sendVO.setCommandType(commandVO.getCommandType());
        sendVO.setOperateTime(commandVO.getTimeStamp());
        commandService.send(sendVO);
    }

    /**
     * 远程控制车辆开关机。
     * @param remotePowerOnOffVO 远程控制参数对象，包含操作类型和车辆名称。
     */
    public MonitorErrorEnum remotePowerOnOff(RemotePowerOnOffVO remotePowerOnOffVO) {
        VehicleBasicDto vehicleInfo = metadataVehicleApiManager.getVehicleInfo(remotePowerOnOffVO.getVehicleName());
        if (vehicleInfo == null) {
            throw new AppException(MonitorErrorEnum.ERROR_VEHICLE_ABSENT.getCode(), MonitorErrorEnum.ERROR_VEHICLE_ABSENT.getMessage());
        }
        if (SupplierEnum.JD.getValue().equals(vehicleInfo.getSupplier())) {
            if (H5RemotePowerEnum.POWER_ON.getValue().equals(remotePowerOnOffVO.getOperateType())) {
                return commandManager.sendPdu(remotePowerOnOffVO.getVehicleName(), CommandTypeEnum.REMOTE_POWER_ON);
            } else if (H5RemotePowerEnum.POWER_OFF.getValue().equals(remotePowerOnOffVO.getOperateType())) {
                MonitorErrorEnum powerEnum = commandManager.remoteShutdown(remotePowerOnOffVO.getVehicleName());
                MonitorErrorEnum pduEnum = commandManager.sendPdu(remotePowerOnOffVO.getVehicleName(), CommandTypeEnum.REMOTE_SHUTDOWN);
                if (MonitorErrorEnum.OK.equals(powerEnum) || MonitorErrorEnum.OK.equals(pduEnum)) {
                    return MonitorErrorEnum.OK;
                } else {
                    return MonitorErrorEnum.ERROR_PDU_CONNECT;
                }
            }
        } else if (SupplierEnum.NEOLIX.getValue().equals(vehicleInfo.getSupplier())) {
            if (H5RemotePowerEnum.POWER_ON.getValue().equals(remotePowerOnOffVO.getOperateType())) {
                neolixManager.remotePowerOnOff(remotePowerOnOffVO.getVehicleName(), 1);
            } else if (H5RemotePowerEnum.POWER_OFF.getValue().equals(remotePowerOnOffVO.getOperateType())) {
                neolixManager.remotePowerOnOff(remotePowerOnOffVO.getVehicleName(), 2);
            }
        }
        return MonitorErrorEnum.OK;
    }

    /**
     * 获取车辆路由信息。
     * @param vehicleRoutingVO 车辆路由信息请求对象，包含车辆名称等信息。
     * @return 车辆路由信息DTO对象，包括规划路点、停靠路点和实际路点。
     */
    public VehicleRoutingDTO getVehicleRouting(VehicleRoutingVO vehicleRoutingVO) {
        ParameterCheckUtility.checkNotNullNorEmpty(vehicleRoutingVO.getVehicleName(), "vehicleRoutingVO#vehicleName");
        VehicleRoutingDTO result = new VehicleRoutingDTO();

        List<String> vehicleNameList = new ArrayList<>();
        vehicleNameList.add(vehicleRoutingVO.getVehicleName());

        Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(vehicleNameList);
        VehicleRealtimeInfoDTO realtimeInfoDto = vehicleRealtimeRepository.get(vehicleRoutingVO.getVehicleName());
        if (realtimeInfoDto == null || StringUtils.equals(realtimeInfoDto.getSystemState(), SystemStateEnum.OFFLINE.getSystemState())) {
            return result;
        }
        MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(vehicleRoutingVO.getVehicleName());
        if (scheduleDto == null || StringUtils.equals(scheduleDto.getScheduleState(), VehicleScheduleState.WAITING.getVehicleScheduleState())) {
            return result;
        }
        List pncRoutingPoint = pncRouteRepository.getByKey(vehicleRoutingVO.getVehicleName());
        result.setPlanningRoutingPoint(pncRoutingPoint);
        result.setStop(buildScheduleStopRouting(scheduleDtoMap.get(vehicleRoutingVO.getVehicleName()), vehicleRoutingVO.getVehicleName()));
        result.setFinishedRoutingPoint(realRouteRepository.getByKey(vehicleRoutingVO.getVehicleName()));
        return result;

    }


    /**
     * 根据监控调度信息构建停靠点路由信息
     * @param scheduleDto 监控调度信息
     * @return 停止路由信息列表
     */
    private List<TransportStopMapInfoDTO> buildScheduleStopRouting(MonitorScheduleDTO scheduleDto, String vehicleName) {
        if (Objects.isNull(scheduleDto) || StringUtils.equals(scheduleDto.getScheduleState(), VehicleScheduleState.WAITING.getVehicleScheduleState())) {
            return new ArrayList<>();
        }
        //获取是否有单点调度
        boolean virtualScheduleResult = vehicleScheduleManager.queryVehicleVirtualSchedule(vehicleName);

        return scheduleDto.getStop().stream().map(stop -> {
            TransportStopMapInfoDTO stopMapInfoDTO = new TransportStopMapInfoDTO();
            stopMapInfoDTO.setId(stop.getId());
            stopMapInfoDTO.setGoalId(stop.getGoalId());
            stopMapInfoDTO.setStopAction(stop.getStopAction());
            StopBasicDTO stopBasicDTO = metadataStopApiManager.getStopInfoById(stop.getId());
            stopMapInfoDTO.setType(stopBasicDTO != null ? stopBasicDTO.getStopType() : null);
            //获取停靠点名称
            if (BooleanUtil.isTrue(virtualScheduleResult)) {
                stopMapInfoDTO.setName(stop.getStopName());
            } else {
                //获取终端接驳点名称
                if (stop.getGoalId() != null && stop.getGoalId() != 0) {
                    String transportPointName = vehicleScheduleManager.getTransportPointNameByScheduleGoalId(stop.getGoalId());
                    stopMapInfoDTO.setName(transportPointName);
                } else {
                    stopMapInfoDTO.setName(stop.getStopName());
                }
            }

            stopMapInfoDTO.setTravelStatus(stop.getTravelStatus());
            if (stop.getLat() == null || stop.getLon() == null) {
                return stopMapInfoDTO;
            }
            MapPointBO stopMapPoint = TransformUtility.toGCJ02Point(stop.getLat(), stop.getLon());
            if (stopMapPoint != null) {
                stopMapInfoDTO.setLon(stopMapPoint.getLongitude());
                stopMapInfoDTO.setLat(stopMapPoint.getLatitude());
            }
            return stopMapInfoDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取t通用接驳指定车辆的路径规划信息。
     * @param vehicleRoutingVO 车辆路径规划请求对象，包含车辆名称等信息。
     * @return 车辆路径规划结果对象，包括规划路线、停靠点和已完成路线。
     */
    public VehicleRoutingDTO generalTransportGetVehicleRouting(VehicleRoutingVO vehicleRoutingVO) {
        ParameterCheckUtility.checkNotNullNorEmpty(vehicleRoutingVO.getVehicleName(), "vehicleRoutingVO#vehicleName");
        VehicleRoutingDTO result = new VehicleRoutingDTO();

        List<String> vehicleNameList = new ArrayList<>();
        vehicleNameList.add(vehicleRoutingVO.getVehicleName());

        Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(vehicleNameList);
        VehicleRealtimeInfoDTO realtimeInfoDto = vehicleRealtimeRepository.get(vehicleRoutingVO.getVehicleName());
        if (realtimeInfoDto == null || StringUtils.equals(realtimeInfoDto.getSystemState(), SystemStateEnum.OFFLINE.getSystemState())) {
            return result;
        }
        MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(vehicleRoutingVO.getVehicleName());
        if (scheduleDto == null || StringUtils.equals(scheduleDto.getScheduleState(), VehicleScheduleState.WAITING.getVehicleScheduleState())) {
            return result;
        }
        List pncRoutingPoint = pncRouteRepository.getByKey(vehicleRoutingVO.getVehicleName());
        result.setPlanningRoutingPoint(pncRoutingPoint);
        result.setStop(buildGeneralTransportScheduleStopRouting(scheduleDtoMap.get(vehicleRoutingVO.getVehicleName())));
        result.setFinishedRoutingPoint(realRouteRepository.getByKey(vehicleRoutingVO.getVehicleName()));
        return result;
    }

    public List<TransportStopMapInfoDTO> buildGeneralTransportScheduleStopRouting(MonitorScheduleDTO scheduleDto) {
        if (Objects.isNull(scheduleDto) || StringUtils.equals(scheduleDto.getScheduleState(), VehicleScheduleState.WAITING.getVehicleScheduleState())) {
            return new ArrayList<>();
        }
        return scheduleDto.getStop().stream().map(stop -> {
            TransportStopMapInfoDTO stopMapInfoDTO = new TransportStopMapInfoDTO();
            stopMapInfoDTO.setId(stop.getId());
            stopMapInfoDTO.setGoalId(stop.getGoalId());
            stopMapInfoDTO.setName(stop.getStopName());
            stopMapInfoDTO.setStopAction(stop.getStopAction());
            StopBasicDTO stopBasicDTO = metadataStopApiManager.getStopInfoById(stop.getId());
            stopMapInfoDTO.setType(stopBasicDTO != null ? stopBasicDTO.getStopType() : null);
            stopMapInfoDTO.setTravelStatus(stop.getTravelStatus());
            if (stop.getLat() == null || stop.getLon() == null) {
                return stopMapInfoDTO;
            }
            MapPointBO stopMapPoint = TransformUtility.toGCJ02Point(stop.getLat(), stop.getLon());
            if (stopMapPoint != null) {
                stopMapInfoDTO.setLon(stopMapPoint.getLongitude());
                stopMapInfoDTO.setLat(stopMapPoint.getLatitude());
            }
            return stopMapInfoDTO;
        }).collect(Collectors.toList());
    }
}