/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.device.jsfapi.domain.vo.devicecommand.ImmediateTaskCreateVO;
import com.jdx.rover.device.jsfapi.service.server.devicecommand.IntelligentDeviceServerDeviceCommandTaskInfoService;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.dto.robot.RobotAsArrivedDTO;
import com.jdx.rover.monitor.dto.robot.RobotChassisCommandDTO;
import com.jdx.rover.monitor.dto.robot.RobotRebootCommandDTO;
import com.jdx.rover.monitor.dto.robot.RobotRemoteCommandDTO;
import com.jdx.rover.monitor.dto.robot.RobotRestartCommandDTO;
import com.jdx.rover.monitor.dto.robot.RobotTakeOverCommandDTO;
import com.jdx.rover.monitor.dto.robot.RobotTakeOverCommandDTO.RobotTakeOverCommand;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.VehicleRemoteOperationStatusEnum;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.device.RobotScheduleDO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.device.DeviceCommandTaskEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotScheduleRepository;
import com.jdx.rover.monitor.vo.MonitorRemoteControlCommandVO;
import com.jdx.rover.monitor.vo.robot.RobotCommandBaseVO;
import com.jdx.rover.monitor.vo.robot.RobotRestartHmiVO;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import com.jdx.rover.transport.base.BaseRequestHeader;
import com.jdx.rover.transport.integrate.ChassisCommand;
import com.jdx.rover.transport.integrate.ChassisCommand.RobotControlCommand;
import com.jdx.rover.transport.integrate.ModuleDelayInfo;
import com.jdx.rover.transport.integrate.MultiRobotHeartbeat;
import com.jdx.rover.transport.integrate.TransportIntegrateDTO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 机器人指令服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotCommandService {

    /**
     * 调度缓存
     */
    private final RobotScheduleRepository robotScheduleRepository;

    /**
     * 接管缓存
     */
    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    /**
     * 指令服务
     */
    private final IntelligentDeviceServerDeviceCommandTaskInfoService commandTaskInfoService;

    /**
     * MQTT消息管理器，用于发送和接收MQTT消息。
     */
    private final MqttSendJsfService mqttSendJsfService;

    /**
     * 服务端
     */
    private static final String SERVER_MODULE = "rover-server";

    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * 发送到达命令
     * @param robotCommandBaseVo 机器人命令基础信息
     * @param deviceCommandTaskEnum 设备命令任务枚举
     * @return 发送结果
     */
    public HttpResult<String> sendAsArrivedCommand(RobotCommandBaseVO robotCommandBaseVo, DeviceCommandTaskEnum deviceCommandTaskEnum) {
        if (Objects.isNull(deviceCommandTaskEnum)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode());
        }
        RobotScheduleDO robotScheduleDo = robotScheduleRepository.get(robotCommandBaseVo.getDeviceName());
        ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(robotCommandBaseVo.getDeviceName());
        createVo.setProductKey(robotCommandBaseVo.getProductKey());
        createVo.setBlockNo("integrate");
        createVo.setIdentifier(deviceCommandTaskEnum.getValue());
        RobotAsArrivedDTO asArrivedDto = new RobotAsArrivedDTO();
        if (Objects.nonNull(robotScheduleDo)) {
            asArrivedDto.setTaskId(robotScheduleDo.getTaskId());
        }
        createVo.setCommandArgs(JsonUtils.writeValueAsString(asArrivedDto));
        return commandTaskInfoService.createImmediateTask(createVo);
    }

    /**
     * 发送接管指令
     * @param robotCommandBaseVo 机器人命令基础信息
     * @param deviceCommandTaskEnum 设备命令任务枚举
     * @return 发送结果
     */
    public HttpResult<String> emergencyStop(RobotCommandBaseVO robotCommandBaseVo, DeviceCommandTaskEnum deviceCommandTaskEnum) {
        String userName = UserUtils.getAndCheckLoginUser();
        if (Objects.isNull(deviceCommandTaskEnum)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode());
        }
        ResultInfo resultInfo = isAbleToPost(robotCommandBaseVo.getDeviceName(), userName);
        if (!resultInfo.ableToPost) {
            return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), resultInfo.getRecordName());
        }
        ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(robotCommandBaseVo.getDeviceName());
        createVo.setProductKey(robotCommandBaseVo.getProductKey());
        createVo.setBlockNo(robotCommandBaseVo.getProductKey());
        createVo.setIdentifier(deviceCommandTaskEnum.getValue());
        RobotRemoteCommandDTO remoteCommandDto = new RobotRemoteCommandDTO();
        RobotTakeOverCommandDTO remoteCmd = new RobotTakeOverCommandDTO();
        remoteCmd.setSequenceNum(1);
        RobotChassisCommandDTO.ModuleDelayInfo moduleDelayInfo = new RobotChassisCommandDTO.ModuleDelayInfo();
        moduleDelayInfo.setModuleName(SERVER_MODULE);
        moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
        moduleDelayInfo.setTransitTime(System.currentTimeMillis());
        remoteCmd.setModuleDelayInfo(Lists.newArrayList(moduleDelayInfo));
        RobotTakeOverCommandDTO.RobotTakeOverCommand robotTakeOverCommand = new RobotTakeOverCommand();
        robotTakeOverCommand.setEnableRemoteControl(Boolean.TRUE);
        remoteCmd.setTakeover(robotTakeOverCommand);
        remoteCommandDto.setRemoteCmd(remoteCmd);
        createVo.setCommandArgs(JsonUtils.writeValueAsString(remoteCommandDto));
        HttpResult<String> httpResult = commandTaskInfoService.createImmediateTask(createVo);
        if (!HttpResult.isSuccess(httpResult)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_SEND_COMMAND.getCode());
        }
        VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
        vehicleTakeOverEntity.setOperationStatus(VehicleRemoteOperationStatusEnum.TAKEOVER.getOperationStatus());
        vehicleTakeOverEntity.setUserName(userName);
        vehicleTakeOverEntity.setOperateTime(new Date());
        vehicleTakeOverRepository.save(robotCommandBaseVo.getDeviceName(), vehicleTakeOverEntity);
        return HttpResult.success();
    }

    /**
     * 执行恢复操作。
     * @param robotCommandBaseVo 机器人命令基本信息。
     * @param deviceCommandTaskEnum 设备命令任务枚举。
     * @return 操作结果。
     */
    public HttpResult<String> recovery(RobotCommandBaseVO robotCommandBaseVo, DeviceCommandTaskEnum deviceCommandTaskEnum) {
        String userName = UserUtils.getAndCheckLoginUser();
        if (Objects.isNull(deviceCommandTaskEnum)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode());
        }
        ResultInfo resultInfo = isAbleToPost(robotCommandBaseVo.getDeviceName(), userName);
        if (!resultInfo.ableToPost) {
            return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), resultInfo.getRecordName());
        }
        ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(robotCommandBaseVo.getDeviceName());
        createVo.setProductKey(robotCommandBaseVo.getProductKey());
        createVo.setBlockNo(robotCommandBaseVo.getProductKey());
        createVo.setIdentifier(deviceCommandTaskEnum.getValue());
        RobotRemoteCommandDTO remoteCommandDto = new RobotRemoteCommandDTO();
        RobotTakeOverCommandDTO remoteCmd = new RobotTakeOverCommandDTO();
        remoteCmd.setSequenceNum(1);
        RobotChassisCommandDTO.ModuleDelayInfo moduleDelayInfo = new RobotChassisCommandDTO.ModuleDelayInfo();
        moduleDelayInfo.setModuleName(SERVER_MODULE);
        moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
        moduleDelayInfo.setTransitTime(System.currentTimeMillis());
        remoteCmd.setModuleDelayInfo(Lists.newArrayList(moduleDelayInfo));
        RobotTakeOverCommandDTO.RobotTakeOverCommand robotTakeOverCommand = new RobotTakeOverCommand();
        robotTakeOverCommand.setEnableRemoteControl(Boolean.FALSE);
        remoteCmd.setTakeover(robotTakeOverCommand);
        remoteCommandDto.setRemoteCmd(remoteCmd);
        createVo.setCommandArgs(JsonUtils.writeValueAsString(remoteCommandDto));
        HttpResult<String> httpResult = commandTaskInfoService.createImmediateTask(createVo);
        if (!HttpResult.isSuccess(httpResult)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_SEND_COMMAND.getCode());
        }
        vehicleTakeOverRepository.remove(robotCommandBaseVo.getDeviceName());
        return HttpResult.success();
    }

    /**
     * 重启机器人
     * @param robotCommandBaseVo 机器人命令基础信息
     * @return 重启结果
     */
    public HttpResult<String> restart(RobotCommandBaseVO robotCommandBaseVo, DeviceCommandTaskEnum deviceCommandTaskEnum) {
        String userName = UserUtils.getAndCheckLoginUser();
        if (Objects.isNull(robotCommandBaseVo)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode());
        }
        ResultInfo resultInfo = isAbleToPost(robotCommandBaseVo.getDeviceName(), userName);
        if (!resultInfo.ableToPost) {
            return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), resultInfo.getRecordName());
        }
        ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(robotCommandBaseVo.getDeviceName());
        createVo.setProductKey(robotCommandBaseVo.getProductKey());
        createVo.setBlockNo(robotCommandBaseVo.getProductKey());
        createVo.setIdentifier(deviceCommandTaskEnum.getValue());
        RobotRemoteCommandDTO remoteCommandDto = new RobotRemoteCommandDTO();
        RobotRestartCommandDTO restartCmd = new RobotRestartCommandDTO();
        restartCmd.setSequenceNum(1);
        RobotChassisCommandDTO.ModuleDelayInfo moduleDelayInfo = new RobotChassisCommandDTO.ModuleDelayInfo();
        moduleDelayInfo.setModuleName(SERVER_MODULE);
        moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
        moduleDelayInfo.setTransitTime(System.currentTimeMillis());
        restartCmd.setModuleDelayInfo(Lists.newArrayList(moduleDelayInfo));
        restartCmd.setServiceReboot(Boolean.TRUE);
        remoteCommandDto.setRemoteCmd(restartCmd);
        createVo.setCommandArgs(JsonUtils.writeValueAsString(remoteCommandDto));
        HttpResult<String> httpResult = commandTaskInfoService.createImmediateTask(createVo);
        if (!HttpResult.isSuccess(httpResult)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_SEND_COMMAND.getCode(), MonitorErrorEnum.ERROR_SEND_COMMAND.getMessage());
        }
        // 重启安卓
        createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(robotCommandBaseVo.getDeviceName());
        createVo.setProductKey(robotCommandBaseVo.getProductKey());
        createVo.setBlockNo("hmi");
        createVo.setIdentifier(DeviceCommandTaskEnum.CMD_REMOTE_HMI_BOOT.getValue());
        RobotRestartHmiVO remoteCommand = new RobotRestartHmiVO();
        remoteCommand.setBootId(String.valueOf(System.currentTimeMillis()));
        createVo.setCommandArgs(JsonUtils.writeValueAsString(remoteCommand));
        commandTaskInfoService.createImmediateTask(createVo);
        vehicleTakeOverRepository.remove(robotCommandBaseVo.getDeviceName());
        return HttpResult.success();
    }

    /**
     * 断电重启。
     * @param robotCommandBaseVo 机器人命令基本信息。
     * @param deviceCommandTaskEnum 设备命令任务枚举。
     * @return 重启结果。
     */
    public HttpResult<String> reboot(RobotCommandBaseVO robotCommandBaseVo, DeviceCommandTaskEnum deviceCommandTaskEnum) {
        String userName = UserUtils.getAndCheckLoginUser();
        if (Objects.isNull(robotCommandBaseVo)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode());
        }
        ResultInfo resultInfo = isAbleToPost(robotCommandBaseVo.getDeviceName(), userName);
        if (!resultInfo.ableToPost) {
            return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), resultInfo.getRecordName());
        }
        ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(robotCommandBaseVo.getDeviceName());
        createVo.setProductKey(robotCommandBaseVo.getProductKey());
        createVo.setBlockNo(robotCommandBaseVo.getProductKey());
        createVo.setIdentifier(deviceCommandTaskEnum.getValue());
        RobotRemoteCommandDTO remoteCommandDto = new RobotRemoteCommandDTO();
        RobotRebootCommandDTO remoteCmd = new RobotRebootCommandDTO();
        remoteCmd.setSequenceNum(1);
        RobotChassisCommandDTO.ModuleDelayInfo moduleDelayInfo = new RobotChassisCommandDTO.ModuleDelayInfo();
        moduleDelayInfo.setModuleName(SERVER_MODULE);
        moduleDelayInfo.setReceiveTime(System.currentTimeMillis());
        moduleDelayInfo.setTransitTime(System.currentTimeMillis());
        remoteCmd.setModuleDelayInfo(Lists.newArrayList(moduleDelayInfo));
        remoteCmd.setSysReboot(Boolean.TRUE);
        remoteCommandDto.setRemoteCmd(remoteCmd);
        createVo.setCommandArgs(JsonUtils.writeValueAsString(remoteCommandDto));
        HttpResult<String> httpResult = commandTaskInfoService.createImmediateTask(createVo);
        if (!HttpResult.isSuccess(httpResult)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_SEND_COMMAND.getCode(), MonitorErrorEnum.ERROR_SEND_COMMAND.getMessage());
        }
        createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(robotCommandBaseVo.getDeviceName());
        createVo.setProductKey(robotCommandBaseVo.getProductKey());
        createVo.setBlockNo("hmi");
        createVo.setIdentifier(DeviceCommandTaskEnum.CMD_REMOTE_HMI_BOOT.getValue());
        RobotRestartHmiVO remoteCommand = new RobotRestartHmiVO();
        remoteCommand.setBootId(String.valueOf(System.currentTimeMillis()));
        createVo.setCommandArgs(JsonUtils.writeValueAsString(remoteCommand));
        commandTaskInfoService.createImmediateTask(createVo);
        vehicleTakeOverRepository.remove(robotCommandBaseVo.getDeviceName());
        return HttpResult.success();
    }

    /**
     * 执行遥控操作。
     * @param remoteControlCommandVO 机器人命令基本信息。
     * @param deviceCommandTaskEnum 设备命令任务枚举。
     * @return 操作结果。
     */
    public WsResult<String> remoteControl(MonitorRemoteControlCommandVO remoteControlCommandVO, DeviceCommandTaskEnum deviceCommandTaskEnum) {
        if (Objects.isNull(deviceCommandTaskEnum)) {
            return WsResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode());
        }
        TransportIntegrateDTO.Builder transportIntegrate = TransportIntegrateDTO.newBuilder();
        BaseRequestHeader.Builder header = BaseRequestHeader.newBuilder();
        header.setRequestTime(System.currentTimeMillis());
        header.setReceiveName(remoteControlCommandVO.getVehicleName());
        header.setMessageType(deviceCommandTaskEnum.getValue());
        header.setRequestId(System.currentTimeMillis());

        transportIntegrate.setRequestHeader(header);
        ChassisCommand.Builder chassisCommand = ChassisCommand.newBuilder();
        chassisCommand.setSequenceNum(remoteControlCommandVO.getId());
        RobotControlCommand.Builder controlCommand = RobotControlCommand.newBuilder();
        controlCommand.setTargetAngle(remoteControlCommandVO.getTargetAngle());
        controlCommand.setTargetVelocity(remoteControlCommandVO.getTargetVelocity());
        chassisCommand.setVehicleControl(controlCommand);
        // 构造页面端延迟数据
        ModuleDelayInfo.Builder moduleDelayInfo = ModuleDelayInfo.newBuilder();
        moduleDelayInfo.setModuleName(remoteControlCommandVO.getModuleName());
        Date controlTime = Optional.ofNullable(remoteControlCommandVO.getTimeStamp()).orElse(new Date());
        moduleDelayInfo.setReceiveTime(controlTime.getTime());
        moduleDelayInfo.setTransitTime(controlTime.getTime());
        chassisCommand.addModuleDelayInfo(moduleDelayInfo);
        // 构造服务端延迟数据
        ModuleDelayInfo.Builder serverModuleDelayInfo = ModuleDelayInfo.newBuilder();
        serverModuleDelayInfo.setModuleName(SERVER_MODULE);
        serverModuleDelayInfo.setReceiveTime(System.currentTimeMillis());
        serverModuleDelayInfo.setTransitTime(controlTime.getTime());
        chassisCommand.addModuleDelayInfo(serverModuleDelayInfo);
        transportIntegrate.setRemoteCmd(chassisCommand);
        //t/{环境标识}/integrate/{设备名称}/integrate/pb/services
        String topic = String.format(MqttTopicEnum.ROBOT_CONTROL_COMMAND.getTopic(), profileActive, remoteControlCommandVO.getProductKey(),
                remoteControlCommandVO.getVehicleName(), remoteControlCommandVO.getProductKey());
        MqttMessageVO<byte[]> mqttMessageVo = new MqttMessageVO<>();
        mqttMessageVo.setTopic(topic);
        mqttMessageVo.setQos(0);
        mqttMessageVo.setRetained(Boolean.FALSE);
        mqttMessageVo.setMessage(transportIntegrate.build().toByteArray());
        mqttSendJsfService.sendBytes(mqttMessageVo);
        return WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_CONTROL.getValue(),new MonitorRemoteCommandDTO());
    }

    /**
     * <p>
     * 校验参数是否符合接管。
     * </p>
     *
     * @param vehicleName the vehicle name
     * @param userName    the username
     * @return ResultInfo
     */
    private ResultInfo isAbleToPost(String vehicleName, String userName) {
        ResultInfo resultInfo = new ResultInfo();
        com.google.common.base.Optional<VehicleTakeOverEntity> op = vehicleTakeOverRepository.getByKey(vehicleName);
        if (op.isPresent() && userName.equals(op.get().getUserName())){
            resultInfo.setAbleToPost(true);
            resultInfo.setHasRecord(true);
        } else if (!op.isPresent()) {
            resultInfo.setAbleToPost(true);
            resultInfo.setHasRecord(false);
        } else {
            resultInfo.setAbleToPost(false);
            resultInfo.setHasRecord(true);
            resultInfo.setRecordName(op.get().getUserName());
        }
        return resultInfo;
    }

    /**
     * <p>
     * 接管响应判断
     * </p>
     */
    @Data
    private class ResultInfo {

        /**
         * <p>
         * 是否支持接管
         * </p>
         */
        private boolean ableToPost;

        /**
         * <p>
         * 是否存在接管状态
         * </p>
         */
        private boolean hasRecord;

        /**
         * <p>
         * 接管用户
         * </p>
         */
        private String recordName;

    }
}
