/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.report;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.repository.redis.ReportBootRepository;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 车辆异常service类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ReportBootService {

    private final ReportBootRepository reportBootRepository;

    /**
     * 处理kafka消息
     *
     * @param messageList
     */
    public void handleMessage(List<String> messageList) {
        List<VehicleBootDTO> poList = new ArrayList<>();
        for (String message : messageList) {
            VehicleBootDTO dto = JsonUtils.readValue(message, VehicleBootDTO.class);
            if (Objects.isNull(dto)) {
                log.error("读取异常信息为空!{}", message);
                continue;
            }
            poList.add(dto);
        }
        reportBootRepository.setList(poList);
    }

    /**
     * 查询启动信息
     *
     * @param vehicleName
     */
    public VehicleBootDTO get(String vehicleName) {
        VehicleBootDTO vehicleBootDTO = reportBootRepository.get(vehicleName);
        return vehicleBootDTO;
    }
}