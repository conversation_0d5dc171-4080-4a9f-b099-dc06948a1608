package com.jdx.rover.monitor.service.data;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.metadata.api.domain.enums.CockpitTypeEnum;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitPageDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamPageDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamUserPageDTO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitPageVO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamPageVO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamUserPageVO;
import com.jdx.rover.monitor.api.domain.enums.CockpitModeEnum;
import com.jdx.rover.monitor.api.domain.enums.CockpitStatusEnum;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.bo.cockpit.GetTeamDurationBO;
import com.jdx.rover.monitor.dto.data.GetCockpitDataPageDTO;
import com.jdx.rover.monitor.dto.data.GetCockpitTeamDataListDTO;
import com.jdx.rover.monitor.dto.data.GetSupportDataListDTO;
import com.jdx.rover.monitor.manager.abnormal.ManualAlarmRecordManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitDataManager;
import com.jdx.rover.monitor.manager.cockpit.WorkRecordManager;
import com.jdx.rover.monitor.manager.ticket.IssueDataApiManager;
import com.jdx.rover.monitor.po.ManualAlarmRecord;
import com.jdx.rover.monitor.po.WorkRecord;
import com.jdx.rover.monitor.vo.data.GetCockpitDataPageVO;
import com.jdx.rover.monitor.vo.data.GetCockpitTeamDataListVO;
import com.jdx.rover.monitor.vo.data.GetSupportDataListVO;
import com.jdx.rover.ticket.domain.dto.data.GetCockpitIssueInfoDTO;
import com.jdx.rover.ticket.domain.dto.data.GetTeamIssueDataDTO;
import com.jdx.rover.ticket.domain.dto.data.GetUserIssueInfoDTO;
import com.jdx.rover.ticket.domain.vo.data.GetCockpitIssueInfoVO;
import com.jdx.rover.ticket.domain.vo.data.GetTeamIssueDataVO;
import com.jdx.rover.ticket.domain.vo.data.GetUserIssueInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 驾舱数据
 */
@Service
@Slf4j
public class DataService {

    @Resource
    private WorkRecordManager workRecordManager;

    @Resource
    private IssueDataApiManager issueDataApiManager;

    @Resource
    private CockpitDataManager cockpitDataManager;

    @Resource
    private ManualAlarmRecordManager manualAlarmRecordManager;

    /**
     * 获取坐席数据列表
     *
     * @param getCockpitDataPageVO getCockpitDataPageVO
     * @return PageDTO<GetCockpitDataPageDTO>
     */
    public PageDTO<GetCockpitDataPageDTO> getCockpitDataPage(GetCockpitDataPageVO getCockpitDataPageVO) {
        PageDTO<GetCockpitDataPageDTO> result = new PageDTO<>();
        result.setList(new ArrayList<>());
        //分页获取坐席信息
        CockpitPageVO cockpitPageVO = new CockpitPageVO();
        cockpitPageVO.setPageNum(getCockpitDataPageVO.getPageNum());
        cockpitPageVO.setPageSize(getCockpitDataPageVO.getPageSize());
        cockpitPageVO.setCockpitNumber(getCockpitDataPageVO.getCockpitNumber());
        cockpitPageVO.setCockpitTypeList(getCockpitDataPageVO.getCockpitTypeList());
        cockpitPageVO.setCockpitTeamNumberList(getCockpitDataPageVO.getCockpitTeamNumberList());
        PageDTO<CockpitPageDTO> cockpitPage = cockpitDataManager.getCockpitPage(cockpitPageVO);
        result.setPageNum(cockpitPage.getPageNum());
        result.setPageSize(cockpitPage.getPageSize());
        result.setPages(cockpitPage.getPages());
        result.setTotal(cockpitPage.getTotal());
        List<String> cockpitNumberList = cockpitPage.getList().stream().map(CockpitPageDTO::getCockpitNumber).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cockpitNumberList)) {
            return result;
        }
        //获取坐席工作时间数据
        List<WorkRecord> workRecordList = workRecordManager.lambdaQuery().in(WorkRecord::getCockpitNumber, cockpitNumberList).between(getCockpitDataPageVO.getStartTime() != null && getCockpitDataPageVO.getEndTime() != null, WorkRecord::getStartTime, getCockpitDataPageVO.getStartTime(), getCockpitDataPageVO.getEndTime()).list();
        GetCockpitIssueInfoVO getCockpitIssueInfoVO = new GetCockpitIssueInfoVO();
        getCockpitIssueInfoVO.setCockpitNumberList(cockpitNumberList);
        getCockpitIssueInfoVO.setStartTime(getCockpitDataPageVO.getStartTime());
        getCockpitIssueInfoVO.setEndTime(getCockpitDataPageVO.getEndTime());
        //获取坐席工单信息
        List<GetCockpitIssueInfoDTO> cockpitIssueInfo = issueDataApiManager.getCockpitIssueInfo(getCockpitIssueInfoVO);
        Map<String, GetCockpitIssueInfoDTO> cockpitIssueInfoDTOMap = cockpitIssueInfo.stream().collect(Collectors.toMap(GetCockpitIssueInfoDTO::getCockpitNumber, Function.identity()));
        //获取坐席告警次数
        List<String> sourceList = Arrays.asList(ManualAlarmSourceEnum.PATROL_REMOTE_CONTROL.getSource(), ManualAlarmSourceEnum.PATROL_MONITOR.getSource());
        List<ManualAlarmRecord> manualAlarmRecordList = manualAlarmRecordManager.lambdaQuery().in(ManualAlarmRecord::getCockpitNumber, cockpitNumberList).between(getCockpitDataPageVO.getStartTime() != null && getCockpitDataPageVO.getEndTime() != null, ManualAlarmRecord::getReportTime, getCockpitDataPageVO.getStartTime(), getCockpitDataPageVO.getEndTime()).in(ManualAlarmRecord::getSource, sourceList).list();
        List<GetCockpitDataPageDTO> getCockpitDataPageDTOList = new ArrayList<>();
        for (CockpitPageDTO cockpitPageDTO : cockpitPage.getList()) {
            GetCockpitDataPageDTO cockpitDataPageDTO = new GetCockpitDataPageDTO();
            cockpitDataPageDTO.setCockpitNumber(cockpitPageDTO.getCockpitNumber());
            cockpitDataPageDTO.setCockpitType(cockpitPageDTO.getCockpitType());
            cockpitDataPageDTO.setCockpitTypeName(CockpitTypeEnum.getNameByValue(cockpitDataPageDTO.getCockpitType()));
            cockpitDataPageDTO.setCockpitTeamName(cockpitPageDTO.getCockpitTeamName());
            //计算时长
            List<WorkRecord> cockitWorkRecordList = workRecordList.stream().filter(workRecord -> Objects.equals(workRecord.getCockpitNumber(), cockpitPageDTO.getCockpitNumber())).toList();
            Integer workDuration = cockitWorkRecordList.stream().filter(workRecord -> Objects.equals(workRecord.getCockpitStatus(), CockpitStatusEnum.WORK.getValue())).mapToInt(workRecord -> calcCost(workRecord.getEndTime() != null ? workRecord.getEndTime() : new Date(), workRecord.getStartTime())).sum();
            Integer restDuration = cockitWorkRecordList.stream().filter(workRecord -> Objects.equals(workRecord.getCockpitStatus(), CockpitStatusEnum.REST.getValue())).mapToInt(workRecord -> calcCost(workRecord.getEndTime() != null ? workRecord.getEndTime() : new Date(), workRecord.getStartTime())).sum();
            Integer listeningModeDuration = cockitWorkRecordList.stream().filter(workRecord -> Objects.equals(workRecord.getCockpitMode(), CockpitModeEnum.LISTENING_MODE.getValue())).mapToInt(workRecord -> calcCost(workRecord.getEndTime() != null ? workRecord.getEndTime() : new Date(), workRecord.getStartTime())).sum();
            cockpitDataPageDTO.setOnlineDuration(workDuration);
            cockpitDataPageDTO.setRestDuration(restDuration);
            cockpitDataPageDTO.setListeningModeCost(listeningModeDuration);
            //计算工单次数
            GetCockpitIssueInfoDTO cockpitIssueInfoDTO = cockpitIssueInfoDTOMap.get(cockpitPageDTO.getCockpitNumber());
            cockpitDataPageDTO.setAcceptCount(cockpitIssueInfoDTO.getAcceptCount());
            cockpitDataPageDTO.setFinishCount(cockpitIssueInfoDTO.getFinishCount());
            cockpitDataPageDTO.setTransferCount(cockpitIssueInfoDTO.getTransferCount());
            cockpitDataPageDTO.setAcceptTransferCount(cockpitIssueInfoDTO.getAcceptTransferCount());
            cockpitDataPageDTO.setDiscardCount(cockpitIssueInfoDTO.getDiscardCount());
            // 巡查提报工单次数
            long patrolReportCount = manualAlarmRecordList.stream().filter(manualAlarmRecord -> Objects.equals(manualAlarmRecord.getCockpitNumber(), cockpitPageDTO.getCockpitNumber())).count();
            cockpitDataPageDTO.setPatrolReportCount((int) patrolReportCount);
            getCockpitDataPageDTOList.add(cockpitDataPageDTO);
        }
        result.setList(getCockpitDataPageDTOList);
        return result;
    }

    /**
     * 计算耗时（秒）
     *
     * @param startTime startTime
     * @param endTime   endTime
     * @return Double
     */
    private Integer calcCost(Date endTime, Date startTime) {
        if (startTime != null && endTime != null) {
            return calcCost(endTime.getTime(), startTime.getTime());
        }
        return null;
    }

    /**
     * 计算耗时（秒）
     *
     * @param startTime startTime
     * @param endTime   endTime
     * @return Double
     */
    private Integer calcCost(long endTime, long startTime) {
        return (int) (endTime - startTime) / 1000;
    }

    /**
     * 获取远驾团队数据列表
     *
     * @param getCockpitTeamDataListVO getCockpitTeamDataListVO
     * @return PageDTO<GetCockpitTeamDataListDTO>
     */
    public PageDTO<GetCockpitTeamDataListDTO> getCockpitTeamDataList(GetCockpitTeamDataListVO getCockpitTeamDataListVO) {
        PageDTO<GetCockpitTeamDataListDTO> pageDTO = new PageDTO<>();
        // 分页获取团队
        CockpitTeamPageVO pageVO = new CockpitTeamPageVO();
        pageVO.setPageNum(getCockpitTeamDataListVO.getPageNum());
        pageVO.setPageSize(getCockpitTeamDataListVO.getPageSize());
        pageVO.setCockpitTeamNumberList(getCockpitTeamDataListVO.getCockpitTeamNumberList());
        PageDTO<CockpitTeamPageDTO> teamPage = cockpitDataManager.getCockpitTeamPage(pageVO);
        pageDTO.setPageNum(teamPage.getPageNum());
        pageDTO.setPageSize(teamPage.getPageSize());
        pageDTO.setPages(teamPage.getPages());
        pageDTO.setTotal(teamPage.getTotal());
        List<CockpitTeamPageDTO> teamList = teamPage.getList();
        if (CollectionUtils.isEmpty(teamList)) {
            pageDTO.setList(new ArrayList<>());
            return pageDTO;
        }
        List<String> teamNumberList = teamList.stream().map(CockpitTeamPageDTO::getCockpitTeamNumber).collect(Collectors.toList());
        // 获取团队工单数据
        GetTeamIssueDataVO getTeamIssueDataVO = new GetTeamIssueDataVO();
        getTeamIssueDataVO.setStartTime(getCockpitTeamDataListVO.getStartTime());
        getTeamIssueDataVO.setEndTime(getCockpitTeamDataListVO.getEndTime());
        getTeamIssueDataVO.setCockpitTeamNumberList(teamNumberList);
        List<GetTeamIssueDataDTO> teamIssueDataList = issueDataApiManager.getTeamIssueData(getTeamIssueDataVO);
        Map<String, GetTeamIssueDataDTO> teamIssueDataMap = teamIssueDataList.stream().collect(Collectors.toMap(GetTeamIssueDataDTO::getCockpitTeamNumber, Function.identity()));
        // 获取团队时长数据
        LambdaQueryWrapper<WorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkRecord::getCockpitTeamNumber, teamNumberList);
        queryWrapper.eq(WorkRecord::getCockpitStatus, CockpitStatusEnum.WORK.getValue());
        queryWrapper.between(getCockpitTeamDataListVO.getStartTime() != null && getCockpitTeamDataListVO.getEndTime() != null, WorkRecord::getStartTime, getCockpitTeamDataListVO.getStartTime(), getCockpitTeamDataListVO.getEndTime());
        queryWrapper.groupBy(WorkRecord::getCockpitTeamNumber);
        List<GetTeamDurationBO> durationList = workRecordManager.getBaseMapper().getDuration(queryWrapper);
        Map<String, Long> durationMap = durationList.stream().collect(Collectors.toMap(GetTeamDurationBO::getCockpitTeamNumber, GetTeamDurationBO::getDuration));
        // 组装
        List<GetCockpitTeamDataListDTO> teamDataList = new ArrayList<>();
        for (CockpitTeamPageDTO teamPageDTO : teamList) {
            GetCockpitTeamDataListDTO dataListDTO = new GetCockpitTeamDataListDTO();
            dataListDTO.setCockpitTeamName(teamPageDTO.getCockpitTeamName());
            dataListDTO.setSupportCount(teamPageDTO.getSupportCount());
            dataListDTO.setMonitorCount(teamPageDTO.getMonitorCount());
            dataListDTO.setRemoteDriveCount(teamPageDTO.getRemoteDriveCount());
            Optional.ofNullable(teamIssueDataMap.get(teamPageDTO.getCockpitTeamNumber())).ifPresent(v -> {
                dataListDTO.setResponseAvgCost(v.getResponseAvgCost());
                dataListDTO.setHandleAvgCost(v.getHandleAvgCost());
                dataListDTO.setAcceptCount(v.getAcceptCount());
                dataListDTO.setMonitorAcceptCount(v.getMonitorAcceptCount());
                dataListDTO.setRemoteDriveAcceptCount(v.getRemoteDriveAcceptCount());
                dataListDTO.setTransferCount(v.getTransferCount());
                dataListDTO.setAcceptTransferCount(v.getAcceptTransferCount());
                dataListDTO.setDiscardCount(v.getDiscardCount());
            });
            Optional.ofNullable(durationMap.get(teamPageDTO.getCockpitTeamNumber())).ifPresent(v -> dataListDTO.setOnlineDuration(v.intValue()));
            teamDataList.add(dataListDTO);
        }
        pageDTO.setList(teamDataList);
        return pageDTO;
    }

    /**
     * 获取技术支持数据列表
     *
     * @param getSupportDataListVO getSupportDataListVO
     * @return PageDTO<GetSupportDataListDTO>
     */
    public PageDTO<GetSupportDataListDTO> getSupportDataList(GetSupportDataListVO getSupportDataListVO) {
        PageDTO<GetSupportDataListDTO> result = new PageDTO<>();
        result.setList(new ArrayList<>());
        //分页获取坐席团队成员信息
        CockpitTeamUserPageVO cockpitTeamUserPageVO = new CockpitTeamUserPageVO();
        cockpitTeamUserPageVO.setPageNum(getSupportDataListVO.getPageNum());
        cockpitTeamUserPageVO.setPageSize(getSupportDataListVO.getPageSize());
        cockpitTeamUserPageVO.setSupportUsername(getSupportDataListVO.getSupportUsername());
        cockpitTeamUserPageVO.setCockpitTeamNumberList(getSupportDataListVO.getCockpitTeamNumberList());
        PageDTO<CockpitTeamUserPageDTO> cockpitTeamUserPage = cockpitDataManager.getCockpitTeamUserPage(cockpitTeamUserPageVO);
        BeanUtils.copyProperties(cockpitTeamUserPage, result);
        if (CollectionUtils.isEmpty(cockpitTeamUserPage.getList())) {
            return result;
        }
        List<String> usernameList = cockpitTeamUserPage.getList().stream().map(CockpitTeamUserPageDTO::getSupportUsername).toList();
        //构造团队成员工作时间数据
        List<String> userNameList = cockpitTeamUserPage.getList().stream().map(CockpitTeamUserPageDTO::getSupportUsername).toList();
        List<WorkRecord> workRecordList = workRecordManager.lambdaQuery().in(WorkRecord::getUserName, userNameList).between(getSupportDataListVO.getStartTime() != null && getSupportDataListVO.getEndTime() != null, WorkRecord::getStartTime, getSupportDataListVO.getStartTime(), getSupportDataListVO.getEndTime()).list();
        //获取处理工单数据
        GetUserIssueInfoVO getUserIssueInfoVO = new GetUserIssueInfoVO();
        getUserIssueInfoVO.setStartTime(getSupportDataListVO.getStartTime());
        getUserIssueInfoVO.setEndTime(getSupportDataListVO.getEndTime());
        getUserIssueInfoVO.setUserNameList(cockpitTeamUserPage.getList().stream().map(CockpitTeamUserPageDTO::getSupportUsername).collect(Collectors.toList()));
        List<GetUserIssueInfoDTO> userIssueInfo = issueDataApiManager.getUserIssueInfo(getUserIssueInfoVO);
        Map<String, GetUserIssueInfoDTO> userIssueInfoDTOMap = userIssueInfo.stream().collect(Collectors.toMap(GetUserIssueInfoDTO::getUserName, Function.identity()));
        //获取人工告警次数
        List<String> sourceList = Arrays.asList(ManualAlarmSourceEnum.PATROL_REMOTE_CONTROL.getSource(), ManualAlarmSourceEnum.PATROL_MONITOR.getSource());
        List<ManualAlarmRecord> manualAlarmRecordList = manualAlarmRecordManager.lambdaQuery().in(ManualAlarmRecord::getReportUser, usernameList).between(getSupportDataListVO.getStartTime() != null && getSupportDataListVO.getEndTime() != null, ManualAlarmRecord::getReportTime, getSupportDataListVO.getStartTime(), getSupportDataListVO.getEndTime()).in(ManualAlarmRecord::getSource, sourceList).list();
        List<GetSupportDataListDTO> getCockpitDataPageDTOList = new ArrayList<>();
        for (CockpitTeamUserPageDTO cockpitTeamUserPageDTO : cockpitTeamUserPage.getList()) {
            GetSupportDataListDTO supportDataListDTO = new GetSupportDataListDTO();
            supportDataListDTO.setSupportUsername(cockpitTeamUserPageDTO.getSupportUsername());
            supportDataListDTO.setCockpitTeamName(cockpitTeamUserPageDTO.getCockpitTeamName());
            //计算时长
            List<WorkRecord> cockitWorkRecordList = workRecordList.stream().filter(workRecord -> Objects.equals(workRecord.getUserName(), cockpitTeamUserPageDTO.getSupportUsername())).toList();
            Integer workDuration = cockitWorkRecordList.stream().filter(workRecord -> Objects.equals(workRecord.getCockpitStatus(), CockpitStatusEnum.WORK.getValue())).mapToInt(workRecord -> calcCost(workRecord.getEndTime() != null ? workRecord.getEndTime() : new Date(), workRecord.getStartTime())).sum();
            Integer restDuration = cockitWorkRecordList.stream().filter(workRecord -> Objects.equals(workRecord.getCockpitStatus(), CockpitStatusEnum.REST.getValue())).mapToInt(workRecord -> calcCost(workRecord.getEndTime() != null ? workRecord.getEndTime() : new Date(), workRecord.getStartTime())).sum();
            Integer listeningModeDuration = cockitWorkRecordList.stream().filter(workRecord -> Objects.equals(workRecord.getCockpitMode(), CockpitModeEnum.LISTENING_MODE.getValue())).mapToInt(workRecord -> calcCost(workRecord.getEndTime() != null ? workRecord.getEndTime() : new Date(), workRecord.getStartTime())).sum();
            supportDataListDTO.setOnlineDuration(workDuration);
            supportDataListDTO.setRestDuration(restDuration);
            supportDataListDTO.setListeningModeCost(listeningModeDuration);
            //计算工单次数
            GetUserIssueInfoDTO userIssueInfoDTO = userIssueInfoDTOMap.get(cockpitTeamUserPageDTO.getSupportUsername());
            supportDataListDTO.setAcceptCount(userIssueInfoDTO.getAcceptCount());
            supportDataListDTO.setFinishCount(userIssueInfoDTO.getFinishCount());
            supportDataListDTO.setTransferCount(userIssueInfoDTO.getTransferCount());
            supportDataListDTO.setAcceptTransferCount(userIssueInfoDTO.getAcceptTransferCount());
            supportDataListDTO.setDiscardCount(userIssueInfoDTO.getDiscardCount());
            //获取巡查提报工单次数
            long patrolReportCount = manualAlarmRecordList.stream().filter(manualAlarmRecord -> Objects.equals(manualAlarmRecord.getReportUser(), cockpitTeamUserPageDTO.getSupportUsername())).count();
            supportDataListDTO.setPatrolReportCount((int) patrolReportCount);
            getCockpitDataPageDTOList.add(supportDataListDTO);
        }
        result.setList(getCockpitDataPageDTOList);
        return result;
    }
}