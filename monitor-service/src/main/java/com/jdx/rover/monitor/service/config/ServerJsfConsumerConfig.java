/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import com.jdx.rover.server.api.jsf.service.command.LocationRemoteCommandService;
import com.jdx.rover.server.api.jsf.service.command.PowerManagerCommandService;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import com.jdx.rover.server.api.jsf.service.vehicle.ServerVehicleService;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 端云服务jsf配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@Component
public class ServerJsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册 ServerVehicleService
     */
    @Bean
    public ServerVehicleService serverVehicleService() {
        return jsfConsumerRegister.createConsumerConfig(ServerVehicleService.class).refer();
    }

    /**
     * 注册 MqttSendJsfService
     */
    @Bean
    public MqttSendJsfService mqttSendJsfService() {
        return jsfConsumerRegister.createConsumerConfig(MqttSendJsfService.class).refer();
    }

    /**
     * 注册 RemoteCommandService
     */
    @Bean
    public RemoteCommandService remoteCommandService() {
        return jsfConsumerRegister.createConsumerConfig(RemoteCommandService.class).refer();
    }

    /**
     * 注册 PowerManagerCommandService
     */
    @Bean
    public PowerManagerCommandService powerManagerCommandService() {
        return jsfConsumerRegister.createConsumerConfig(PowerManagerCommandService.class).refer();
    }

    /**
     * 注册 LocationRemoteCommandService
     */
    @Bean
    public LocationRemoteCommandService locationRemoteCommandService() {
        return jsfConsumerRegister.createConsumerConfig(LocationRemoteCommandService.class).refer();
    }

}