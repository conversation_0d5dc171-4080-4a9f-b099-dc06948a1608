/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot.alarm;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.monitor.dto.robot.RobotDeviceRealtimeInfoDTO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO.DeviceAlarmEventDO;
import com.jdx.rover.monitor.entity.device.RobotGroupInfoDO;
import com.jdx.rover.monitor.enums.AlarmLevelEnum;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCodeEnum;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotAbnormalInfo;
import com.jdx.rover.monitor.repository.redis.metadata.RobotGroupInfoRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotAlarmRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotScheduleRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotStopHistoryRepository;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机器人实时信息检测服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotRealtimeAlarmService {

    /**
     * 机器人停车记录。
     */
    public final RobotStopHistoryRepository robotStopHistoryRepository;

    /**
     * 机器人调度信息缓存。
     */
    public final RobotScheduleRepository robotScheduleRepository;

    /**
     * 机器人告警缓存服务。
     */
    private final RobotAlarmRepository robotAlarmRepository;

    /**
     * 机器人告警服务。
     */
    private final RobotAlarmService robotAlarmService;

    /**
     * 机器人实时信息服务。
     */
    private final RobotRealtimeInfoManager robotRealtimeInfoManager;

    /**
     * 机器人分组信息缓存。
     */
    public final RobotGroupInfoRepository robotGroupInfoRepository;

    /**
     * 处理机器人报警事件。
     */
    public boolean processRobotPropertyAlarm(RobotDeviceRealtimeInfoDTO realtimeInfoDto) {
        Map<String, RobotAlarmDO.DeviceAlarmEventDO> alarmMap = robotAlarmRepository.get(realtimeInfoDto.getDeviceName());
        boolean batteryFlag = processLowBatteryAlarm(realtimeInfoDto, alarmMap);
        boolean stopFlag = processVehicleStopAlarm(realtimeInfoDto, alarmMap);
        boolean  flag = batteryFlag || stopFlag;
        if (flag) {
            robotAlarmService.sendJmqMessage(realtimeInfoDto.getDeviceName(), alarmMap.values());
        }
        return flag;
    }

    /**
     * 处理机器人遇阻报警事件。
     */
    public void processRobotCongestionAlarm(Collection<String> oldVehicleList, Collection<String> congestionVehicleList) {
        Collection<String> disappearList = CollectionUtils.subtract(oldVehicleList, congestionVehicleList);
        if (CollectionUtils.isNotEmpty(disappearList)) {
            // 告警消失
            disappearList.stream().forEach(device -> {
                robotAlarmRepository.fastRemoveMapKey(device, Arrays.asList(DeviceAlarmCodeEnum.VEHICLE_CONGESTED.getAlarmCode()));
                robotAlarmService.endDeviceAbnormal(device, ProductTypeEnum.INTEGRATE.getValue(), DeviceAlarmCodeEnum.VEHICLE_CONGESTED.getAlarmCode());
            });
            Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> alarmListMap = robotAlarmRepository.listMap(Lists.newArrayList(disappearList.iterator()));
            disappearList.stream().forEach(device -> robotAlarmService.sendJmqMessage(device, alarmListMap.get(device)));
        }
        Collection<String> addList = CollectionUtils.subtract(congestionVehicleList, oldVehicleList);
        if (CollectionUtils.isNotEmpty(addList)) {
            // 告警新增
            RobotAlarmDO.DeviceAlarmEventDO alarmEventDo = new RobotAlarmDO.DeviceAlarmEventDO();
            alarmEventDo.setReportTime(new Date());
            alarmEventDo.setErrorLevel(AlarmLevelEnum.ALARM_NORMAL.getValue());
            alarmEventDo.setErrorCode(DeviceAlarmCodeEnum.VEHICLE_CONGESTED.getAlarmCode());
            alarmEventDo.setErrorMsg(DeviceAlarmCodeEnum.VEHICLE_CONGESTED.getAlarmMsg());
            addList.stream().forEach(vehicle -> {
                robotAlarmRepository.add(vehicle, DeviceAlarmCodeEnum.VEHICLE_CONGESTED.getAlarmCode(), alarmEventDo);
                robotAlarmService.addDeviceAbnormal(vehicle, ProductTypeEnum.INTEGRATE.getValue(), alarmEventDo);
            });
            Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> alarmListMap = robotAlarmRepository.listMap(Lists.newArrayList(addList.iterator()));
            addList.stream().forEach(device -> robotAlarmService.sendJmqMessage(device, alarmListMap.get(device)));
        }
    }

    /**
     * 处理机器人停止报警事件。
     */
    private boolean processVehicleStopAlarm(RobotDeviceRealtimeInfoDTO realtimeInfoDto, Map<String, DeviceAlarmEventDO> alarmMap) {
        Double speed = realtimeInfoDto.getLinear();
        String deviceName = realtimeInfoDto.getDeviceName();
        if (Objects.isNull(speed)) {
            return false;
        }
        DeviceAlarmEventDO alarmEventDo = alarmMap.get(DeviceAlarmCodeEnum.VEHICLE_STOP_TIMEOUT.getAlarmCode());
        boolean isTask = Objects.nonNull(robotScheduleRepository.get(deviceName));
        Date startDate = robotStopHistoryRepository.get(deviceName);
        RobotAbnormalInfo robotAbnormalInfo = new RobotAbnormalInfo();
        robotAbnormalInfo.setDeviceName(deviceName);
        robotAbnormalInfo.setProductKey(realtimeInfoDto.getProductKey());
        if (isTask && speed < 0.05 && Objects.isNull(alarmEventDo)) {
            if (Objects.nonNull(startDate) && DateUtil.between(startDate, new Date(), DateUnit.SECOND) > 15) {
                // 告警产生
                alarmEventDo = new DeviceAlarmEventDO();
                alarmEventDo.setErrorCode(DeviceAlarmCodeEnum.VEHICLE_STOP_TIMEOUT.getAlarmCode());
                alarmEventDo.setReportTime(new Date());
                alarmEventDo.setErrorLevel(AlarmLevelEnum.ALARM_NORMAL.getValue());
                alarmEventDo.setErrorMsg(DeviceAlarmCodeEnum.VEHICLE_STOP_TIMEOUT.getAlarmMsg());
                robotAlarmRepository.add(deviceName, DeviceAlarmCodeEnum.VEHICLE_STOP_TIMEOUT.getAlarmCode(), alarmEventDo);
                alarmMap.put(DeviceAlarmCodeEnum.VEHICLE_STOP_TIMEOUT.getAlarmCode(), alarmEventDo);
                robotAlarmService.addDeviceAbnormal(deviceName, realtimeInfoDto.getProductKey(), alarmEventDo);
                robotStopHistoryRepository.remove(deviceName);
                return true;
            } else if (Objects.isNull(startDate)) {
                robotStopHistoryRepository.add(realtimeInfoDto.getDeviceName(), new Date());
                return false;
            }
        }
        if (speed > 0.05 || !isTask) {
            if (Objects.nonNull(alarmEventDo)) {
                // 告警消失
                alarmMap.remove(DeviceAlarmCodeEnum.VEHICLE_STOP_TIMEOUT.getAlarmCode());
                robotAlarmRepository.fastRemoveMapKey(deviceName, Arrays.asList(DeviceAlarmCodeEnum.VEHICLE_STOP_TIMEOUT.getAlarmCode()));
                robotStopHistoryRepository.remove(robotAbnormalInfo.getDeviceName());
                robotAlarmService.endDeviceAbnormal(realtimeInfoDto.getDeviceName(), realtimeInfoDto.getProductKey(), DeviceAlarmCodeEnum.VEHICLE_STOP_TIMEOUT.getAlarmCode());
            }
            if (Objects.nonNull(startDate)) {
                robotStopHistoryRepository.remove(robotAbnormalInfo.getDeviceName());
            }
        }
        return false;
    }

    /**
     * 处理低电量告警事件
     */
    private boolean processLowBatteryAlarm(RobotDeviceRealtimeInfoDTO realtimeInfoDto, Map<String, RobotAlarmDO.DeviceAlarmEventDO> alarmMap) {
        if (Objects.isNull(realtimeInfoDto.getBattery())) {
            return false;
        }
        String groupNo = robotRealtimeInfoManager.getGroupNoNyDevice(realtimeInfoDto.getProductKey(), realtimeInfoDto.getDeviceName());
        RobotGroupInfoDO robotGroupInfoDo = robotGroupInfoRepository.get(groupNo);
        if (Objects.isNull(robotGroupInfoDo) || Objects.isNull(robotGroupInfoDo.getForceChargeLimit())) {
            return false;
        }
        if ( realtimeInfoDto.getBattery() < robotGroupInfoDo.getForceChargeLimit() && Objects.isNull(alarmMap.get(DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmCode()))) {
            //告警产生
            RobotAlarmDO.DeviceAlarmEventDO alarmEventDo = new RobotAlarmDO.DeviceAlarmEventDO();
            alarmEventDo.setReportTime(new Date());
            alarmEventDo.setErrorLevel(AlarmLevelEnum.ALARM_SLIGHT.getValue());
            alarmEventDo.setErrorCode(DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmCode());
            alarmEventDo.setErrorMsg(DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmMsg());
            alarmMap.put(DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmCode(), alarmEventDo);
            robotAlarmRepository.add(realtimeInfoDto.getDeviceName(), DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmCode(), alarmEventDo);
            robotAlarmService.addDeviceAbnormal(realtimeInfoDto.getDeviceName(), realtimeInfoDto.getProductKey(), alarmEventDo);
            return true;
        } else if ( realtimeInfoDto.getBattery() > robotGroupInfoDo.getForceChargeLimit() && Objects.nonNull(alarmMap.get(DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmCode()))) {
            // 告警消失
            alarmMap.remove(DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmCode());
            robotAlarmRepository.fastRemoveMapKey(realtimeInfoDto.getDeviceName(), Arrays.asList(DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmCode()));
            robotAlarmService.endDeviceAbnormal(realtimeInfoDto.getDeviceName(), realtimeInfoDto.getProductKey(), DeviceAlarmCodeEnum.LOW_BATTERY.getAlarmCode());
            return true;
        }
        return false;
    }


}
