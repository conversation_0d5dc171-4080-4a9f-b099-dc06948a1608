/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.jmq.produce;

import cn.hutool.core.date.DateUtil;
import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 统一发送Jmq
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JmqProducerService {

    /**
     * Producer
     */
    private final Producer producer;

    /**
     * sendMessage
     *
     * @param topic topic
     * @param data  data
     */
    public void sendMessage(String topic, Object data) {
        try {
            Message message = new Message();
            message.setTopic(topic);
            message.setText(data instanceof String? (String)data : JsonUtils.writeValueAsString(data));
            message.setSendTime(System.currentTimeMillis());
            message.setBusinessId(String.valueOf(DateUtil.thisSecond()));
            producer.send(message);
            log.info("发送Jmq消息主题{}, 内容{}", topic, data);
        } catch (Exception e) {
            log.error("发送Jmq消息主题{}, 内容{}触发异常", topic, data, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * sendMessage
     *
     * @param topic topic
     * @param businessId 业务顺序ID
     * @param data  data
     */
    public void sendMessage(String topic, String businessId, Object data) {
        try {
            Message message = new Message();
            message.setTopic(topic);
            message.setText(data instanceof String? (String)data : JsonUtils.writeValueAsString(data));
            message.setSendTime(System.currentTimeMillis());
            message.setBusinessId(businessId);
            message.setOrdered(StringUtils.isNotBlank(businessId));
            producer.send(message);
            log.info("业务顺序{}发送Jmq消息主题{}, 内容{}", businessId, topic, data);
        } catch (Exception e) {
            log.error("业务顺序{}发送Jmq消息主题{}, 内容{}触发异常", businessId, topic, data, e);
            throw new RuntimeException(e);
        }
    }

}
