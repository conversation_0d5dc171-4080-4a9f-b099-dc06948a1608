/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.mapcollection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.map.api.domain.dto.MapInfoDTO;

import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.stop.StopBasicDTO;
import com.jdx.rover.monitor.base.BaseModel;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.domain.jsonhandler.StringListTypeHandler;
import com.jdx.rover.monitor.domain.jsonhandler.TaskRouteListTypeHandler;
import com.jdx.rover.monitor.dto.deployment.DeployEnumDTO;
import com.jdx.rover.monitor.dto.deployment.FuzzySearchDTO;
import com.jdx.rover.monitor.dto.deployment.FuzzySearchDTO.Element;
import com.jdx.rover.monitor.dto.deployment.SubTaskDTO;
import com.jdx.rover.monitor.dto.mapcollection.PointInfoDTO;
import com.jdx.rover.monitor.dto.deployment.DeployTaskInfoDTO;
import com.jdx.rover.monitor.dto.deployment.DeployTaskListDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO.PositionDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.UserStopInfoEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.deployment.ElementTypeEnum;
import com.jdx.rover.monitor.enums.mapcollection.RouteColorEnum;
import com.jdx.rover.monitor.enums.mapcollection.TaskRouteTypeEnum;
import com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum;
import com.jdx.rover.monitor.manager.map.MapInfoManager;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskManager;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskTimeRecordManager;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.stop.MetadataStopApiManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTask;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTaskTimeRecord;
import com.jdx.rover.monitor.po.mapcollection.json.TaskRoutePoint;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.mapcollection.MapCollectionTaskRedis;
import com.jdx.rover.monitor.service.config.ducc.DuccMobileProperties;
import com.jdx.rover.monitor.service.enums.EnumService;
import com.jdx.rover.monitor.vo.deployment.DeployTaskDeleteVO;
import com.jdx.rover.monitor.vo.deployment.FuzzySearchVO;
import com.jdx.rover.monitor.vo.deployment.DeployTaskCreateVO;
import com.jdx.rover.monitor.vo.deployment.DeployTaskEditVO;
import com.jdx.rover.monitor.vo.deployment.DeployTaskListVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/18 22:30
 * @description 部署平台线路勘查
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeploymentMapTaskService {

    /**
     * EnumService
     */
    private final EnumService enumService;

    /**
     * MetadataStationApiManager
     */
    private final MetadataStationApiManager metadataStationApiManager;

    /**
     * MetadataStopApiManager
     */
    private final MetadataStopApiManager metadataStopApiManager;

    /**
     * UserVehicleNameRepository
     */
    private final UserVehicleNameRepository userVehicleNameRepository;

    /**
     * MapCollectionTaskManager
     */
    private final MapCollectionTaskManager mapCollectionTaskManager;

    /**
     * MapInfoManager
     */
    private final MapInfoManager mapInfoManager;

    /**
     * PointQueryService
     */
    private final PointQueryService pointQueryService;

    /**
     * VehicleBasicRepository
     */
    private final VehicleBasicRepository vehicleBasicRepository;

    /**
     * VehicleRealtimeRepository
     */
    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    /**
     * MetadataUserApiManager
     */
    private final MetadataUserApiManager metadataUserApiManager;

    /**
     * MapCollectionRedis
     */
    private final MapCollectionTaskRedis mapCollectionTaskRedis;

    /**
     * MapCollectionTaskTimeRecordManager
     */
    private final MapCollectionTaskTimeRecordManager mapCollectionTaskTimeRecordManager;

    /**
     * DuccMobileProperties
     */
    private final DuccMobileProperties duccMobileProperties;

    /**
     * ThreadPoolExecutor
     */
    private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(4, 8, 1,
        TimeUnit.MINUTES, new ArrayBlockingQueue<>(200), new CustomizableThreadFactory("Deployment-"),
        new CallerRunsPolicy());

    /**
     * 获取部署枚举列表
     *
     * @return List<DeployEnumDTO>
     */
    public List<DeployEnumDTO> getEnumMapping() {
        return enumService.getDeploymentEnumList();
    }

    /**
     * 元素列表模糊搜索
     * @param fuzzySearchVO fuzzySearchVO
     * @return FuzzySearchDTO
     */
    public List<FuzzySearchDTO> elementFuzzySearch(FuzzySearchVO fuzzySearchVO) {
        String username = UserUtils.getAndCheckLoginUser();
        List<FuzzySearchDTO> fuzzySearchDTOList = Lists.newArrayListWithCapacity(4);

        // 站点搜索
        CompletableFuture<FuzzySearchDTO> stationFuture = CompletableFuture.supplyAsync(() -> {
            List<com.jdx.rover.metadata.domain.dto.station.StationBasicDTO> stationList = metadataStationApiManager.getStationListByUser(username);
            return generateFuzzySearchDTO(fuzzySearchVO, ElementTypeEnum.STATION, stationList, StationBasicDTO::getStationId, StationBasicDTO::getStationName);
        }, threadPool);

        // 停靠点搜索
        CompletableFuture<FuzzySearchDTO> stopFuture = CompletableFuture.supplyAsync(() -> {
            List<UserStopInfoEntity> stopList = metadataStopApiManager.getStopListByUser(username);
            return generateFuzzySearchDTO(fuzzySearchVO, ElementTypeEnum.STOP, stopList, UserStopInfoEntity::getId, UserStopInfoEntity::getName);
        }, threadPool);

        // 车辆搜索
        CompletableFuture<FuzzySearchDTO> vehicleFuture = CompletableFuture.supplyAsync(() -> {
            Set<String> vehicleNameSet = userVehicleNameRepository.get(username);
            return generateFuzzySearchDTO(fuzzySearchVO, ElementTypeEnum.VEHICLE, Lists.newArrayList(vehicleNameSet), vehicleName -> vehicleName, vehicleName -> vehicleName);
        }, threadPool);

        // 线路搜索
        CompletableFuture<FuzzySearchDTO> taskFuture = CompletableFuture.supplyAsync(() -> {
            List<MapCollectionTask> mapCollectionTasks = mapCollectionTaskManager.searchTaskByUserStation(metadataStationApiManager.getStationListByUser(username));
            return generateFuzzySearchDTO(fuzzySearchVO, ElementTypeEnum.MAP_COLLECTION_TASK, mapCollectionTasks, MapCollectionTask::getId, MapCollectionTask::getTaskName);
        }, threadPool);

        // 等待所有任务完成并收集结果
        CompletableFuture.allOf(stationFuture, stopFuture, vehicleFuture, taskFuture).join();
        try {
            fuzzySearchDTOList.add(stationFuture.get());
            fuzzySearchDTOList.add(stopFuture.get());
            fuzzySearchDTOList.add(vehicleFuture.get());
            fuzzySearchDTOList.add(taskFuture.get());
        } catch (Exception e) {
            log.error("元素列表模糊搜索失败：{}", e.getMessage(), e);
        }
        return fuzzySearchDTOList;
    }

    /**
     * 通用搜索
     *
     * @param fuzzySearchVO fuzzySearchVO
     * @param elementType   elementType
     * @param elements      elements
     * @param idGetter      idGetter
     * @param nameGetter    nameGetter
     * @return FuzzySearchDTO
     */
    private <T> FuzzySearchDTO generateFuzzySearchDTO(FuzzySearchVO fuzzySearchVO, ElementTypeEnum elementType, List<T> elements,
        Function<T, Object> idGetter, Function<T, String> nameGetter) {
        FuzzySearchDTO fuzzySearchDTO = new FuzzySearchDTO();
        fuzzySearchDTO.setElementType(elementType.getCode());
        fuzzySearchDTO.setElementList(Lists.newArrayList());
        if (CollUtil.isEmpty(elements)) {
            return fuzzySearchDTO;
        }

        List<Element> filteredElements = elements.stream()
            .map(element -> new Element(String.valueOf(idGetter.apply(element)), nameGetter.apply(element)))
            .filter(element -> StrUtil.indexOfIgnoreCase(element.getElementName(), fuzzySearchVO.getElementName()) != -1)
            .sorted(Comparator.comparing(Element::getElementName))
            .limit(25)
            .collect(Collectors.toList());
        fuzzySearchDTO.getElementList().addAll(filteredElements);
        return fuzzySearchDTO;
    }

    /**
     * 获取部署地图元素列表
     *
     * @param deployTaskListVO deployTaskListVO
     * @return DeployTaskListDTO
     */
    public DeployTaskListDTO getTaskRouteList(DeployTaskListVO deployTaskListVO) {
        DeployTaskListDTO deployTaskListDTO = new DeployTaskListDTO();

        // 地图元素聚焦经纬度
        Double elementLat = deployTaskListVO.getLatitude();
        Double elementLon = deployTaskListVO.getLongitude();
        String username = UserUtils.getAndCheckLoginUser();

        // 基于地理位置搜索
        if (isGeoSearch(deployTaskListVO)) {
            // 获取配置公里范围内用户权限站点和停靠点
            List<Integer> stationIds = pointQueryService.queryStationIdsByRadiusAndUser(elementLat, elementLon, duccMobileProperties.getInitialPointRadius(), username);
            if (CollUtil.isNotEmpty(stationIds)) {
                LambdaQueryWrapper<MapCollectionTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.like(StrUtil.isNotBlank(deployTaskListVO.getTaskName()), MapCollectionTask::getTaskName, deployTaskListVO.getTaskName());
                lambdaQueryWrapper.like(StrUtil.isNotBlank(deployTaskListVO.getCreatorUsername()), MapCollectionTask::getTaskCreator, deployTaskListVO.getCreatorUsername());
                if (StrUtil.isNotBlank(deployTaskListVO.getTaskStatus())) {
                    lambdaQueryWrapper.eq(MapCollectionTask::getTaskStatus, deployTaskListVO.getTaskStatus());
                } else {
                    lambdaQueryWrapper.ne(MapCollectionTask::getTaskStatus, TaskStatusEnum.TASK_DELETED.getCode());
                }
                if (ObjectUtil.isNotNull(deployTaskListVO.getStationId())) {
                    lambdaQueryWrapper.eq(ObjectUtil.isNotNull(deployTaskListVO.getStationId()), MapCollectionTask::getStationId, deployTaskListVO.getStationId());
                } else {
                    lambdaQueryWrapper.in(MapCollectionTask::getStationId, stationIds);
                }
                List<MapCollectionTask> mapCollectionTasks = mapCollectionTaskManager.getBaseMapper().selectList(lambdaQueryWrapper);
                buildTaskList(deployTaskListDTO, mapCollectionTasks, stationIds);
            }
        } else {
            // 基于元素搜索
            StationBasicDTO stationBasicDTO = null;
            switch (ElementTypeEnum.of(deployTaskListVO.getElementType())) {
                case STATION -> {
                    stationBasicDTO = metadataStationApiManager.getById(Integer.valueOf(deployTaskListVO.getElementId()));

                    // 地图聚焦：站点
                    if (ObjectUtil.isNotNull(stationBasicDTO)) {
                        elementLat = stationBasicDTO.getLatitude();
                        elementLon = stationBasicDTO.getLongitude();
                    }
                }
                case STOP -> {
                    StopBasicDTO stopInfoDTO = metadataStopApiManager.getStopInfoById(Integer.valueOf(deployTaskListVO.getElementId()));

                    // 地图聚焦：停靠点
                    if (ObjectUtil.isNotNull(stopInfoDTO)) {
                        stationBasicDTO = metadataStationApiManager.getById(stopInfoDTO.getStationId());
                        elementLat = stopInfoDTO.getLatitude();
                        elementLon = stopInfoDTO.getLongitude();
                    }
                }
                case VEHICLE -> {
                    VehicleBasicDTO vehicleBasicDTO = vehicleBasicRepository.get(deployTaskListVO.getElementId());
                    if (ObjectUtil.isNotNull(vehicleBasicDTO)) {
                        stationBasicDTO = metadataStationApiManager.getById(vehicleBasicDTO.getStationId());
                        VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleBasicDTO.getName());

                        // 地图聚焦：车辆，若车辆无经纬度，则聚焦站点
                        if (ObjectUtil.isNotNull(vehicleRealtimeInfoDTO)) {
                            elementLat = vehicleRealtimeInfoDTO.getLat();
                            elementLon = vehicleRealtimeInfoDTO.getLon();
                        } else {
                            elementLat = stationBasicDTO.getLatitude();
                            elementLon = stationBasicDTO.getLongitude();
                        }
                    }
                }
                case MAP_COLLECTION_TASK -> {
                    MapCollectionTask mapCollectionTask = mapCollectionTaskManager.queryTask(Integer.valueOf(deployTaskListVO.getElementId()));
                    if (ObjectUtil.isNotNull(mapCollectionTask)) {
                        stationBasicDTO = metadataStationApiManager.getById(mapCollectionTask.getStationId());

                        // 地图聚焦：线路起始点，若无点位信息，则聚焦站点
                        if (CollUtil.isEmpty(mapCollectionTask.getTaskRoute())) {
                            elementLat = stationBasicDTO.getLatitude();
                            elementLon = stationBasicDTO.getLongitude();
                        } else {
                            elementLat = mapCollectionTask.getTaskRoute().get(0).getLatitude();
                            elementLon = mapCollectionTask.getTaskRoute().get(0).getLongitude();
                        }
                    }
                }
            }

            if (stationBasicDTO != null) {
                LambdaQueryWrapper<MapCollectionTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(MapCollectionTask::getStationId, stationBasicDTO.getStationId());
                if (StrUtil.isNotBlank(deployTaskListVO.getTaskStatus())) {
                    lambdaQueryWrapper.eq(MapCollectionTask::getTaskStatus, deployTaskListVO.getTaskStatus());
                } else {
                    lambdaQueryWrapper.ne(MapCollectionTask::getTaskStatus, TaskStatusEnum.TASK_DELETED.getCode());
                }
                lambdaQueryWrapper.like(StrUtil.isNotBlank(deployTaskListVO.getTaskName()), MapCollectionTask::getTaskName, deployTaskListVO.getTaskName());
                lambdaQueryWrapper.like(StrUtil.isNotBlank(deployTaskListVO.getCreatorUsername()), MapCollectionTask::getTaskCreator, deployTaskListVO.getCreatorUsername());
                List<MapCollectionTask> mapCollectionTasks = mapCollectionTaskManager.getBaseMapper().selectList(lambdaQueryWrapper);
                buildTaskList(deployTaskListDTO, mapCollectionTasks, Lists.newArrayList(stationBasicDTO.getStationId()));

                List<PointInfoDTO> pointList = Lists.newArrayList();
                pointList.addAll(PointInfoDTO.createByStationList(Lists.newArrayList(stationBasicDTO)));
                pointList.addAll(PointInfoDTO.createByStopList(metadataStopApiManager.getStopByStationId(stationBasicDTO.getStationId())));
                deployTaskListDTO.setPointList(pointList);
            }
        }

        // 获取高精地图信息
        deployTaskListDTO.setElementLat(elementLat);
        deployTaskListDTO.setElementLon(elementLon);
        MapInfoDTO mapInfoDTO = mapInfoManager.getMapByPosition(elementLat, elementLon);
        if (ObjectUtil.isNotNull(mapInfoDTO)) {
            deployTaskListDTO.setMapId(mapInfoDTO.getId());
            deployTaskListDTO.setMapVersion(mapInfoDTO.getMapVersion());
        }
        return deployTaskListDTO;
    }

    /**
     * 是否为地理位置搜索
     *
     * @param deployTaskListVO deployTaskListVO
     * @return boolean
     */
    private boolean isGeoSearch(DeployTaskListVO deployTaskListVO) {
        return deployTaskListVO.getLatitude() != null && deployTaskListVO.getLongitude() != null;
    }

    /**
     * 构建任务列表
     *
     * @param deployTaskListDTO deployTaskListDTO
     * @param mapCollectionTasks mapCollectionTasks
     * @param stationIds stationIds
     */
    private void buildTaskList(DeployTaskListDTO deployTaskListDTO, List<MapCollectionTask> mapCollectionTasks, List<Integer> stationIds) {
        List<DeployTaskInfoDTO> taskList = Lists.newArrayList();
        deployTaskListDTO.setTaskList(taskList);
        if (CollUtil.isEmpty(mapCollectionTasks)) {
            return;
        }

        Map<Integer, StationBasicDTO> stationBasicDTOMap = metadataStationApiManager.getStationById(stationIds.toArray(new Integer[0]));
        mapCollectionTasks.forEach(mapCollectionTask -> {
            DeployTaskInfoDTO deployTaskInfoDTO = new DeployTaskInfoDTO();
            deployTaskInfoDTO.setTaskId(mapCollectionTask.getId());
            deployTaskInfoDTO.setTaskName(mapCollectionTask.getTaskName());
            deployTaskInfoDTO.setTaskCreator(mapCollectionTask.getTaskCreator());
            deployTaskInfoDTO.setTaskRouteType(mapCollectionTask.getTaskRouteType());
            deployTaskInfoDTO.setTaskStatus(mapCollectionTask.getTaskStatus());
            deployTaskInfoDTO.setStationId(mapCollectionTask.getStationId());
            if (stationBasicDTOMap.containsKey(mapCollectionTask.getStationId())) {
                deployTaskInfoDTO.setStationName(stationBasicDTOMap.get(mapCollectionTask.getStationId()).getStationName());
            }
            deployTaskInfoDTO.setCreateTime(DateUtil.format(mapCollectionTask.getCreateTime(), DatePattern.NORM_DATETIME_FORMAT));
            deployTaskInfoDTO.setTotalMileage(mapCollectionTask.getTotalMileage());
            deployTaskInfoDTO.setTaskRouteColor(mapCollectionTask.getRouteColor());
            deployTaskInfoDTO.setVehicleName(mapCollectionTask.getVehicleName());
            deployTaskInfoDTO.setRoutePlanType(mapCollectionTask.getRoutePlanType());
            deployTaskInfoDTO.setPreciseType(mapCollectionTask.getPreciseType());

            // 处理线路点位
            if (CollUtil.isNotEmpty(mapCollectionTask.getTaskRoute())) {
                List<PositionDTO> taskRouteList = Lists.newArrayListWithCapacity(mapCollectionTask.getTaskRoute().size());
                mapCollectionTask.getTaskRoute().forEach(taskRoute -> {
                    PositionDTO position = new PositionDTO();
                    position.setLatitude(taskRoute.getLatitude());
                    position.setLongitude(taskRoute.getLongitude());
                    taskRouteList.add(position);
                });
                deployTaskInfoDTO.setTaskRouteList(taskRouteList);
            } else {
                deployTaskInfoDTO.setTaskRouteList(Lists.newArrayList());
            }

            // 处理路名
            if (CollUtil.isNotEmpty(mapCollectionTask.getRoadNames())) {
                deployTaskInfoDTO.setRoadNameList(mapCollectionTask.getRoadNames());
            } else {
                deployTaskInfoDTO.setRoadNameList(Lists.newArrayList());
            }
            taskList.add(deployTaskInfoDTO);
        });
        taskList.sort(Comparator.comparing(DeployTaskInfoDTO::getCreateTime).reversed());
    }

    /**
     * 获取勘查任务详情
     *
     * @param taskBaseVO taskBaseVO
     * @return DeployTaskInfoDTO
     */
    public DeployTaskInfoDTO getTaskDetail(TaskBaseVO taskBaseVO) {
        DeployTaskInfoDTO deployTaskInfoDTO = new DeployTaskInfoDTO();

        // 根据任务ID查询任务详情
        MapCollectionTask mapCollectionTask = mapCollectionTaskManager.queryTask(taskBaseVO.getTaskId());
        if (ObjectUtil.isNull(mapCollectionTask)) {
            return deployTaskInfoDTO;
        }

        deployTaskInfoDTO.setTaskName(mapCollectionTask.getTaskName());
        deployTaskInfoDTO.setTaskStatus(mapCollectionTask.getTaskStatus());
        deployTaskInfoDTO.setStationId(mapCollectionTask.getStationId());

        // 获取站点名称
        Map<Integer, StationBasicDTO> stationBasicDTOMap = metadataStationApiManager.getStationById(mapCollectionTask.getStationId());
        if (stationBasicDTOMap.containsKey(mapCollectionTask.getStationId())) {
            deployTaskInfoDTO.setStationName(stationBasicDTOMap.get(mapCollectionTask.getStationId()).getStationName());
            if (stationBasicDTOMap.get(mapCollectionTask.getStationId()).getAddressInfo() != null) {
                deployTaskInfoDTO.setCityName(stationBasicDTOMap.get(mapCollectionTask.getStationId()).getAddressInfo().getCityName());
            }
        }
        deployTaskInfoDTO.setTaskCreator(mapCollectionTask.getTaskCreator());
        deployTaskInfoDTO.setTaskRouteType(mapCollectionTask.getTaskRouteType());

        // 获取创建人手机号
        UserExtendInfoDTO extendInfoDto = metadataUserApiManager.getUserExtendInfoByName(mapCollectionTask.getTaskCreator());
        deployTaskInfoDTO.setPhoneNumber(String.valueOf(PhoneUtil.hideBetween(extendInfoDto.getPhone())));
        deployTaskInfoDTO.setCreateTime(DateUtil.format(mapCollectionTask.getCreateTime(), DatePattern.NORM_DATETIME_FORMAT));
        deployTaskInfoDTO.setTotalMileage(mapCollectionTask.getTotalMileage());

        // 处理路名
        if (CollUtil.isNotEmpty(mapCollectionTask.getRoadNames())) {
            deployTaskInfoDTO.setRoadNameList(mapCollectionTask.getRoadNames());
        } else {
            deployTaskInfoDTO.setRoadNameList(Lists.newArrayList());
        }

        // 待拷贝、拷贝完成、已完成任务查询子任务明细
        if (StrUtil.equalsAny(mapCollectionTask.getTaskStatus(), TaskStatusEnum.TASK_CLOSED.getCode(), TaskStatusEnum.PENDING_COPY.getCode(), TaskStatusEnum.COPY_COMPLETED.getCode())) {
            List<MapCollectionTaskTimeRecord> mapCollectionTaskTimeRecords = mapCollectionTaskTimeRecordManager.queryRecordByTaskId(mapCollectionTask.getId());
            if (CollUtil.isNotEmpty(mapCollectionTaskTimeRecords)) {
                mapCollectionTaskTimeRecords.sort(Comparator.comparing(MapCollectionTaskTimeRecord::getStartTime));
                List<SubTaskDTO> subTaskList = Lists.newArrayListWithCapacity(mapCollectionTaskTimeRecords.size());
                mapCollectionTaskTimeRecords.forEach(mapCollectionTaskTimeRecord -> {
                    SubTaskDTO subTaskDTO = new SubTaskDTO();
                    subTaskDTO.setStartTime(DateUtil.format(mapCollectionTaskTimeRecord.getStartTime(), DatePattern.NORM_DATETIME_FORMAT));
                    subTaskDTO.setEndTime(DateUtil.format(mapCollectionTaskTimeRecord.getEndTime(), DatePattern.NORM_DATETIME_FORMAT));
                    subTaskList.add(subTaskDTO);
                });
                deployTaskInfoDTO.setSubTaskList(subTaskList);
            }
        }
        return deployTaskInfoDTO;
    }

    /**
     * 创建线路规划
     *
     * @param deployTaskCreateVO deployTaskCreateVO
     */
    public void createTaskRoute(DeployTaskCreateVO deployTaskCreateVO) {
        MapCollectionTask mapCollectionTask = new MapCollectionTask();
        mapCollectionTask.setTaskName(deployTaskCreateVO.getTaskName());
        mapCollectionTask.setTaskCreator(UserUtils.getAndCheckLoginUser());
        mapCollectionTask.setStationId(deployTaskCreateVO.getStationId());
        Map<Integer, StationBasicDTO> stationBasicDTOMap = metadataStationApiManager.getStationById(mapCollectionTask.getStationId());
        if (stationBasicDTOMap.containsKey(mapCollectionTask.getStationId()) && ObjectUtil.isNotNull(stationBasicDTOMap.get(mapCollectionTask.getStationId()).getAddressInfo())) {
            mapCollectionTask.setCityId(stationBasicDTOMap.get(mapCollectionTask.getStationId()).getAddressInfo().getCityId());
        }
        mapCollectionTask.setRoutePlanType(deployTaskCreateVO.getRoutePlanType());
        mapCollectionTask.setPreciseType(deployTaskCreateVO.getPreciseType());
        mapCollectionTask.setTotalMileage(deployTaskCreateVO.getTotalMileage());
        mapCollectionTask.setTaskStatus(TaskStatusEnum.PENDING_CLAIM.getCode());
        mapCollectionTask.setTaskSubmitTime(DateUtil.date());
        // 默认去程线路类型
        mapCollectionTask.setTaskRouteType(StrUtil.isBlank(deployTaskCreateVO.getTaskRouteType()) ? TaskRouteTypeEnum.SETOUT.getCode() : deployTaskCreateVO.getTaskRouteType());
        if (RouteColorEnum.of(deployTaskCreateVO.getTaskRouteColor()) != RouteColorEnum.UNKNOWN) {
            mapCollectionTask.setRouteColor(deployTaskCreateVO.getTaskRouteColor());
        }

        // 点位
        List<TaskRoutePoint> taskRouteList = Lists.newArrayList();
        mapCollectionTask.setTaskRoute(taskRouteList);
        deployTaskCreateVO.getTaskRouteList().forEach(taskRoute -> {
            TaskRoutePoint taskRoutePoint = new TaskRoutePoint();
            taskRoutePoint.setLatitude(taskRoute.getLatitude());
            taskRoutePoint.setLongitude(taskRoute.getLongitude());
            taskRouteList.add(taskRoutePoint);
        });

        // 路线名称
        if (CollUtil.isNotEmpty(deployTaskCreateVO.getRoadNameList())) {
            mapCollectionTask.setRoadNames(deployTaskCreateVO.getRoadNameList().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList()));
        }

        mapCollectionTaskManager.getBaseMapper().insert(mapCollectionTask);
    }

    /**
     * 编辑线路规划
     *
     * @param deployTaskEditVO deployTaskEditVO
     */
    public void modifyTaskRoute(DeployTaskEditVO deployTaskEditVO) {
        String username = UserUtils.getLoginUser();
        LambdaUpdateWrapper<MapCollectionTask> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BaseModel::getId, deployTaskEditVO.getTaskId());
        lambdaUpdateWrapper.set(MapCollectionTask::getTaskName, deployTaskEditVO.getTaskName());
        lambdaUpdateWrapper.set(MapCollectionTask::getTaskStatus, TaskStatusEnum.PENDING_CLAIM.getCode());
        lambdaUpdateWrapper.set(MapCollectionTask::getStationId, deployTaskEditVO.getStationId());
        Map<Integer, StationBasicDTO> stationBasicDTOMap = metadataStationApiManager.getStationById(deployTaskEditVO.getStationId());
        if (stationBasicDTOMap.containsKey(deployTaskEditVO.getStationId()) && ObjectUtil.isNotNull(stationBasicDTOMap.get(deployTaskEditVO.getStationId()).getAddressInfo())) {
            lambdaUpdateWrapper.set(MapCollectionTask::getCityId, stationBasicDTOMap.get(deployTaskEditVO.getStationId()).getAddressInfo().getCityId());
        }
        lambdaUpdateWrapper.set(MapCollectionTask::getRoutePlanType, deployTaskEditVO.getRoutePlanType());
        lambdaUpdateWrapper.set(MapCollectionTask::getPreciseType, deployTaskEditVO.getPreciseType());
        lambdaUpdateWrapper.set(MapCollectionTask::getTotalMileage, deployTaskEditVO.getTotalMileage());
        lambdaUpdateWrapper.set(StrUtil.isNotBlank(deployTaskEditVO.getTaskRouteColor()), MapCollectionTask::getRouteColor, deployTaskEditVO.getTaskRouteColor());
        lambdaUpdateWrapper.set(MapCollectionTask::getTaskRoute, deployTaskEditVO.getTaskRouteList(), "typeHandler=" + TaskRouteListTypeHandler.class.getName());
        lambdaUpdateWrapper.set(MapCollectionTask::getRoadNames, deployTaskEditVO.getRoadNameList().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList()), "typeHandler=" + StringListTypeHandler.class.getName());
        lambdaUpdateWrapper.set(BaseModel::getModifyUser, username);
        lambdaUpdateWrapper.set(MapCollectionTask::getTaskSubmitTime, DateUtil.date());
        mapCollectionTaskManager.getBaseMapper().update(lambdaUpdateWrapper);
    }

    /**
     * 删除任务
     *
     * @param deployTaskDeleteVO deployTaskDeleteVO
     */
    public void deleteTask(DeployTaskDeleteVO deployTaskDeleteVO) {
        // 非勘查中、待认领状态无法删除
        MapCollectionTask mapCollectionTask = mapCollectionTaskManager.queryTaskStatus(deployTaskDeleteVO.getTaskId());
        if (Objects.isNull(mapCollectionTask)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EXIST.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EXIST.getMessage());
        }
        TaskStatusEnum taskStatusEnum = TaskStatusEnum.of(mapCollectionTask.getTaskStatus());
        if (taskStatusEnum != TaskStatusEnum.UNDER_EXPLORATION && taskStatusEnum != TaskStatusEnum.PENDING_CLAIM) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_CANNOT_DELETE.getCode(), MonitorErrorEnum.ERROR_TASK_CANNOT_DELETE.getMessage());
        }

        // 如果待勘查任务此时有用户正在勘查中，则任务无法删除
        if (taskStatusEnum == TaskStatusEnum.UNDER_EXPLORATION) {
            String currentProcessor = mapCollectionTaskRedis.getTaskProcessor(deployTaskDeleteVO.getTaskId());
            if (StrUtil.isNotBlank(currentProcessor)) {
                throw new AppException(MonitorErrorEnum.ERROR_TASK_CANNOT_PROCESS.getCode(), String.format(MonitorErrorEnum.ERROR_TASK_CANNOT_PROCESS.getMessage(), currentProcessor));
            }
        }

        // 任务状态发生变更，无法删除
        if (taskStatusEnum != TaskStatusEnum.of(deployTaskDeleteVO.getTaskStatus())) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_STATUS_CHANGE.getCode(), MonitorErrorEnum.ERROR_TASK_STATUS_CHANGE.getMessage());
        }

        // 更新任务状态为任务关闭
        mapCollectionTaskManager.updateTaskStatus(deployTaskDeleteVO.getTaskId(), TaskStatusEnum.TASK_DELETED);
    }

    /**
     * 撤回待认领任务
     *
     * @param taskBaseVO taskBaseVO
     */
    public void revokeTask(TaskBaseVO taskBaseVO) {
        // 非待认领状态无法回退状态
        MapCollectionTask mapCollectionTask = mapCollectionTaskManager.queryTaskStatus(taskBaseVO.getTaskId());
        if (Objects.isNull(mapCollectionTask)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EXIST.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EXIST.getMessage());
        }
        TaskStatusEnum taskStatusEnum = TaskStatusEnum.of(mapCollectionTask.getTaskStatus());
        if (taskStatusEnum != TaskStatusEnum.PENDING_CLAIM) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getMessage());
        }

        // 更新任务状态为待认领
        mapCollectionTaskManager.updateTaskStatus(taskBaseVO.getTaskId(), TaskStatusEnum.UNDER_EXPLORATION);
    }

    /**
     * 设置勘查任务状态（OPS）
     *
     * @param taskIds taskIds
     * @param taskStatus taskStatus
     */
    public void refreshTaskStatus(List<Integer> taskIds, String taskStatus) {
        if (CollUtil.isEmpty(taskIds) || TaskStatusEnum.of(taskStatus) == TaskStatusEnum.UNKNOWN) {
            return;
        }
        mapCollectionTaskManager.lambdaUpdate()
            .in(BaseModel::getId, taskIds)
            .set(MapCollectionTask::getTaskStatus, taskStatus)
            .update();
    }
}
