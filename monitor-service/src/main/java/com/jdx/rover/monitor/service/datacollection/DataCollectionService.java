/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.service.datacollection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.monitor.common.exception.AppException;
import com.jdx.rover.monitor.dto.datacollection.*;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionStatusEnum;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionTagTypeEnum;
import com.jdx.rover.monitor.manager.datacollection.*;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.po.datacollection.*;
import com.jdx.rover.monitor.repository.util.PageUtils;
import com.jdx.rover.monitor.vo.datacollection.*;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据采集服务类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionService {

    /**
     * DataCollectionRequirementManager
     */
    private final DataCollectionRequirementManager dataCollectionRequirementManager;

    /**
     * DataCollectionTagManager
     */
    private final DataCollectionTagManager dataCollectionTagManager;

    /**
     * DataCollectionRequirementTagManager
     */
    private final DataCollectionRequirementTagManager dataCollectionRequirementTagManager;

    /**
     * DataCollectionSceneManager
     */
    private final DataCollectionSceneManager dataCollectionSceneManager;

    /**
     * DataCollectionSceneTagManager
     */
    private final DataCollectionSceneTagManager dataCollectionSceneTagManager;

    /**
     * DataCollectionSceneRequirementManager
     */
    private final DataCollectionSceneRequirementManager dataCollectionSceneRequirementManager;

    /**
     * MetadataUserApiManager
     */
    private final MetadataUserApiManager metadataUserApiManager;

    /**
     * 新建数据采集需求（包含标签关联）
     *
     * @param addDataCollectionRequirementVO 新建需求请求参数
     * @param createUser 创建用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void addRequirement(AddDataCollectionRequirementVO addDataCollectionRequirementVO, String createUser) {
        createUser = getErpByUsername(createUser);

        // 创建需求
        DataCollectionRequirement requirement = dataCollectionRequirementManager.createRequirement(addDataCollectionRequirementVO.getDescription(),
            addDataCollectionRequirementVO.getRequiredDetail(), addDataCollectionRequirementVO.getForbiddenDetail(), createUser);
        if (CollUtil.isEmpty(addDataCollectionRequirementVO.getRelatedTags())) {
            return;
        }

        // 关联标签
        List<DataCollectionRequirementTag> requirementTags = Lists.newArrayList();
        for (RequirementDataTagVO requirementDataTagVO : addDataCollectionRequirementVO.getRelatedTags()) {
            // 验证标签是否存在
            DataCollectionTag dataCollectionTag = dataCollectionTagManager.getById(requirementDataTagVO.getTagId());
            if (dataCollectionTag == null) {
                throw new AppException("标签不存在，标签ID: " + requirementDataTagVO.getTagId());
            }

            // 验证标签名称是否匹配
            if (!dataCollectionTag.getTagName().equals(requirementDataTagVO.getTagName())) {
                throw new AppException("标签名称不匹配，标签ID: " + requirementDataTagVO.getTagId());
            }

            // 创建需求标签关联
            DataCollectionRequirementTag requirementTag = new DataCollectionRequirementTag();
            requirementTag.setRequirementId(requirement.getId());
            requirementTag.setTagId(requirementDataTagVO.getTagId());
            requirementTag.setTagName(requirementDataTagVO.getTagName());
            requirementTag.setParentTagId(requirementDataTagVO.getParentId());
            if (null != requirementDataTagVO.getFeId()) {
                requirementTag.setFeId(requirementDataTagVO.getFeId());
            }
            if(null != requirementDataTagVO.getFeParentId()) {
                requirementTag.setFeParentId(requirementDataTagVO.getFeParentId());
            }
            requirementTag.setTagType(requirementDataTagVO.getTagType());
            requirementTag.setCount(requirementDataTagVO.getCount());
            requirementTag.setEnabled(true);
            requirementTag.setCreateUser(createUser);
            requirementTags.add(requirementTag);
        }

        // 批量保存需求标签关联
        if (CollectionUtil.isNotEmpty(requirementTags)) {
            dataCollectionRequirementTagManager.saveBatchRequirementTags(requirementTags);
        }
    }

    /**
     * 新建数据采集标签
     *
     * @param addDataCollectionTagVO 新建标签请求参数
     * @param createUser 创建用户
     * @return AddDataCollectionTagDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public DataCollectionTagDTO addTag(AddDataCollectionTagVO addDataCollectionTagVO, String createUser) {
        String tagName = addDataCollectionTagVO.getTagName().trim();
        DataCollectionTagDTO result = new DataCollectionTagDTO();
        createUser = getErpByUsername(createUser);

        // 检查标签名称是否已存在
        DataCollectionTag byTagName = dataCollectionTagManager.getByTagName(tagName);

        // 标签已存在，直接返回已有标签id及name
        if (null != byTagName) {
            result.setTagId(byTagName.getId());
            result.setTagName(byTagName.getTagName());
            return result;
        }

        // 创建标签
        DataCollectionTag dataCollectionTag = new DataCollectionTag();
        dataCollectionTag.setTagName(tagName);
        dataCollectionTag.setCreateUser(createUser);
        // 保存标签
        Integer tagId = dataCollectionTagManager.saveTag(dataCollectionTag);

        // 构建返回结果
        result.setTagId(tagId);
        result.setTagName(dataCollectionTag.getTagName());
        return result;
    }

    /**
     * 分页获取数据采集需求列表
     *
     * @param dataCollectionRequirementPageVO 分页查询参数
     * @return PageDTO<DataCollectionRequirementPageDTO>
     */
    public PageDTO<DataCollectionRequirementPageDTO> getRequirementPageList(DataCollectionRequirementPageVO dataCollectionRequirementPageVO) {
        // 构建查询条件
        LambdaQueryWrapper<DataCollectionRequirement> queryWrapper = new LambdaQueryWrapper<>();

        // 时间范围查询
        if (null != dataCollectionRequirementPageVO.getStartTime() && null != dataCollectionRequirementPageVO.getEndTime()) {
            queryWrapper.between(DataCollectionRequirement::getCreateTime, dataCollectionRequirementPageVO.getStartTime(), dataCollectionRequirementPageVO.getEndTime());
        }

        // 需求说明模糊搜索
        if (StringUtils.isNotBlank(dataCollectionRequirementPageVO.getDescription())) {
            queryWrapper.like(DataCollectionRequirement::getDescription, dataCollectionRequirementPageVO.getDescription());
        }

        // 创建人精确匹配
        if (StringUtils.isNotBlank(dataCollectionRequirementPageVO.getCreateUser())) {
            queryWrapper.like(DataCollectionRequirement::getCreateUser, dataCollectionRequirementPageVO.getCreateUser());
        }

        // 需求状态精确匹配
        if (StringUtils.isNotBlank(dataCollectionRequirementPageVO.getStatus())) {
            queryWrapper.eq(DataCollectionRequirement::getStatus, dataCollectionRequirementPageVO.getStatus());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(DataCollectionRequirement::getCreateTime);

        // 分页查询
        Page<DataCollectionRequirement> page = new Page<>(dataCollectionRequirementPageVO.getPageNum(), dataCollectionRequirementPageVO.getPageSize());
        IPage<DataCollectionRequirement> pageResult = dataCollectionRequirementManager.getBaseMapper().selectPage(page, queryWrapper);

        // 转换为PageDTO
        return PageUtils.convertPageDTO(pageResult, this::convertToPageDTO);
    }

    /**
     * 将DataCollectionRequirement转换为DataCollectionRequirementPageDTO
     *
     * @param dataCollectionRequirement 需求实体
     * @return DataCollectionRequirementPageDTO
     */
    private DataCollectionRequirementPageDTO convertToPageDTO(DataCollectionRequirement dataCollectionRequirement) {
        DataCollectionRequirementPageDTO dataCollectionRequirementPageDTO = new DataCollectionRequirementPageDTO();
        dataCollectionRequirementPageDTO.setRequirementId(dataCollectionRequirement.getId());
        dataCollectionRequirementPageDTO.setRequirementNumber(dataCollectionRequirement.getRequirementNumber());
        dataCollectionRequirementPageDTO.setDescription(dataCollectionRequirement.getDescription());
        dataCollectionRequirementPageDTO.setCreateTime(dataCollectionRequirement.getCreateTime());
        dataCollectionRequirementPageDTO.setCreateUser(dataCollectionRequirement.getCreateUser());
        dataCollectionRequirementPageDTO.setStatus(dataCollectionRequirement.getStatus());

        // 检查是否有关联标签，无关联标签返回null，有关联标签返回进度（保留两位小数）
        List<DataCollectionRequirementTag> dataCollectionRequirementTagList = dataCollectionRequirementTagManager.listByRequirementId(dataCollectionRequirement.getId(), true);
        dataCollectionRequirementPageDTO.setProgress(formatProgress(dataCollectionRequirement, dataCollectionRequirementTagList));
        return dataCollectionRequirementPageDTO;
    }

    /**
     * 更改需求状态
     *
     * @param dataCollectionRequirementChangeStateVO 更改状态请求参数
     * @param modifyUser 修改用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeRequirementState(DataCollectionRequirementChangeStateVO dataCollectionRequirementChangeStateVO, String modifyUser) {
        Integer requirementId = dataCollectionRequirementChangeStateVO.getRequirementId();
        String actionType = dataCollectionRequirementChangeStateVO.getActionType();
        modifyUser = getErpByUsername(modifyUser);

        // 根据需求ID获取需求信息
        DataCollectionRequirement requirement = dataCollectionRequirementManager.getById(requirementId);
        if (requirement == null) {
            throw new AppException("需求不存在");
        }

        // 根据actionType确定目标状态
        String targetStatus = switch (actionType.toUpperCase()) {
            case "OPEN" -> {
                // 开启动作时，根据进度判断状态
                Double progress = requirement.getProgress();
                if (progress != null && progress >= 1.0) {
                    yield DataCollectionStatusEnum.FINISHED.getValue();
                } else {
                    yield DataCollectionStatusEnum.ONGOING.getValue();
                }
            }
            case "CLOSE" -> DataCollectionStatusEnum.CLOSED.getValue();
            default -> throw new AppException("不支持的操作类型: " + actionType);
        };

        // 更新需求状态
        dataCollectionRequirementManager.updateStatus(requirement.getId(), targetStatus, modifyUser);
    }

    /**
     * 编辑需求详情
     *
     * @param dataCollectionRequirementEditVO 编辑需求详情请求参数
     * @param modifyUser 修改用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void editRequirement(DataCollectionRequirementEditVO dataCollectionRequirementEditVO, String modifyUser) {
        modifyUser = getErpByUsername(modifyUser);
        Integer requirementId = dataCollectionRequirementEditVO.getRequirementId();

        // 根据需求ID获取需求信息
        DataCollectionRequirement requirement = dataCollectionRequirementManager.getById(requirementId);
        if (requirement == null) {
            throw new AppException("需求不存在");
        }

        // 更新需求描述
        requirement.setDescription(dataCollectionRequirementEditVO.getDescription());
        requirement.setRequiredDetail(dataCollectionRequirementEditVO.getRequiredDetail());
        requirement.setForbiddenDetail(dataCollectionRequirementEditVO.getForbiddenDetail());
        requirement.setModifyUser(modifyUser);
        dataCollectionRequirementManager.updateById(requirement);

        // 更新关联标签
        editRequirementTags(requirementId, dataCollectionRequirementEditVO.getRelatedTags(), modifyUser);

        // 重新计算需求进度
        recalculateRequirementProgress(requirementId);
    }

    /**
     * 获取全量标签列表
     *
     * @return List<AddDataCollectionTagDTO>
     */
    public List<DataCollectionTagDTO> getTagList() {
        // 获取所有标签
        List<DataCollectionTag> tagList = dataCollectionTagManager.listAllTags();

        // 转换为DTO
        List<DataCollectionTagDTO> result = Lists.newArrayList();
        for (DataCollectionTag tag : tagList) {
            DataCollectionTagDTO dataCollectionTagDTO = new DataCollectionTagDTO();
            dataCollectionTagDTO.setTagId(tag.getId());
            dataCollectionTagDTO.setTagName(tag.getTagName());
            result.add(dataCollectionTagDTO);
        }
        return result;
    }

    /**
     * 获取需求详情
     *
     * @param dataCollectionRequirementDetailVO 需求详情查询参数
     * @return DataCollectionRequirementDetailDTO
     */
    public DataCollectionRequirementDetailDTO getTaskDetail(DataCollectionRequirementDetailVO dataCollectionRequirementDetailVO) {
        Integer requirementId = dataCollectionRequirementDetailVO.getRequirementId();

        // 1. 根据requirementId查询需求基本信息
        DataCollectionRequirement requirement = dataCollectionRequirementManager.getById(requirementId);
        if (requirement == null) {
            throw new AppException("需求不存在，需求ID: " + requirementId);
        }

        // 2. 查询关联的标签信息
        List<DataCollectionRequirementTag> requirementTags = dataCollectionRequirementTagManager.listByRequirementId(requirementId, false);
        List<DataCollectionRequirementDetailDataTag> relatedTags = Lists.newArrayList();
        if (CollUtil.isNotEmpty(requirementTags)) {
            for (DataCollectionRequirementTag requirementTag : requirementTags) {
                DataCollectionRequirementDetailDataTag dataTag = new DataCollectionRequirementDetailDataTag();
                dataTag.setTagId(requirementTag.getTagId());
                dataTag.setParentId(requirementTag.getParentTagId());
                dataTag.setFeId(requirementTag.getFeId());
                dataTag.setFeParentId(requirementTag.getFeParentId());
                dataTag.setTagName(requirementTag.getTagName());
                dataTag.setTagType(requirementTag.getTagType());
                dataTag.setCount(requirementTag.getCount());
                dataTag.setEnabled(requirementTag.getEnabled());
                relatedTags.add(dataTag);
            }
        }

        // 3. 组装返回数据
        DataCollectionRequirementDetailDTO dataCollectionRequirementDetailDTO = new DataCollectionRequirementDetailDTO();
        dataCollectionRequirementDetailDTO.setRequirementId(requirement.getId());
        dataCollectionRequirementDetailDTO.setRequirementNumber(requirement.getRequirementNumber());
        dataCollectionRequirementDetailDTO.setDescription(requirement.getDescription());
        dataCollectionRequirementDetailDTO.setRequiredDetail(requirement.getRequiredDetail());
        dataCollectionRequirementDetailDTO.setForbiddenDetail(requirement.getForbiddenDetail());
        dataCollectionRequirementDetailDTO.setCreateTime(requirement.getCreateTime());
        dataCollectionRequirementDetailDTO.setCreateUser(requirement.getCreateUser());
        dataCollectionRequirementDetailDTO.setStatus(requirement.getStatus());
        dataCollectionRequirementDetailDTO.setProgress(formatProgress(requirement, requirementTags));
        dataCollectionRequirementDetailDTO.setRelatedTags(relatedTags);
        return dataCollectionRequirementDetailDTO;
    }

    /**
     * 获取需求详情
     *
     * @param dataCollectionRequirementScenePageVO dataCollectionRequirementScenePageVO
     * @return DataCollectionRequirementDetailScene
     */
    public PageDTO<DataCollectionRequirementDetailScene> getRequirementScenePageList(DataCollectionRequirementScenePageVO dataCollectionRequirementScenePageVO) {
        // 构建查询条件
        LambdaQueryWrapper<DataCollectionSceneRequirement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataCollectionSceneRequirement::getRequirementId, dataCollectionRequirementScenePageVO.getRequirementId());
        queryWrapper.eq(DataCollectionSceneRequirement::getLinked, true);

        // 分页查询
        Page<DataCollectionSceneRequirement> page = new Page<>(dataCollectionRequirementScenePageVO.getPageNum(), dataCollectionRequirementScenePageVO.getPageSize());
        IPage<DataCollectionSceneRequirement> pageResult = dataCollectionSceneRequirementManager.getBaseMapper().selectPage(page, queryWrapper);
        List<DataCollectionSceneRequirement> pageResultRecords = pageResult.getRecords();

        // 组装数据
        PageDTO<DataCollectionRequirementDetailScene> pageDTO = new PageDTO<>();
        List<DataCollectionRequirementDetailScene> result = Lists.newArrayList();
        pageDTO.setPageNum((int) pageResult.getCurrent());
        pageDTO.setPageSize((int) pageResult.getSize());
        pageDTO.setPages((int) pageResult.getPages());
        pageDTO.setTotal(pageResult.getTotal());
        pageDTO.setList(result);
        if (CollUtil.isEmpty(pageResultRecords)) {
            return pageDTO;
        }

        // 按场景关联时间倒序排列
        pageResultRecords.sort(Comparator.comparing(DataCollectionSceneRequirement::getCreateTime).reversed());

        // 根据场景ID分组
        List<Integer> sceneIds = pageResultRecords.stream().map(DataCollectionSceneRequirement::getSceneId).toList();
        List<DataCollectionScene> dataCollectionSceneList = dataCollectionSceneManager.listByIds(sceneIds);
        Map<Integer, List<DataCollectionScene>> sceneGroupById = dataCollectionSceneList.stream().collect(Collectors.groupingBy(DataCollectionScene::getId));

        // 组装数据
        for (DataCollectionSceneRequirement scene : pageResultRecords) {
            DataCollectionScene dataCollectionScene = sceneGroupById.get(scene.getSceneId()).get(0);
            DataCollectionRequirementDetailScene sceneDetail = new DataCollectionRequirementDetailScene();
            sceneDetail.setSceneId(dataCollectionScene.getId());
            sceneDetail.setSceneNumber(dataCollectionScene.getSceneNumber());
            sceneDetail.setVehicleName(dataCollectionScene.getVehicleName());

            // 获取场景关联的标签
            List<DataCollectionSceneTag> sceneTagList = dataCollectionSceneTagManager.getBySceneId(dataCollectionScene.getId());
            if (CollUtil.isNotEmpty(sceneTagList)) {
                sceneDetail.setTagNameList(sceneTagList.stream().map(DataCollectionSceneTag::getTagName).collect(Collectors.toList()));
            }
            result.add(sceneDetail);
        }

        return pageDTO;
    }

    /**
     * 编辑需求关联标签
     *
     * @param requirementId 需求ID
     * @param relatedTags 编辑需求关联标签
     * @param modifyUser 修改用户
     */
    private void editRequirementTags(Integer requirementId, List<RequirementDataTagVO> relatedTags, String modifyUser) {
        // 删除需求关联标签
        dataCollectionRequirementTagManager.deleteEnabledTagsByRequirementId(requirementId);

        // 新增关联关系
        if (CollUtil.isNotEmpty(relatedTags)) {
            List<DataCollectionRequirementTag> requirementTags = Lists.newArrayList();
            for (RequirementDataTagVO relatedTag : relatedTags) {
                // 验证标签是否存在
                DataCollectionTag dataCollectionTag = dataCollectionTagManager.getById(relatedTag.getTagId());
                if (dataCollectionTag == null) {
                    throw new AppException("标签不存在，标签ID: " + relatedTag.getTagId());
                }

                // 验证标签名称是否匹配
                if (!dataCollectionTag.getTagName().equals(relatedTag.getTagName())) {
                    throw new AppException("标签名称不匹配，标签ID: " + relatedTag.getTagId());
                }

                // 创建需求标签关联
                DataCollectionRequirementTag requirementTag = new DataCollectionRequirementTag();
                requirementTag.setRequirementId(requirementId);
                requirementTag.setTagId(relatedTag.getTagId());
                requirementTag.setTagName(relatedTag.getTagName());
                requirementTag.setParentTagId(relatedTag.getParentId());
                if (null != relatedTag.getFeId()) {
                    requirementTag.setFeId(relatedTag.getFeId());
                }
                if(null != relatedTag.getFeParentId()) {
                    requirementTag.setFeParentId(relatedTag.getFeParentId());
                }
                requirementTag.setTagType(relatedTag.getTagType());
                requirementTag.setCount(relatedTag.getCount());
                requirementTag.setEnabled(relatedTag.getEnabled());
                requirementTag.setCreateUser(modifyUser);
                requirementTags.add(requirementTag);
            }

            // 批量保存需求标签关联
            dataCollectionRequirementTagManager.saveBatchRequirementTags(requirementTags);
        }
    }

    /**
     * 重新计算需求进度
     *
     * @param requirementId 需求ID
     */
    public void recalculateRequirementProgress(Integer requirementId) {
        // 查询需求关联标签（启用）
        List<DataCollectionRequirementTag> requirementTags = dataCollectionRequirementTagManager.listByRequirementId(requirementId, true);

        // 查询需求关联场景
        List<DataCollectionSceneRequirement> sceneList = dataCollectionSceneRequirementManager.getSceneByRequirementId(requirementId);

        // 计算进度
        Double progress = calculateProgress(requirementTags, sceneList);

        // 更新需求进度
        dataCollectionRequirementManager.updateProgress(requirementId, progress, "system");

        // 更新需求状态
        if (progress >= 1.0) {
            dataCollectionRequirementManager.updateStatusNotClosed(requirementId, DataCollectionStatusEnum.FINISHED.getValue(), "system");
        } else {
            dataCollectionRequirementManager.updateStatusNotClosed(requirementId, DataCollectionStatusEnum.ONGOING.getValue(), "system");
        }
    }

    /**
     * 计算进度，需求计算公式=已审核匹配的标签场景数量÷需求总标签样本数量
     *
     * @param requirementTags requirementTags
     * @param sceneList sceneList
     * @return progress
     */
    private Double calculateProgress(List<DataCollectionRequirementTag> requirementTags, List<DataCollectionSceneRequirement> sceneList) {
        if (CollUtil.isEmpty(requirementTags) || CollUtil.isEmpty(sceneList)) {
            return 0.0;
        }

        // 获取标签名称集合
        Set<String> tagNameSet = requirementTags.stream().map(DataCollectionRequirementTag::getTagName).collect(Collectors.toSet());

        // 获取场景ID列表
        List<Integer> sceneIds = sceneList.stream().map(DataCollectionSceneRequirement::getSceneId).toList();

        // 计算进度，匹配上的场景总数
        int numerator = sceneList.size();
//        int numerator = 0;
//        for (Integer sceneId : sceneIds) {
//            // 查询场景关联的标签
//            List<DataCollectionSceneTag> sceneTags = dataCollectionSceneTagManager.getBySceneId(sceneId);
//            for (DataCollectionSceneTag sceneTag : sceneTags) {
//                if (tagNameSet.contains(sceneTag.getTagName())) {
//                    numerator += 1;
//                    break;
//                }
//            }
//        }

        // 需求总数量
        int denominator = 0;
        for (DataCollectionRequirementTag requirementTag : requirementTags) {
            if (requirementTag.getTagType().equals(DataCollectionTagTypeEnum.CHILDREN.getValue())) {
                denominator += requirementTag.getCount() == null ? 0 : requirementTag.getCount();
            }
        }

        // 避免除数为0
        if (denominator == 0) {
            return 0.0;
        }

        return Math.round((double) numerator / denominator * 100.0) / 100.0;
    }

    /**
     * 计算进度
     *
     * @param dataCollectionRequirement dataCollectionRequirement
     * @param dataCollectionRequirementTagList dataCollectionRequirementTagList
     * @return progress
     */
    public Double formatProgress(DataCollectionRequirement dataCollectionRequirement, List<DataCollectionRequirementTag> dataCollectionRequirementTagList) {
        if (CollUtil.isEmpty(dataCollectionRequirementTagList)) {
            return null;
        }

        // 用户需求中，关联标签若没有定义所需场景数返回null
        Map<String, List<DataCollectionRequirementTag>> groupByTagType = dataCollectionRequirementTagList.stream()
            .filter(DataCollectionRequirementTag::getEnabled)
            .collect(Collectors.groupingBy(DataCollectionRequirementTag::getTagType));
        if (!groupByTagType.containsKey(DataCollectionTagTypeEnum.CHILDREN.getValue())) {
            return null;
        }
        boolean childrenTagHasCount = groupByTagType.get(DataCollectionTagTypeEnum.CHILDREN.getValue()).stream()
            .anyMatch(tag -> tag.getCount() != null && tag.getCount() > 0);
        if (!childrenTagHasCount) {
            return null;
        }

        // 保留两位小数
        return Math.round(dataCollectionRequirement.getProgress() * 100.0) / 100.0;
    }

    /**
     * 查看需求关联场景详情
     *
     * @param dataCollectionSceneDetailVO 场景详情查询参数
     * @return 场景详情
     */
    public DataCollectionSceneDetailDTO getSceneDetail(DataCollectionSceneDetailVO dataCollectionSceneDetailVO) {
        Integer sceneId = dataCollectionSceneDetailVO.getSceneId();

        // 根据sceneId查询场景信息
        DataCollectionScene dataCollectionScene = dataCollectionSceneManager.getById(sceneId);
        if (dataCollectionScene == null) {
            throw new AppException("场景不存在，场景ID: " + sceneId);
        }

        // 查询场景关联的标签
        List<DataCollectionSceneTag> sceneTags = dataCollectionSceneTagManager.getBySceneId(sceneId);

        // 获取场景关联标签
        List<DataCollectionTagDTO> tagList = Lists.newArrayList();
        for (DataCollectionSceneTag sceneTag : sceneTags) {
            DataCollectionTagDTO tag = new DataCollectionTagDTO();
            tag.setTagId(sceneTag.getTagId());
            tag.setTagName(sceneTag.getTagName());
            tagList.add(tag);
        }

        // 构建返回结果
        DataCollectionSceneDetailDTO result = new DataCollectionSceneDetailDTO();
        result.setSceneNumber(dataCollectionScene.getSceneNumber());
        result.setVehicleName(dataCollectionScene.getVehicleName());
        result.setTagList(tagList);
        return result;
    }

    /**
     * 根据查询条件获取数据采集需求详情列表
     * @param requirementListVo 数据采集需求查询条件对象，包含查询所需的筛选参数
     * @return 符合条件的数据采集需求详情DTO集合，若无结果返回空列表
     */
    public List<DataCollectionRequirementPageDTO> getRequirementList(DataCollectionRequirementListVO requirementListVo) {
        // 构建查询条件
        LambdaQueryWrapper<DataCollectionRequirement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.notIn(DataCollectionRequirement::getStatus, DataCollectionStatusEnum.CLOSED.getValue());
        // 时间范围查询
        queryWrapper.between(DataCollectionRequirement::getCreateTime, requirementListVo.getStartTime(), requirementListVo.getEndTime());

        // 需求说明模糊搜索
        if (StringUtils.isNotBlank(requirementListVo.getDescription())) {
            queryWrapper.like(DataCollectionRequirement::getDescription, requirementListVo.getDescription());
        }
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(DataCollectionRequirement::getCreateTime).last("LIMIT 200");
        List<DataCollectionRequirement> listResult = dataCollectionRequirementManager.getBaseMapper().selectList(queryWrapper);
        // 构建返回结果
        if (CollectionUtils.isEmpty(listResult)) {
            return new ArrayList<>();
        }
        return listResult.stream().map(this::convertToPageDTO).collect(Collectors.toList());
    }

    /**
     * 根据用户名获取erp
     *
     * @param username 用户名
     * @return erp
     */
    private String getErpByUsername(String username) {
        UserExtendInfoDTO userExtendInfoDTO = metadataUserApiManager.getUserExtendInfoByName(username);
        if (userExtendInfoDTO == null || userExtendInfoDTO.getJdErp() == null) {
            return username;
        }
        return userExtendInfoDTO.getJdErp();
    }
}