/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.device.jsfapi.domain.vo.devicecommand.ImmediateTaskCreateVO;
import com.jdx.rover.device.jsfapi.service.server.devicecommand.IntelligentDeviceServerDeviceCommandTaskInfoService;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotOperationFollowDTO;
import com.jdx.rover.monitor.entity.device.RobotIssueDO;
import com.jdx.rover.monitor.enums.AlarmLevelEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.device.DeviceAlarmProcessModeEnum;
import com.jdx.rover.monitor.enums.device.DeviceCommandTaskEnum;
import com.jdx.rover.monitor.manager.robot.RobotAbnormalInfoManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.po.robot.RobotAbnormalInfo;
import com.jdx.rover.monitor.repository.redis.robot.RobotAlarmRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotIssueRepository;
import com.jdx.rover.monitor.vo.robot.RobotIssueAdoptVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机器人工单服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotIssueService {

    /**
     * 告警缓存
     */
    public final RobotAlarmRepository robotAlarmRepository;
    /**
     * 工单缓存
     */
    public final RobotIssueRepository robotIssueRepository;
    /**
     * 用户服务
     */
    public final MetadataUserApiManager metadataUserApiManager;

    /**
     * 机器人异常信息服务。
     */
    private final RobotAbnormalInfoManager robotAbnormalInfoManager;

    /**
     * 指令服务
     */
    private final IntelligentDeviceServerDeviceCommandTaskInfoService commandTaskInfoService;

    /**
     * 处理车辆告警
     * @param robotIssueAdoptVo 机器人处理告警信息
     * @return 发送结果
     */
    public HttpResult<String> adoptIssue(RobotIssueAdoptVO robotIssueAdoptVo) {
        if (Objects.isNull(robotIssueAdoptVo)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode(), MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getMessage());
        }
        String deviceName = robotIssueAdoptVo.getDeviceName();
        String userName = JsfLoginUtil.getUsername();
        if (StringUtils.isBlank(deviceName) || StringUtils.isBlank(robotIssueAdoptVo.getAlarmCode())) {
            return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode(), MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getMessage());
        }
        LambdaQueryWrapper<RobotAbnormalInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(RobotAbnormalInfo::getProductKey, robotIssueAdoptVo.getProductKey());
        queryWrapper.eq(RobotAbnormalInfo::getDeviceName, robotIssueAdoptVo.getDeviceName());
        queryWrapper.isNull(RobotAbnormalInfo::getEndTime);
        queryWrapper.eq(RobotAbnormalInfo::getErrorCode, robotIssueAdoptVo.getAlarmCode());
        List<RobotAbnormalInfo> abnormalInfoList = robotAbnormalInfoManager.list(queryWrapper);
        if (CollectionUtils.isEmpty(abnormalInfoList)) {
            return HttpResult.success();
        }
        Map<String, RobotAbnormalInfo> alarmEventDOMap = abnormalInfoList.stream().collect(Collectors.toMap(RobotAbnormalInfo::getErrorCode, Function.identity()));
        RobotAbnormalInfo deviceAlarmEventDo = alarmEventDOMap.get(robotIssueAdoptVo.getAlarmCode());
        if (Objects.isNull(deviceAlarmEventDo) || !StringUtils.equalsAny(deviceAlarmEventDo.getErrorLevel(),
                AlarmLevelEnum.ALARM_URGENT.getValue(), AlarmLevelEnum.ALARM_MAJOR.getValue())) {
            return HttpResult.error("一级告警或二级告警才能处理");
        }
        UserExtendInfoDTO userExtendInfoDto = metadataUserApiManager.getUserExtendInfoByName(userName);
        if (Objects.nonNull(userExtendInfoDto) && StringUtils.isNotBlank(userExtendInfoDto.getJdErp())) {
            addAlarmAdoptUser(robotIssueAdoptVo, userName);
            RobotIssueDO robotIssueDo = robotIssueRepository.get(deviceName);
            Optional.ofNullable(robotIssueDo).ifPresent(issue -> {
                if (MapUtils.isEmpty(issue.getFollowUserMap())) {
                    // 通知到HMI端
                    pushHmiFollowInfo(deviceName, robotIssueAdoptVo.getProductKey(), userExtendInfoDto.getJdErp());
                    issue.setFollowUserMap(new HashMap<>());
                }
                issue.getFollowUserMap().put(robotIssueAdoptVo.getAlarmCode(), userExtendInfoDto.getJdErp());
                robotIssueRepository.set(deviceName, issue);
            });
        }
        return HttpResult.success();
    }

    /**
     * 添加机器人工单关联报警事件。
     * @param deviceName 设备名称。
     * @param addAlarm 新增的报警事件列表。
     */
    public void addRobotIssue(String deviceName, List<AlarmInfoDTO> addAlarm) {
        RobotIssueDO robotIssueDo = robotIssueRepository.get(deviceName);
        if (Objects.isNull(robotIssueDo)) {
            robotIssueDo = new RobotIssueDO();
        }
        robotIssueDo.setRecordTime(new Date());
        robotIssueDo.setFollowUserMap(robotIssueDo.getFollowUserMap());
        robotIssueRepository.set(deviceName, robotIssueDo);
    }

    /**
     * 添加机器人工单关联报警事件。
     * @param deviceName 设备名称。
     */
    public void endRobotIssue(String productType, String deviceName) {
        RobotIssueDO robotIssueDo = robotIssueRepository.get(deviceName);
        if (Objects.isNull(robotIssueDo)) {
            return;
        }
        robotIssueRepository.remove(deviceName);
        // 通知到HMI端
        pushHmiFollowInfo(deviceName, productType, "");
    }

    /**
     * 添加机器人告警并更新异常信息。
     * @param robotIssueAdoptVo 包含设备名称、产品密钥和告警代码的对象。
     */
    private void addAlarmAdoptUser(RobotIssueAdoptVO robotIssueAdoptVo, String userErp) {
        LambdaUpdateWrapper<RobotAbnormalInfo> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(RobotAbnormalInfo::getProductKey, robotIssueAdoptVo.getProductKey());
        updateWrapper.eq(RobotAbnormalInfo::getDeviceName, robotIssueAdoptVo.getDeviceName());
        updateWrapper.isNull(RobotAbnormalInfo::getEndTime);
        updateWrapper.eq(RobotAbnormalInfo::getErrorCode, robotIssueAdoptVo.getAlarmCode());
        updateWrapper.set(RobotAbnormalInfo::getProcessMode, DeviceAlarmProcessModeEnum.LOCAL_PROCESS.getValue());
        updateWrapper.set(RobotAbnormalInfo::getFollowUser, userErp);
        robotAbnormalInfoManager.update(updateWrapper);
    }

    /**
     * 通知到HMI端
     * @param deviceName 设备名称。
     * @param productType 产品类型。
     */
    private void pushHmiFollowInfo(String deviceName, String productType, String erpUser) {
        ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
        createVo.setDeviceName(deviceName);
        createVo.setProductKey(productType);
        createVo.setBlockNo("hmi");
        createVo.setIdentifier(DeviceCommandTaskEnum.CMD_OPERATION_FOLLOW.getValue());
        RobotOperationFollowDTO hmiFollowInfo = new RobotOperationFollowDTO();
        hmiFollowInfo.setFollowUser(erpUser);
        createVo.setCommandArgs(JsonUtils.writeValueAsString(hmiFollowInfo));
        commandTaskInfoService.createImmediateTask(createVo);
    }

}
