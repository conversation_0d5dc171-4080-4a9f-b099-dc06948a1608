/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.robot;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceMessageData;
import com.jdx.rover.monitor.dto.robot.RobotDeviceRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCategoryEnum;
import com.jdx.rover.monitor.enums.device.DeviceRealtimeStateEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.transport.api.domain.dto.status.PropertyChangeDTO;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import org.redisson.api.RTopic;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物联网设备机器人服务
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
public interface IRobotDeviceService {

    /**
     * 批量更新分组信息
     */
    void batchUpdateDeviceGroup(Map<String, List<DeviceMessageData>> mapResult);

    /**
     * 批量更新设备实时信息
     */
    default void batchUpdateDeviceRealtimeInfo(List<String> deviceList) throws Exception{
        List<List<String>> partitionList = Lists.partition(deviceList, 300);
        partitionList.stream().forEach(batchList -> {;
                try {
                    batchUpdateDeviceState(batchList.stream().collect(Collectors.toList()));
                } catch (Exception e) {
                    throw e;
                }
        });
    }

    /**
     * 批量更新设备实时信息
     */
    void batchUpdateDeviceState(List<String> deviceList);

    /**
     * 事件上报
     */
    void eventsReport(TransportDeviceMessage transportDeviceMessage);

    /**
     * 心跳事件上报
     */
    void propertyReport(String transportDeviceMessage);

    /**
     * 事件响应回馈
     */
    void eventsReply(TransportDeviceMessage transportDeviceMessage);

    /**
     * 状态变更消息
     */
    void statusChange(PropertyChangeDTO propertyChangeDto);

    /**
     * 发送机器人地图位置信息到指定主题。
     * @param vehicleMapInfoDto 机器人实时信息DTO对象。
     */
    default void sendRobotMapPosition(RobotMapRealtimeInfoDTO vehicleMapInfoDto) {
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_POSITION_UPDATE.getValue(), vehicleMapInfoDto);
        String topicName = RedisTopicEnum.MAP_ROBOT_POSITION_PREFIX.getValue() + vehicleMapInfoDto.getDeviceName();
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        rTopic.publish(JsonUtils.writeValueAsString(wsResult));
    }


}