package com.jdx.rover.monitor.service.listener.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.dto.message.MessageDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehicleDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.vehicle.single.SingleVehicleTabTypeEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;
import java.io.IOException;
import java.util.Objects;

/**
 * 改变告警信息推送
 *
 * <AUTHOR>
 */
@Slf4j
public class SingleVehicleMessageListener implements MessageListener<String> {
  private Session session;
  private WebsocketClientBO client;

  public SingleVehicleMessageListener(Session session, WebsocketClientBO client) {
    this.session = session;
    this.client = client;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    boolean isSend = false;
    WsResult dto = JsonUtils.readValue(msg, WsResult.class);
    String eventType = dto.getEventType();
    if (Objects.equals(WebsocketEventTypeEnum.SINGLE_VEHICLE.getValue(), eventType)) {
      TypeReference<WsResult<SingleVehicleDTO>> typeReference = new TypeReference<WsResult<SingleVehicleDTO>>() {
      };
      WsResult<SingleVehicleDTO> detailDTO = JsonUtils.readValue(msg, typeReference);
      if (!Objects.equals(this.client.getSingleVehicleTabType(), SingleVehicleTabTypeEnum.DRIVABLE.getValue())) {
        detailDTO.getData().setVehicleDrivableDirection(null);
        msg = JsonUtils.writeValueAsString(detailDTO);
      }
      isSend = true;
    } else if (StringUtils.equalsAny(eventType, WebsocketEventTypeEnum.COMMON_MESSAGE.getValue())) {
      TypeReference<WsResult<MessageDTO>> typeReference = new TypeReference<WsResult<MessageDTO>>() {
      };
      WsResult<MessageDTO> detailDTO = JsonUtils.readValue(msg, typeReference);
      if (detailDTO.getData() != null && Objects.equals(this.client.getUsername(), detailDTO.getData().getUsername())) {
        isSend = true;
      }
    } else if (StringUtils.equalsAny(eventType
        , WebsocketEventTypeEnum.SINGLE_VEHICLE_GPS.getValue()
        , WebsocketEventTypeEnum.SINGLE_VEHICLE_TRAFFIC_LIGHT.getValue()
        , WebsocketEventTypeEnum.SINGLE_VEHICLE_TAKEOVER_ISSUE.getValue()
        , WebsocketEventTypeEnum.SINGLE_VEHICLE_SCHEDULE.getValue()
            , WebsocketEventTypeEnum.SINGLE_VEHICLE_ALARM.getValue()
        , WebsocketEventTypeEnum.REMOTE_CONTROL_POWER_REBOOT.getValue()
        , WebsocketEventTypeEnum.REMOTE_CONTROL_POWER_OFF.getValue()
        , WebsocketEventTypeEnum.REMOTE_NO_SIGNAL_INTERSECTION.getValue()
        , WebsocketEventTypeEnum.SINGLE_VEHICLE_NO_SIGNAL_INTERSECTION.getValue()
        , WebsocketEventTypeEnum.WEB_TERMINAL_COMMAND_DATA.getValue())) {
      isSend = true;
    } else {
      for (SingleVehicleTabTypeEnum itemEnum : SingleVehicleTabTypeEnum.values()) {
        if (Objects.equals(itemEnum.getValue(), this.client.getSingleVehicleTabType()) && Objects.equals(itemEnum.getEventType(), eventType)) {
          isSend = true;
          break;
        }
      }
    }

    if (!isSend) {
      return;
    }
    if (this.session == null || !this.session.isOpen()) {
      log.info("单车页连接已经关闭{}", this.client);
      return;
    }
    synchronized (this.session) {
      try {
        this.session.getBasicRemote().sendText(msg);
      } catch (IOException e) {
        log.info("单车页发送数据失败{}", this.client);
        throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getMessage());
      }
    }
  }

}
