/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import com.jdx.rover.map.api.service.MapInfoJsfService;
import com.jdx.rover.map.api.service.MapOtaJsfService;
import com.jdx.rover.map.api.service.MapVariableJsfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 地图服务jsf配置
 *
 * <AUTHOR>
 * @date 2025/2/20
 */
@Slf4j
@Configuration
@Component
public class MapJsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册 MapInfoJsfService
     */
    @Bean
    public MapInfoJsfService mapInfoJsfService() {
        return jsfConsumerRegister.createConsumerConfig(MapInfoJsfService.class).refer();
    }

    /**
     * 注册 MapOtaJsfService
     */
    @Bean
    public MapOtaJsfService mapOtaJsfService() {
        return jsfConsumerRegister.createConsumerConfig(MapOtaJsfService.class).refer();
    }

    /**
     * 注册 MapVariableJsfService
     */
    @Bean
    public MapVariableJsfService mapVariableJsfService() {
        return jsfConsumerRegister.createConsumerConfig(MapVariableJsfService.class).refer();
    }


}