package com.jdx.rover.monitor.service.listener.redis;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.MonitorStationVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;
import java.io.IOException;

/**
 * 地图页车辆调度信息更新推送
 *
 * <AUTHOR>
 */
@Slf4j
public class MapVehicleMessageListener implements MessageListener<String> {
  private Session session;

  public MapVehicleMessageListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    WsResult<MonitorStationVehicleMapInfoDTO> dto = JsonUtils.readValue(msg, WsResult.class);
    log.info("Listener send vehicle schedule update info {}", msg);
    if (dto == null) {
      return;
    }
    synchronized (this.session) {
      try {
        String data = JsonUtils.writeValueAsString(dto);
        this.session.getBasicRemote().sendText(data);
      } catch (IOException e) {
        log.error("Map schedule update exception", e);
      }
    }
  }
}
