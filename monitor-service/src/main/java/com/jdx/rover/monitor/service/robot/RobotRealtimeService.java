/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.enums.common.EnableEnum;
import com.jdx.rover.metadata.domain.dto.warehouse.WarehouseRobotDetailInfoDTO;
import com.jdx.rover.metadata.domain.vo.warehouse.WarehouseRobotInfoGetVO;
import com.jdx.rover.metadata.jsf.service.warehouse.WarehouseBusinessJsfService;
import com.jdx.rover.monitor.enums.device.DeviceWorkModeEnum;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.monitor.service.robot.alarm.RobotScheduleAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 机器人调度服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotRealtimeService {

    /**
     * 用于访问操作实时状态实体的Mapper接口
     */
    public final RobotRealtimeInfoManager robotRealtimeInfoManager;

    /**
     * 提供仓库业务JSF服务的实例
     */
    public final WarehouseBusinessJsfService warehouseBusinessJsfService;


    /**
     * 提供机器人调度报警服务的实例。
     */
    private final RobotScheduleAlarmService robotScheduleAlarmService;

    /**
     * 设置机器人工作模式
     * @param deviceName 设备名称
     * @param workMode 工作模式
     */
    public void processRobotWorkMode(String deviceName, String workMode) {
        LambdaUpdateWrapper<RobotRealtimeInfo> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(RobotRealtimeInfo::getProductKey, ProductTypeEnum.INTEGRATE.getValue());
        updateWrapper.eq(RobotRealtimeInfo::getDeviceName, deviceName);
        updateWrapper.set(RobotRealtimeInfo::getWorkMode, workMode);
        robotRealtimeInfoManager.update(updateWrapper);
    }

    /**
     * 设置机器人启停用模式
     * @param deviceName 设备名称
     * @param enable 工作模式
     */
    public void processRobotEnableState(String deviceName, Integer deviceId, Integer enable) {
        LambdaUpdateWrapper<RobotRealtimeInfo> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(RobotRealtimeInfo::getDeviceName, deviceName);
        if (Objects.equals(EnableEnum.ENABLE.getEnable(), enable)) {
            WarehouseRobotInfoGetVO robotInfoGetVo = new WarehouseRobotInfoGetVO();
            robotInfoGetVo.setDeviceBaseId(deviceId);
            HttpResult<WarehouseRobotDetailInfoDTO> httpResult = warehouseBusinessJsfService.getWarehouseRobotInfoById(robotInfoGetVo);
            if (HttpResult.isSuccess(httpResult) && Objects.nonNull(httpResult.getData())) {
                updateWrapper.set(RobotRealtimeInfo::getWorkMode,httpResult.getData().getWorkMode());
                updateWrapper.set(RobotRealtimeInfo::getEnable, Boolean.TRUE);
            }
        } else {
            robotScheduleAlarmService.processReportTaskState(deviceName, new Date(), new Date());
            updateWrapper.set(RobotRealtimeInfo::getWorkMode,DeviceWorkModeEnum.DISABLE.getWorkModeType());
            updateWrapper.set(RobotRealtimeInfo::getEnable, Boolean.FALSE);
        }
        robotRealtimeInfoManager.update(updateWrapper);
    }
}
