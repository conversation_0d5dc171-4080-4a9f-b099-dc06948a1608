package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.accident.AccidentFlowDTO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AccidentFlowService {

    @Autowired
    private JmqProducerService jmqProducerService;

    /**
     * 推送事故流程kafka
     * @param accidentNo
     * @param accidentFlowEnum
     */
    public void pushAccidentFlowEvent(String accidentNo, String accidentFlowEnum, String userName) {
        AccidentFlowDTO accidentFlowDTO = new AccidentFlowDTO();
        accidentFlowDTO.setAccidentNo(accidentNo);
        accidentFlowDTO.setAccidentFlowEnum(accidentFlowEnum);
        accidentFlowDTO.setOperator(userName);
        log.info("发送事故流程Kafka:{}",JsonUtils.writeValueAsString(accidentFlowDTO));
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_ACCIDENT_FLOW_EVENT.getTopic(), accidentFlowDTO);
    }
}
