/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.monitor.api.domain.dto.*;
import com.jdx.rover.monitor.dto.MonitorNavibarVehicleRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleStopDTO;
import com.jdx.rover.monitor.dto.MonitorVehicleEnvEnableDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.IssueCacheEntity;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.ScheduleTaskTypeEnum;
import com.jdx.rover.monitor.enums.VehicleRealtimeStateEnum;
import com.jdx.rover.monitor.manager.schedule.VehicleScheduleManager;
import com.jdx.rover.monitor.manager.vehicle.SystemStateManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleVersionManager;
import com.jdx.rover.monitor.repository.redis.IssueCacheRepository;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 获取车辆实时信息。
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class MonitorVehicleRealtimeInfoService {

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private VehicleScheduleManager vehicleScheduleManager;

  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;

  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;

  @Autowired
  private IssueCacheRepository issueCacheRepository;

  @Autowired
  private VehicleManager vehicleManager;

  @Autowired
  private VehicleVersionManager vehicleVersionManager;

  /**
   * <p>
   * 获取车辆实时四路使能信息。
   * </p>
   * 
   * @param
   */
  public MonitorVehicleEnvEnableDTO getVehicleEnvEnable(String vehicleName) {
    MonitorVehicleEnvEnableDTO vehicleEnvEnableDto = new MonitorVehicleEnvEnableDTO();
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (vehicleRealtimeInfoDto == null) {
      return vehicleEnvEnableDto;
    }
    if (vehicleRealtimeInfoDto.getDrivableDirection() != null) {
      vehicleEnvEnableDto.setBack(vehicleRealtimeInfoDto.getDrivableDirection().getEnableBack());
      vehicleEnvEnableDto.setFront(vehicleRealtimeInfoDto.getDrivableDirection().getEnableFront());
      vehicleEnvEnableDto.setLeft(vehicleRealtimeInfoDto.getDrivableDirection().getEnableLeft());
      vehicleEnvEnableDto.setRight(vehicleRealtimeInfoDto.getDrivableDirection().getEnableRight());
    }
    return vehicleEnvEnableDto;
  }

  /**
   * <p>
   * 获取多车页导航栏车辆实时信息。
   * </p>
   *
   * @param
   */
  public HttpResult<MonitorNavibarVehicleRealtimeInfoDTO> getMultiVehicleRealtimeInfo(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    MonitorNavibarVehicleRealtimeInfoDTO realtimeInfoDto = new MonitorNavibarVehicleRealtimeInfoDTO();
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(Lists.newArrayList(vehicleName));
    MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(vehicleName);
    VehicleBasicDTO vehiclebasicDto = vehicleManager.getBasicByName(vehicleName);
    realtimeInfoDto.setBusinessType(Optional.ofNullable(vehiclebasicDto).map(basic -> basic.getBusinessType()).orElse(VehicleBusinessTypeEnum.DISPATCH.getValue()));
    realtimeInfoDto.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
    realtimeInfoDto.setTaskType(TaskType.NOTASK.getTaskType());
    if (scheduleDto != null) {
      realtimeInfoDto.setScheduleState(scheduleDto.getScheduleState());
      realtimeInfoDto.setTaskType(scheduleDto.getTaskType());
    }
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (vehicleRealtimeInfoDto == null || StringUtils.equalsAny(vehicleRealtimeInfoDto.getSystemState(),
            SystemStateEnum.OFFLINE.getSystemState(),SystemStateEnum.CONNECTION_LOST.getSystemState())) {
      return HttpResult.success(realtimeInfoDto);
    }
    realtimeInfoDto.setMileage(getCurrentScheduleFinishedMileage(scheduleDto, vehicleRealtimeInfoDto));
    realtimeInfoDto.setSpeed(vehicleRealtimeInfoDto.getSpeed());
    String vehicleAlarmEvent = vehicleAlarmRepository.getHighPriorityAlarm(vehicleName);
    realtimeInfoDto.setAlarmEvent(vehicleAlarmEvent);
    return HttpResult.success(realtimeInfoDto);
  }

  private Double getCurrentScheduleFinishedMileage(MonitorScheduleDTO scheduleDto, VehicleRealtimeInfoDTO vehicleRealtimeInfoDto ) {
    if (scheduleDto == null) {
      return Double.valueOf(0.0);
    }
    Double totalMileage = scheduleDto.getTotalFinishedMileage() + scheduleDto.getFinishedMileage();
    if (StringUtils.equalsAny(scheduleDto.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(),
            VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(),
            VehicleScheduleState.RETURN.getVehicleScheduleState())) {
      if (vehicleRealtimeInfoDto.getCurrentStopFinishedMileage() != null) {
        totalMileage = totalMileage + vehicleRealtimeInfoDto.getCurrentStopFinishedMileage();
      }
  }
    return totalMileage;
  }

  /**
   * <p>
   * 获取多车页和单车页车辆实时总里程信息。
   * </p>
   *
   */
  public HttpResult<Double> getVehicleScheduleMileage(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(Lists.newArrayList(vehicleName));
    MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(vehicleName);
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (vehicleRealtimeInfoDto == null) {
      return HttpResult.success(Double.valueOf(0.0));
    }
    return HttpResult.success(getCurrentScheduleFinishedMileage(scheduleDto, vehicleRealtimeInfoDto));
  }

  /**
   * 获取车辆实时信息
   */
  public HttpResult getVehicleRealtimeInfo(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (vehicleRealtimeInfoDto == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_ABSENT.getCode(),
              MonitorErrorEnum.ERROR_VEHICLE_ABSENT.getMessage());
    }
    MonitorShadowVehicleRealtimeDTO shadowDto = new MonitorShadowVehicleRealtimeDTO();
    shadowDto.setSystemState(vehicleRealtimeInfoDto.getSystemState());
    MonitorScheduleDTO scheduleDto =
            vehicleScheduleManager.getScheduleDetailMap(Lists.newArrayList(vehicleName)).get(vehicleName);
    if (scheduleDto != null) {
      VehicleScheduleDTO currentSchedule = new VehicleScheduleDTO();
      currentSchedule.setScheduleNo(scheduleDto.getScheduleNo());
      currentSchedule.setStartDateTime(scheduleDto.getStartDateTime());
      List<MonitorScheduleStopDTO> stopList = scheduleDto.getStop();
      if (CollectionUtil.isNotEmpty(stopList)) {
        currentSchedule.setStop(stopList.stream().map(stop -> {
          VehicleScheduleStopDTO dto = new VehicleScheduleStopDTO();
          dto.setName(stop.getStopName());
          dto.setStopAction(stop.getStopAction());
          dto.setTravelStatus(stop.getTravelStatus());
          dto.setWaitingTime(stop.getWaitingTime());
          return dto;
        }).collect(Collectors.toList()));
      }
      shadowDto.setSchedule(currentSchedule);
      shadowDto.setScheduleState(scheduleDto.getScheduleState());
      shadowDto.setTotalMileage(scheduleDto.getGlobalMileage());
      Double finishedMileage = getCurrentScheduleFinishedMileage(scheduleDto, vehicleRealtimeInfoDto);
      scheduleDto.setTotalFinishedMileage(finishedMileage);
      shadowDto.setCurrentScheduleFinishedMileage(finishedMileage);
      shadowDto.setTaskType(Optional.ofNullable(scheduleDto).map(schedule ->
              ScheduleTaskTypeEnum.of(schedule.getTaskType()).getName()).orElse(ScheduleTaskTypeEnum.NOTASK.getName()));
    }
    Map<String, String> versionMap = vehicleVersionManager.getVehicleVersion(vehicleName);
    Map<String, String> cloudVersionMap = vehicleVersionManager.getVehicleCloudVersion(vehicleName);
    VehicleVersionDTO versionDto = new VehicleVersionDTO();
    versionDto.setRoverVersion(versionMap.get(OtaMoudleEnum.ROVER.getName()));
    versionDto.setVideoVersion(versionMap.get(OtaMoudleEnum.VIDEO.getName()));
    versionDto.setAndroidVersion(versionMap.get(OtaMoudleEnum.ANDROID.getName()));
    versionDto.setMapVersion(versionMap.get(OtaMoudleEnum.MAP.getName()));
    versionDto.setRoverCloudVersion(cloudVersionMap.get(OtaMoudleEnum.ROVER.getName()));
    versionDto.setVideoCloudVersion(cloudVersionMap.get(OtaMoudleEnum.VIDEO.getName()));
    versionDto.setAndroidCloudVersion(cloudVersionMap.get(OtaMoudleEnum.ANDROID.getName()));
    shadowDto.setVersion(versionDto);
    if (SystemStateManager.isOfflineOrLost(vehicleRealtimeInfoDto.getSystemState())) {
      return HttpResult.success(shadowDto);
    }
    shadowDto.setVehicleState(vehicleRealtimeInfoDto.getVehicleState());
    shadowDto.setLatitude(vehicleRealtimeInfoDto.getLat());
    shadowDto.setLongitude(vehicleRealtimeInfoDto.getLon());
    shadowDto.setHeading(vehicleRealtimeInfoDto.getHeading());
    shadowDto.setPower(vehicleRealtimeInfoDto.getPower());
    shadowDto.setSpeed(vehicleRealtimeInfoDto.getSpeed());
    shadowDto.setSteerZero(vehicleRealtimeInfoDto.getSteerZero());
    List<String> vehicleAlarm = vehicleAlarmRepository.getAlarmTypeList(vehicleName);
    shadowDto.setAlarmEventList(vehicleAlarm);
    return HttpResult.success(shadowDto);
  }

  /**
   * 获取车辆接管信息
   */
  public MonitorShadowVehicleTakeOverDTO getVehicleTakeOverInfo(String vehicleName) {
    MonitorShadowVehicleTakeOverDTO takeOverDto = new MonitorShadowVehicleTakeOverDTO();
    com.google.common.base.Optional<VehicleTakeOverEntity> op = vehicleTakeOverRepository.getByKey(vehicleName);
    if (op.isPresent()) {
      takeOverDto.setTakeOverUser(op.get().getUserName());
    }
    IssueCacheEntity issueCacheEntity = issueCacheRepository.getByKey(vehicleName);
    if (issueCacheEntity != null) {
      takeOverDto.setIssueState(issueCacheEntity.getIssueState());
    }
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (vehicleRealtimeInfoDto != null) {
      takeOverDto.setVehicleState(vehicleRealtimeInfoDto.getVehicleState());
    }
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(Lists.newArrayList(vehicleName));
    MonitorScheduleDTO scheduleDto = scheduleDtoMap.get(vehicleName);
    if (scheduleDto != null) {
      takeOverDto.setScheduleState(scheduleDto.getScheduleState());
    }
    return takeOverDto;
  }

  /**
   * 获取车辆告警信息
   */
  public MonitorShadowVehicleAlarmDTO getVehicleRealtimeAlarmInfo(String vehicleName) {
    MonitorShadowVehicleAlarmDTO alarmDto = new MonitorShadowVehicleAlarmDTO();
    alarmDto.setVehicleName(vehicleName);
    VehicleRealtimeInfoDTO realtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (realtimeInfoDto == null || StringUtils.equalsAny(realtimeInfoDto.getSystemState(),
            VehicleRealtimeStateEnum.OFFLINE.getValue(),VehicleRealtimeStateEnum.CONNECTION_LOST.getValue())) {
      return alarmDto;
    }
    List<VehicleAlarmDO.VehicleAlarmEventDO> dataInRedis = vehicleAlarmRepository.get(vehicleName);
    alarmDto.setAlarm(dataInRedis.stream().map(data -> {
      SingleVehicleAlarmDTO alarmItem = new SingleVehicleAlarmDTO();
      alarmItem.setAlarmEvent(data.getType());
      alarmItem.setReportTime(data.getReportTime());
      return alarmItem;
    }).collect(Collectors.toList()));
    return alarmDto;
  }
}

