/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.integrate.api.domain.message.AmrCongestionInfoMessage;
import com.jdx.rover.monitor.dto.robot.RobotDeviceRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO;
import com.jdx.rover.monitor.entity.device.RobotMapCongestionInfoDO;
import com.jdx.rover.monitor.entity.device.RobotMapCongestionInfoDO.CongestionArea;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCategoryEnum;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCodeEnum;
import com.jdx.rover.monitor.enums.device.DevicePropertyCodeEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.robot.RobotAlarmRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotCongestionInfoRepository;
import com.jdx.rover.monitor.service.robot.alarm.RobotRealtimeAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机器人调度服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotCongestionInfoService {

    /**
     * 机器人拥堵信息缓存。
     */
    private final RobotCongestionInfoRepository robotCongestionInfoRepository;

    /**
     * 设备状态服务接口
     * 获取指定设备的实时信息
     */
    private final TransportDeviceApiManager transportDeviceApiManager;

    /**
     * 用于访问实时告警的Repo接口
     */
    private final RobotAlarmRepository robotAlarmRepository;

    /**
     * 用于处理实时告警的服务接口。
     */
    private final RobotRealtimeAlarmService robotRealtimeAlarmService;

    /**
     * 机器人实时信息服务。
     */
    private final RobotRealtimeInfoManager robotRealtimeInfoManager;

    /**
     * 处理拥堵信息消息。
     * @param congestionInfoMessage 消息对象。
     * @throws Exception 如果处理过程中出现异常。
     */
    public void processCongestionInfoChange(AmrCongestionInfoMessage congestionInfoMessage) {
        RobotMapCongestionInfoDO oldCongestionInfo = robotCongestionInfoRepository.get(congestionInfoMessage.getMapId());
        // 1、更新缓存
        RobotMapCongestionInfoDO congestionInfoDo = new RobotMapCongestionInfoDO();
        BeanUtil.copyProperties(congestionInfoMessage, congestionInfoDo);
        robotCongestionInfoRepository.set(congestionInfoMessage.getMapId(), congestionInfoDo);
        // 2、 生成机器人拥堵告警
        HashSet<String> oldVehicle = new HashSet<>();
        if (Objects.nonNull(oldCongestionInfo) && CollectionUtils.isNotEmpty(oldCongestionInfo.getCongestionAreaList())) {
            oldCongestionInfo.getCongestionAreaList().stream().map(CongestionArea::getRobotIdList).forEach(robotId -> oldVehicle.addAll(robotId));
        }
        HashMap<String, CongestionArea> newVehicleCongestion = new HashMap<>();
        if (CollectionUtils.isNotEmpty(congestionInfoDo.getCongestionAreaList())) {
            congestionInfoDo.getCongestionAreaList().stream().forEach(area -> {
                area.getRobotIdList().stream().forEach(robotId -> newVehicleCongestion.put(robotId, area));
            });
        }
        robotRealtimeAlarmService.processRobotCongestionAlarm(oldVehicle, newVehicleCongestion.keySet());
        Collection<String> disappearList = CollectionUtils.subtract(oldVehicle, newVehicleCongestion.keySet());
        // 3、推送地图页拥堵区域变更
        if (CollectionUtils.isNotEmpty(disappearList)) {
            disappearList.stream().forEach(vehicle -> {
                String topicName = RedisTopicEnum.MAP_ROBOT_POSITION_PREFIX.getValue() + vehicle;
                RTopic rTopic = RedissonUtils.getRTopic(topicName);
                WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_CONGESTION_INFO_UPDATE.getValue(), new ArrayList<RobotMapRealtimeInfoDTO>());
                rTopic.publish(JsonUtils.writeValueAsString(wsResult));
            });
        }
        Map<String, RobotMapRealtimeInfoDTO> vehicleRealtimeInfoMap = getMapRobotRealtimeUnderCongestion(ProductTypeEnum.INTEGRATE.getValue(), Lists.newArrayList(newVehicleCongestion.keySet())).
                stream().collect(Collectors.toMap(RobotMapRealtimeInfoDTO::getDeviceName, Function.identity()));
        for(Map.Entry<String, CongestionArea> entry : newVehicleCongestion.entrySet()) {
            String vehicle = entry.getKey();
            String topicName = RedisTopicEnum.MAP_ROBOT_POSITION_PREFIX.getValue() + vehicle;
            RTopic rTopic = RedissonUtils.getRTopic(topicName);
            List<RobotMapRealtimeInfoDTO> robotMapRealtimeInfoList = entry.getValue().getRobotIdList().stream().filter(
                    robotId -> !StringUtils.equals(robotId, vehicle)).map(vehicleName -> vehicleRealtimeInfoMap.get(vehicleName)).filter(Objects::nonNull).collect(Collectors.toList());
            WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_CONGESTION_INFO_UPDATE.getValue(), robotMapRealtimeInfoList);
            rTopic.publish(JsonUtils.writeValueAsString(wsResult));
        }

    }

    /**
     * 获取相同拥堵区域的机器人列表。
     * @param productKey 产品标识。
     * @param deviceName 机器人车辆。
     * @return 机器人相同拥堵下车辆列表。
     */
    public List<String> getSameCongestionRobotList(String productKey, String deviceName) {
        List<String> resultList = new ArrayList<>();
        RobotDeviceRealtimeInfoDTO realtimeStateDto = new RobotDeviceRealtimeInfoDTO();
        Map<String, Object> realtimeMap = transportDeviceApiManager.getDeviceStatusDetail(deviceName, DevicePropertyCodeEnum.MONITOR_ROBOT_VERSION.getPropertyList());
        BeanUtil.fillBeanWithMapIgnoreCase(realtimeMap, realtimeStateDto, true);
        if (StringUtils.isBlank(realtimeStateDto.getMapId())) {
            return resultList;
        }
        RobotMapCongestionInfoDO mapCongestionInfoDo = robotCongestionInfoRepository.get(realtimeStateDto.getMapId());
        if (Objects.isNull(mapCongestionInfoDo) || CollectionUtils.isEmpty(mapCongestionInfoDo.getCongestionAreaList())) {
            return resultList;
        }
        Optional<CongestionArea> op = mapCongestionInfoDo.getCongestionAreaList().stream().filter(congestionArea -> CollectionUtils.isNotEmpty(congestionArea.getRobotIdList()) &&
                congestionArea.getRobotIdList().contains(deviceName)).findFirst();
        if (op.isPresent()) {
             return op.get().getRobotIdList().stream().filter(
                     name -> !StringUtils.equals(deviceName, name)).collect(Collectors.toList());
         }
        return resultList;
    }

    /**
     * 获取机器人地图实时拥堵信息。
     * @param productKey 产品标识。
     * @param robotNameList 机器人车辆列表信息。
     * @return 机器人地图实时拥堵信息列表。
     */
    public List<RobotMapRealtimeInfoDTO> getMapRobotRealtimeUnderCongestion(String productKey, List<String> robotNameList) {
        List<RobotMapRealtimeInfoDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(robotNameList)) {
            return resultList;
        }
        LambdaQueryWrapper<RobotRealtimeInfo> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(RobotRealtimeInfo::getProductKey, productKey);
        lambdaQueryWrapper.in(RobotRealtimeInfo::getDeviceName, robotNameList);
        // 这里必填的
        lambdaQueryWrapper.select(RobotRealtimeInfo::getDeviceName, RobotRealtimeInfo::getRemarkName, RobotRealtimeInfo::getGroupName,  RobotRealtimeInfo::getGroupLevelName, RobotRealtimeInfo::getRealtimeStatus);
        List<RobotRealtimeInfo> robotRealtimeInfos = robotRealtimeInfoManager.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(robotRealtimeInfos)) {
            return resultList;
        }
        Map<String, RobotRealtimeInfo> robotRealtimeInfoMap = robotRealtimeInfos.stream().collect(Collectors.toMap(RobotRealtimeInfo::getDeviceName, Function.identity()));
        List<Map<String, Object>> mapStatusResult = transportDeviceApiManager.getDeviceStatusList(robotNameList, DevicePropertyCodeEnum.MONITOR_ROBOT_STATUS.getPropertyList());
        Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> robotAlarmDMap = robotAlarmRepository.listMap(robotNameList);
        if (CollectionUtils.isNotEmpty(mapStatusResult)) {
            return mapStatusResult.stream().map(data -> {
                RobotMapRealtimeInfoDTO realtimeStateDto = new RobotMapRealtimeInfoDTO();
                BeanUtil.fillBeanWithMapIgnoreCase(data, realtimeStateDto, true);
                RobotRealtimeInfo robotRealtimeInfo = robotRealtimeInfoMap.get(realtimeStateDto.getDeviceName());
                if (Objects.nonNull(robotRealtimeInfo)) {
                    realtimeStateDto.setRemarkName(robotRealtimeInfo.getRemarkName());
                    realtimeStateDto.setProductKey(robotRealtimeInfo.getProductKey());
                }
                realtimeStateDto.setAlarmState(DeviceAlarmCategoryEnum.NORMAL.getValue());
                List<RobotAlarmDO.DeviceAlarmEventDO> robotAlarmDo = robotAlarmDMap.get(realtimeStateDto.getDeviceName());
                    if (CollectionUtils.isNotEmpty(robotAlarmDo)) {
                    Optional<DeviceAlarmCategoryEnum> op = robotAlarmDo.stream().map(alarmEvent ->
                            DeviceAlarmCategoryEnum.getByValue(DeviceAlarmCodeEnum.of(alarmEvent.getErrorCode()).getCategory())).filter(Objects::nonNull).sorted(Comparator.comparingInt(Enum::ordinal)).findFirst();
                    op.ifPresent(alarmCategory -> realtimeStateDto.setAlarmState(alarmCategory.getValue()));
                }
                return realtimeStateDto;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
}
