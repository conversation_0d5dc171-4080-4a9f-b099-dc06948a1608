/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.service.datacollection;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.amazonaws.HttpMethod;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.api.domain.enums.RoverBucketEnum;
import com.jdx.rover.monitor.common.exception.AppException;
import com.jdx.rover.monitor.common.utils.jts.GeometryUtils;
import com.jdx.rover.monitor.common.utils.jts.WktUtils;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionSceneDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionSceneLinkRequirementDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionTaskDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionTaskPageDTO;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionSceneStatusEnum;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionStatusEnum;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionTaskStatusEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.config.DuccMonitorProperties;
import com.jdx.rover.monitor.manager.datacollection.*;
import com.jdx.rover.monitor.manager.shadow.ShadowVehicleRouteManager;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.po.datacollection.*;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.repository.util.PageUtils;
import com.jdx.rover.monitor.service.tencent.TencentMapService;
import com.jdx.rover.monitor.vo.datacollection.*;
import com.jdx.rover.monitor.vo.datacollection.ws.DataCollectionTaskVehicleTopicMsg;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.shadow.api.domain.dto.ShadowVehicleRouteInfoDTO;
import com.jdx.rover.shadow.api.domain.vo.ShadowVehicleRouteVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 数采车质检接口
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionQcService {

    /**
     * 管理数据采集场景的增删改查等操作
     */
    private final DataCollectionSceneManager dataCollectionSceneManager;

    /**
     * 管理数据采集任务的增删改查等操作
     */
    private final DataCollectionTaskManager dataCollectionTaskManager;

    /**
     * 管理数据采集场景标签的增删改查等操作
     */
    private final DataCollectionSceneTagManager dataCollectionSceneTagManager;

    /**
     * 管理数据采集标签的增删改查等操作
     */
    private final DataCollectionTagManager dataCollectionTagManager;

    /**
     * 管理数据采集需求的增删改查等操作
     */
    private final DataCollectionRequirementManager dataCollectionRequirementManager;

    /**
     * 管理数据采集需求标签的增删改查等操作
     */
    private final DataCollectionRequirementTagManager dataCollectionRequirementTagManager;

    /**
     * 管理数据采集场景需求的增删改查等操作
     */
    private final DataCollectionSceneRequirementManager dataCollectionSceneRequirementManager;

    /**
     * 管理车辆实时数据的存储库操作
     */
    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    /**
     * 提供腾讯地图相关服务功能，如地理位置解析和路径规划等
     */
    private final TencentMapService tencentMapService;

    /**
     * 管理影子车辆行驶路径数据，提供路径信息的增删改查等操作
     */
    private final ShadowVehicleRouteManager vehicleRouteManager;

    /**
     * S3配置信息
     */
    private final S3Properties s3Properties;

    /**
     * 提供中文自然语言处理功能，如分词、关键词提取等
     */
    private final DuccMonitorProperties duccMonitorProperties;

    /**
     * DataCollectionService
     */
    private final DataCollectionService dataCollectionService;

    /**
     * 1、新增或结束数据采集任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDataCollectionTask(String vehicleName, Date startTime, Date endTime) {
        // 获取开机或关机时刻车辆实时位置
        String address = "";
        VehicleRealtimeInfoDTO realtimeInfo = vehicleRealtimeRepository.get(vehicleName);
        if (Objects.nonNull(realtimeInfo) && Objects.nonNull(realtimeInfo.getLat())
                && Objects.nonNull(realtimeInfo.getLon())) {
            if (!TransformUtility.isOutOfChina(realtimeInfo.getLat(), realtimeInfo.getLon())) {
                address = StringUtils.substringBefore(tencentMapService.wgs84ToAddress(realtimeInfo.getLat(), realtimeInfo.getLon()), "市");
            }
        }
        if (Objects.nonNull(startTime) && Objects.isNull(endTime)) {
            // 1、PDU开机事件，关闭之前未结束的采集任务
            endCollectionTask(vehicleName, new Date(), address);
            // 2、创建新的采集任务
            DataCollectionTask dataCollectionTask = new DataCollectionTask();
            dataCollectionTask.setVehicleName(vehicleName);
            dataCollectionTask.setStartTime(startTime);
            dataCollectionTask.setCreateTime(new Date());
            dataCollectionTask.setAddress(address);
            dataCollectionTask.setStatus(DataCollectionTaskStatusEnum.STANDBY.getValue());
            dataCollectionTaskManager.save(dataCollectionTask);
        } else if (Objects.isNull(startTime) && Objects.nonNull(endTime)) {
            // PDU关机事件
            endCollectionTask(vehicleName, endTime, address);
            // 事务提交之后，再去拉取路径
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendTaskFinishedTopic(vehicleName);
                }
            });
        }
    }

    /**
     * 2、分页获取数据采集任务列表
     * @param dataCollectionTaskPageVo 数据采集任务分页查询条件
     * @return 包含数据采集任务分页结果的PageDTO对象
     */
    public PageDTO<DataCollectionTaskPageDTO> getPageCollectionTask(DataCollectionTaskPageVO dataCollectionTaskPageVo) {
        LambdaQueryWrapper<DataCollectionTask> queryWrapper = new LambdaQueryWrapper<>();
        // 时间范围查询
        queryWrapper.between(DataCollectionTask::getStartTime, dataCollectionTaskPageVo.getStartTime(), dataCollectionTaskPageVo.getEndTime());
        // 车辆名称查询
        queryWrapper.like(StringUtils.isNotBlank(dataCollectionTaskPageVo.getVehicleName()), DataCollectionTask::getVehicleName, dataCollectionTaskPageVo.getVehicleName());
        // 按创建时间倒序排列
        queryWrapper.isNotNull(DataCollectionTask::getEndTime);
        queryWrapper.eq(DataCollectionTask::isLinkedScene, Boolean.TRUE);
        queryWrapper.orderBy(true, false, DataCollectionTask::getStatus)  // 第一个排序字段，FINISHED在后
                .orderBy(true, false, DataCollectionTask::getStartTime); // 第二个排序字段，降序
        // 分页查询
        Page<DataCollectionTask> page = new Page<>(dataCollectionTaskPageVo.getPageNum(), dataCollectionTaskPageVo.getPageSize());
        IPage<DataCollectionTask> pageResult = dataCollectionTaskManager.page(page, queryWrapper);
        // 转换为PageDTO
        return PageUtils.convertPageDTO(pageResult, this::convertToPageDTO);
    }

    /**
     * 3、获取数据采集任务详情
     * @param dataCollectionTaskDetailVo 数据采集任务详情视图对象，包含查询所需的参数信息
     * @return 数据采集任务数据传输对象，包含任务详情数据
     */
    public DataCollectionTaskDTO getCollectionTaskDetail(DataCollectionTaskDetailVO dataCollectionTaskDetailVo) {
        DataCollectionTaskDTO taskDto = new DataCollectionTaskDTO();
        DataCollectionTask dataCollectionTask = dataCollectionTaskManager.getById(dataCollectionTaskDetailVo.getCollectionTaskId());
        if (Objects.isNull(dataCollectionTask)) {
            return taskDto;
        }
        taskDto.setId(dataCollectionTask.getId());
        taskDto.setDrivePath(dataCollectionTask.getDrivePath());
        Date startTime = Optional.ofNullable(dataCollectionTask.getStartTime()).orElse(new Date());
        Date endTime = Optional.ofNullable(dataCollectionTask.getEndTime()).orElse(new Date());
        if (StringUtils.isBlank(dataCollectionTask.getDrivePath())) {
            if (DateUtil.between(startTime, new Date(), DateUnit.MINUTE) > 5) {
                ShadowVehicleRouteVO shadowVehicleRouteVo = new ShadowVehicleRouteVO();
                shadowVehicleRouteVo.setStartTime(startTime);
                shadowVehicleRouteVo.setEndTime(endTime);
                shadowVehicleRouteVo.setVehicleName(dataCollectionTask.getVehicleName());
                ShadowVehicleRouteInfoDTO routeInfoDto = vehicleRouteManager.getVehicleHistoryRoute(shadowVehicleRouteVo);
                if (Objects.nonNull(routeInfoDto) && StringUtils.isNotBlank(routeInfoDto.getPath())) {
                    try {
                        taskDto.setDrivePath(GeometryUtils.getGeoJsonStr(String.valueOf(dataCollectionTask.getId()), WktUtils.readWkt(routeInfoDto.getPath()), new HashMap<>()));
                    }catch (Exception e) {
                        log.error("获取轨迹失败", e);
                    }
                }
            }
        }
        taskDto.setSceneList(new ArrayList<>());
        LambdaQueryWrapper<DataCollectionScene> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(DataCollectionScene::getVehicleName, dataCollectionTask.getVehicleName());
        lambdaQueryWrapper.between(DataCollectionScene::getReportTime, startTime, endTime);
        lambdaQueryWrapper.orderByDesc(DataCollectionScene::getReportTime);
        List<DataCollectionScene> sceneList = dataCollectionSceneManager.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(sceneList)) {
            taskDto.setSceneList(sceneList.stream().map(scene -> {
                DataCollectionTaskDTO.DataCollectionTaskLinkScene linkScene = new DataCollectionTaskDTO.DataCollectionTaskLinkScene();
                linkScene.setSceneId(scene.getId());
                linkScene.setStatus(scene.getStatus());
                try {
                    if(StringUtils.isNotBlank(scene.getDrivePath())) {
                        linkScene.setPath(scene.getDrivePath());
                    }
                }catch (Exception e) {
                    log.error("获取场景轨迹失败", e);
                }
                linkScene.setStartTime(scene.getStartTime());
                linkScene.setEndTime(scene.getEndTime());
                linkScene.setReportTime(scene.getReportTime());
                return linkScene;
            }).collect(Collectors.toList()));
        }
        return taskDto;
    }

    /**
     * 4、获取任务场景详细信息
     * @param dataCollectionSceneVo 数据收集场景视图对象，包含查询所需参数
     * @return 数据收集场景数据传输对象，包含任务场景的详细数据
     */
    public DataCollectionSceneDTO getTaskSceneDetail(DataCollectionSceneVO dataCollectionSceneVo) {
        DataCollectionSceneDTO sceneDto = new DataCollectionSceneDTO();
        DataCollectionScene dataCollectionScene = dataCollectionSceneManager.getById(dataCollectionSceneVo.getSceneId());
        if (Objects.isNull(dataCollectionScene)) {
            return sceneDto;
        }
        sceneDto.setSceneId(dataCollectionScene.getId());
        sceneDto.setStatus(dataCollectionScene.getStatus());
        String keyword = Optional.ofNullable(dataCollectionScene.getAudioRecognitionKeyWords()).orElse("");
        sceneDto.setKeywords(Arrays.asList(keyword.split(";")));
        if (StringUtils.isNotBlank(dataCollectionScene.getAudioFileKey())) {
            final Date expiration = DateUtil.offsetHour(new Date(), 24); // 链接有效期24小时
            URL downloadUrl = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(),
                    s3Properties.getOutEndpoint(), RoverBucketEnum.ROVER_OPERATION.getName(), dataCollectionScene.getAudioFileKey(), HttpMethod.GET, expiration);
            sceneDto.setAudioFileKey(downloadUrl.toString());
        }
        sceneDto.setAudioRecognitionResult(dataCollectionScene.getAudioRecognitionResult());
        sceneDto.setMatchedTagList(new ArrayList<>());
        sceneDto.setSceneNumber(dataCollectionScene.getSceneNumber());
        LambdaQueryWrapper<DataCollectionSceneTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataCollectionSceneTag::getSceneId, dataCollectionScene.getId());
        List<DataCollectionSceneTag> sceneTags = dataCollectionSceneTagManager.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(sceneTags)) {
            sceneDto.setMatchedTagList(sceneTags.stream().map(scene -> {
                DataCollectionSceneDTO.DataCollectionTag matchedTag = new DataCollectionSceneDTO.DataCollectionTag();
                matchedTag.setTagId(scene.getTagId());
                matchedTag.setTagName(scene.getTagName());
                return matchedTag;
            }).collect(Collectors.toList()));
        }
        return sceneDto;
    }

    /**
     * 5、添加场景标签
     * @param dataCollectionSceneAddTagVo 数据采集场景添加标签值对象，包含需要添加的标签信息
     * @return 返回添加操作的结果状态码
     */
    public Integer addSceneTag(DataCollectionSceneAddTagVO dataCollectionSceneAddTagVo) {
        DataCollectionScene dataCollectionScene = dataCollectionSceneManager.getById(dataCollectionSceneAddTagVo.getSceneId());
        if (Objects.isNull(dataCollectionScene)) {
            throw new AppException("输入场景不存在");
        }
        LambdaQueryWrapper<DataCollectionSceneTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataCollectionSceneTag::getSceneId, dataCollectionScene.getId());
        queryWrapper.in(DataCollectionSceneTag::getTagId, dataCollectionSceneAddTagVo.getTagId());
        List<DataCollectionSceneTag> sceneTags = dataCollectionSceneTagManager.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(sceneTags)) {
            throw new AppException("该标签已存在");
        }
        dataCollectionSceneAddTagVo.getTagId().stream().forEach(tagId -> {
            DataCollectionSceneTag dataCollectionSceneTag = new DataCollectionSceneTag();
            dataCollectionSceneTag.setSceneId(dataCollectionSceneAddTagVo.getSceneId());
            dataCollectionSceneTag.setTagId(tagId);
            DataCollectionTag tag = dataCollectionTagManager.getById(tagId);
            if(Objects.nonNull(tag)) {
                dataCollectionSceneTag.setTagName(tag.getTagName());
            }
            dataCollectionSceneTag.setCreateTime(new Date());
            dataCollectionSceneTagManager.save(dataCollectionSceneTag);
        });
        return dataCollectionSceneAddTagVo.getSceneId();
    }

    /**
     * 6、获取匹配的需求列表
     * @param dataCollectionSceneDetailVo 数据采集场景详情视图对象，包含场景相关信息
     * @return 匹配的需求DTO列表，包含符合条件的需求信息
     */
    public List<DataCollectionSceneLinkRequirementDTO> getMatchedRequirementList(DataCollectionSceneDetailVO dataCollectionSceneDetailVo) {
        LinkedList<DataCollectionSceneLinkRequirementDTO> resultList = new LinkedList<>();
        // 1、 获取场景关联的标签列表
        List<DataCollectionSceneTag> sceneTagList = dataCollectionSceneTagManager.getBySceneId(dataCollectionSceneDetailVo.getSceneId());
        List<String> tagNameList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sceneTagList)) {
            tagNameList = sceneTagList.stream().map(DataCollectionSceneTag::getTagName).collect(Collectors.toList());
        }
        List<DataCollectionSceneRequirement> storeSceneRequirementList = dataCollectionSceneRequirementManager.getRequirementBySceneId(dataCollectionSceneDetailVo.getSceneId())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<Integer> hasLinkedRequireId = storeSceneRequirementList.stream().map(DataCollectionSceneRequirement::getRequirementId).collect(Collectors.toList());
        // 2、分拆出手动添加的，按创建时间倒序
        List<DataCollectionSceneRequirement> manualList = storeSceneRequirementList.stream().filter(requirement -> Objects.equals(requirement.getManual(), Boolean.TRUE)).sorted(new Comparator<DataCollectionSceneRequirement>() {
            @Override
            public int compare(DataCollectionSceneRequirement o1, DataCollectionSceneRequirement o2) {
                return o1.getCreateTime().compareTo(o2.getCreateTime());
            }
        }).collect(Collectors.toList());

        Map<Integer, DataCollectionRequirement> linkedRequireMentMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hasLinkedRequireId)) {
            linkedRequireMentMap = dataCollectionRequirementManager.listByIds(hasLinkedRequireId).stream().collect(Collectors.toMap(DataCollectionRequirement::getId, Function.identity()));
        }

        // 3、非手动添加的的需求列表按照按照符合度排序
        List<DataCollectionSceneRequirement> linkedList = storeSceneRequirementList.stream().filter(requirement -> Objects.equals(requirement.getManual(), Boolean.FALSE)).collect(Collectors.toList());
        List<DataCollectionRequirementTag> requirementTagName =
                dataCollectionRequirementTagManager.listByRequirementIdCollAndEnable(hasLinkedRequireId, true);
        Map<Integer, Set<String>> linkedRequireMentTagMap =
                requirementTagName.stream().collect(Collectors.groupingBy(DataCollectionRequirementTag::getRequirementId,  TreeMap::new,   Collectors.mapping(DataCollectionRequirementTag::getTagName,Collectors.toSet())));

        // 4、获取场景关联的标签列表
        for (DataCollectionSceneRequirement linked : linkedList) {
            DataCollectionSceneLinkRequirementDTO result = new DataCollectionSceneLinkRequirementDTO();
            Integer requirementId = linked.getRequirementId();
            DataCollectionRequirement requirement = linkedRequireMentMap.getOrDefault(requirementId, new DataCollectionRequirement());
            result.setRequirementId(requirementId);
            result.setDescription(requirement.getDescription());
            result.setRequiredDetail(requirement.getRequiredDetail());
            result.setForbiddenDetail(requirement.getForbiddenDetail());
            Set<String> requirementTagList = linkedRequireMentTagMap.getOrDefault(requirementId, new HashSet<>());
            result.setTagNameList(Lists.newArrayList(requirementTagList));
            result.setOverlap(getOverlapQualified(tagNameList, requirementTagList));
            result.setHasAssociated(linked.getLinked());
            resultList.add(result);
        }

        // 4、获取标签关联的需求
        List<DataCollectionSceneLinkRequirementDTO> searchRequirementList = searchAssistRequirement(dataCollectionSceneDetailVo.getSceneId(), hasLinkedRequireId);
        resultList.addAll(searchRequirementList);
        // 5、合并结果
        resultList.sort(Comparator.comparing(DataCollectionSceneLinkRequirementDTO::getOverlap).reversed());
        // 将人工添加的按创建时间倒序排在前面
       for (DataCollectionSceneRequirement manual : manualList) {
            DataCollectionSceneLinkRequirementDTO result = new DataCollectionSceneLinkRequirementDTO();
            Integer requirementId = manual.getRequirementId();
            DataCollectionRequirement requirement = linkedRequireMentMap.get(requirementId);
            result.setRequirementId(requirementId);
            result.setDescription(requirement.getDescription());
            result.setRequiredDetail(requirement.getRequiredDetail());
            result.setForbiddenDetail(requirement.getForbiddenDetail());
            Set<String> requirementTagList = linkedRequireMentTagMap.getOrDefault(requirementId, new HashSet<>());
            result.setTagNameList(Lists.newArrayList(requirementTagList));
            result.setOverlap(getOverlapQualified(tagNameList, requirementTagList));
            result.setHasAssociated(manual.getLinked());
            resultList.addFirst(result);
        }
        return resultList;
    }

    /**
     * 7、场景（取消）关联需求
     * @param sceneLinkRequirementVo 包含场景与需求关联信息的VO对象
     */
    public void associateSceneAndRequirement(DataCollectionSceneLinkRequirementVO sceneLinkRequirementVo) {
        // 1、关联场景与需求
        if(sceneLinkRequirementVo.getLinked()) {
            LambdaQueryWrapper<DataCollectionSceneRequirement> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DataCollectionSceneRequirement::getSceneId, sceneLinkRequirementVo.getSceneId());
            queryWrapper.eq(DataCollectionSceneRequirement::getRequirementId, sceneLinkRequirementVo.getRequirementId());
            List<DataCollectionSceneRequirement> dataList = dataCollectionSceneRequirementManager.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(dataList)) {
                // 如果已经手动添加的但是没点关联更新状态
                LambdaUpdateWrapper<DataCollectionSceneRequirement> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(DataCollectionSceneRequirement::getSceneId, sceneLinkRequirementVo.getSceneId());
                updateWrapper.eq(DataCollectionSceneRequirement::getRequirementId, sceneLinkRequirementVo.getRequirementId());
                updateWrapper.set(DataCollectionSceneRequirement::getLinked, Boolean.TRUE);
                dataCollectionSceneRequirementManager.update(updateWrapper);
                // 更新需求进度
                dataCollectionService.recalculateRequirementProgress(sceneLinkRequirementVo.getRequirementId());
                return;
            }
            DataCollectionSceneRequirement sceneRequirement = new DataCollectionSceneRequirement();
            sceneRequirement.setRequirementId(sceneLinkRequirementVo.getRequirementId());
            sceneRequirement.setSceneId(sceneLinkRequirementVo.getSceneId());
            sceneRequirement.setCreateTime(new Date());
            sceneRequirement.setLinked(sceneLinkRequirementVo.getLinked());
            sceneRequirement.setCreateTime(new Date());
            dataCollectionSceneRequirementManager.save(sceneRequirement);
        } else {
            LambdaUpdateWrapper<DataCollectionSceneRequirement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DataCollectionSceneRequirement::getSceneId, sceneLinkRequirementVo.getSceneId());
            updateWrapper.eq(DataCollectionSceneRequirement::getRequirementId, sceneLinkRequirementVo.getRequirementId());
            updateWrapper.set(DataCollectionSceneRequirement::getLinked, Boolean.FALSE);
            dataCollectionSceneRequirementManager.update(updateWrapper);
        }
//        // 2、反查关联任务
//        DataCollectionScene scene = dataCollectionSceneManager.getById(sceneLinkRequirementVo.getSceneId());
//        if (Objects.isNull(scene)) {
//            return;
//        }
//        DataCollectionTask task = dataCollectionTaskManager.getCoverSceneTask(scene.getVehicleName(),scene.getReportTime());
//        if(Objects.isNull(task)) {
//            return;
//        }
//        // 3、更新任务状态
//        task.setStatus(DataCollectionTaskStatusEnum.PROCESS.getValue());
//        dataCollectionTaskManager.updateById(task);

        // 更新需求进度
        dataCollectionService.recalculateRequirementProgress(sceneLinkRequirementVo.getRequirementId());
    }

    /**
     * 8、完成场景质检
     * @param sceneDetailVo 场景详情数据对象，包含任务执行所需的场景详细信息
     */
    public void completeSceneTask(DataCollectionSceneDetailVO sceneDetailVo) {
        DataCollectionScene scene = dataCollectionSceneManager.getById(sceneDetailVo.getSceneId());
        if (Objects.isNull(scene)) {
            return;
        }
        // 1、更新场景状态为已完成
        scene.setStatus(DataCollectionSceneStatusEnum.FINISHED.getValue());
        dataCollectionSceneManager.updateById(scene);
        // 2、反查关联任务是否全部完成质检，更新任务状态
        DataCollectionTask task = dataCollectionTaskManager.getCoverSceneTask(scene.getVehicleName(),scene.getReportTime());
        if(Objects.isNull(task) || Objects.isNull(task.getEndTime())) {
            return;
        }
        LambdaQueryWrapper<DataCollectionScene> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(DataCollectionScene::getVehicleName, scene.getVehicleName());
        lambdaQueryWrapper.between(DataCollectionScene::getReportTime, task.getStartTime(), task.getEndTime());
        lambdaQueryWrapper.eq(DataCollectionScene::getStatus, DataCollectionSceneStatusEnum.STANDBY.getValue());
        List<DataCollectionScene> sceneList = dataCollectionSceneManager.list(lambdaQueryWrapper);
        // 3、更新任务状态为已完成
        if (CollectionUtils.isEmpty(sceneList) || sceneList.stream().filter(processScene ->
                !Objects.equals(processScene.getId(), scene.getId())).count() == 0) {
            task.setStatus(DataCollectionTaskStatusEnum.FINISHED.getValue());
            dataCollectionTaskManager.updateById(task);
        }
    }

    /**
     * 9、删除场景标签
     * @param dataCollectionSceneDeleteTagVo 数据采集场景添加标签值对象，包含需要添加的标签信息
     * @return 返回添加操作的结果状态码
     */
    public Integer deleteSceneTag(DataCollectionSceneDeleteTagVO dataCollectionSceneDeleteTagVo) {
        DataCollectionScene dataCollectionScene = dataCollectionSceneManager.getById(dataCollectionSceneDeleteTagVo.getSceneId());
        if (Objects.isNull(dataCollectionScene)) {
            throw new AppException("输入场景不存在");
        }
        LambdaUpdateWrapper<DataCollectionSceneTag> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.eq(DataCollectionSceneTag::getSceneId, dataCollectionSceneDeleteTagVo.getSceneId());
        lambdaUpdateWrapper.eq(DataCollectionSceneTag::getTagId, dataCollectionSceneDeleteTagVo.getTagId());
        lambdaUpdateWrapper.set(DataCollectionSceneTag::getDeleted, new Date());
        dataCollectionSceneTagManager.update(lambdaUpdateWrapper);
        return dataCollectionScene.getId();
    }

    /**
     * 10、添加匹配的数据收集场景链接需求。
     * @param sceneLinkRequirementVo 数据收集场景链接需求详细信息
     * @return 添加后的数据收集场景链接需求列表
     */
    public List<DataCollectionSceneLinkRequirementDTO> addMatchedRequirement(DataCollectionSceneLinkRequirementVO sceneLinkRequirementVo) {
        String userName = JsfLoginUtil.getUsername();
        List<DataCollectionSceneRequirement> sceneRequirementList = dataCollectionSceneRequirementManager.getRequirementBySceneId(sceneLinkRequirementVo.getSceneId());
        // 1、获取场景关联的标签列表
        Optional<DataCollectionSceneRequirement> isPresent =
                sceneRequirementList.stream().filter(requirement -> Objects.equals(sceneLinkRequirementVo.getRequirementId(), requirement.getRequirementId())).findFirst();
        if (isPresent.isPresent()) {
            // 更新状态
            DataCollectionSceneRequirement sceneRequirement = isPresent.get();
            sceneRequirement.setManual(Boolean.TRUE);
            dataCollectionSceneRequirementManager.updateById(sceneRequirement);
        } else {
            DataCollectionSceneRequirement sceneRequirement = new DataCollectionSceneRequirement();
            sceneRequirement.setSceneId(sceneLinkRequirementVo.getSceneId());
            sceneRequirement.setRequirementId(sceneLinkRequirementVo.getRequirementId());
            sceneRequirement.setCreateTime(new Date());
            sceneRequirement.setCreateUser(userName);
            sceneRequirement.setManual(Boolean.TRUE);
            sceneRequirement.setLinked(Boolean.FALSE);
            dataCollectionSceneRequirementManager.save(sceneRequirement);
        }
        DataCollectionSceneDetailVO detail = new DataCollectionSceneDetailVO();
        detail.setSceneId(sceneLinkRequirementVo.getSceneId());
        return getMatchedRequirementList(detail);
    }

    /**
     * 结束指定车辆的数据采集任务并更新相关信息
     * @param vehicleName 车辆名称，用于查找未完成的数据采集任务
     * @param endTime 任务结束时间，用于更新任务结束时间字段
     * @param address 任务结束地址，若不为空则更新任务地址字段
     */
    private void endCollectionTask(String vehicleName, Date endTime, String address) {
        DataCollectionTask dataCollectionTask = dataCollectionTaskManager.getUnfinishedTaskByVehicleName(vehicleName);
        if (dataCollectionTask != null) {
            dataCollectionTask.setEndTime(endTime);
            if (StringUtils.isNotBlank(address)) {
                dataCollectionTask.setAddress(address);
            }
            ShadowVehicleRouteVO shadowVehicleRouteVo = new ShadowVehicleRouteVO();
            shadowVehicleRouteVo.setStartTime(dataCollectionTask.getStartTime());
            shadowVehicleRouteVo.setEndTime(endTime);
            shadowVehicleRouteVo.setVehicleName(dataCollectionTask.getVehicleName());
            ShadowVehicleRouteInfoDTO routeInfoDto = vehicleRouteManager.getVehicleHistoryRoute(shadowVehicleRouteVo);
            if (Objects.nonNull(routeInfoDto) && StringUtils.isNotBlank(routeInfoDto.getPath())) {
                try {
                    String path = GeometryUtils.getGeoJsonStr(dataCollectionTask.getVehicleName(), WktUtils.readWkt(routeInfoDto.getPath()), new HashMap<>());
                    if (path.length() < 1000) {
                        dataCollectionTask.setDrivePath(path);
                    }
                } catch (Exception e) {
                    log.error("获取任务轨迹失败", e);
                }
            }
            dataCollectionTask.setLinkedScene(dataCollectionSceneManager.getSceneListByVehiceAndTime(
                    vehicleName, dataCollectionTask.getStartTime(), dataCollectionTask.getEndTime()));
            dataCollectionTaskManager.updateById(dataCollectionTask);
        }
    }

    /**
     * 将DataCollectionTask对象转换为DataCollectionTaskPageDTO对象
     * @param dataCollectionTask 数据采集任务实体对象，包含任务相关信息
     * @return 转换后的DataCollectionTaskPageDTO对象，包含页面展示所需的字段
     */
    private DataCollectionTaskPageDTO convertToPageDTO(DataCollectionTask dataCollectionTask) {
        DataCollectionTaskPageDTO dataCollectionTaskPageDto = new DataCollectionTaskPageDTO();
        dataCollectionTaskPageDto.setId(dataCollectionTask.getId());
        dataCollectionTaskPageDto.setDate(DateUtil.formatDate(dataCollectionTask.getStartTime()));
        dataCollectionTaskPageDto.setStartTime(dataCollectionTask.getStartTime());
        dataCollectionTaskPageDto.setEndTime(dataCollectionTask.getEndTime());
        dataCollectionTaskPageDto.setVehicleName(dataCollectionTask.getVehicleName());
        dataCollectionTaskPageDto.setCollectionUser(dataCollectionTask.getCreateUser());
        dataCollectionTaskPageDto.setAddress(dataCollectionTask.getAddress());
        dataCollectionTaskPageDto.setStatus(dataCollectionTask.getStatus());
        return dataCollectionTaskPageDto;
    }

    /**
     * 检查输入标签集合是否符合与需求标签集合的重叠条件
     * @param requirementTags 需求标签集合，不可为null
     * @param inputTags 输入标签列表，不可为null或空
     * @return 当输入标签与需求标签的交集数量达到输入标签总数的2/3（向上取整）时返回true，否则返回false
     */
    private Integer getOverlapQualified(List<String> requirementTags, Set<String> inputTags) {
        // 空值安全检查
        if (inputTags == null || inputTags.isEmpty() || requirementTags == null) {
            return 0;
        }
        // 计算交集
        Set<String> intersection = new HashSet<>(requirementTags);
        intersection.retainAll(inputTags);

        // 阈值计算（向上取整）
        return (int) Math.ceil(intersection.size() * 100 / inputTags.size());
    }

    /**
     * 发送采集任务结束消息
     * @param vehicleName 车辆名称
     */
    private void sendTaskFinishedTopic(String vehicleName) {
        try {
            String topicName = RedisTopicEnum.DATA_COLLECTION_TASK_VEHICLE.getValue() + vehicleName;
            RTopic rTopic = RedissonUtils.getRTopic(topicName);
            DataCollectionTaskVehicleTopicMsg dataCollectionTaskVehicleTopicMsg = new DataCollectionTaskVehicleTopicMsg();
            dataCollectionTaskVehicleTopicMsg.setMessageType(WebsocketEventTypeEnum.DATA_COLLECTION_FINISHED.getValue());
            String jsonUrl = JsonUtils.writeValueAsString(dataCollectionTaskVehicleTopicMsg);
            rTopic.publish(jsonUrl);
            log.info("[数采车]发送采集任务结束消息, topic={}, msg={}", topicName, jsonUrl);
        } catch (Exception e) {
            log.error("[数采车]发送采集任务结束消息失败, vehicleName={}", vehicleName, e);
        }
    }

    /**
     * 根据场景ID和已关联需求ID列表，搜索并返回满足条件的助手需求DTO列表。
     * @param sceneId 场景ID
     * @param hasLinkedRequireId 已关联需求ID列表
     * @return 满足条件的助手需求DTO列表
     */
    private List<DataCollectionSceneLinkRequirementDTO> searchAssistRequirement(Integer sceneId, List<Integer> hasLinkedRequireId) {
        List<DataCollectionSceneTag> sceneTagList = dataCollectionSceneTagManager.getBySceneId(sceneId);
        if (CollectionUtils.isEmpty(sceneTagList)) {
            return new ArrayList<>();
        }

        // 2、根据场景关联的标签列表获取关联的需求列表
        List<Integer> tagList = sceneTagList.stream().map(DataCollectionSceneTag::getTagId).collect(Collectors.toList());
        List<String> tagNameList = sceneTagList.stream().map(DataCollectionSceneTag::getTagName).collect(Collectors.toList());
        // 3、根据需求标签列表获取关联的需求列表
        List<DataCollectionRequirementTag> requirementTags = dataCollectionRequirementTagManager.listByTagId(tagList, Boolean.TRUE);
        if (CollectionUtils.isEmpty(requirementTags)) {
            return new ArrayList<>();
        }
        Map<Integer, Set<String>> requirementToTagMap =
                requirementTags.stream().filter(data -> Objects.nonNull(data.getTagName())).filter(data -> !hasLinkedRequireId.contains(data.getRequirementId()))
                        .collect(Collectors.groupingBy(DataCollectionRequirementTag::getRequirementId,
                                Collectors.mapping(DataCollectionRequirementTag::getTagName, Collectors.toSet())));
        Map<Integer, Set<String>> resultMap = new HashMap<>();
        requirementToTagMap.forEach((requirementId, tagNameSet) -> {
            if (getOverlapQualified(tagNameList, tagNameSet) > duccMonitorProperties.getDataCollectionOverlapValue()) {
                resultMap.put(requirementId, tagNameSet);
            }
        });
        if (MapUtils.isEmpty(resultMap)) {
            return new ArrayList<>();
        }
        List<DataCollectionRequirementTag> requirementTagName =
                dataCollectionRequirementTagManager.listByRequirementIdCollAndEnable(requirementToTagMap.keySet(), true);
        Map<Integer, Set<String>> linkedRequireMentTagMap =
                requirementTagName.stream().collect(Collectors.groupingBy(DataCollectionRequirementTag::getRequirementId,  TreeMap::new,   Collectors.mapping(DataCollectionRequirementTag::getTagName,Collectors.toSet())));



        // 4、根据需求ID列表获取需求列表
        Map<Integer, DataCollectionRequirement> requirementMap = dataCollectionRequirementManager.listByIds(resultMap.keySet())
                .parallelStream()
                .filter(Objects::nonNull)
                .collect(Collectors.toConcurrentMap(
                        DataCollectionRequirement::getId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        List<DataCollectionSceneLinkRequirementDTO> resultList = new ArrayList<>();
        for (Map.Entry<Integer, DataCollectionRequirement> entry : requirementMap.entrySet()) {
            DataCollectionSceneLinkRequirementDTO result = new DataCollectionSceneLinkRequirementDTO();
            Integer requirementId = entry.getKey();
            DataCollectionRequirement requirement = requirementMap.getOrDefault(requirementId, new DataCollectionRequirement());
            if (DataCollectionStatusEnum.CLOSED.getValue().equals(requirement.getStatus())) {
                continue;
            }
            result.setRequirementId(requirementId);
            result.setDescription(requirement.getDescription());
            result.setRequiredDetail(requirement.getRequiredDetail());
            result.setForbiddenDetail(requirement.getForbiddenDetail());
            Set<String> requirementTagList = linkedRequireMentTagMap.getOrDefault(requirementId, new HashSet<>());
            result.setTagNameList(Lists.newArrayList(requirementTagList));
            result.setOverlap(getOverlapQualified(tagNameList, requirementTagList));
            result.setHasAssociated(false);
            resultList.add(result);
        }
        return resultList;
    }

}