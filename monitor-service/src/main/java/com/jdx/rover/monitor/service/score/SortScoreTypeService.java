package com.jdx.rover.monitor.service.score;

import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.repository.redis.sort.BusinessTypeScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.redis.sort.PowerScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.redis.sort.StationScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.redis.sort.VehicleNameScoreSortedSetRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SortScoreTypeService {
  @Autowired
  private VehicleNameScoreSortedSetRepository vehicleNameScoreSortedSetRepository;
  @Autowired
  private PowerScoreSortedSetRepository powerScoreSortedSetRepository;
  @Autowired
  private BusinessTypeScoreSortedSetRepository businessTypeScoreSortedSetRepository;

  @Autowired
  private StationScoreSortedSetRepository stationScoreSortedSetRepository;

  public void updateScore(VehicleScoreBO bo) {
    //调用对应的策略方法
    UpdateScoreTypeEnum em = UpdateScoreTypeEnum.valueOf(bo.getScoreType());
    Consumer<VehicleScoreBO> consumer = em.getConsumer();
    if (Objects.isNull(consumer)) {
      log.error("车辆分数类型不存在={}", bo.getVehicleName());
      return;
    }
    consumer.accept(bo);
  }

  /**
   * 设置电量分数
   */
  protected void updatePowerScore(VehicleScoreBO bo) {
    double powerScore = SortScoreUtils.getScore(bo);
    powerScoreSortedSetRepository.set(powerScore, bo.getVehicleName());
  }

  /**
   * 设置车辆分数
   */
  protected void updateVehicleNameScore(VehicleScoreBO bo) {
    double vehicleNameScore = SortScoreUtils.getScore(bo);
    vehicleNameScoreSortedSetRepository.set(vehicleNameScore, bo.getVehicleName());
  }

  /**
   * 设置业务类型分数
   */
  protected void updateBusinessTypeScore(VehicleScoreBO bo) {
    double businessTypeScore = SortScoreUtils.getScore(bo);
    businessTypeScoreSortedSetRepository.set(businessTypeScore, bo.getVehicleName());
  }

  /**
   * 设置站点分数
   */
  protected void updateStationScore(VehicleScoreBO bo) {
    double stationScore = SortScoreUtils.getScore(bo);
    stationScoreSortedSetRepository.set(stationScore, bo.getVehicleName());
  }
}

