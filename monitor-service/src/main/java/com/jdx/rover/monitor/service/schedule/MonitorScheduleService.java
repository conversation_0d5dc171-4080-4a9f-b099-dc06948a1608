package com.jdx.rover.monitor.service.schedule;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.VehicleMileageDTO;
import com.jdx.rover.monitor.api.domain.vo.VehicleMileageVO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import jakarta.ejb.Schedule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 监控调度服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MonitorScheduleService {

    private final VehicleScheduleRepository vehicleScheduleRepository;

    public List<VehicleMileageDTO> getVehicleMileage(VehicleMileageVO vehicleMileageVO) {
        List<MonitorScheduleEntity> list = vehicleScheduleRepository.list(vehicleMileageVO.getVehicleNameList());
        List<VehicleMileageDTO> result = new ArrayList<>();
        for (MonitorScheduleEntity monitorScheduleEntity : list) {
            VehicleMileageDTO vehicleMileageDTO = new VehicleMileageDTO();
            vehicleMileageDTO.setVehicleName(monitorScheduleEntity.getVehicleName());
            vehicleMileageDTO.setTraveledMileage(monitorScheduleEntity.getFinishedMileage());
            vehicleMileageDTO.setTotalMileage(monitorScheduleEntity.getGlobalMileage());
            result.add(vehicleMileageDTO);
        }
        return result;
    }
}
