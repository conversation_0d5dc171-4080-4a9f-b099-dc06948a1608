/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;


import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceMessageData;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.dto.MonitorVideoModeDTO;
import com.jdx.rover.monitor.dto.neolix.RoverRobotAlarmInfoDTO;
import com.jdx.rover.monitor.dto.neolix.RoverRobotRealtimeInfoDTO;
import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.MqttCommandTypeEnum;
import com.jdx.rover.monitor.enums.OnlineStatusEnum;
import com.jdx.rover.monitor.enums.device.DeviceEventTypeEnum;
import com.jdx.rover.monitor.enums.mobile.SystemStatusEnum;
import com.jdx.rover.monitor.manager.abnormal.GuardianVehicleAbnormalManager;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.po.GuardianVehicleAbnormal;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRealRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.service.datacollection.DataCollectionQcService;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.score.VehicleScoreSortService;
import com.jdx.rover.monitor.service.web.MonitorVideoService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import com.jdx.rover.transport.api.domain.dto.status.PropertyChangeDTO;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceHeader;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 物联网无人车机器人服务
 *
 * <AUTHOR>
 * @date 2025/03/05
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoverVehicleService implements IRobotDeviceService {

    /**
     * 车辆排序更新信息服务。
     */
    private final VehicleScoreSortService vehicleScoreSortService;

    /**
     * 机器人实时信息服务。
     */
    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    /**
     * 设备实时服务接口
     */
    public final TransportDeviceApiManager realtimeDeviceApiManager;

    /**
     * 车辆状态
     */
    private final VehicleStatusRepository vehicleStatusRepository;

    /**
     * 车辆调度
     */
    private final VehicleScheduleRepository vehicleScheduleRepository;

    /**
     * 车辆实时路径
     */
    private final VehicleScheduleRealRouteRepository vehicleScheduleRealRouteRepository;

    /**
     * 车辆告警信息
     */
    private final GuardianVehicleAbnormalManager guardianVehicleAbnormalManager;

    /**
     * 视频服务对象
     */
    private final MonitorVideoService videoService;

    /**
     * 数据采集质检服务
     */
    private final DataCollectionQcService dataCollectionQcService;
    
    /**
     * 用于收集影子事件的追踪服务。
     */
    private final TrackingEventCollectService trackingEventCollectService;

    @Override
    public void batchUpdateDeviceGroup(Map<String, List<DeviceMessageData>> mapResult) {}

    /**
     * 批量更新无人车设备状态。
     * @param deviceNameList 设备名称列表。
     */
    @Override
    public void batchUpdateDeviceState(List<String> deviceNameList) {
    }

    /**
     * 处理设备消息上报事件。
     */
    @Override
    public void eventsReport(TransportDeviceMessage transportDeviceMessage) {
        String productType = transportDeviceMessage.getHeader().getProductKey();
        String messageType = transportDeviceMessage.getHeader().getMessageType();
        String deviceName = transportDeviceMessage.getHeader().getDeviceName();
        String blockNo = transportDeviceMessage.getHeader().getBlockNo();
        DeviceEventTypeEnum deviceEventTypeEnum = DeviceEventTypeEnum.getByValue(messageType);
        if (Objects.isNull(deviceEventTypeEnum)) {
            return;
        }
        switch (deviceEventTypeEnum) {
            case REPORT_ROBOT_ERROR -> {
                // 处理告警信息
                processReportAlarmInfo(transportDeviceMessage, deviceName, blockNo);
            }
            case REPORT_PDU_POWER_ON -> {
                // 处理PDU上电消息
                processReportPduPowerOn(transportDeviceMessage, deviceName);
            }
            case REPORT_PDU_POWER_OFF -> {
                // 处理PDU下电消息
                processReportPduPowerOff(transportDeviceMessage, deviceName);
            }

            default -> {}
        }
    }

    /**
     * 处理PDU设备开机消息，根据当前采集任务状态决定是否创建新任务
     * @param transportDeviceMessage 传输设备消息对象，包含PDU设备相关信息
     * @param deviceName 设备名称，用于标识具体PDU设备
     */
    public void processReportPduPowerOn(TransportDeviceMessage transportDeviceMessage, String deviceName) {
        // 收到PDU开机，查询当前库里如果最新的采集任务是否已结束，如果是，则新建，如果未关闭，则先关闭之前的任务，然后重新建立一条
        Date reportTime = new Date();
        dataCollectionQcService.updateDataCollectionTask(deviceName, reportTime, null);
        trackingEventCollectService.pushTaskCollectionEvent(ProductTypeEnum.ROVER.getValue(), deviceName, reportTime);
    }

    /**
     * 处理PDU关机报告，关闭当天该车辆未关闭的采集任务
     * @param transportDeviceMessage 传输设备消息对象，包含PDU关机相关信息
     * @param deviceName 设备名称，标识具体车辆设备
     */
    public void processReportPduPowerOff(TransportDeviceMessage transportDeviceMessage, String deviceName) {
        // 收到PDU关机，关闭今天当前车未关闭的采集任务
        dataCollectionQcService.updateDataCollectionTask(deviceName, null, new Date());
    }

    /**
     * 处理报警信息
     */
    private void processReportAlarmInfo(TransportDeviceMessage transportDeviceMessage, String deviceName, String blockNo) {
        RoverRobotAlarmInfoDTO alarmInfoDto = JsonUtils.readValue(JsonUtils.writeValueAsString(transportDeviceMessage.getData()), RoverRobotAlarmInfoDTO.class);
        if (Objects.isNull(alarmInfoDto)) {
            return;
        }
        GuardianVehicleAbnormal guardianVehicleAbnormal = new GuardianVehicleAbnormal();
        guardianVehicleAbnormal.setVehicleName(deviceName);
        guardianVehicleAbnormal.setStartTime(alarmInfoDto.getStartTime());
        guardianVehicleAbnormal.setEndTime(new Date());
        guardianVehicleAbnormal.setModuleName(blockNo);
        guardianVehicleAbnormal.setErrorCode(alarmInfoDto.getErrorCode());
        guardianVehicleAbnormal.setErrorLevel(alarmInfoDto.getErrorLevel());
        guardianVehicleAbnormal.setErrorMessage(alarmInfoDto.getErrorMsg());
        guardianVehicleAbnormalManager.saveOrUpdateBatchList(Lists.newArrayList(guardianVehicleAbnormal));
    }

    /**
     * 解析并处理实时设备信息。
     */
    @Override
    public void propertyReport(String transportDeviceMessage) {
        RoverRobotRealtimeInfoDTO realtimeInfoDto = JsonUtils.readValue(transportDeviceMessage, RoverRobotRealtimeInfoDTO.class);
        if (Objects.isNull(realtimeInfoDto) || StringUtils.isBlank(realtimeInfoDto.getDeviceName())) {
            return;
        }
        // 构建并存储车辆实时信息缓存
        VehicleRealtimeInfoDTO vehicleRealtimeInfo = buildRealtimeInfoDto(realtimeInfoDto);
        vehicleRealtimeRepository.set(vehicleRealtimeInfo);
        // 更新车辆状态缓存
        handByVehicleStatus(vehicleRealtimeInfo);
        // 追加车辆实时路径
        handlePosition(vehicleRealtimeInfo);

    }

    /**
     * 处理传输设备消息的事件回复。
     * @param transportDeviceMessage 传输设备消息内容。
     */
    @Override
    public void eventsReply(TransportDeviceMessage transportDeviceMessage) {
        TransportDeviceHeader headerMessage = transportDeviceMessage.getHeader();
        if (StringUtils.equals(headerMessage.getMessageType(), MqttCommandTypeEnum.CHANGE_VIDEO_QUALITY.getValue())) {
            MonitorVideoModeDTO videoModeDto = JsonUtils.readValue(JsonUtils.writeValueAsString(transportDeviceMessage.getData()), MonitorVideoModeDTO.class);
            if (Objects.isNull(videoModeDto)) {
                return;
            }
            videoService.notifyVideoModeChangeResponse(headerMessage.getDeviceName(), videoModeDto);
        }
    }

    /**
     * 处理属性更改事件。
     * @param propertyChangeDto 属性更改的详细信息。
     */
    @Override
    public void statusChange(PropertyChangeDTO propertyChangeDto) {

    }

    private void handByVehicleStatus(VehicleRealtimeInfoDTO dto) {
        VehicleStatusDO vehicleStatus = vehicleStatusRepository.get(dto.getVehicleName());
        if (Objects.equals(dto.getVehicleState(), vehicleStatus.getVehicleState())
                && Objects.equals(dto.getSystemState(), vehicleStatus.getSystemState())
                    && Objects.equals(dto.getPower(), vehicleStatus.getPower())) {
            return;
        }
        ParamMap<VehicleStatusDO> paramMap = new ParamMap<VehicleStatusDO>()
                .addNonNullProperty(VehicleStatusDO::getVehicleState, dto.getVehicleState());
        if (!Objects.equals(dto.getSystemState(), vehicleStatus.getSystemState())) {
            paramMap.addNonNullProperty(VehicleStatusDO::getSystemState, dto.getSystemState());
        }
        if (!Objects.equals(dto.getPower(), vehicleStatus.getPower())) {
            paramMap.addNonNullProperty(VehicleStatusDO::getPower, dto.getPower());
        }
        vehicleStatusRepository.putAllMapObject(dto.getVehicleName(), paramMap.toMap());
        vehicleScoreSortService.updateVehicleRealtimeInfo(dto);
    }

    private void handlePosition(VehicleRealtimeInfoDTO dto) {
        try {
            if (!isSavePosition(dto)) {
                return;
            }
            MapPointBO mapPoint = TransformUtility.toGCJ02Point(dto.getLat(), dto.getLon());
            if (mapPoint == null) {
                return;
            }
            MonitorRoutingPointEntity entity = new MonitorRoutingPointEntity();
            entity.setLon(mapPoint.getLongitude());
            entity.setLat(mapPoint.getLatitude());
            vehicleScheduleRealRouteRepository.push(dto.getVehicleName(), entity);
        } catch (Exception e) {
            log.error("Handler vehicle transport realtime info exception", e);
        }
    }

    /**
     * 通过guardian连接状态处理
     */
    public void handByGuardianConnect(RoverRobotRealtimeInfoDTO realtimeInfo) {
        String guardianOnline;
        if (Objects.equals(realtimeInfo.getSystemState(), SystemStatusEnum.OFFLINE.getValue())) {
            guardianOnline = OnlineStatusEnum.OFFLINE.name();
        } else {
            guardianOnline = OnlineStatusEnum.ONLINE.name();
        }
        vehicleStatusRepository.putMapValue(realtimeInfo.getDeviceName(), VehicleStatusDO::getGuardianOnline, guardianOnline);
    }

    /**
     * 是否保存车辆实时位置信息
     *
     * @param vehicleRealtimeInfoDTO
     * @return
     */
    private boolean isSavePosition(VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO) {
        if (vehicleRealtimeInfoDTO.getLon() == null || vehicleRealtimeInfoDTO.getLat() == null) {
            return false;
        }
        // 速度小于0.1,不保存实时位置
        if (vehicleRealtimeInfoDTO.getSpeed() == null || Double.compare(vehicleRealtimeInfoDTO.getSpeed(), 0.5) < 0) {
            return false;
        }
        MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.get(vehicleRealtimeInfoDTO.getVehicleName());
        // 没有调度,不保存实时位置
        if (scheduleEntity == null || scheduleEntity.getScheduleNo() == null) {
            return false;
        }
        return true;
    }


    /**
     * 将NeolixRealtimeInfoDTO对象转换为VehicleRealtimeInfoDTO对象。
     * @return 转换后的VehicleRealtimeInfoDTO对象。
     */
    private VehicleRealtimeInfoDTO buildRealtimeInfoDto(RoverRobotRealtimeInfoDTO realtimeInfoDto) {
        VehicleRealtimeInfoDTO vehicleRealtimeInfo = new VehicleRealtimeInfoDTO();
        vehicleRealtimeInfo.setVehicleName(realtimeInfoDto.getDeviceName());
        String systemState = realtimeInfoDto.getSystemState();
        if (Objects.nonNull(realtimeInfoDto.getOnlineStatus()) && realtimeInfoDto.getOnlineStatus() == 0) {
            // 离线根据调度判断失联还是离线
            MonitorScheduleEntity monitorScheduleEntity = vehicleScheduleRepository.get(realtimeInfoDto.getDeviceName());
            systemState = isTodaySchedule(monitorScheduleEntity) ? SystemStateEnum.CONNECTION_LOST.getSystemState() : SystemStateEnum.OFFLINE.getSystemState();
        }
        vehicleRealtimeInfo.setSystemState(systemState);
        vehicleRealtimeInfo.setLat(realtimeInfoDto.getLatitude());
        vehicleRealtimeInfo.setLon(realtimeInfoDto.getLongitude());
        vehicleRealtimeInfo.setCurrentStopFinishedMileage(realtimeInfoDto.getCurrentStopFinishedMileage());
        vehicleRealtimeInfo.setCurrentStopIndex(realtimeInfoDto.getCurrentStopId());
        vehicleRealtimeInfo.setHeading(realtimeInfoDto.getHeading());
        vehicleRealtimeInfo.setMileageToNextStop(realtimeInfoDto.getMileageToNextStop());
        vehicleRealtimeInfo.setRecordTime(realtimeInfoDto.getReportTime());
        vehicleRealtimeInfo.setPower(realtimeInfoDto.getPower());
        vehicleRealtimeInfo.setSpeed(realtimeInfoDto.getSpeed());
        vehicleRealtimeInfo.setVehicleState(realtimeInfoDto.getVehicleState());
        return vehicleRealtimeInfo;
    }

    /**
     * 判断今天是否有调度
     *
     * @param monitorScheduleEntity
     * @return
     */
    private boolean isTodaySchedule(MonitorScheduleEntity monitorScheduleEntity) {
        if (monitorScheduleEntity == null) {
            return false;
        }
        if (monitorScheduleEntity.getStartDateTime() == null) {
            return false;
        }
        Date beginOfToday = DateUtil.beginOfDay(new Date());
        if (beginOfToday.after(monitorScheduleEntity.getStartDateTime())) {
            // 今天开始时间大于调度开始时间,表示今天没有调度
            return false;
        }
        return true;
    }

}