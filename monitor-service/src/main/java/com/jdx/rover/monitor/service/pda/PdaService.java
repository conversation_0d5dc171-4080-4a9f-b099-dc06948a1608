/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.pda;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.rover.device.jsfapi.domain.dto.device.DeviceDetailDTO;
import com.jdx.rover.device.jsfapi.domain.dto.group.GroupInfoDTO;
import com.jdx.rover.device.jsfapi.domain.dto.product.ProductModelListDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.*;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.PdaBasicSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.PdaPageSearchVO;
import com.jdx.rover.monitor.entity.pda.PdaGroupOnlineStatusDO;
import com.jdx.rover.monitor.enums.device.DeviceEnum;
import com.jdx.rover.monitor.enums.device.DeviceNetworkTypeEnum;
import com.jdx.rover.monitor.enums.device.DeviceRealtimeStateEnum;
import com.jdx.rover.monitor.manager.device.IntelligentDeviceApiManager;
import com.jdx.rover.monitor.manager.device.IntelligentDeviceGroupApiManager;
import com.jdx.rover.monitor.manager.device.IntelligentDeviceProductApiManager;
import com.jdx.rover.monitor.manager.pda.PdaRealtimeInfoManager;
import com.jdx.rover.monitor.po.pda.PdaRealtimeInfo;
import com.jdx.rover.monitor.repository.redis.pda.PdaOnlineStatusStaticRepository;
import com.jdx.rover.monitor.repository.util.PageUtils;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 物联网设备实时信息服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PdaService {

    /**
     * 设备服务接口
     */
    public final IntelligentDeviceApiManager basicDeviceApiManager;

    /**
     * 用于访问操作实体的Mapper接口
     */
    public final PdaRealtimeInfoManager pdaRealtimeInfoManager;

    /**
     * 用于访问产品型号接口
     */
    public final IntelligentDeviceProductApiManager pdaProductManager;

    /**
     * 设备服务接口
     */
    public final DeviceStatusService pdaStatusService;

    /**
     * 设备分组接口
     */
    public final IntelligentDeviceGroupApiManager deviceGroupApiManager;

    /**
     * 设备的在线状态接口
     */
    public final PdaOnlineStatusStaticRepository statusStaticRepository;

    /**
     * 获取Pda设备组。
     */
    public List<PdaGroupBasicInfoDTO> getGroupTree() {
        List<GroupInfoDTO> result = deviceGroupApiManager.getGroupTree(ProductTypeEnum.PDA.getValue());
        List<PdaGroupBasicInfoDTO> groupInfoDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(result)) {
            log.error("查询设备分组信息不存在");
            return groupInfoDtoList;
        }

        Map<String, PdaGroupOnlineStatusDO> pdaRealtimeGroupMap = statusStaticRepository.listMap();
        for (GroupInfoDTO groupNoDto : result) {
            PdaGroupBasicInfoDTO basicInfoDto = new PdaGroupBasicInfoDTO();
            basicInfoDto.setGroupNo(groupNoDto.getGroupNo());
            basicInfoDto.setGroupName(groupNoDto.getName());
            List<GroupInfoDTO> children = groupNoDto.getChildren();
            List<PdaGroupBasicInfoDTO> childList = buildGroupChild(groupNoDto.getGroupNo(), children, pdaRealtimeGroupMap, new AtomicInteger(1));
            String groupNoKey = new StringBuilder().append(groupNoDto.getGroupNo()).append("_").append("null").append("_").append("null").toString();
            PdaGroupOnlineStatusDO pdaGroupDo = pdaRealtimeGroupMap.getOrDefault(groupNoKey, new PdaGroupOnlineStatusDO());
            basicInfoDto.setOnlineCount(pdaGroupDo.getOnline());
            basicInfoDto.setTotalCount(pdaGroupDo.getOnline()+ pdaGroupDo.getOffline());
            if (CollectionUtils.isNotEmpty(childList)) {
                basicInfoDto.setChild(childList);
                Integer totalCount = childList.stream().mapToInt(PdaGroupBasicInfoDTO::getTotalCount).sum();
                Integer onlineCount = childList.stream().mapToInt(PdaGroupBasicInfoDTO::getOnlineCount).sum();
                basicInfoDto.setOnlineCount(onlineCount + pdaGroupDo.getOnline());
                basicInfoDto.setTotalCount(totalCount+ pdaGroupDo.getOffline() + pdaGroupDo.getOnline());
            }
            groupInfoDtoList.add(basicInfoDto);
        }
        return groupInfoDtoList;
    }

    /**
     * 统计模型下设备列表
     *
     */
    public List<PdaModuleDeviceStatisticDTO> getModuleDeviceStatisticList() {
        List<ProductModelListDTO> productModelList = pdaProductManager.getProductModuleList(DeviceEnum.PDA.getValue());
        if (CollectionUtils.isEmpty(productModelList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper();
        List<PdaStatusStatisticInfoDTO> pdaStatusStatisticInfoList = pdaRealtimeInfoManager.getGroupCountByModuleNo(lambdaQueryWrapper);
        Map<String, Long> countMap =
                pdaStatusStatisticInfoList.stream().collect(Collectors.toMap(PdaStatusStatisticInfoDTO::getParam, PdaStatusStatisticInfoDTO::getCount));
        return productModelList.stream().map(module -> {
            PdaModuleDeviceStatisticDTO statisticInfo = new PdaModuleDeviceStatisticDTO();
            statisticInfo.setModelName(module.getModelName());
            statisticInfo.setModelNo(module.getModelNo());
            statisticInfo.setTotalCount(countMap.getOrDefault(module.getModelNo(), 0L).intValue());
            return statisticInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 分页获取设备列表
     *
     */
    public PdaPageSearchDTO pageSearchList(PdaPageSearchVO pdaPageSearchVo) {
        // 获取用户权限下车辆
        PdaPageSearchDTO pageInfo = new PdaPageSearchDTO();
        LambdaQueryWrapper<PdaRealtimeInfo> queryWrapper = new LambdaQueryWrapper<PdaRealtimeInfo>();
        queryWrapper.eq(StringUtils.isNotEmpty(pdaPageSearchVo.getDeviceName()),PdaRealtimeInfo::getDeviceName, pdaPageSearchVo.getDeviceName());
        queryWrapper.eq(StringUtils.isNotEmpty(pdaPageSearchVo.getGroupOne()), PdaRealtimeInfo::getGroupOne, pdaPageSearchVo.getGroupOne());
        queryWrapper.eq(StringUtils.isNotEmpty(pdaPageSearchVo.getGroupTwo()), PdaRealtimeInfo::getGroupTwo, pdaPageSearchVo.getGroupTwo());
        queryWrapper.eq(StringUtils.isNotEmpty(pdaPageSearchVo.getGroupThree()), PdaRealtimeInfo::getGroupThree, pdaPageSearchVo.getGroupThree());
        queryWrapper.in(CollectionUtils.isNotEmpty(pdaPageSearchVo.getProductModelNo()), PdaRealtimeInfo::getProductModelNo, pdaPageSearchVo.getProductModelNo());
        queryWrapper.eq(StringUtils.isNotBlank(pdaPageSearchVo.getNetworkType()), PdaRealtimeInfo::getNetworkType, pdaPageSearchVo.getNetworkType());
        queryWrapper.eq(!Objects.isNull(pdaPageSearchVo.getPositionStatus()), PdaRealtimeInfo::getPositionStatus, pdaPageSearchVo.getPositionStatus());
        queryWrapper.eq(!Objects.isNull(pdaPageSearchVo.getBurialStatus()), PdaRealtimeInfo::getBurialStatus, pdaPageSearchVo.getBurialStatus());
        Map<String, Long> statisticMap = new HashMap<>();
        if (queryWrapper.isEmptyOfWhere()) {
            PdaGroupOnlineStatusDO totalPdaRealtimeGroup = statusStaticRepository.get(PdaOnlineStatusStaticRepository.TOTAL_KEY);
            if (Objects.nonNull(totalPdaRealtimeGroup)) {
                statisticMap.put(DeviceRealtimeStateEnum.ONLINE.getValue(), Long.valueOf(totalPdaRealtimeGroup.getOnline()));
                statisticMap.put(DeviceRealtimeStateEnum.OFFLINE.getValue(), Long.valueOf(totalPdaRealtimeGroup.getOffline()));
            }
        } else {
            List<PdaStatusStatisticInfoDTO> statusGroupStatistic = pdaRealtimeInfoManager.getGroupCountByStatus(queryWrapper);
            if (CollectionUtils.isNotEmpty(statusGroupStatistic)) {
                statisticMap =
                        statusGroupStatistic.stream().collect(Collectors.toMap(PdaStatusStatisticInfoDTO::getParam, PdaStatusStatisticInfoDTO::getCount));
            }
        }
        Long onlineCount = statisticMap.get(DeviceRealtimeStateEnum.ONLINE.getValue());
        Long offlineCount = statisticMap.get(DeviceRealtimeStateEnum.OFFLINE.getValue());
        pageInfo.setOnlineCount(onlineCount == null? 0:onlineCount);
        pageInfo.setOfflineCount(offlineCount == null? 0:offlineCount);
        pageInfo.setTotalCount(pageInfo.getOnlineCount() + pageInfo.getOfflineCount());
        queryWrapper.eq(StringUtils.isNotEmpty(pdaPageSearchVo.getRealtimeStatus()), PdaRealtimeInfo::getRealtimeStatus, pdaPageSearchVo.getRealtimeStatus());
        Page<PdaRealtimeInfo> iPage = PageUtils.toMpPage(pdaPageSearchVo,PdaRealtimeInfo.class);
        // 1、关闭Mybatis-Plus的统计查询
        iPage.setSearchCount(false);
        // 2、设置我们自己写的统计数量的查询
        Long totalCount = pdaRealtimeInfoManager.getQueryCount(queryWrapper);
        pageInfo.setTotal(totalCount);
        queryWrapper.select(PdaRealtimeInfo::getDeviceName, PdaRealtimeInfo::getGroupName, PdaRealtimeInfo::getRealtimeStatus,
                PdaRealtimeInfo::getBurialStatus, PdaRealtimeInfo::getNetworkType, PdaRealtimeInfo::getPositionStatus,
                PdaRealtimeInfo::getProductModelNo, PdaRealtimeInfo::getProductModelName, PdaRealtimeInfo::getModifiedTime);
        queryWrapper.orderByDesc(PdaRealtimeInfo::getModifiedTime);
        IPage<PdaRealtimeInfo> pdaRealtimeInfoIPage = pdaRealtimeInfoManager.page(iPage, queryWrapper);
        pageInfo.setPageNum((int)pdaRealtimeInfoIPage.getCurrent());
        pageInfo.setPageSize((int)pdaRealtimeInfoIPage.getSize());
        if(CollectionUtils.isEmpty(pdaRealtimeInfoIPage.getRecords())) {
            return pageInfo;
        }
        List<PdaRealtimeBasicInfoDTO> pdaStatusList = pdaStatusService.buildPdaBasicStatusInfo(pdaRealtimeInfoIPage.getRecords());
        pageInfo.setList(pdaStatusList);
        return pageInfo;
    }

    /**
     * 获取指定设备的基本信息。
     * @param pdaBasicSearchVo 设备请求
     */
    public PdaBasicDetailInfoDTO getDeviceBasicInfo(PdaBasicSearchVO pdaBasicSearchVo) {
        PdaBasicDetailInfoDTO detailInfoDto = new PdaBasicDetailInfoDTO();
        LambdaQueryWrapper<PdaRealtimeInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(PdaRealtimeInfo::getDeviceName, pdaBasicSearchVo.getDeviceName());
        PdaRealtimeInfo pdaRealtimeInfo = pdaRealtimeInfoManager.getOne(queryWrapper);
        if (Objects.isNull(pdaRealtimeInfo)) {
            return detailInfoDto;
        }
        DeviceDetailDTO deviceDetailDto = basicDeviceApiManager.getDeviceDetail(pdaRealtimeInfo.getProductKey(), pdaBasicSearchVo.getDeviceName());
        if (Objects.isNull(deviceDetailDto)) {
            return detailInfoDto;
        }
        detailInfoDto.setDeviceNo(deviceDetailDto.getDeviceNo());
        detailInfoDto.setDeviceName(deviceDetailDto.getDeviceName());
        detailInfoDto.setGroupNo(deviceDetailDto.getGroupNo());
        detailInfoDto.setGroupName(deviceDetailDto.getGroupName());
        detailInfoDto.setProductKey(deviceDetailDto.getProductKey());
        detailInfoDto.setActiveTime(deviceDetailDto.getActiveTime());
        detailInfoDto.setGroupLevelName(deviceDetailDto.getGroupLevelName());
        detailInfoDto.setProductModelName(deviceDetailDto.getProductModelName());
        detailInfoDto.setProductModelNo(deviceDetailDto.getProductModelNo());
        detailInfoDto.setProductName(deviceDetailDto.getProductName());
        detailInfoDto.setRegisterTime(deviceDetailDto.getRegisterTime());
        detailInfoDto.setRemarkName(deviceDetailDto.getRemarkName());
        return detailInfoDto;
    }

    /**
     * 获取实时设备状态。
     * @param pdaBasicSearchVo 设备请求。
     */
    public PdaRealtimeStatusInfoDTO getRealtimeStatus(PdaBasicSearchVO pdaBasicSearchVo) {
        LambdaQueryWrapper<PdaRealtimeInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(PdaRealtimeInfo::getDeviceName, pdaBasicSearchVo.getDeviceName());
        PdaRealtimeInfo pdaRealtimeInfo = pdaRealtimeInfoManager.getOne(queryWrapper);
        if (Objects.isNull(pdaRealtimeInfo)) {
            return new PdaRealtimeStatusInfoDTO();
        }
        PdaRealtimeStatusInfoDTO result = pdaStatusService.getRealtimeStatus(pdaBasicSearchVo.getDeviceName());
        result.setNetworkTypeName(DeviceNetworkTypeEnum.getNameByValue(pdaRealtimeInfo.getNetworkType()));
        result.setModifyTime(result.getRecordTime());
        return result;
    }

    /**
     * 刷新设备状态
     */
    public void refreshPdaStatus() {
        LambdaQueryWrapper<PdaRealtimeInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.groupBy(PdaRealtimeInfo::getGroupOne, PdaRealtimeInfo::getGroupTwo, PdaRealtimeInfo::getGroupThree, PdaRealtimeInfo::getRealtimeStatus);
        queryWrapper.select(PdaRealtimeInfo::getGroupOne, PdaRealtimeInfo::getGroupTwo, PdaRealtimeInfo::getGroupThree, PdaRealtimeInfo::getRealtimeStatus, PdaRealtimeInfo::getCountNum);
        List<PdaRealtimeInfo> pdaRealtimeGroupList = pdaRealtimeInfoManager.list(queryWrapper);
        if (CollectionUtils.isEmpty(pdaRealtimeGroupList)) {
            return;
        }
        Map<String, PdaGroupOnlineStatusDO> pdaGroupOnlineStatusMap = new HashMap<>();
        AtomicInteger offlineCount = new AtomicInteger();
        AtomicInteger onlineCount = new AtomicInteger();
        pdaRealtimeGroupList.stream().forEach(groupStatistic -> {
            String groupKey = String.format("%s_%s_%s", groupStatistic.getGroupOne(), groupStatistic.getGroupTwo(), groupStatistic.getGroupThree());
            PdaGroupOnlineStatusDO statusDo = pdaGroupOnlineStatusMap.getOrDefault(groupKey, new PdaGroupOnlineStatusDO());
            statusDo.setGroupNo(groupKey);
            Integer count = groupStatistic.getCountNum();
            if (Objects.nonNull(count)) {
                if (StringUtils.equals(groupStatistic.getRealtimeStatus(), DeviceRealtimeStateEnum.ONLINE.getValue())) {
                    onlineCount.addAndGet(count);
                    statusDo.setOnline(count);
                } else if (StringUtils.equals(groupStatistic.getRealtimeStatus(), DeviceRealtimeStateEnum.OFFLINE.getValue())) {
                    offlineCount.addAndGet(count);
                    statusDo.setOffline(count);
                }
            }
            statusDo.setRecordTime(new Date());
            pdaGroupOnlineStatusMap.put(groupKey, statusDo);
        });
        PdaGroupOnlineStatusDO totalStatusDo = new PdaGroupOnlineStatusDO();
        totalStatusDo.setGroupNo(PdaOnlineStatusStaticRepository.TOTAL_KEY);
        totalStatusDo.setOnline(onlineCount.get());
        totalStatusDo.setOffline(offlineCount.get());
        pdaGroupOnlineStatusMap.put(PdaOnlineStatusStaticRepository.TOTAL_KEY, totalStatusDo);
        statusStaticRepository.putMapAll(pdaGroupOnlineStatusMap);

    }

    /**
     * 构建群组子节点列表
     * @param groupNo 群组编号
     * @param children 群组子节点列表
     * @return 构建后的群组子节点列表
     */
    private List<PdaGroupBasicInfoDTO> buildGroupChild(String groupNo, List<GroupInfoDTO> children, Map<String, PdaGroupOnlineStatusDO> countMap, AtomicInteger countNum) {
        if (CollectionUtils.isEmpty(children)) {
            return null;
        }
        List<PdaGroupBasicInfoDTO> child = new ArrayList<>();
        for (GroupInfoDTO groupNoDto : children) {
            PdaGroupBasicInfoDTO basicInfoDto = new PdaGroupBasicInfoDTO();
            basicInfoDto.setGroupNo(groupNoDto.getGroupNo());
            basicInfoDto.setGroupName(groupNoDto.getName());
            String childGroupNo = groupNo + "_" + groupNoDto.getGroupNo();
            String totalGroupCountKey = childGroupNo;
            if (groupNoDto.getLevel() == 2) {
                totalGroupCountKey = new StringBuilder().append(childGroupNo).append("_").append("null").toString();
            }
            PdaGroupOnlineStatusDO currentLevelPdaDo = countMap.getOrDefault(totalGroupCountKey, new PdaGroupOnlineStatusDO());
            List<PdaGroupBasicInfoDTO> childList = buildGroupChild(childGroupNo, groupNoDto.getChildren(), countMap, new AtomicInteger(countNum.incrementAndGet()));
            basicInfoDto.setChild(childList);
            basicInfoDto.setOnlineCount(currentLevelPdaDo.getOnline());
            basicInfoDto.setTotalCount(currentLevelPdaDo.getOnline() + currentLevelPdaDo.getOffline());
            if (CollectionUtils.isNotEmpty(childList)) {
                Integer totalCount = childList.stream().mapToInt(PdaGroupBasicInfoDTO::getTotalCount).sum();
                Integer onlineCount = childList.stream().mapToInt(PdaGroupBasicInfoDTO::getOnlineCount).sum();
                basicInfoDto.setOnlineCount(onlineCount + currentLevelPdaDo.getOnline());
                basicInfoDto.setTotalCount(totalCount + currentLevelPdaDo.getOnline() + currentLevelPdaDo.getOffline());
            }
            child.add(basicInfoDto);
        }
        return child;
    }

}