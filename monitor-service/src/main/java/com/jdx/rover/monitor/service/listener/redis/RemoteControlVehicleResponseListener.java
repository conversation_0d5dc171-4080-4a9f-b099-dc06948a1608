package com.jdx.rover.monitor.service.listener.redis;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.server.api.domain.dto.hermes.HermesVehicleControlCommandDelayInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;

/**
 * 地图页车辆信息更新推送
 *
 * <AUTHOR>
 */
@Slf4j
public class RemoteControlVehicleResponseListener implements MessageListener<String> {
  private Session session;

  public RemoteControlVehicleResponseListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    synchronized (this.session) {
      try {
        this.session.getBasicRemote().sendText(msg);
      } catch (Exception e) {
        log.error("Send vehicle delay info exception", e);
      }
    }
  }
}
