/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.vehicle;

import cn.hutool.core.collection.CollUtil;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorSpeedLimitDTO;
import com.jdx.rover.monitor.dto.vehicle.CheckSpeedLimitDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleSpeedLimitDTO;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.SpeedLimitTypeEnum;
import com.jdx.rover.monitor.manager.vehicle.SystemStateManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSpeedLimitRepository;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.vo.MonitorSetSpeedLimitVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.server.api.domain.vo.MaxVelocityCommandVO;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import java.util.ArrayList;
import java.util.Date;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/8 18:04
 * @description 车辆限速服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VehicleSpeedLimitService {

    /**
     * 车辆限速Redis
     */
    private final VehicleSpeedLimitRepository vehicleSpeedLimitRepository;

    /**
     * 车辆实时信息Redis
     */
    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    /**
     * 远程命令服务
     */
    private final RemoteCommandService remoteCommandJsfService;

    /**
     * 车辆状态Redis
     */
    private final VehicleStatusRepository vehicleStatusRepository;

    /**
     * 解除限速值
     */
    private static final double REMOVE_SPEED_LIMIT_VAL = -1.0;

    /**
     * 清除所有车辆限速配置
     */
    public void clearAllSpeedLimitConfig() {
        val speedLimitVehicleSet = vehicleSpeedLimitRepository.getSpeedLimitVehicleSet();
        if (CollUtil.isEmpty(speedLimitVehicleSet)) {
            log.info("【车辆限速】没有车辆限速配置");
            return;
        }
        log.info("【车辆限速】开始清除所有车辆限速配置：[{}]", speedLimitVehicleSet);

        // 获取所有车辆实时信息
        val realtimeInfoMap = vehicleRealtimeRepository.listMap(new ArrayList<>(speedLimitVehicleSet));
        speedLimitVehicleSet.forEach(vehicleName -> {
            // 判断车辆是否在线
            val vehicleRealtimeInfoDTO = realtimeInfoMap.get(vehicleName);
            if (vehicleRealtimeInfoDTO != null && !SystemStateManager.isOfflineOrLost(vehicleRealtimeInfoDTO.getSystemState())) {
                // 下发解除限速指令
                try {
                    postMaxVelocityRequest(REMOVE_SPEED_LIMIT_VAL, vehicleName, "monitor");
                    log.info("【车辆限速】解除限速成功：[{}]", vehicleName);
                } catch (Exception ignore) {}
            }

            // 清除限速配置缓存
            vehicleSpeedLimitRepository.remove(vehicleName);
        });
    }

    /**
     * 下发车辆最大限速
     *
     * @param maxVelocity maxVelocity
     * @param vehicleName vehicleName
     */
    public void postMaxVelocityRequest(Double maxVelocity, String vehicleName, String username) {
        log.info("【车辆限速】发送限速指令：[vehicle: {}, maxVelocity: {}, userName: {}]", vehicleName, maxVelocity, username);
        MaxVelocityCommandVO maxVelocityCommandVO = new MaxVelocityCommandVO();
        maxVelocityCommandVO.setVehicleName(vehicleName);
        maxVelocityCommandVO.setReceiveTimeStamp(new Date());
        maxVelocityCommandVO.setTransitTimeStamp(new Date());
        maxVelocityCommandVO.setMaxVelocity(maxVelocity);

        try {
            HttpResult<Void> result = remoteCommandJsfService.publishMaxVelocity(maxVelocityCommandVO);
            log.info("【车辆限速】下发限速指令结果：[{}]", result);
            if (!HttpResult.isSuccess(result)) {
                throw new RuntimeException(result.getMessage());
            }
        } catch (Exception e) {
            log.error("【车辆限速】下发限速失败：[{}]", e.getMessage(), e);
            throw new AppException(e.getMessage());
        }
    }


    /**
     * 查询车辆当前是否有限速配置
     *
     * @param vehicleName vehicleName
     * @return CheckSpeedLimitDTO
     */
    public CheckSpeedLimitDTO checkSpeedLimit(String vehicleName) {
        CheckSpeedLimitDTO checkSpeedLimitDTO = new CheckSpeedLimitDTO();
        if (!vehicleSpeedLimitRepository.existsSpeedLimit(vehicleName)) {
            return checkSpeedLimitDTO;
        }

        VehicleSpeedLimitDTO vehicleSpeedLimitDTO = vehicleSpeedLimitRepository.get(vehicleName);
        if (null == vehicleSpeedLimitDTO || vehicleSpeedLimitDTO.getSpeedLimit() == null) {
            return checkSpeedLimitDTO;
        }

        checkSpeedLimitDTO.setHasSpeedLimit(true);
        checkSpeedLimitDTO.setMaxVelocity((double) Math.round(vehicleSpeedLimitDTO.getSpeedLimit() / 3.6 * 10.0) / 10.0);
        return checkSpeedLimitDTO;
    }

    /**
     * 下发车辆最大限速
     *
     * @param monitorSetSpeedLimitVO monitorSetSpeedLimitVO
     */
    public void postMaxVelocityRequest(MonitorSetSpeedLimitVO monitorSetSpeedLimitVO) {
        // 解除限速
        if (!monitorSetSpeedLimitVO.getIsTempSpeedLimitEnabled()) {
            postMaxVelocityRequest(REMOVE_SPEED_LIMIT_VAL, monitorSetSpeedLimitVO.getVehicleName(), UserUtils.getLoginUser());
            vehicleSpeedLimitRepository.remove(monitorSetSpeedLimitVO.getVehicleName());
            return;
        }

        // 限速类型
        SpeedLimitTypeEnum speedLimitTypeEnum = SpeedLimitTypeEnum.getByName(monitorSetSpeedLimitVO.getTempSpeedLimitType());
        if (speedLimitTypeEnum == SpeedLimitTypeEnum.UNKNOWN) {
            throw new AppException("未知限速类型");
        }
        if (speedLimitTypeEnum == SpeedLimitTypeEnum.OTHER) {
            if (monitorSetSpeedLimitVO.getTempSpeedLimit() == null) {
                throw new AppException("自定义限速值不能为空");
            }
            if (monitorSetSpeedLimitVO.getTempSpeedLimit() <= 0) {
                throw new AppException("自定义限速值不能小于0");
            }
        }

        // 下发限速指令
        double convertedSpeed = Math.round(monitorSetSpeedLimitVO.getTempSpeedLimit() / 3.6 * 10.0) / 10.0;
        postMaxVelocityRequest(convertedSpeed, monitorSetSpeedLimitVO.getVehicleName(), UserUtils.getLoginUser());

        // 增加限速配置缓存
        VehicleSpeedLimitDTO vehicleSpeedLimitDTO = new VehicleSpeedLimitDTO();
        vehicleSpeedLimitDTO.setVehicleName(monitorSetSpeedLimitVO.getVehicleName());
        vehicleSpeedLimitDTO.setSpeedLimit(monitorSetSpeedLimitVO.getTempSpeedLimit());
        vehicleSpeedLimitDTO.setSpeedLimitType(monitorSetSpeedLimitVO.getTempSpeedLimitType());
        vehicleSpeedLimitRepository.set(vehicleSpeedLimitDTO);
    }

    /**
     * 获取车辆限速配置
     *
     * @param vehicleNameBasicVO vehicleNameBasicVO
     * @return MonitorSpeedLimitDTO
     */
    public MonitorSpeedLimitDTO getTempSpeedLimitInfo(VehicleNameBasicVO vehicleNameBasicVO) {
        MonitorSpeedLimitDTO monitorSpeedLimitDTO = new MonitorSpeedLimitDTO();
        String vehicleName = vehicleNameBasicVO.getVehicleName();
        if (StringUtils.isBlank(vehicleName)) {
            return null;
        }

        // 获取车辆上报conf限速
        VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
        if (null != vehicleStatusDO && null != vehicleStatusDO.getReportMaxVelocity()) {
            monitorSpeedLimitDTO.setXgflags((double) Math.round(vehicleStatusDO.getReportMaxVelocity() * 3.6));
        }

        // 获取车辆临时限速配置
        if (vehicleSpeedLimitRepository.existsSpeedLimit(vehicleName)) {
            VehicleSpeedLimitDTO vehicleSpeedLimitDTO = vehicleSpeedLimitRepository.get(vehicleName);
            monitorSpeedLimitDTO.setIsTempSpeedLimitEnabled(true);
            monitorSpeedLimitDTO.setTempSpeedLimitType(vehicleSpeedLimitDTO.getSpeedLimitType());
            monitorSpeedLimitDTO.setTempSpeedLimit(vehicleSpeedLimitDTO.getSpeedLimit());
        } else {
            monitorSpeedLimitDTO.setIsTempSpeedLimitEnabled(false);
        }

        return monitorSpeedLimitDTO;
    }
}
