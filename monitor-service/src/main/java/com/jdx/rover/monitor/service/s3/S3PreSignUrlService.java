package com.jdx.rover.monitor.service.s3;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.amazonaws.HttpMethod;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.s3.S3PreSignDTO;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import com.jdx.rover.monitor.vo.s3.S3PreSignVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.Date;
import java.util.Objects;

/**
 * s3预签名url
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/26
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class S3PreSignUrlService {
    private final S3Properties s3Properties;
    private final MonitorRemoteCommandService monitorRemoteCommandService;

    public String preSignUrl(S3PreSignVO s3PreSignVO, HttpMethod httpMethod) {
        if (Objects.isNull(s3PreSignVO.getUserName())) {
            s3PreSignVO.setUserName(UserUtils.getAndCheckLoginUser());
        }
        String endpoint = "https://s3.cn-north-1.jdcloud-oss.com";
        final String bucketName = "webterminal";
        StringBuilder keyName = new StringBuilder();
        keyName.append(DateUtil.format(new Date(), DatePattern.NORM_DATE_FORMAT)).append("/");
        keyName.append(s3PreSignVO.getUserName()).append("/");
        if (!Objects.isNull(s3PreSignVO.getVehicleName())) {
            keyName.append(s3PreSignVO.getVehicleName()).append("/");
        }
        keyName.append(s3PreSignVO.getFileName());
        final Date expiration = DateUtil.offsetHour(new Date(), 24);
        URL url = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), endpoint
                , bucketName, keyName.toString(), httpMethod, expiration);
        return url.toString();
    }

    /**
     * 预签名get和put url,用于下载上传
     *
     * @param s3PreSignVO
     * @return
     */
    public S3PreSignDTO preSignGetAndPutUrl(S3PreSignVO s3PreSignVO) {
        String putUrl = preSignUrl(s3PreSignVO, HttpMethod.PUT);
        String getUrl = preSignUrl(s3PreSignVO, HttpMethod.GET);
        S3PreSignDTO s3PreSignDTO = new S3PreSignDTO();
        s3PreSignDTO.setFileName(s3PreSignVO.getFileName());
        s3PreSignDTO.setPutUrl(putUrl);
        s3PreSignDTO.setGetUrl(getUrl);

        String data = JsonUtils.writeValueAsString(s3PreSignVO);
        monitorRemoteCommandService.sendRemoteCommandRecordLog(data, WebsocketEventTypeEnum.WEB_TERMINAL_COMMAND_DATA.getValue(),
                s3PreSignVO.getUserName(), RemoteCommandSourceEnum.MONITOR.getCommandSource());

        log.info("预签名s3上传下载url,{}", data);
        return s3PreSignDTO;
    }
}
