/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.cockpit;

import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleSystemStateSortEnum;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.repository.redis.AllVehicleNameSortRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.sort.CockpitScoreSortedSetRepository;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import com.jdx.rover.monitor.service.score.SortScoreUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 调度分数
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class CockpitScoreService {
    private final AllVehicleNameSortRepository allVehicleNameSortRepository;
    private final VehicleScheduleRepository vehicleScheduleRepository;
    private final CockpitScoreSortedSetRepository cockpitScoreSortedSetRepository;
    private final VehicleRealtimeRepository vehicleRealtimeRepository;
    private final MetadataVehicleApiManager metadataVehicleApiManager;
    private final DuccConfigProperties duccConfigProperties;

    /**
     * 初始化所有车辆基础数据
     */
    public void initScore() {
        log.info("初始化所有坐席模式分数开始!");
        List<VehicleBasicDTO> vehicleList = metadataVehicleApiManager.listAll();

        if (CollectionUtils.isEmpty(vehicleList)) {
            return;
        }
        for (VehicleBasicDTO vehicleBasic : vehicleList) {
            updateScore(vehicleBasic.getName());
        }
        log.info("初始化所有坐席模式分数结束!");
    }

    /**
     * 初始化车辆基础数据
     */
    public void updateScore(String vehicleName) {
        log.info("初始化单个坐席模式分数开始!={}", vehicleName);
        String scheduleState = vehicleScheduleRepository.getScheduleState(vehicleName);
        String systemState = vehicleRealtimeRepository.getSystemState(vehicleName);
        List<String> vehicleNameSortList = allVehicleNameSortRepository.get();

        VehicleScoreBO bo = new VehicleScoreBO();
        bo.setVehicleName(vehicleName);
        bo.setVehicleNameSortList(vehicleNameSortList);

        // 通过业务类型排序(售卖/配送)
        bo.setScheduleState(scheduleState);
        bo.setSystemState(systemState);
        double score = getScheduleStateScore(bo);

        // 6FE车辆特殊逻辑，配置车辆score设置排序靠最前
        if (CollectionUtils.isNotEmpty(duccConfigProperties.getMonitorMultiVehiclePage()) && duccConfigProperties.getMonitorMultiVehiclePage().contains(vehicleName)) {
            VehicleSystemStateSortEnum systemStateEnum = VehicleSystemStateSortEnum.of(bo.getSystemState());
            if (!VehicleSystemStateSortEnum.OFFLINE.equals(systemStateEnum)) {
                score = SortScoreUtils.getVehicleNameScore(bo.getVehicleName(), bo.getVehicleNameSortList());
                log.info("置顶在线车辆分数更新：[vehicleName:{}, score:{}]", vehicleName, score);
            }
        }

        cockpitScoreSortedSetRepository.set(score, bo.getVehicleName());
        log.info("初始化单个坐席模式分数结束!vehicleName={},scheduleState={},systemState={},score={}", vehicleName, scheduleState, systemState, score);
    }

    /**
     * 获取车号分数
     */
    public static double getScheduleStateScore(VehicleScoreBO bo) {
        double vehicleNameScore = SortScoreUtils.getVehicleNameScore(bo.getVehicleName(), bo.getVehicleNameSortList());
        double systemStateScore = getSystemStateScore(bo.getSystemState());
        double scheduleStateScore = SortScoreUtils.getScheduleStateScore(bo.getScheduleState());
        return vehicleNameScore + scheduleStateScore + systemStateScore;
    }

    /**
     * 通过调度状态取分数
     * 失联组车辆按照“配送状态”排序。（去程->返程->去装载->去卸载->投递中->装载中->卸载中->已装载->已卸载->已到达->待装载）没有配送状态车辆在最后
     */
    private static double getSystemStateScore(String online) {
        for (VehicleSystemStateSortEnum em : VehicleSystemStateSortEnum.values()) {
            if (em.name().equals(online)) {
                return em.getValue();
            }
        }
        return VehicleSystemStateSortEnum.OFFLINE.getValue();
    }


}

