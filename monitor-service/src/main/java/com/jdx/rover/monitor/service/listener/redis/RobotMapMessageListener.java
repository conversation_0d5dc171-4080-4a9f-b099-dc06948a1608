package com.jdx.rover.monitor.service.listener.redis;

import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import java.io.IOException;

/**
 * 地图信息更新推送
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
@Slf4j
public class RobotMapMessageListener implements MessageListener<String> {
  private Session session;

  public RobotMapMessageListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    synchronized (this.session) {
      try {
        log.info("收到设备地图信息变化{}", msg);
        this.session.getBasicRemote().sendText(msg);
      } catch (IOException e) {
        log.error("Map send robot congestion update exception", e);
      }
    }
  }
}
