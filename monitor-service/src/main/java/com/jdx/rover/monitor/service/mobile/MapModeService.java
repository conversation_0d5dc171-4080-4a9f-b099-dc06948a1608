package com.jdx.rover.monitor.service.mobile;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CoordinateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.dto.MonitorStationVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.MonitorStopMapInfoDTO;
import com.jdx.rover.monitor.dto.mobile.map.*;
import com.jdx.rover.monitor.dto.mobile.map.MapVehiclePathDTO.Position;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleRealtimeDTO.ScheduleStop;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleRealtimeDTO.TakeOverInfo;
import com.jdx.rover.monitor.dto.vehicle.AlarmEventDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.mobile.SystemStatusEnum;
import com.jdx.rover.monitor.manager.mobile.MetadataInsuranceManager;
import com.jdx.rover.monitor.manager.mobile.VehiclePositionManager;
import com.jdx.rover.monitor.manager.ticket.IssueQueryApiManager;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.manager.utils.vehicle.SupplierUtils;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleVersionManager;
import com.jdx.rover.monitor.repository.redis.*;
import com.jdx.rover.monitor.service.web.MonitorMapInfoService;
import com.jdx.rover.monitor.vo.mobile.common.PositionVO;
import com.jdx.rover.monitor.vo.mobile.mapmode.VehicleInsuranceVO;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import com.jdx.rover.ticket.domain.dto.query.VehicleCurrentIssueDTO;
import com.jdx.rover.ticket.domain.vo.query.QueryVehicleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/17 10:44
 * @description 车辆地图模式
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MapModeService {

    /**
     * 车辆实时信息
     */
    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    /**
     * 用户车辆权限
     */
    private final UserVehicleNameRepository userVehicleNameRepository;

    /**
     * 车辆基本信息
     */
    private final VehicleBasicRepository vehicleBasicRepository;

    /**
     * 车辆调度信息
     */
    private final VehicleScheduleRepository vehicleScheduleRepository;

    /**
     * 车辆位置信息
     */
    private final VehiclePositionManager vehiclePositionManager;

    /**
     * 工单查询
     */
    private final IssueQueryApiManager issueQueryApiManager;

    /**
     * MetadataVehicleApiManager
     */
    private final MetadataVehicleApiManager metadataVehicleApiManager;

    /**
     * 车辆版本
     */
    private final VehicleVersionManager vehicleVersionManager;

    /**
     * 车辆状态
     */
    private final VehicleStatusRepository vehicleStatusRepository;

    /**
     * 实时告警信息
     */
    private final VehicleAlarmRepository vehicleAlarmRepository;

    /**
     * 监控地图信息
     */
    private final MonitorMapInfoService monitorMapInfoService;

    /**
     * 监控车辆接管状态
     */
    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    /**
     * 主数据保险信息
     */
    private final MetadataInsuranceManager metadataInsuranceManager;

    // 地图模式线程池
    private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(5, 10, 1,
        TimeUnit.MINUTES, new ArrayBlockingQueue<>(200), new CustomizableThreadFactory("MapMode-"),
        new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 获取用户权限下车辆列表
     *
     * @param username username
     * @return List<MapGetVehicleListDTO>
     */
    public List<VehicleListDTO> getVehicleList(String username) {
        // 获取用户权限下车辆
        Set<String> vehicleNameSet = userVehicleNameRepository.get(username);
        if (CollUtil.isEmpty(vehicleNameSet)) {
            return Collections.emptyList();
        }

        final List<String> vehicleNameList = Lists.newArrayList(vehicleNameSet);
        // 获取车辆基础信息
        Map<String, VehicleBasicDTO> basicMap = vehicleBasicRepository.listMap(vehicleNameList);
        // 获取车辆实时信息
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
        // 获取车辆调度信息
        Map<String, MonitorScheduleEntity> scheduleMap = vehicleScheduleRepository.listMap(vehicleNameList);
        // 获取车辆保险信息
        Map<String, Boolean> insuranceMap = metadataInsuranceManager.hasDeviceEffectiveInsurance(vehicleNameList);

        // 组装数据
        List<VehicleListDTO> result = Lists.newArrayList();
        vehicleNameList.forEach(vehicleName -> {
            VehicleListDTO vehicleListDTO = new VehicleListDTO();
            vehicleListDTO.setVehicleName(vehicleName);

            // 站点信息
            VehicleBasicDTO vehicleBasicDTO = basicMap.get(vehicleName);
            if (Objects.isNull(vehicleBasicDTO) || !SupplierUtils.isJdVehicle(vehicleBasicDTO.getSupplier())) {
                return;
            }
            vehicleListDTO.setStationName(vehicleBasicDTO.getStationName());

            // 系统状态、车辆是否被接管
            VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = realtimeMap.get(vehicleName);
            if (null != vehicleRealtimeInfoDTO) {
                vehicleListDTO.setSystemStatus(SystemStatusEnum.getCodeMapping(vehicleRealtimeInfoDTO.getSystemState()).getCode());
            } else {
                vehicleListDTO.setSystemStatus(SystemStatusEnum.OFFLINE.getCode());
            }

            // 车辆业务状态
            MonitorScheduleEntity monitorScheduleEntity = scheduleMap.get(vehicleName);
            if (null != monitorScheduleEntity) {
                vehicleListDTO.setBusinessStatus(true);
            }

            // 车辆保险状态
            if (insuranceMap.containsKey(vehicleName)) {
                vehicleListDTO.setInsuranceEffective(insuranceMap.get(vehicleName));
            }

            result.add(vehicleListDTO);
        });

        // 结果排序，异常>正常>离线
        Map<String, List<VehicleListDTO>> groupedByStatus = result.stream().collect(Collectors.groupingBy(VehicleListDTO::getSystemStatus));
        List<VehicleListDTO> sortedResult = Lists.newArrayList();
        Optional.ofNullable(groupedByStatus.get(SystemStatusEnum.ABNORMAL.getCode())).ifPresent(sortedResult::addAll);
        Optional.ofNullable(groupedByStatus.get(SystemStatusEnum.NORMAL.getCode())).ifPresent(sortedResult::addAll);
        Optional.ofNullable(groupedByStatus.get(SystemStatusEnum.OFFLINE.getCode())).ifPresent(sortedResult::addAll);
        return sortedResult;
    }

    /**
     * 获取地图上车辆
     *
     * @param positionVO positionVO
     * @return List<MapVehicleDTO>
     */
    public List<MapVehicleDTO> getMapVehicle(PositionVO positionVO) {
        // 获取5km范围内车辆
        CoordinateUtil.Coordinate coordinate = CoordinateUtil.gcj02ToWgs84(positionVO.getLongitude(), positionVO.getLatitude());
        List<String> vehicleNameList = vehiclePositionManager.getBaseMapper().getVehicleByDistance(coordinate.getLng(), coordinate.getLat(), 5000);
        if (CollectionUtils.isEmpty(vehicleNameList)) {
            return Collections.emptyList();
        }

        // 获取车辆实时信息
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);

        // 组装数据
        List<MapVehicleDTO> result = Lists.newArrayList();
        vehicleNameList.forEach(vehicleName -> {
            MapVehicleDTO mapVehicleDTO = new MapVehicleDTO();
            mapVehicleDTO.setVehicleName(vehicleName);

            // 车辆实时信息
            VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = realtimeMap.get(vehicleName);
            if (null != vehicleRealtimeInfoDTO) {
                mapVehicleDTO.setSystemStatus(SystemStatusEnum.getCodeMapping(vehicleRealtimeInfoDTO.getSystemState()).getCode());
                MapPointBO mapPoint = TransformUtility.toGCJ02Point(vehicleRealtimeInfoDTO.getLat(), vehicleRealtimeInfoDTO.getLon());
                if (mapPoint != null) {
                    mapVehicleDTO.setLongitude(mapPoint.getLongitude());
                    mapVehicleDTO.setLatitude(mapPoint.getLatitude());
                }
            } else {
                mapVehicleDTO.setSystemStatus(SystemStatusEnum.OFFLINE.getCode());
            }

            result.add(mapVehicleDTO);
        });
        return result;
    }

    /**
     * 获取车辆基础信息
     *
     * @param vehicleName vehicleName
     * @return VehicleBasicDTO
     */
    public MapVehicleBasicDTO getVehicleBasic(String vehicleName, String username) {
        MapVehicleBasicDTO mapVehicleBasicDTO = new MapVehicleBasicDTO();
        mapVehicleBasicDTO.setVehicleName(vehicleName);

        // 获取用户权限下车辆
        Set<String> vehicleNameSet = userVehicleNameRepository.get(username);
        if (CollUtil.isEmpty(vehicleNameSet)) {
            return mapVehicleBasicDTO;
        }
        mapVehicleBasicDTO.setPermission(vehicleNameSet.contains(vehicleName));
        return mapVehicleBasicDTO;
    }

    /**
     * 获取工单信息（运维工单）
     *
     * @param vehicleName vehicleName
     * @return VehicleIssueDTO
     */
    public VehicleIssueDTO getVehicleIssue(String vehicleName) {
        QueryVehicleVO queryVehicleVO = new QueryVehicleVO();
        queryVehicleVO.setVehicleName(vehicleName);
        VehicleCurrentIssueDTO vehicleCurrentIssueDTO = issueQueryApiManager.queryVehicleCurrentIssue(queryVehicleVO);
        if (null == vehicleCurrentIssueDTO) {
            return null;
        }

        // 组装数据
        VehicleIssueDTO vehicleIssueDTO = new VehicleIssueDTO();
        vehicleIssueDTO.setIssueNumber(vehicleCurrentIssueDTO.getIssueNumber());
        vehicleIssueDTO.setIssueStatus(vehicleCurrentIssueDTO.getIssueStatus());
        vehicleIssueDTO.setAlarmEventList(vehicleCurrentIssueDTO.getAlarmEventList());
        vehicleIssueDTO.setCreateTime(vehicleCurrentIssueDTO.getCreateTime());
        vehicleIssueDTO.setAcceptUsername(vehicleCurrentIssueDTO.getAcceptUsername());
        vehicleIssueDTO.setAcceptCockpitNumber(vehicleCurrentIssueDTO.getAcceptCockpitNumber());
        return vehicleIssueDTO;
    }

    /**
     * 获取车辆版本信息
     *
     * @param vehicleName vehicleName
     * @return MapVehicleVersionDTO
     */
    public MapVehicleVersionDTO getVehicleVersion(String vehicleName) {
        MapVehicleVersionDTO mapVehicleVersionDTO = new MapVehicleVersionDTO();
        mapVehicleVersionDTO.setVehicleName(vehicleName);

        // 获取车辆类型名称
        VehicleBasicDTO vehicleBasicDTO = metadataVehicleApiManager.getByName(vehicleName);
        if (null != vehicleBasicDTO && null != vehicleBasicDTO.getVehicleTypeId()) {
            String vehicleTypeName = metadataVehicleApiManager.getVehicleTypeName(vehicleBasicDTO.getVehicleTypeId());
            mapVehicleVersionDTO.setVehicleTypeName(vehicleTypeName);
        }

        // 获取版本信息
        Map<String, String> versionMap = vehicleVersionManager.getVehicleVersion(vehicleName);
        mapVehicleVersionDTO.setRoverVersion(versionMap.get(OtaMoudleEnum.ROVER.getName()));
        mapVehicleVersionDTO.setVideoVersion(versionMap.get(OtaMoudleEnum.VIDEO.getName()));
        mapVehicleVersionDTO.setAndroidVersion(versionMap.get(OtaMoudleEnum.ANDROID.getName()));
        mapVehicleVersionDTO.setMapVersion(versionMap.get(OtaMoudleEnum.MAP.getName()));
        return mapVehicleVersionDTO;
    }

    /**
     * 获取实时信息
     *
     * @param vehicleName vehicleName
     * @return MapVehicleRealtimeDTO
     */
    public MapVehicleRealtimeDTO getVehicleRealTime(String vehicleName) {
        MapVehicleRealtimeDTO mapVehicleRealtimeDTO = new MapVehicleRealtimeDTO();
        mapVehicleRealtimeDTO.setVehicleName(vehicleName);

        // 异步任务计数器
        CountDownLatch countDownLatch = new CountDownLatch(4);

        // 获取车辆实时信息
        threadPool.execute(() -> {
            try {
                VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
                if (null != vehicleRealtimeInfoDTO) {
                    mapVehicleRealtimeDTO.setLongitude(vehicleRealtimeInfoDTO.getLon());
                    mapVehicleRealtimeDTO.setLatitude(vehicleRealtimeInfoDTO.getLat());
                    mapVehicleRealtimeDTO.setSystemStatus(SystemStatusEnum.getCodeMapping(vehicleRealtimeInfoDTO.getSystemState()).getCode());
                    mapVehicleRealtimeDTO.setSystemState(vehicleRealtimeInfoDTO.getSystemState());
                    mapVehicleRealtimeDTO.setDriveMode(vehicleRealtimeInfoDTO.getVehicleState());
                    mapVehicleRealtimeDTO.setPower(vehicleRealtimeInfoDTO.getPower());
                    mapVehicleRealtimeDTO.setSpeed(vehicleRealtimeInfoDTO.getSpeed());
                    mapVehicleRealtimeDTO.setDrivableDirection(vehicleRealtimeInfoDTO.getDrivableDirection());
                } else {
                    mapVehicleRealtimeDTO.setSystemStatus(SystemStatusEnum.OFFLINE.getCode());
                }

                // 获取车辆调度信息
                MonitorScheduleEntity monitorScheduleEntity = vehicleScheduleRepository.get(vehicleName);
                if (null != monitorScheduleEntity) {
                    mapVehicleRealtimeDTO.setScheduleName(monitorScheduleEntity.getScheduleNo());
                    mapVehicleRealtimeDTO.setScheduleStatus(monitorScheduleEntity.getScheduleState());
                    mapVehicleRealtimeDTO.setFinishMileage(getFinishedMileage(monitorScheduleEntity, vehicleRealtimeInfoDTO));
                    mapVehicleRealtimeDTO.setTotalMileage(monitorScheduleEntity.getGlobalMileage());
                    if (CollUtil.isNotEmpty(monitorScheduleEntity.getStop())) {
                        LinkedList<MonitorScheduleStopEntity> stops = monitorScheduleEntity.getStop();
                        List<ScheduleStop> scheduleStopList = Lists.newArrayListWithCapacity(stops.size());
                        stops.forEach(stop -> {
                            ScheduleStop scheduleStop = new ScheduleStop();
                            scheduleStop.setId(stop.getId());
                            scheduleStop.setName(stop.getName());
                            scheduleStop.setStopAction(stop.getStopAction());
                            scheduleStop.setTravelStatus(stop.getTravelStatus());
                            scheduleStop.setStartTime(stop.getStartTime());
                            scheduleStop.setArrivedTime(stop.getArrivedTime());
                            scheduleStop.setDepartTime(stop.getDepartTime());
                            scheduleStop.setEstDepartTime(stop.getEstDepartTime());
                            scheduleStop.setWaitingTime(stop.getWaitingTime());
                            scheduleStopList.add(scheduleStop);
                        });
                        mapVehicleRealtimeDTO.setScheduleStopList(scheduleStopList);
                    } else {
                        mapVehicleRealtimeDTO.setScheduleStopList(Collections.emptyList());
                    }
                }
            } catch (Exception e) {
                log.warn("[MapModeService] get vehicle realtime error - {}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
            }
        });

        // 车辆接管/临时停车状态
        threadPool.execute(() -> {
            try {
                VehicleTakeOverEntity vehicleTakeOverEntity = vehicleTakeOverRepository.get(vehicleName);
                if (null != vehicleTakeOverEntity) {
                    TakeOverInfo takeOverInfo = new TakeOverInfo();
                    takeOverInfo.setTakeOverUserName(vehicleTakeOverEntity.getUserName());
                    takeOverInfo.setTakeOverSource(vehicleTakeOverEntity.getCommandSource());
                    takeOverInfo.setTakeOverStatus(vehicleTakeOverEntity.getOperationStatus());
                    mapVehicleRealtimeDTO.setTakeOverInfo(takeOverInfo);
                }
            } catch (Exception e) {
                log.warn("[MapModeService] get vehicle take over error - {}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
            }
        });

        // 获取车辆告警信息
        threadPool.execute(() -> {
            try {
                List<VehicleAlarmDO.VehicleAlarmEventDO> alarmEventDTOList = vehicleAlarmRepository.get(vehicleName);
                List<AlarmEventDTO> alarmEventList = alarmEventDTOList.stream().map(tmp -> {
                    AlarmEventDTO alarmEventDTO = new AlarmEventDTO();
                    alarmEventDTO.setAlarmEvent(tmp.getType());
                    alarmEventDTO.setReportTime(tmp.getReportTime());
                    return alarmEventDTO;
                }).collect(Collectors.toList());
                mapVehicleRealtimeDTO.setAlarmEventList(alarmEventList);
            } catch (Exception e) {
                log.warn("[MapModeService] get vehicle alarm error - {}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
            }
        });

        // 获取车辆状态信息
        threadPool.execute(() -> {
            try {
                VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
                mapVehicleRealtimeDTO.setGpsSignal(vehicleStatusDO.getGpsSignal());
                mapVehicleRealtimeDTO.setSceneSignal(vehicleStatusDO.getSceneSignal());
            } catch (Exception e) {
                log.warn("[MapModeService] get vehicle status error - {}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
            }
        });

        // 等待异步任务执行完毕
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("[MapModeService] getVehicleRealtime task fail");
        }
        return mapVehicleRealtimeDTO;
    }

    /**
     * 当前调度完成里程
     *
     * @param monitorScheduleEntity monitorScheduleEntity
     * @param vehicleRealtimeInfoDTO vehicleRealtimeInfoDTO
     * @return finishedMileage
     */
    private Double getFinishedMileage(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO) {
        Double finishedMileage = Optional.ofNullable(monitorScheduleEntity.getFinishedMileage()).orElse(0.0);
        if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(),
            VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(),
            VehicleScheduleState.RETURN.getVehicleScheduleState())) {
            Double currentStopFinishedMileage;
            if (vehicleRealtimeInfoDTO == null || vehicleRealtimeInfoDTO.getCurrentStopFinishedMileage() == null) {
                currentStopFinishedMileage = 0.0;
            } else {
                currentStopFinishedMileage = vehicleRealtimeInfoDTO.getCurrentStopFinishedMileage();
            }
            finishedMileage += currentStopFinishedMileage;
        }
        return finishedMileage;
    }

    /**
     * 获取规划路径
     *
     * @param vehicleName vehicleName
     * @return MapVehiclePathDTO
     */
    public MapVehiclePathDTO getVehiclePath(String vehicleName) {
        // 获取车辆路径信息
        MonitorStationVehicleMapInfoDTO singleVehicleRouting = monitorMapInfoService.getSingleVehicleRouting(vehicleName);
        if (null == singleVehicleRouting) {
            return null;
        }

        // 获取车辆当前位置
        MapVehiclePathDTO mapVehiclePathDTO = new MapVehiclePathDTO();
        mapVehiclePathDTO.setVehiclePosition(new Position(singleVehicleRouting.getLon(), singleVehicleRouting.getLat()));

        // 获取调度停靠点
        List<MonitorStopMapInfoDTO> stops = singleVehicleRouting.getStop();
        if (CollUtil.isNotEmpty(stops)) {
            List<Position> stopPointList = Lists.newArrayListWithCapacity(stops.size());
            stops.forEach(stop -> {
                if (stop.getLon() == null || stop.getLat() == null) {
                    return;
                }
                stopPointList.add(new Position(stop.getLon(), stop.getLat()));
            });
            mapVehiclePathDTO.setStopPointList(stopPointList);
        } else {
            mapVehiclePathDTO.setStopPointList(Collections.emptyList());
        }

        // 获取规划路径点位
        mapVehiclePathDTO.setRoutingPointList(singleVehicleRouting.getPlanningRoutingPoint());

        return mapVehiclePathDTO;
    }

    /**
     * 获取车辆保单信息列表
     *
     * @param vehicleInsuranceVO vehicleInsuranceVO
     * @return List<VehicleInsuranceDTO>
     */
    public List<VehicleInsuranceDTO> getVehicleEffectiveInsuranceList(VehicleInsuranceVO vehicleInsuranceVO) {
        if (StrUtil.isBlank(vehicleInsuranceVO.getVehicleName())) {
            return Lists.newArrayList();
        }
        return metadataInsuranceManager.getVehicleEffectiveInsuranceList(vehicleInsuranceVO.getVehicleName());
    }

    /**
     * 获取保单文件外链
     *
     * @param vehicleInsuranceVO vehicleInsuranceVO
     * @return InsuranceAttachmentDTO
     */
    public InsuranceAttachmentDTO getPolicyAttachmentFileUrl(VehicleInsuranceVO vehicleInsuranceVO) {
        if (Objects.isNull(vehicleInsuranceVO.getVehicleInsuranceId())) {
            return new InsuranceAttachmentDTO();
        }
        return metadataInsuranceManager.getPolicyAttachmentFileUrl(vehicleInsuranceVO.getVehicleInsuranceId());
    }
}
