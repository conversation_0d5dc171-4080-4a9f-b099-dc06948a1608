/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.cockpit;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.monitor.api.domain.enums.CockpitModeEnum;
import com.jdx.rover.monitor.api.domain.enums.CockpitStatusEnum;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.cockpit.CockpitTeamManagerStatisticDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitTransferIssueDTO;
import com.jdx.rover.monitor.dto.cockpit.MultiCockpitDTO;
import com.jdx.rover.monitor.dto.cockpit.MultiCockpitPageDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.cockpit.CockpitTeamStatusDO;
import com.jdx.rover.monitor.entity.user.UserIssueDO;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitTeamStatusRepository;
import com.jdx.rover.monitor.repository.redis.metadata.CockpitVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.user.UserIssueRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 驾驶团队服务类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class CockpitTeamService {

  /**
   * 座舱团队管理服务
   */
  private final CockpitTeamManager cockpitTeamManager;
  /**
   * 座舱车辆列表缓存服务
   */
  private final CockpitVehicleNameRepository cockpitVehicleNameRepository;
  /**
   * 座舱状态列表缓存服务
   */
  private final CockpitStatusRepository cockpitStatusRepository;
  /**
   * 座舱团队状态缓存服务
   */
  private final CockpitTeamStatusRepository cockpitTeamStatusRepository;
  /**
   * 座舱当前工单列表
   */
  private final UserIssueRepository userIssueRepository;
  /**
   * 座舱当前工单列表
   */
  private final UserStatusRepository userStatusRepository;
  /**
   * 车辆实时缓存
   */
  private final VehicleRealtimeRepository vehicleRealtimeRepository;
  /**
   * 车辆调度缓存
   */
  private final VehicleScheduleRepository vehicleScheduleRepository;

  /**
   * 初始化驾舱所有车辆基础数据
   */
  public MultiCockpitPageDTO getMultiCockpitRealInfo(String cockpitTeamNumber) {
    ParameterCheckUtility.checkNotNullNorEmpty(cockpitTeamNumber, "getMultiCockpitRealInfo#cockpitTeamNumber");
    CockpitTeamStatusDO teamInfoDto = cockpitTeamStatusRepository.get(cockpitTeamNumber);
    MultiCockpitPageDTO cockpitPageResult = new MultiCockpitPageDTO();
    if (Objects.isNull(teamInfoDto) || CollectionUtils.isEmpty(teamInfoDto.getCockpitList())) {
      return cockpitPageResult;
    }
    List<CockpitStatusDO> cockpitStatusList = cockpitStatusRepository.list(teamInfoDto.getCockpitList());
    List<String> userNameList = cockpitStatusList.stream()
            .filter(user -> StringUtils.isNotBlank(user.getCockpitUserName()))
            .map(CockpitStatusDO ::getCockpitUserName)
            .collect(Collectors.toList());
    Map<String, UserIssueDO> userIssueMap = userIssueRepository.listMap(userNameList);
    List<String> vehicleNameList = new ArrayList<>();
    userIssueMap.entrySet().stream().forEach(entry -> {
      if (Objects.isNull(entry.getValue()) ||
              CollectionUtils.isEmpty(entry.getValue().getIssueList())) {
        return;
      }
      List<UserIssueDO.Issue> issueList = entry.getValue().getIssueList();
      vehicleNameList.addAll(issueList.stream().filter(issue ->
              !issue.getEmergencyIssue()).map(UserIssueDO.Issue::getVehicleName).collect(Collectors.toList()));
    });

    Map<String, UserStatusDO> userStatusMap = userStatusRepository.listMap(userNameList);
    Map<String, VehicleRealtimeInfoDTO> vehicleRealtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
    Map<String, MonitorScheduleEntity> scheduleEntityMap = vehicleScheduleRepository.listMap(vehicleNameList);
    Map<String, List<CockpitStatusDO>> cockpitStatusMap =
            cockpitStatusList.stream().collect(Collectors.groupingBy(statusDO ->
                    Optional.ofNullable(statusDO.getCockpitStatus()).orElse(CockpitStatusEnum.OFFLINE.getValue())));
    List<CockpitStatusDO> workCockpitList = cockpitStatusMap.get(CockpitStatusEnum.WORK.getValue());
    if (CollectionUtils.isNotEmpty(workCockpitList)) {
      cockpitPageResult.setWorkCockpitList(
              workCockpitList.stream().map(cockpitStaus ->
                      buildMultiCockpitDto(cockpitStaus, userStatusMap, vehicleRealtimeMap, scheduleEntityMap, userIssueMap)).sorted(Comparator.comparing(MultiCockpitDTO :: getCockpitNumber)).collect(Collectors.toList()));
    }
    List<CockpitStatusDO> restCockpitList = cockpitStatusMap.get(CockpitStatusEnum.REST.getValue());
    if (CollectionUtils.isNotEmpty(restCockpitList)) {
      cockpitPageResult.setRestCockpitList(
              restCockpitList.stream().map(cockpitStaus ->
                      buildMultiCockpitDto(cockpitStaus, userStatusMap, vehicleRealtimeMap, scheduleEntityMap, userIssueMap)).sorted(Comparator.comparing(MultiCockpitDTO :: getCockpitNumber)).collect(Collectors.toList()));
    }
    List<CockpitStatusDO> offlineCockpitList = cockpitStatusMap.get(CockpitStatusEnum.OFFLINE.getValue());
    if (CollectionUtils.isNotEmpty(offlineCockpitList)) {
      cockpitPageResult.setOfflineCockpitList(
              offlineCockpitList.stream().map(cockpitStaus ->
                      buildMultiCockpitDto(cockpitStaus, userStatusMap, vehicleRealtimeMap, scheduleEntityMap, userIssueMap)).sorted(Comparator.comparing(MultiCockpitDTO :: getCockpitNumber)).collect(Collectors.toList()));
    }
    return cockpitPageResult;
  }

  /**
   * 获取可转单座席列表
   */
  public List<CockpitTransferIssueDTO> getAllowIssueCockpitList(String cockpitTeamNumber, String vehicleName) {
    List<CockpitTransferIssueDTO> transferCockpitList = new ArrayList<>();
    CockpitTeamStatusDO teamInfoDto = cockpitTeamStatusRepository.get(cockpitTeamNumber);
    if (Objects.isNull(teamInfoDto) || CollectionUtils.isEmpty(teamInfoDto.getCockpitList())) {
      return transferCockpitList;
    }
    String userName = UserUtils.getAndCheckLoginUser();
    UserStatusDO userStatusDo = userStatusRepository.get(userName);
    if(Objects.isNull(userStatusDo)) {
      return transferCockpitList;
    }

    List<CockpitStatusDO> cockpitStatusList = cockpitStatusRepository.list(teamInfoDto.getCockpitList());
    if (CollectionUtils.isEmpty(cockpitStatusList)) {
      return transferCockpitList;
    }
    // 工作中并且是听单或者盯车模式
    List<String> userNameList = cockpitStatusList.stream().filter(cockpitStatusDO ->
            !StringUtils.equals(userName, cockpitStatusDO.getCockpitUserName()) &&
            StringUtils.equals(cockpitStatusDO.getCockpitStatus(), CockpitStatusEnum.WORK.getValue()) &&
            StringUtils.equalsAny(cockpitStatusDO.getCockpitMode(), CockpitModeEnum.LISTENING_MODE.getValue(), CockpitModeEnum.WATCH_CAR_MODE.getValue()))
            .filter(cockpitStatusDO -> {
               if (StringUtils.equals(userStatusDo.getCockpitMode(), CockpitModeEnum.LISTENING_MODE.getValue())) {
                return !StringUtils.equals(cockpitStatusDO.getCockpitMode(), CockpitModeEnum.WATCH_CAR_MODE.getValue());
              } else if (StringUtils.equals(userStatusDo.getCockpitMode(), CockpitModeEnum.WATCH_CAR_MODE.getValue())) {
                 if (CockpitModeEnum.WATCH_CAR_MODE.getValue().equals(cockpitStatusDO.getCockpitMode())) {
                   Set<String> vehicleNameSet = cockpitVehicleNameRepository.get(cockpitStatusDO.getCockpitNumber());
                   return vehicleNameSet.contains(vehicleName);
                 }
                 return true;
               }
              return true;
            }).map(CockpitStatusDO :: getCockpitUserName).collect(Collectors.toList());
    List<UserIssueDO> userIssueList = userIssueRepository.list(userNameList);
    if (CollectionUtils.isNotEmpty(userIssueList)) {
      for (UserIssueDO userIssue : userIssueList) {
        if (Objects.isNull(userIssue) || CollectionUtils.isEmpty(userIssue.getIssueList())) {
          continue;
        }
        List<UserIssueDO.Issue> issueLists =
                userIssue.getIssueList().stream().filter(issue ->
                        !issue.getEmergencyIssue()).collect(Collectors.toList());
       if (!issueLists.isEmpty()) {
         userNameList.remove(userIssue.getUserName());
       }
      }
    }
    cockpitStatusList.stream().filter(cockpit -> userNameList.contains(cockpit.getCockpitUserName())).forEach(cockpit -> {
      CockpitTransferIssueDTO transferCockpit = new CockpitTransferIssueDTO();
      transferCockpit.setCockpitNumber(cockpit.getCockpitNumber());
      transferCockpit.setCockpitUserName(cockpit.getCockpitUserName());
      transferCockpit.setCockpitType(cockpit.getCockpitType());
      transferCockpitList.add(transferCockpit);
    });
    return transferCockpitList;
  }

  /**
   * 获取驾仓团队管理员统计看板信息
   */
  public CockpitTeamManagerStatisticDTO getTeamManagerStatistic(String cockpitTeamNumber) {
    String userName = UserUtils.getAndCheckLoginUser();
    CockpitTeamStatusDO cockpitTeamDO = cockpitTeamStatusRepository.get(cockpitTeamNumber);
    CockpitTeamManagerStatisticDTO cockpitTeamManagerDTO = new CockpitTeamManagerStatisticDTO();
    cockpitTeamManagerDTO.setCockpitTeamNumber(cockpitTeamNumber);
    if (Objects.isNull(cockpitTeamDO)) {
      return cockpitTeamManagerDTO;
    }
    Date currentDate = new Date();
    cockpitTeamManagerDTO.setCockpitTeamName(cockpitTeamDO.getCockpitTeamName());
    boolean isTodayRequest = Objects.equals(DateUtil.today(), cockpitTeamDO.getIssueStatisticDate());
    cockpitTeamManagerDTO.setWaitAcceptIssueCount(isTodayRequest? cockpitTeamDO.getWaitAcceptIssueCount() : 0);
    cockpitTeamManagerDTO.setCompleteIssueCount(isTodayRequest? cockpitTeamDO.getCompleteIssueCount() : 0);
    CockpitTeamManagerStatisticDTO.CockpitTeamManagerInfo managerInfo = new CockpitTeamManagerStatisticDTO.CockpitTeamManagerInfo();
    UserStatusDO userStatus = userStatusRepository.get(userName);
    if (!Objects.isNull(userStatus)) {
      managerInfo.setCompleteIssueCount(userStatus.getCompleteIssueCount());
      int dateTime = 0;
      if (!Objects.isNull(userStatus.getWorkStartTime()) && DateUtil.isSameDay(userStatus.getWorkStartTime(), currentDate)) {
        dateTime = (int) DateUtil.between(userStatus.getWorkStartTime(), currentDate, DateUnit.SECOND);
      }
      managerInfo.setWorkTimeTotal(Objects.isNull(userStatus.getWorkTimeTotalHistory()) ?
              dateTime : userStatus.getWorkTimeTotalHistory() + dateTime);
    } else {
      managerInfo.setWorkTimeTotal(0);
      managerInfo.setCompleteIssueCount(0);
    }
    cockpitTeamManagerDTO.setManagerInfo(managerInfo);
    return cockpitTeamManagerDTO;
  }

  /**
   * 构造多车座席卡片
   */
  private MultiCockpitDTO buildMultiCockpitDto(CockpitStatusDO cockpitStatus, Map<String, UserStatusDO> userStatusMap,
          Map<String, VehicleRealtimeInfoDTO> vehicleRealtimeMap,
           Map<String, MonitorScheduleEntity> scheduleEntityMap, Map<String, UserIssueDO> userIssueMap) {
    MultiCockpitDTO cockpitDto = new MultiCockpitDTO();
    cockpitDto.setCockpitType(cockpitStatus.getCockpitType());
    cockpitDto.setCockpitMode(cockpitStatus.getCockpitMode());
    cockpitDto.setCockpitNumber(cockpitStatus.getCockpitNumber());
    if (StringUtils.isBlank(cockpitStatus.getCockpitStatus())
            || StringUtils.equals(cockpitStatus.getCockpitStatus(), CockpitStatusEnum.OFFLINE.getValue())) {
      return cockpitDto;
    }
    cockpitDto.setOnlineUser(cockpitStatus.getCockpitUserName());
    UserIssueDO userIssue = userIssueMap.get(cockpitStatus.getCockpitUserName());
    UserStatusDO userStatus = userStatusMap.get(cockpitStatus.getCockpitUserName());
    // 用户每日统计数据
    Date currentDate = new Date();
    if (!Objects.isNull(userStatus)) {
      cockpitDto.setCompletedIssueCount(userStatus.getCompleteIssueCount());
      int dateTime = 0;
      if (!Objects.isNull(userStatus.getWorkStartTime()) && DateUtil.isSameDay(userStatus.getWorkStartTime(), currentDate)) {
        dateTime = (int) DateUtil.between(userStatus.getWorkStartTime(), currentDate, DateUnit.SECOND);
      }
      cockpitDto.setWorkTimeTotal(Objects.isNull(userStatus.getWorkTimeTotalHistory()) ?
              dateTime : userStatus.getWorkTimeTotalHistory() + dateTime);
    }
    if (StringUtils.equals(cockpitStatus.getCockpitStatus(), CockpitStatusEnum.WORK.getValue()) &&
       !Objects.isNull(userIssue)) {
      List<UserIssueDO.Issue> issueList = userIssue.getIssueList();
      if (CollectionUtils.isEmpty(issueList)) {
        return cockpitDto;
      }
      List<MultiCockpitDTO.CockpitIssue> cockpitIssueList =
              issueList.stream().filter(issue -> !Objects.isNull(issue) && !issue.getEmergencyIssue()).map(issue -> {
        MultiCockpitDTO.CockpitIssue cockpitIssue = new MultiCockpitDTO.CockpitIssue();
        cockpitIssue.setVehicleName(issue.getVehicleName());
        cockpitIssue.setIssueNumber(issue.getIssueNumber());
        cockpitIssue.setIssueState(issue.getIssueStatus());
        cockpitIssue.setIssueReminder(issue.getIssueReminder());
        cockpitIssue.setIssueType(issue.getIssueType());
        if (!Objects.isNull(issue.getIssueStartTime())) {
           cockpitIssue.setIssueStartTime(issue.getIssueStartTime());
           cockpitIssue.setIssueProcessTime((int) DateUtil.between(issue.getIssueStartTime(), new Date(), DateUnit.SECOND));
        }
        VehicleRealtimeInfoDTO realtimeInfo = vehicleRealtimeMap.get(issue.getVehicleName());
        MonitorScheduleEntity scheduleEntity = scheduleEntityMap.get(issue.getVehicleName());
        if (!Objects.isNull(realtimeInfo)) {
          cockpitIssue.setVehicleState(realtimeInfo.getVehicleState());
          cockpitIssue.setSystemState(realtimeInfo.getSystemState());
          cockpitIssue.setSpeed(Objects.isNull(realtimeInfo.getSpeed()) ? 0 : realtimeInfo.getSpeed().floatValue());
        } else {
          cockpitIssue.setSystemState(SystemStateEnum.OFFLINE.getSystemState());
        }
        if (CollectionUtils.isNotEmpty(issue.getIssueEventList())) {
          cockpitIssue.setAlarmEventList(issue.getIssueEventList().stream().map(alarm -> {
            MultiCockpitDTO.AlarmEvent alarmEvent = new MultiCockpitDTO.AlarmEvent();
            alarmEvent.setAlarmEvent(alarm.getEventType());
            alarmEvent.setReportTime(alarm.getStartTime());
            return alarmEvent;
          }).collect(Collectors.toList()));
        }
        cockpitIssue.setScheduleState(Objects.isNull(scheduleEntity) ?
                VehicleScheduleState.WAITING.getVehicleScheduleState():scheduleEntity.getScheduleState());
        return cockpitIssue;
      }).collect(Collectors.toList());
      cockpitDto.setIssueList(cockpitIssueList);
    }
    return cockpitDto;
  }


}
