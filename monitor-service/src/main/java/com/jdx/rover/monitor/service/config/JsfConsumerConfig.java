/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.k2.indoor.map.jsf.service.MapPointInfoJsfService;
import com.jdx.rover.device.jsfapi.service.server.device.IntelligentDeviceServerDeviceService;
import com.jdx.rover.device.jsfapi.service.server.devicecommand.IntelligentDeviceServerDeviceCommandTaskInfoService;
import com.jdx.rover.device.jsfapi.service.server.group.IntelligentDeviceServerGroupService;
import com.jdx.rover.device.jsfapi.service.server.product.IntelligentDeviceServerProductService;
import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import com.jdx.rover.transport.api.service.TransportDevicePropertyJsfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * jsf配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@Component
public class JsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册设备基础服务接口
     */
    @Bean
    public IntelligentDeviceServerDeviceService metadataDeviceServerService() {
        return jsfConsumerRegister.createConsumerConfig(IntelligentDeviceServerDeviceService.class).refer();
    }

    /**
     * 注册设备组服务接口
     */
    @Bean
    public IntelligentDeviceServerGroupService metadataDeviceServerGroupService() {
        return jsfConsumerRegister.createConsumerConfig(IntelligentDeviceServerGroupService.class).refer();
    }

    /**
     * 注册设备端云服务接口
     */
    @Bean
    public TransportDevicePropertyJsfService transportDeviceServerService() {
        return jsfConsumerRegister.createConsumerConfig(TransportDevicePropertyJsfService.class).refer();
    }

    /**
     * 注册设备端云服务接口
     */
    @Bean
    public IntelligentDeviceServerProductService transportDeviceProductrService() {
        return jsfConsumerRegister.createConsumerConfig(IntelligentDeviceServerProductService.class).refer();
    }

    /**
     * 注册机器人地图服务接口
     */
    @Bean
    public MapPointInfoJsfService indoorMapPointJsfService() {
        return jsfConsumerRegister.createConsumerConfig(MapPointInfoJsfService.class).refer();
    }

    /**
     * 注册 IntelligentDeviceServerDeviceCommandTaskInfoService
     */
    @Bean
    public IntelligentDeviceServerDeviceCommandTaskInfoService intelligentDeviceServerDeviceCommandTaskInfoService() {
        return jsfConsumerRegister.createConsumerConfig(IntelligentDeviceServerDeviceCommandTaskInfoService.class).refer();
    }

}