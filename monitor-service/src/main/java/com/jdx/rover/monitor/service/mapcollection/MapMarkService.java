/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.mapcollection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.amazonaws.HttpMethod;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.metadata.api.domain.enums.RoverBucketEnum;
import com.jdx.rover.monitor.dto.mapcollection.MarkCreateDTO;
import com.jdx.rover.monitor.dto.mapcollection.MarkSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.MarkSearchDTO.AttachmentDTO;
import com.jdx.rover.monitor.dto.mapcollection.MarkSearchDTO.MarkDTO;
import com.jdx.rover.monitor.vo.mapcollection.MarkCreateVO;
import com.jdx.rover.monitor.vo.mapcollection.MarkDeleteVO;
import com.jdx.rover.monitor.vo.mapcollection.MarkUpdateVO;
import com.jdx.rover.monitor.vo.mapcollection.MarkUpdateVO.AttachmentVO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mapcollection.MarkTypeEnum;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionMarkManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionMark;
import com.jdx.rover.monitor.repository.redis.mapcollection.MapCollectionMarkRedis;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.service.config.ducc.DuccMobileProperties;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/12 16:48
 * @description 采集标记服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MapMarkService {

    /**
     * MapCollectionMarkManager
     */
    private final MapCollectionMarkManager mapCollectionMarkManager;

    /**
     * MapCollectionMarkRedis
     */
    private final MapCollectionMarkRedis mapCollectionMarkRedis;

    /**
     * S3Properties
     */
    private final S3Properties s3Properties;

    /**
     * DuccMobileProperties
     */
    private final DuccMobileProperties duccMobileProperties;

    /**
     * 获取标记列表
     *
     * @param positionVO positionVO
     * @return MarkSearchDTO
     */
    public MarkSearchDTO getMarkList(PositionVO positionVO) {
        MarkSearchDTO markSearchDTO = new MarkSearchDTO();
        List<Integer> markIdList = mapCollectionMarkRedis.searchAllTypeMember(positionVO.getLatitude(), positionVO.getLongitude(), duccMobileProperties.getMarkRadius());
        if (CollUtil.isEmpty(markIdList)) {
            return markSearchDTO;
        }

        List<MapCollectionMark> mapCollectionMarkList = mapCollectionMarkManager.listByIds(markIdList);
        if (CollUtil.isEmpty(mapCollectionMarkList)) {
            return markSearchDTO;
        }

        List<MarkDTO> markList = Lists.newArrayListWithCapacity(mapCollectionMarkList.size());
        markSearchDTO.setMarkList(markList);
        final Date expiration = DateUtil.offsetHour(new Date(), 24);
        mapCollectionMarkList.forEach(mapCollectionMark -> {
            MarkDTO markDTO = new MarkDTO();
            markDTO.setLatitude(mapCollectionMark.getLatitude());
            markDTO.setLongitude(mapCollectionMark.getLongitude());
            markDTO.setAddressName(mapCollectionMark.getAddressName());
            markDTO.setMarkId(mapCollectionMark.getId());
            markDTO.setMarkType(mapCollectionMark.getMarkType());
            markDTO.setRemark(mapCollectionMark.getRemark());
            if (CollUtil.isNotEmpty(mapCollectionMark.getAttachmentList())) {
                List<AttachmentDTO> attachmentList = Lists.newArrayListWithCapacity(mapCollectionMark.getAttachmentList().size());
                mapCollectionMark.getAttachmentList().forEach(attachment -> {
                    AttachmentDTO attachmentDTO = new AttachmentDTO();
                    attachmentDTO.setType(attachment.getType());
                    attachmentDTO.setFileKey(attachment.getFileKey());
                    attachmentDTO.setUrl(S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(),
                        RoverBucketEnum.ROVER_OPERATION.getName(), attachment.getFileKey(), HttpMethod.GET, expiration).toString());
                    attachmentList.add(attachmentDTO);
                });
                markDTO.setAttachmentList(attachmentList);
            }
            markList.add(markDTO);
        });
        return markSearchDTO;
    }

    /**
     * 新增标记
     *
     * @param markCreateVO markCreateVO
     * @return MarkCreateDTO
     */
    public MarkCreateDTO createMark(MarkCreateVO markCreateVO) {
        int markId = mapCollectionMarkManager.createMark(markCreateVO);

        // GeoHash更新
        mapCollectionMarkRedis.addGeoMember(markCreateVO, markId);

        MarkCreateDTO markCreateDTO = new MarkCreateDTO();
        markCreateDTO.setMarkId(markId);
        return markCreateDTO;
    }

    /**
     * 修改标记
     *
     * @param markUpdateVO markUpdateVO
     */
    public void updateMark(MarkUpdateVO markUpdateVO) {
        // 附件校验，不能存在相同fileKey
        if (CollUtil.isNotEmpty(markUpdateVO.getAttachmentList())) {
            Set<String> distinctFileKey = markUpdateVO.getAttachmentList().stream()
                .map(AttachmentVO::getFileKey)
                .collect(Collectors.toSet());
            if (distinctFileKey.size() != markUpdateVO.getAttachmentList().size()) {
                throw new AppException(MonitorErrorEnum.ERROR_DUPLICATE_FILE_KEY.getCode(), MonitorErrorEnum.ERROR_DUPLICATE_FILE_KEY.getMessage());
            }
        }

        // 更新数据库
        boolean updated = mapCollectionMarkManager.updateMark(markUpdateVO);

        // 更新成功，GeoHash更新
        if (updated) {
            mapCollectionMarkRedis.addGeoMember(markUpdateVO, markUpdateVO.getMarkId());
        }
    }

    /**
     * 删除标记
     *
     * @param markDeleteVO markDeleteVO
     */
    public void deleteMark(MarkDeleteVO markDeleteVO) {
        // 数据库删除
        mapCollectionMarkManager.deleteMark(markDeleteVO.getMarkId());

        // 清除GeoHash
        mapCollectionMarkRedis.removeGeoMember(markDeleteVO.getMarkId(), MarkTypeEnum.of(markDeleteVO.getMarkType()));
    }
}
