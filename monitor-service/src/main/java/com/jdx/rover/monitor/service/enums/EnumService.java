/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.enums;

import com.google.common.collect.Lists;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitListDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamListDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitUserListDTO;
import com.jdx.rover.monitor.dto.EnumDTO;
import com.jdx.rover.monitor.dto.MonitorDataCategoryDTO;
import com.jdx.rover.monitor.dto.deployment.DeployEnumDTO;
import com.jdx.rover.monitor.enums.CategoryFieldDisplayEnum;
import com.jdx.rover.monitor.enums.FieldDisplayEnum;
import com.jdx.rover.monitor.enums.PdaFieldDisplayEnum;
import com.jdx.rover.monitor.enums.deployment.DeploymentFieldDisplayEnum;
import com.jdx.rover.monitor.manager.cockpit.CockpitDataManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公共获取枚举列表数据
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class EnumService {

  private final CockpitDataManager cockpitDataManager;

  /**
   * 通过FieldDisplayEnum.value获取枚举列表数据
   */
  public Map<String, List<Map<String, Object>>> getEnumListMap(List<String> enumList) {
    Map<String, List<Map<String, Object>>> result = new HashMap<>();
    if (CollectionUtils.isEmpty(enumList)) {
      return result;
    }
    for (FieldDisplayEnum em : FieldDisplayEnum.values()) {
      if (!enumList.contains(em.getValue())) {
        continue;
      }
      List<Map<String, Object>> list = getFieldMapList(em.getClazz(), em.getFieldNameList(), em.getDisplayNameList());
      if (!CollectionUtils.isEmpty(list)) {
        result.put(em.getValue(), list);
      }
    }
    return result;
  }

  /**
   * 获得枚举对应指定字段值的Map<br>
   * 键为枚举名，值为字段值
   *
   * @param clazz           枚举类
   * @param fieldNameList   字段名列表
   * @param displayNameList 字段名展示列表
   * @return 枚举名对应指定字段值的Map
   */
  private static List<Map<String, Object>> getFieldMapList(Class<? extends Enum<?>> clazz, List<String> fieldNameList, List<String> displayNameList) {
    if (null == clazz) {
      return null;
    }
    final Enum<?>[] enums = clazz.getEnumConstants();
    if (null == enums) {
      return null;
    }
    if (CollectionUtils.isEmpty(displayNameList) || fieldNameList.size() > displayNameList.size()) {
      displayNameList = fieldNameList;
    }
    List<Field> fieldList = new ArrayList<>();
    for (String fieldName : fieldNameList) {
      Field field = ReflectionUtils.findField(clazz, fieldName);
      field.setAccessible(true);
      fieldList.add(field);
    }
    List<Map<String, Object>> result = new ArrayList<>();
    for (Enum<?> e : enums) {
      Map<String, Object> map = new HashMap<>();
      for (int i = 0; i < fieldList.size(); i++) {
        Field field = fieldList.get(i);
        map.put(displayNameList.get(i), ReflectionUtils.getField(field, e));
      }
      result.add(map);
    }
    return result;
  }


  public Map<String, List<MonitorDataCategoryDTO>> getMultiCategoryList(List<String> keyList) {
    Map<String, List<MonitorDataCategoryDTO>> result = new HashMap<>();
    if (CollectionUtils.isEmpty(keyList)) {
      return result;
    }
    for (CategoryFieldDisplayEnum em : CategoryFieldDisplayEnum.values()) {
      if (!keyList.contains(em.getValue())) {
        continue;
      }
      Class<? extends Enum<?>> clazz = em.getClazz();
      final Enum<? extends Enum<?>>[] enums = clazz.getEnumConstants();
      List<MonitorDataCategoryDTO> dataCategoryDTOList = Lists.newLinkedList();
      List<Field> fieldList = new ArrayList<>();
      for (String fieldName : em.getFieldNameList()) {
        Field field = ReflectionUtils.findField(clazz, fieldName);
        field.setAccessible(true);
        fieldList.add(field);
      }
      Class<? extends Enum<?>> childClazz = em.getChildClazz();
      List<Field> childFieldList = new ArrayList<>();
      for (String fieldName : em.getChildFieldNameList()) {
        Field field = ReflectionUtils.findField(childClazz, fieldName);
        field.setAccessible(true);
        childFieldList.add(field);
      }
      for (Enum categoryEnum : enums) {
        MonitorDataCategoryDTO dataCategoryDTO = new MonitorDataCategoryDTO();
        dataCategoryDTO.setValue(categoryEnum.name());
        dataCategoryDTO.setLable(ReflectionUtils.getField(fieldList.get(0),categoryEnum).toString());
        List<? extends Enum> childEnumList = (List<? extends Enum>) ReflectionUtils.getField(fieldList.get(1),categoryEnum);
        dataCategoryDTO.setChild(childEnumList.stream().map(childEnum -> {
          MonitorDataCategoryDTO child = new MonitorDataCategoryDTO();
          child.setValue(ReflectionUtils.getField(childFieldList.get(0),childEnum).toString());
          child.setLable(ReflectionUtils.getField(childFieldList.get(1),childEnum).toString());
          return child;
        }).collect(Collectors.toList()));
        dataCategoryDTOList.add(dataCategoryDTO);
      }
      result.put(em.getValue(), dataCategoryDTOList);
    }
    return result;
  }

  /**
   * 获取所属团队下拉列表
   *
   * @return List<EnumDTO>
   */
  public List<EnumDTO> getCockpitTeamList() {
    List<CockpitTeamListDTO> cockpitTeamList = cockpitDataManager.getCockpitTeamList(null);
    List<EnumDTO> enumDTOList = new ArrayList<>();
    cockpitTeamList.forEach(v -> enumDTOList.add(EnumDTO.builder().code(v.getTeamNumber()).name(v.getTeamName()).build()));
    return enumDTOList;
  }

  /**
   * 获取工单受理人下拉列表
   *
   * @return List<EnumDTO>
   */
  public List<EnumDTO> getUserList() {
    List<CockpitUserListDTO> cockpitUserList = cockpitDataManager.getCockpitUserList(null);
    List<EnumDTO> enumDTOList = new ArrayList<>();
    cockpitUserList.forEach(v -> enumDTOList.add(EnumDTO.builder().code(v.getUsername()).name(v.getUsername()).build()));
    return enumDTOList;
  }

  /**
   * 获取工单受理坐席下拉列表
   *
   * @return List<EnumDTO>
   */
  public List<EnumDTO> getCockpitList() {
    List<CockpitListDTO> cockpitList = cockpitDataManager.getCockpitList(null);
    List<EnumDTO> enumDTOList = new ArrayList<>();
    cockpitList.forEach(v -> enumDTOList.add(EnumDTO.builder().code(v.getCockpitNumber()).name(v.getCockpitNumber()).build()));
    return enumDTOList;
  }

  public Map<String, List<Map<String, Object>>> getPdaEnumListMap(List<String> keyList) {
    Map<String, List<Map<String, Object>>> result = new HashMap<>();
    if (CollectionUtils.isEmpty(keyList)) {
      return result;
    }
    for (PdaFieldDisplayEnum em : PdaFieldDisplayEnum.values()) {
      if (!keyList.contains(em.getValue())) {
        continue;
      }
      List<Map<String, Object>> list = getFieldMapList(em.getClazz(), em.getFieldNameList(), em.getDisplayNameList());
      if (!CollectionUtils.isEmpty(list)) {
        result.put(em.getValue(), list);
      }
    }
    return result;
  }

  /**
   * 获取部署平台线路勘查枚举
   *
   * @return List<DeployEnumDTO>
   */
  public List<DeployEnumDTO> getDeploymentEnumList() {
    List<DeployEnumDTO> deploymentEnumList = Lists.newLinkedList();
    for (DeploymentFieldDisplayEnum em : DeploymentFieldDisplayEnum.values()) {
      if (em == DeploymentFieldDisplayEnum.DEFAULT) {
        continue;
      }

      List<Map<String, Object>> list = getFieldMapList(em.getClazz(), em.getFieldNameList(), em.getDisplayNameList());
      if (!CollectionUtils.isEmpty(list)) {
        list.removeIf(map -> map.containsValue("UNKNOWN"));
        DeployEnumDTO deployEnumDTO = new DeployEnumDTO();
        deployEnumDTO.setEnumType(em.getValue());
        deployEnumDTO.setEnumList(list);
        deploymentEnumList.add(deployEnumDTO);
      }
    }
    return deploymentEnumList;
  }
}