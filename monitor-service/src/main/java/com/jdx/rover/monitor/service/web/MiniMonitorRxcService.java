/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.vo.word.WordMonitorAddVo;
import com.jdx.rover.metadata.domain.vo.word.WordMonitorDeleteVo;
import com.jdx.rover.metadata.jsf.service.word.MetadataWordMonitorService;
import com.jdx.rover.monitor.enums.MqttMsgTypeEnum;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteShoutVO;
import com.jdx.rover.monitor.vo.MiniMonitorWordAddVO;
import com.jdx.rover.monitor.vo.MiniMonitorWordDeleteVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * This is a remote call service function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
public class MiniMonitorRxcService {

  @Autowired
  private MetadataWordMonitorService metadataWordMonitorJsfService;

  /**
   * mqtt消息发送服务
   */
  @Autowired
  private MqttManager mqttManager;

  /**
   * <p>
   * Get word list.
   * </p>
   */
  public HttpResult getWordList() {
    return metadataWordMonitorJsfService.getWordList();
  }

  /**
   * <p>
   * Add word to metadata.
   * </p>
   */
  public HttpResult addWord(MiniMonitorWordAddVO wordMonitorAddVo) {
    WordMonitorAddVo wordAddVo = new WordMonitorAddVo();
    wordAddVo.setUserName(LoginUtils.getUsername());
    wordAddVo.setContent(wordMonitorAddVo.getContent());
    return metadataWordMonitorJsfService.addWord(wordAddVo);
  }

  /**
   * <p>
   * delete word in metadata.
   * </p>
   */
  public HttpResult deleteWord(MiniMonitorWordDeleteVO miniMonitorWordDeleteVo) {
    WordMonitorDeleteVo wordMonitorDeleteVo = new WordMonitorDeleteVo();
    wordMonitorDeleteVo.setId(miniMonitorWordDeleteVo.getId());
    wordMonitorDeleteVo.setUserName(LoginUtils.getUsername());
    return metadataWordMonitorJsfService.deleteWord(wordMonitorDeleteVo);
  }

  /**
   * <p>
   * 声音播报
   * </p>
   */
  public HttpResult remoteBroadCastWord(String vehicleName, MiniMonitorRemoteShoutVO remoteShoutVo) {
    String topic = "android/command/" + vehicleName;
    mqttManager.sendQos1NoRetainNoResponse(vehicleName, topic, MqttMsgTypeEnum.VEHICLE_MSG_BROADCAST.getValue(), JsonUtils.writeValueAsString(remoteShoutVo));
    return HttpResult.success();
  }

  /**
   * <p>
   * 声音鸣笛
   * </p>
   */
  public HttpResult remoteVoiceWhistle(String vehicleName) {
    String topic = "android/command/" + vehicleName;
    mqttManager.sendQos1NoRetainNoResponse(vehicleName, topic, MqttMsgTypeEnum.VEHICLE_VOICE_WHISTLE.getValue(), null);
    return HttpResult.success();
  }
}
