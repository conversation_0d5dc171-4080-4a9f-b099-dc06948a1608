package com.jdx.rover.monitor.service.alarm.notice;

import com.google.common.collect.Lists;
import com.jdx.rover.infrastructure.api.domain.entity.bo.notice.jdme.JdMeNoticeMessage;
import com.jdx.rover.infrastructure.jsf.service.InfraNoticeJsfService;
import com.jdx.rover.metadata.api.domain.dto.vehicle.VehicleBootUpExceptionInfoDTO;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.monitor.bo.vehicle.VehicleAbnormalBootBO;
import com.jdx.rover.monitor.bo.vehicle.VehicleBootAlarmNoticeBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.manager.vehicle.VehicleBootManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.po.VehicleAbnormalBoot;
import com.jdx.rover.monitor.repository.redis.metadata.VehicleBootModuleRepository;
import com.jdx.rover.monitor.service.config.MonitorNotifyUserProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleBootAlarmNoticeService implements AlarmNotice {

  @Autowired
  private VehicleBootManager vehicleBootManager;
  @Autowired
  private VehicleManager vehicleManager;
  @Autowired
  private InfraNoticeJsfService infraNoticeJsfService;
  @Autowired
  private VehicleBootModuleRepository vehicleBootModuleRepository;
  @Autowired
  private MonitorNotifyUserProperties monitorNotifyUserProperties;


  private Map<String, VehicleBootAlarmNoticeBO> buildVehicleAlarmNotice(AlarmNoticeProcessEnum alarmEnum, String vehicleName) {
    Map<String, VehicleBootAlarmNoticeBO> abnormalBootModule = new HashMap<String, VehicleBootAlarmNoticeBO>();
    VehicleAbnormalBootBO abnormalBootBo = vehicleBootManager.getAbnormalModuleBootList(vehicleName);
    if (CollectionUtils.isEmpty(abnormalBootBo.getAbnormalModuleBootList())) {
      return abnormalBootModule;
    }
    Map<Integer, List<VehicleAbnormalBoot>> bootCountMap = abnormalBootBo.getAbnormalModuleBootList().stream().sorted(new Comparator<VehicleAbnormalBoot>() {
      @Override
      public int compare(VehicleAbnormalBoot o1, VehicleAbnormalBoot o2) {
        return o1.getBootCount() - o2.getBootCount();
      }
    }).collect(Collectors.groupingBy(VehicleAbnormalBoot::getBootCount));
    List<VehicleAbnormalBoot> vehicleAbnormalBootList = bootCountMap.get(bootCountMap.size());
    List<VehicleBootUpExceptionInfoDTO> bootUpExceptionInfo = vehicleBootModuleRepository.get();
    Map<String, VehicleBootUpExceptionInfoDTO> bootMap =
            bootUpExceptionInfo.stream().collect(Collectors.toMap(obj -> obj.getDevice() + "_" + obj.getModuleField(), Function.identity()));
    if (CollectionUtils.isNotEmpty(vehicleAbnormalBootList)) {
      vehicleAbnormalBootList.stream().forEach(moduleBoot -> {
        VehicleBootAlarmNoticeBO vehicleAlarmNoticeBO = new VehicleBootAlarmNoticeBO();
        vehicleAlarmNoticeBO.setVehicleName(vehicleName);
        vehicleAlarmNoticeBO.setTitle(alarmEnum.getTitle());
        vehicleAlarmNoticeBO.setContent(alarmEnum.getContent());
        VehicleBootUpExceptionInfoDTO bootModuleConf = bootMap.get(moduleBoot.getNodeName() + "_" + moduleBoot.getModuleName());
        vehicleAlarmNoticeBO.setModuleName(moduleBoot.getModuleName());
        if (!Objects.isNull(bootModuleConf)) {
          vehicleAlarmNoticeBO.setModuleName(bootModuleConf.getModuleFieldName());
        }
        vehicleAlarmNoticeBO.setModuleDuration(moduleBoot.getDuration());
        vehicleAlarmNoticeBO.setNodeName(moduleBoot.getNodeName());
        vehicleAlarmNoticeBO.setMonitorUrl(monitorNotifyUserProperties.getMonitorUrl() + vehicleName);
        vehicleAlarmNoticeBO.setVehicleState("ROVER第" + moduleBoot.getBootCount() + "次启动");
        vehicleAlarmNoticeBO.setDuration(abnormalBootBo.getDuration());
        vehicleAlarmNoticeBO.setAlarmDetail(moduleBoot.getErrorMessage());
        Lists.newArrayList(moduleBoot.getUsers().split(";")).forEach(user -> abnormalBootModule.put(user, vehicleAlarmNoticeBO));
      });
    }
    return abnormalBootModule;
  }

  /*
   * 发送告警通知
   */
  private void sendDDMsg(String user, VehicleBootAlarmNoticeBO vehicleAlarmNoticeBO) {
    JdMeNoticeMessage msgVo = new JdMeNoticeMessage();
    msgVo.setTitle(String.format(vehicleAlarmNoticeBO.getTitle(), vehicleAlarmNoticeBO.getNodeName(),vehicleAlarmNoticeBO.getModuleName()));
    String content = String.format(vehicleAlarmNoticeBO.getContent(), vehicleAlarmNoticeBO.getNodeName(), vehicleAlarmNoticeBO.getModuleName(), vehicleAlarmNoticeBO.getModuleDuration(), vehicleAlarmNoticeBO.getVehicleName(),
            vehicleAlarmNoticeBO.getVehicleState(), vehicleAlarmNoticeBO.getDuration(), vehicleAlarmNoticeBO.getAlarmDetail(), vehicleAlarmNoticeBO.getMonitorUrl());
    msgVo.setContent(content);
    msgVo.setErps(Lists.newArrayList(user));
    infraNoticeJsfService.sendJdMeNotice(msgVo);
  }

  @Override
  public boolean process(AlarmNoticeProcessEnum alarmEvent, String vehicleName) {
    try {
      VehicleBasicDTO vehicleBasicDto = vehicleManager.getBasicByName(vehicleName);
      if (!StringUtils.equalsAny(vehicleBasicDto.getOwnerUseCase(), VehicleOwnerUseCaseEnum.OPEN.getValue(),
              VehicleOwnerUseCaseEnum.SOLUTION.getValue())) {
        return false;
      }
      Map<String, VehicleBootAlarmNoticeBO> vehicleAlarmNoticeBO = buildVehicleAlarmNotice(alarmEvent, vehicleName);
      if (vehicleAlarmNoticeBO.isEmpty()) {
        return false;
      }
      for (Map.Entry<String, VehicleBootAlarmNoticeBO> entry : vehicleAlarmNoticeBO.entrySet()) {
        sendDDMsg(entry.getKey(), entry.getValue());
      }

    } catch (Exception e) {
      log.info("发送咚咚提醒消息异常:{}", vehicleName);
    }
    return true;
  }
}
