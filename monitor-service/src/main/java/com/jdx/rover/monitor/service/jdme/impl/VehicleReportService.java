package com.jdx.rover.monitor.service.jdme.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.monitor.dto.jdme.*;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.manager.accident.AccidentAttachmentManager;
import com.jdx.rover.monitor.manager.accident.AccidentFlowLogManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfig;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.service.jdme.IAccidentFlowEventService;
import com.jdx.rover.shadow.api.domain.dto.ShadowSubscribeEventTaskDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 车端上报事故处理
 */
@Slf4j
public class VehicleReportService extends AbstractJdmeMessageService implements IAccidentFlowEventService {
    private AccidentJdmePushManager accidentJdmePushManager;
    private AccidentManager accidentManager;
    private VehicleManager monitorVehicleManager;
    private AccidentFlowLogManager accidentFlowLogManager;

    public VehicleReportService() {
        accidentJdmePushManager = SpringUtil.getBean(AccidentJdmePushManager.class);
        accidentManager = SpringUtil.getBean(AccidentManager.class);
        monitorVehicleManager = SpringUtil.getBean(VehicleManager.class);
        accidentFlowLogManager = SpringUtil.getBean(AccidentFlowLogManager.class);
    }

    /**
     * 处理消息
     * @param accidentNo 事故号
     * @param accidentFlowType 事故处理环节
     * @param operator 操作员
     * @param repeated 重试消息
     */
    @Override
    public void handleMessage(String accidentNo, String accidentFlowType, String operator, boolean repeated) throws Exception {
        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if(null == accident) {
            log.warn("车端上报事故处理未找到事故编号[{}]对应的事故信息,忽略处理！", accidentNo);
            return;
        }
        VehicleBasicDTO vehicleBasicDto = monitorVehicleManager.getBasicByName(accident.getVehicleName());
        AccidentJdmePush accidentJdmePush = accidentJdmePushManager.getByEventId(accident.getShadowEventId());
        if(null == accidentJdmePush) {
            accidentJdmePush = buildAccidentJdmePush(accident, vehicleBasicDto);
        }
        //影子事件订阅
        try {
            ShadowSubscribeEventTaskDTO shadowEventTask = subscribeShadowEvent(accident);
            //持久化异常提报的需要发送的卡片消息
            if (null != shadowEventTask) {
                accidentJdmePush.setStatus(shadowEventTask.getStatus());
                accidentJdmePush.setVideoUrl(shadowEventTask.getVideoUrl());
                accidentJdmePush.setEventPlayUrl(shadowEventTask.getEventPlayUrl());
            }
        } catch (Exception e) {
            log.info("订阅影子事件[{}]订阅失败: {}", accident.getShadowEventId(), e);
        }

//        if(StrUtil.isBlank(newGroupId)) {
//            //根据当前事故编码创建新京ME群，从而获取到新群号，作为卡片消息的参数之一发送出去
//            JdmeGroup jdmeGroup = buildJdmeGroup(accidentJdmePush, accident);
//            newGroupId = accidentJdmePushManager.createGroup(jdmeGroup);
//            accidentJdmePush.setAccidentGroupId(newGroupId);
//        }
        accidentJdmePush.setAccidentFlowType(accidentFlowType);
        boolean bool = accidentJdmePushManager.saveOrUpdate(accidentJdmePush);
        log.info("京ME推送事故消息存储结果：{{}}", bool ? "成功" : "失败");

        //车端上报事故暂不进行卡片消息发送
//        JueCardData jueCardData = buildCardData(accident, accidentJdmePush, flowLogList,  newGroupId, true);
//        //给统一的领导群推送固定格式的卡片消息，卡片上的交互按钮的逻辑在此提前生成，按钮交互由前端JS完成
//        jueCardData.setGroupId(jdmeConfig.getRobot().getFixedGroupId());
//        jueCardData.setSummary(buildSummary(jueCardData, accident));
//        if(!repeated) {
//            this.accidentJdmePushManager.sendJUEMsg(jueCardData);
//        }
//
//        //给新群推送卡片消息，卡片内容相同，取消加入处理群
//        jueCardData.setGroupId(newGroupId);
//        jueCardData.getButtons().getButtons().remove(0);
//        this.accidentJdmePushManager.sendJUEMsg(jueCardData);
    }

   /**
     * 构建卡片数据
     * @param jdmePush 推送消息
     * @param flowLogList 事故流程记录
     * @param newGroupId 处理群号
     * @param showProcessButton 是否显示处理按钮
     * @return
     */
    private JueCardData buildCardData(Accident accident, AccidentJdmePush jdmePush, List<AccidentFlowLog> flowLogList, String newGroupId, boolean showProcessButton) {
        //组装卡片消息头
        StringBuilder titleSb = new StringBuilder("【事故报备】碰撞-" + jdmePush.getVehicleName());

        JueCardDataHeaderTitle headerTitle = new JueCardDataHeaderTitle();
        headerTitle.setContent(titleSb.toString());

        JueCardDataHeader header = new JueCardDataHeader();
        header.setTheme(JueCardDataEnums.LabelColorType.GRAY.getCode());
        header.setTitle(headerTitle);

        //组装卡片消息体
        List<JueCardDataElementText> elementList = new ArrayList<>();
        //发生时间
        if(StrUtil.isNotBlank(jdmePush.getDebugTime())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("发生时间", jdmePush.getDebugTime(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //发生位置
        if(StrUtil.isNotBlank(jdmePush.getAccidentAddress())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("发生位置", jdmePush.getAccidentAddress(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //站点名称
        if(StrUtil.isNotBlank(accident.getStationName())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("站点名称", accident.getStationName(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //rover版本
        if(StrUtil.isNotBlank(accident.getRoverVersion())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("Rover版本", accident.getRoverVersion(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //事故进度
        if(CollectionUtil.isNotEmpty(flowLogList)) {
            StringBuilder sb = new StringBuilder("\n");
            for(int i = 0; i < flowLogList.size(); i++) {
                AccidentFlowLog log = flowLogList.get(i);
                sb.append(i + 1);
                sb.append("、");
                sb.append(log.getContent());
                if(i < flowLogList.size() - 1) {
                    sb.append("\n");
                }
            }

            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("事故进度", sb.toString(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }

        //组装卡片消息按钮
        List<JueCardDataButton> buttonList = new ArrayList<>();
        if(showProcessButton) {
            //加入处理群按钮
            JueCardDataElementTextItem processButtonTextItem = new JueCardDataElementTextItem();
            processButtonTextItem.setLabel("加入处理群");

            JueCardDataButtonText processButtonText = new JueCardDataButtonText();
            processButtonText.setContent(processButtonTextItem);

            JueCardDataButtonHref processButtonHref = new JueCardDataButtonHref();
            processButtonHref.setPc("");
            processButtonHref.setMobile("");

            JueCardDataButtonBehavior processButtonBehavior = new JueCardDataButtonBehavior();
            processButtonBehavior.setMethod(JueCardDataEnums.ButtonBehaviorMethod.JOIN_GROUP_CHAT);
            processButtonBehavior.setParams(newGroupId);

            JueCardDataButton processButton = new JueCardDataButton();
            processButton.setType(JueCardDataEnums.ButtonType.PRIMARY.getCode());
            processButton.setEnable(true);
            processButton.setText(processButtonText);
            processButton.setHref(processButtonHref);
            processButton.setBehavior(processButtonBehavior);
            buttonList.add(processButton);
        }

        //咨询人工按钮
        JueCardDataElementTextItem manualButtonTextItem = new JueCardDataElementTextItem();
        manualButtonTextItem.setLabel("咨询人工");

        JueCardDataButtonText manualButtonText = new JueCardDataButtonText();
        manualButtonText.setContent(manualButtonTextItem);

        JueCardDataButtonHref manualButtonHref = new JueCardDataButtonHref();
        JdmeConfig jdmeConfig = accidentJdmePushManager.getLatestConfig();
        manualButtonHref.setPc("timline://chat/?topin=" + jdmeConfig.getRobot().getManualPin());
        manualButtonHref.setMobile("jdme://jm/biz/im/contact/details?mparam={\"erp\":\"" + jdmeConfig.getRobot().getManualPin() + "\"}");

        JueCardDataButton manualButton = new JueCardDataButton();
        manualButton.setType(JueCardDataEnums.ButtonType.DEFAULT.getCode());
        manualButton.setEnable(true);
        manualButton.setText(manualButtonText);
        manualButton.setHref(manualButtonHref);
        buttonList.add(manualButton);

        JueCardDataButtons buttons = new JueCardDataButtons();
        buttons.setLayout(JueCardDataEnums.ButtonLayout.ROW);
        buttons.setButtons(buttonList);

        JueCardData jueCardData = new JueCardData();
        jueCardData.setHeader(header);
        jueCardData.setElements(elementList);
        jueCardData.setButtons(buttons);

        return jueCardData;
    }

    private AccidentJdmePush buildAccidentJdmePush(Accident accident, VehicleBasicDTO vehicleBasicDto) {
        //构建持久化对象
        String createTime = DateUtil.format(accident.getAccidentReportTime(), "yyyy/MM/dd HH:mm:ss");
        AccidentJdmePush jdmePush = new AccidentJdmePush();
        jdmePush.setShadowEventId(accident.getShadowEventId());
        jdmePush.setAccidentNo(accident.getAccidentNo());
//        jdmePush.setTitle("事故发生时间：" + createTime + "  事故编号：" + accident.getAccidentNo());
        jdmePush.setVehicleName(accident.getVehicleName());
        jdmePush.setBugCode(accident.getBugCode());
        jdmePush.setDebugTime(createTime);
        jdmePush.setAccidentAddress(accident.getAccidentAddress());
        jdmePush.setCreateUser(accident.getCreateUser());
        jdmePush.setModifyUser(accident.getModifyUser());
        return jdmePush;
    }
}
