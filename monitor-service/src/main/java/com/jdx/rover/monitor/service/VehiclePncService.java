/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service;

import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.VehiclePncRouteInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.VehiclePncRouteStopInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.VehicleRoutePositionDTO;
import com.jdx.rover.monitor.dto.MonitorStationVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehicleNoSignalIntersectionDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePncTaskAndTrafficLightDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.vehicle.single.TrafficLightSourceEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.repository.redis.*;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.server.api.domain.dto.guardian.PassNoSignalIntersectionDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncTrafficLightInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.IntersectionStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a vehicle pnc service.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class VehiclePncService {

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private VehicleSchedulePncRouteRepository pncRouteRepository;

  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;

  @Autowired
  private SingleVehicleManager singleVehicleManager;

  @Autowired
  private TrackingEventCollectService eventService;
  @Autowired
  private NoSignalIntersectionRepository noSignalIntersectionRepository;

  @Autowired
  private JmqProducerService jmqProducerService;

  /**
   * <p>
   * Handle realtime schedule pnc routing message.
   * </p>
   *
   * @param
   */
  public void handleVehicleSchedulePncInfo(VehiclePncInfoDTO vehiclePncInfoDto) {
    try {
      // 处理PNC任务类型变动和红绿灯变化
      handlePncTaskAndTrafficLight(vehiclePncInfoDto);
      handleNoSignalIntersection(vehiclePncInfoDto);
      if (CollectionUtil.isEmpty(vehiclePncInfoDto.getNavigationStop())) {
        return;
      }
      List<MonitorRoutingPointEntity> routingPointEntityList = new LinkedList<>();
      vehiclePncInfoDto.getNavigationStop().stream().forEach(stop -> {
        stop.getRouting().stream().map(point -> TransformUtility.toGCJ02Point(point.getLat(), point.getLon())).filter(
                mapPoint -> mapPoint != null).forEach(mapPoint -> {
          MonitorRoutingPointEntity entity = new MonitorRoutingPointEntity();
          entity.setLon(mapPoint.getLongitude());
          entity.setLat(mapPoint.getLatitude());
          routingPointEntityList.add(entity);
        });
      });
      if (CollectionUtils.isEmpty(routingPointEntityList)) {
        return;
      }
      String key = RedisKeyEnum.SCHEDULE_UPDATE_LOCK_PREFIX.getValue() + vehiclePncInfoDto.getVehicleName();
      if (RedissonUtils.tryLock(key, 3, TimeUnit.SECONDS)) {
        MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.getFromRedis(vehiclePncInfoDto.getVehicleName());
        if (scheduleEntity == null ||
                scheduleEntity.getNavRefreshId().equals(vehiclePncInfoDto.getNaviRefreshId())) {
          RedissonUtils.unLock(key);
          return;
        }
        log.info("Push vehicle {} pnc routing withine task {}", vehiclePncInfoDto.getVehicleName(), scheduleEntity.getTaskType());
        pncRouteRepository.push(vehiclePncInfoDto.getVehicleName(), routingPointEntityList);
        scheduleEntity.setNavRefreshId(vehiclePncInfoDto.getNaviRefreshId());
        vehicleScheduleRepository.set(scheduleEntity);
        sendPncRouteRePlanning(scheduleEntity.getScheduleNo(), vehiclePncInfoDto);
        RedissonUtils.unLock(key);
      }
      // 通知车辆导航路径变更
      MonitorStationVehicleMapInfoDTO vehicleMapInfoDto = new MonitorStationVehicleMapInfoDTO();
      vehicleMapInfoDto.setName(vehiclePncInfoDto.getVehicleName());
      WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_UPDATE.getValue(), vehicleMapInfoDto);
      String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + vehiclePncInfoDto.getVehicleName();
      RTopic rTopic = RedissonUtils.getRTopic(topicName);
      rTopic.publish(JsonUtils.writeValueAsString(wsResult));
    } catch (Exception e) {
      log.info("Handle vehicle schedule pncInfo exception", e);
    }
  }

  private void sendPncRouteRePlanning(String scheduleNo, VehiclePncInfoDTO vehiclePncInfoDto) {
      VehiclePncRouteInfoDTO pncRouteInfoDto = new VehiclePncRouteInfoDTO();
      pncRouteInfoDto.setVehicleName(vehiclePncInfoDto.getVehicleName());
      pncRouteInfoDto.setNaviRefreshId(vehiclePncInfoDto.getNaviRefreshId());
      pncRouteInfoDto.setRecordTime(vehiclePncInfoDto.getRecordTime());
      pncRouteInfoDto.setScheduleNo(scheduleNo);
      pncRouteInfoDto.setGlobalMileage(vehiclePncInfoDto.getGlobalMileage());
      List<VehiclePncRouteStopInfoDTO> navigationStop = vehiclePncInfoDto.getNavigationStop().stream().map(stop-> {
                VehiclePncRouteStopInfoDTO stopInfoDto = new VehiclePncRouteStopInfoDTO();
                stopInfoDto.setStopId(stop.getStopId());
                stopInfoDto.setRouting(stop.getRouting().stream().map(point -> {
                  VehicleRoutePositionDTO position = new VehicleRoutePositionDTO();
                  position.setLatitude(point.getLat());
                  position.setLongitude(point.getLon());
                  return position;
                }).collect(Collectors.toList()));
      return stopInfoDto;
      }).collect(Collectors.toList());
      pncRouteInfoDto.setNavigationStop(navigationStop);
      jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_PNC_ROUTE.getTopic(), vehiclePncInfoDto.getVehicleName(), pncRouteInfoDto);
      eventService.pushPncTask(vehiclePncInfoDto);
  }

  /**
   * 处理红绿灯
   *
   * @param pncInfoDTO
   */
  private void handlePncTaskAndTrafficLight(VehiclePncInfoDTO pncInfoDTO) {
    String vehicleName = pncInfoDTO.getVehicleName();
    String redisKey = RedisKeyEnum.TRAFFIC_LIGHT_SET_VEHICLE.getValue() + vehicleName;
    SingleVehiclePncTaskAndTrafficLightDTO trafficLightRedisDTO = RedissonUtils.getObject(redisKey);

    SingleVehiclePncTaskAndTrafficLightDTO trafficLight = new SingleVehiclePncTaskAndTrafficLightDTO();
    trafficLight.setPncTaskType(pncInfoDTO.getType());
    if (pncInfoDTO.getTrafficLightInfo() == null) {
      trafficLight.setTrafficLightIdList(new ArrayList<>());
    } else {
      List<String> list = pncInfoDTO.getTrafficLightInfo().stream().map(VehiclePncTrafficLightInfoDTO::getId).collect(Collectors.toList());
      trafficLight.setTrafficLightIdList(list);
    }
    Boolean passLightAvailable = getPassLightAvailable(pncInfoDTO);
    trafficLight.setPassLightAvailable(passLightAvailable);
    boolean change = isChange(trafficLightRedisDTO, trafficLight);
    log.info("{} pnc task and traffic light data {}", change, JsonUtils.writeValueAsString(trafficLight));
    if (!change) {
      if (CollectionUtil.isNotEmpty(pncInfoDTO.getNavigationStop())) {
      }
      return;
    }
    RedissonUtils.setObject(redisKey, trafficLight);

    // 只有被接管才处理红绿灯消息
    if (passLightAvailable!= null && passLightAvailable) {
      Boolean takeOver = vehicleTakeOverRepository.haskey(vehicleName);
      if (!takeOver) {
        return;
      }
    }
    singleVehicleManager.pushPncTaskAndTrafficLight(vehicleName);
  }

  /**
   * 获取是否可以通过红绿灯状态
   *
   * @param pncInfoDTO
   * @return
   */
  private Boolean getPassLightAvailable(VehiclePncInfoDTO pncInfoDTO) {
    // 不在路口内,不可操作,置灰
    if (!Objects.equals(IntersectionStatusEnum.IN_INTERSECTION.getValue(), pncInfoDTO.getIntersectionStatus())) {
      return null;
    }
    // 来自监控,不能通过红绿灯,可以取消通过红绿灯,任何人都可以操作
    for (VehiclePncTrafficLightInfoDTO vehiclePncTrafficLightInfoDTO : pncInfoDTO.getTrafficLightInfo()) {
      if (Objects.equals(vehiclePncTrafficLightInfoDTO.getTrafficLightSource(), TrafficLightSourceEnum.SUPERVISOR.getValue())) {
        return false;
      }
    }
    Boolean takeOver = vehicleTakeOverRepository.haskey(pncInfoDTO.getVehicleName());
    // 只有被接管才可以一键通过路口
    if (!takeOver) {
      return null;
    }
    // 其它,可以一键通过红绿灯
    return true;
  }

  /**
   * 是否变化
   *
   * @param trafficLightRedisDTO
   * @param trafficLight
   * @return
   */
  private boolean isChange(SingleVehiclePncTaskAndTrafficLightDTO trafficLightRedisDTO, SingleVehiclePncTaskAndTrafficLightDTO trafficLight) {
    if (trafficLightRedisDTO == null) {
    return (CollectionUtils.isNotEmpty(trafficLight.getTrafficLightIdList()) || trafficLight.getPncTaskType() != null);
    }

    if (!Objects.equals(trafficLightRedisDTO.getPncTaskType(), trafficLight.getPncTaskType())) {
      return true;
    }
    if (!Objects.equals(trafficLightRedisDTO.getPassLightAvailable(), trafficLight.getPassLightAvailable())) {
      return true;
    }
    if (trafficLightRedisDTO.getTrafficLightIdList().size() != trafficLight.getTrafficLightIdList().size()) {
      return true;
    }
    if (trafficLightRedisDTO.getTrafficLightIdList().retainAll(trafficLight.getTrafficLightIdList())) {
      return true;
  }
    return false;
  }

  /**
   * 处理无信号路口
   *
   * @param pncInfoDTO
   */
  private void handleNoSignalIntersection(VehiclePncInfoDTO pncInfoDTO) {
    PassNoSignalIntersectionDTO noSignal = pncInfoDTO.getPassNoSignalIntersection();
    if (noSignal == null) {
      return;
    }
    String vehicleName = pncInfoDTO.getVehicleName();
    SingleVehicleNoSignalIntersectionDTO dtoRedis = noSignalIntersectionRepository.get(vehicleName);

    SingleVehicleNoSignalIntersectionDTO dto = new SingleVehicleNoSignalIntersectionDTO();
    dto.setStatus(noSignal.getStatus());
    dto.setGroupLaneIdList(Optional.ofNullable(noSignal.getGroupLaneIdList()).orElse(new ArrayList<>()));
    dto.setRecordTime(pncInfoDTO.getRecordTime());
    boolean change = isChange(dtoRedis, dto);
    log.info("{} no signal intersection data {}", change, JsonUtils.writeValueAsString(dto));
    if (!change) {
      return;
    }
    noSignalIntersectionRepository.set(vehicleName, dto);
    singleVehicleManager.pushNoSignalIntersectionDTO(vehicleName, dto);
  }

  /**
   * 无信号路口是否变化
   *
   * @param redisDTO
   * @param dto
   * @return
   */
  private boolean isChange(SingleVehicleNoSignalIntersectionDTO redisDTO, SingleVehicleNoSignalIntersectionDTO dto) {
    if (redisDTO == null) {
      return StringUtils.isNotBlank(dto.getStatus()) || CollectionUtils.isNotEmpty(dto.getGroupLaneIdList());
    }
    if (!Objects.equals(redisDTO.getStatus(), dto.getStatus())) {
      return true;
    }

    if (redisDTO.getGroupLaneIdList().size() != dto.getGroupLaneIdList().size()) {
      return true;
    }
    if (!redisDTO.getGroupLaneIdList().containsAll(dto.getGroupLaneIdList())) {
      return true;
    }
    if (!dto.getGroupLaneIdList().containsAll(redisDTO.getGroupLaneIdList())) {
      return true;
    }
    return false;
  }

}

