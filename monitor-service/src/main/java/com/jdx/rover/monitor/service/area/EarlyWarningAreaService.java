/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.area;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.Week;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.map.api.domain.dto.MapVariableApiJsfDTO;
import com.jdx.rover.map.api.domain.enums.VariableMapEffectVehicleTypeEnum;
import com.jdx.rover.map.api.domain.enums.VariableMapStatusEnum;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.VehicleAlarmDTO;
import com.jdx.rover.monitor.api.domain.enums.AlarmSourceEnum;
import com.jdx.rover.monitor.common.utils.jts.GeometryUtils;
import com.jdx.rover.monitor.entity.alarm.EarlyWarningDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.manager.map.MapVariableManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.alarm.EarlyWarningRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.service.vehicle.VehicleLocationService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Envelope;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;

import java.io.Serial;
import java.io.Serializable;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 预警区域服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/20
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class EarlyWarningAreaService {
    public static final String ALARM_TYPE = "ATTENTION_REGION_WARN";
    public static final String ALARM_ERROR_MESSAGE = "enter attention areId=%s,areaName=%s";

    private final MapVariableManager mapVariableManager;

    private final EarlyWarningRepository earlyWarningRepository;

    private final JmqProducerService jmqProducerService;

    public final static Map<String, Set<AreaLocation>> areaCacheMap = new ConcurrentHashMap<>();

    private static Map<String, Set<AreaLocation>> lastVehicleInAreaMap = new HashMap<>();

    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    /**
     * 处理预警区域
     */
    public void handEarlyWarning() {
        if (MapUtils.isEmpty(areaCacheMap)) {
            initMapArea();
        }

        Map<String, Set<AreaLocation>> currentVehicleInAreaMap = getVehicleInAreaMap();

        log.info("计算变化区域,上次区域:{},当前区域:{}", lastVehicleInAreaMap, currentVehicleInAreaMap);
        // 删除预警
        removeAlarm(currentVehicleInAreaMap);

        // 新增预警
        addAlarm(currentVehicleInAreaMap);

        // 存储上次状态,用于下次使用
        lastVehicleInAreaMap = currentVehicleInAreaMap;
    }

    /**
     * 新增预警
     *
     * @param currentVehicleInAreaMap 当前车辆所在预警区域计划
     */
    private void addAlarm(Map<String, Set<AreaLocation>> currentVehicleInAreaMap) {
        Set<String> enterVehicleSet = subtractMap(currentVehicleInAreaMap, lastVehicleInAreaMap);
        if (CollectionUtils.isEmpty(enterVehicleSet)) {
            return;
        }
        log.info("计算进入预警区域车辆={}", enterVehicleSet);

        List<String> vehicleNameList = Lists.newArrayList(enterVehicleSet);
        Map<String, EarlyWarningDO> earlyWarningMap = earlyWarningRepository.listMap(vehicleNameList);
        if (CollectionUtils.isNotEmpty(vehicleNameList)) {
            log.info("当前车辆redis预警区域告警={}", earlyWarningMap);
        }
        // 添加预警
        for (String vehicleName : enterVehicleSet) {
            if (earlyWarningMap.containsKey(vehicleName)) {
                log.info("redis中已经存在车辆预警,直接忽略!{}", earlyWarningMap.get(vehicleName));
                continue;
            }
            EarlyWarningDO dto = buildEarlyWarningDO(vehicleName, currentVehicleInAreaMap);
            earlyWarningRepository.set(vehicleName, dto);
            VehicleAlarmDTO vehicleAlarmDTO = buildAlarmDTO(vehicleName, dto);
            jmqProducerService.sendMessage(JmqProducerTopicEnum.MAP_VEHICLE_ALARM.getTopic(), vehicleName, vehicleAlarmDTO);
            log.info("产生预警区域告警信息{}", JsonUtils.writeValueAsString(vehicleAlarmDTO));
        }
    }

    /**
     * 删除预警
     *
     * @param currentVehicleInAreaMap 当前车辆所在预警区域计划
     */
    private void removeAlarm(Map<String, Set<AreaLocation>> currentVehicleInAreaMap) {
        for (Map.Entry<String, VehicleLocationService.VehicleLocation> entry : VehicleLocationService.CACHE.asMap().entrySet()) {
            String vehicleName = entry.getKey();
            if (currentVehicleInAreaMap.containsKey(vehicleName)) {
                // 当前车在告警区域里面，不删除预警
                continue;
            }
            EarlyWarningDO dto = earlyWarningRepository.get(vehicleName);
            if (Objects.isNull(dto)) {
                // 当前车不存在预警，不删除预警
                continue;
            }
            VehicleLocationService.VehicleLocation vehicleLocation = VehicleLocationService.CACHE.getIfPresent(vehicleName);
            if (Objects.isNull(vehicleLocation)) {
                // 速度为0或者没有数据上报(离线) 60s以上,缓存没有了
                VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
                log.info("本地缓存消失,暂不处理告警!告警={},实时数据={}", dto, vehicleRealtimeInfoDTO);
                continue;
            }
            dto.setEndTime(new Date());
            earlyWarningRepository.remove(vehicleName);
            VehicleAlarmDTO vehicleAlarmDTO = buildAlarmDTO(vehicleName, dto);
            vehicleAlarmDTO.getAlarmEventList().forEach(item -> item.setEndTime(new Date()));
            jmqProducerService.sendMessage(JmqProducerTopicEnum.MAP_VEHICLE_ALARM.getTopic(), vehicleName, vehicleAlarmDTO);
            log.info("消失预警区域告警信息{}", JsonUtils.writeValueAsString(vehicleAlarmDTO));
        }
    }

    /**
     * 构建新增预警
     */
    private EarlyWarningDO buildEarlyWarningDO(String vehicleName, Map<String, Set<AreaLocation>> currentVehicleInAreaMap) {
        EarlyWarningDO alarm = new EarlyWarningDO();
        alarm.setVehicleName(vehicleName);
        alarm.setAlarmType(ALARM_TYPE);
        alarm.setStartTime(new Date());
        AreaLocation area = currentVehicleInAreaMap.get(vehicleName).iterator().next();
        String errorMessage = String.format(ALARM_ERROR_MESSAGE, area.getId(), area.getRemark());
        alarm.setErrorMessage(errorMessage);
        return alarm;
    }

    /**
     * 构建新增告警
     */
    private static VehicleAlarmDTO buildAlarmDTO(String vehicleName, EarlyWarningDO dto) {
        VehicleAlarmDTO vehicleAlarmDTO = new VehicleAlarmDTO();
        vehicleAlarmDTO.setVehicleName(vehicleName);
        vehicleAlarmDTO.setRecordTime(new Date());
        vehicleAlarmDTO.setAlarmSource(AlarmSourceEnum.CLOUD_ALARM.getSource());
        AlarmInfoDTO alarmInfoDTO = new AlarmInfoDTO();
        alarmInfoDTO.setAlarmType(dto.getAlarmType());
        alarmInfoDTO.setAlarmSource(AlarmSourceEnum.CLOUD_ALARM.getSource());
        alarmInfoDTO.setStartTime(dto.getStartTime());
        alarmInfoDTO.setErrorMessage(dto.getErrorMessage());
        vehicleAlarmDTO.setAlarmEventList(Lists.newArrayList(alarmInfoDTO));
        return vehicleAlarmDTO;
    }

    /**
     * 获取车辆处于哪些地图区域中
     */
    public static Map<String, Set<AreaLocation>> getVehicleInAreaMap() {
        Map<String, Set<AreaLocation>> currentVehicleInAreaMap = new HashMap<>();
        log.info("运动车辆位置缓存={}", VehicleLocationService.CACHE.asMap());
        for (Map.Entry<String, VehicleLocationService.VehicleLocation> entry : VehicleLocationService.CACHE.asMap().entrySet()) {
            String key = getKey(entry.getValue().getLon(), entry.getValue().getLat());
            Set<AreaLocation> areaSet = areaCacheMap.get(key);
            if (CollectionUtils.isEmpty(areaSet)) {
                continue;
            }
            Point point = GeometryUtils.createPoint(entry.getValue().getLon(), entry.getValue().getLat());
            Set<AreaLocation> areaIdSet = new HashSet<>();
            String vehicleName = entry.getKey();
            areaSet.stream()
                    .filter(areaLocation -> isEffectVehicle(areaLocation, vehicleName))
                    .filter(areaLocation -> areaLocation.getGeometryWgs84().covers(point))
                    .filter(EarlyWarningAreaService::isEffectTime)
                    .forEach(areaIdSet::add);
            if (CollectionUtils.isNotEmpty(areaIdSet)) {
                // 存在匹配区域,保存到车辆对应区域map中
                currentVehicleInAreaMap.put(vehicleName, areaIdSet);
            }
        }
        return currentVehicleInAreaMap;
    }

    /**
     * 判断时间有效性
     */
    private static boolean isEffectTime(AreaLocation areaLocation) {
        LocalDateTime currentLocalDateTime = LocalDateTime.now();
        if (CollectionUtils.isNotEmpty(areaLocation.getWeekLimit())) {
            // 不在限定星期区间内
            Week week = LocalDateTimeUtil.dayOfWeek(currentLocalDateTime.toLocalDate());
            if (!areaLocation.getWeekLimit().contains(week.name())) {
                return false;
            }
        }
        List<MapVariableApiJsfDTO.TimeLimit> timeLimitList = areaLocation.getTimeLimit();
        if (CollectionUtils.isNotEmpty(timeLimitList)) {
            // 不在限定时间区间内
            LocalTime localTime = currentLocalDateTime.toLocalTime();
            if (timeLimitList.stream().noneMatch(
                    timeLimit -> localTime.isAfter(timeLimit.getStartTime()) && localTime.isBefore(timeLimit.getEndTime()))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断车辆有效性
     */
    private static boolean isEffectVehicle(AreaLocation areaLocation, String vehicleName) {
        if (CollectionUtils.isEmpty(areaLocation.getVehicleLimit())) {
            return true;
        }
        if (Objects.equals(VariableMapEffectVehicleTypeEnum.PART_EFFECT.getValue(), areaLocation.getEffectVehicleType())
                && !areaLocation.getVehicleLimit().contains(vehicleName)) {
            // 白名单,部分生效,车辆列表必须包含当前车辆
            return false;
        }
        if (Objects.equals(VariableMapEffectVehicleTypeEnum.PART_EFFECTLESS.getValue(), areaLocation.getEffectVehicleType())
                && areaLocation.getVehicleLimit().contains(vehicleName)) {
            // 黑名单,部分无效,车辆列表必须不包含当前车辆
            return false;
        }
        return true;
    }

    /**
     * map相减
     * minuend 被减数
     * subtrahend 减数
     */
    public Set<String> subtractMap(Map<String, Set<AreaLocation>> minuend, Map<String, Set<AreaLocation>> subtrahend) {
        Set<String> result = new HashSet<>();
        for (Map.Entry<String, Set<AreaLocation>> entry : minuend.entrySet()) {
            String vehicleName = entry.getKey();
            if (subtrahend.containsKey(vehicleName) && !CollectionUtils.isEmpty(subtrahend.get(vehicleName))) {
                continue;
            }
            result.add(vehicleName);
            log.info("变化预警区域车辆名称:{},区域集合:{}", vehicleName, entry.getValue().toArray());
        }
        return result;
    }

    /**
     * 获取key
     *
     * @param x 经度
     * @param y 纬度
     */
    public static String getKey(double x, double y) {
        DecimalFormat df = new DecimalFormat("0.0");
        df.setRoundingMode(RoundingMode.DOWN);
        return df.format(x) + "_" + df.format(y);
    }

    /**
     * 通过feign接口从地图服务rover-map中获取预警区域,存入本地缓存中
     * 初始化预警区域
     */
    public void updateMapVariable(Integer mapVariableId) {
        log.info("更新动态地图关注区域缓存指定ID={}", mapVariableId);
        MapVariableApiJsfDTO mapVariable = mapVariableManager.getByMapVariableId(mapVariableId);
        Set<String> keySet = bindKeyToMapVariable(mapVariable);
        log.info("绑定动态地图到指定key结果={}", keySet);
        Iterator<Map.Entry<String, Set<AreaLocation>>> itr = areaCacheMap.entrySet().iterator();
        while (itr.hasNext()) {
            Map.Entry<String, Set<AreaLocation>> entry = itr.next();
            entry.getValue().removeIf(areaLocation -> Objects.equals(areaLocation.getId(), mapVariableId) && !keySet.contains(entry.getKey()));
            if (CollectionUtils.isEmpty(entry.getValue())) {
                itr.remove();
                log.info("删除多余的key={}, mapVariableId={}", entry.getKey(), mapVariableId);
                break;
            }
        }
        log.info("更新完成后预警区域的结果={}", JsonUtils.writeValueAsString(areaCacheMap));
    }

    /**
     * 绑定动态地图到指定key
     */
    public static Set<String> bindKeyToMapVariable(MapVariableApiJsfDTO mapVariable) {
        Set<String> keySet = new HashSet<>();
        if (Objects.isNull(mapVariable) || Objects.isNull(mapVariable.getGeometryWgs84())) {
            return keySet;
        }
        if (VariableMapStatusEnum.RS_DISCARDED.getValue().equals(mapVariable.getStatus())) {
            log.info("动态预警区域已经停用!{}", mapVariable);
            return keySet;
        }
        if (StringUtils.isNotBlank(mapVariable.getGeometryWgs84())) {
            log.info("mapVariable.getGeometryWgs84()={}", mapVariable.getGeometryWgs84());
            log.info("JsonUtils.getObjectMapper().getRegisteredModuleIds()={}", JsonUtils.getObjectMapper().getRegisteredModuleIds());
            Geometry geometry = JsonUtils.readValue(mapVariable.getGeometryWgs84(), Geometry.class);
            if (Objects.nonNull(geometry) && geometry instanceof Polygon polygon) {
                keySet = getKeySet(polygon);
                AreaLocation areaLocation = new AreaLocation(mapVariable.getId(), mapVariable.getType()
                        , mapVariable.getRemark(), polygon, mapVariable.getEffectVehicleType(), mapVariable.getVehicleLimit()
                        , mapVariable.getWeekList(), mapVariable.getTimeLimitList());
                for (String key : keySet) {
                    Set<AreaLocation> areaLocationSet = areaCacheMap.get(key);
                    if (CollectionUtils.isNotEmpty(areaLocationSet)) {
                        areaLocationSet.removeIf(item -> Objects.equals(item.getId(), mapVariable.getId()));
                        areaLocationSet.add(areaLocation);
                    } else {
                        areaCacheMap.put(key, Sets.newHashSet(areaLocation));
                    }
                }
            }
        }
        return keySet;
    }

    /**
     * 获取经纬度增量0.1的key,格式为 经度_纬度 [116.4_39.7, 116.4_39.8, 116.5_39.7, 116.5_39.8, 116.6_39.7, 116.6_39.8]
     */
    private static Set<String> getKeySet(Polygon polygon) {
        Set<String> keySet = new HashSet<>();
        Envelope envelope = polygon.getEnvelopeInternal();
        int minXPlusTen = (int) (envelope.getMinX() * 10);
        int minYPlusTen = (int) (envelope.getMinY() * 10);
        int maxXPlusTen = (int) (envelope.getMaxX() * 10);
        int maxYPlusTen = (int) (envelope.getMaxY() * 10);
        int maxI = maxXPlusTen - minXPlusTen;
        int maxJ = maxYPlusTen - minYPlusTen;
        for (int i = 0; i <= maxI; i++) {
            for (int j = 0; j <= maxJ; j++) {
                double x = (minXPlusTen + i) * 0.1;
                double y = (minYPlusTen + j) * 0.1;
                Polygon p = GeometryUtils.createEnvelopePolygon(x, y, x + 0.1, y + 0.1);
                if (!p.intersects(polygon)) {
                    // 不相交的才是无效key
                    continue;
                }
                String key = getKey(x, y);
                keySet.add(key);
            }
        }
        return keySet;
    }

    /**
     * 通过feign接口从地图服务rover-map中获取预警区域,存入本地缓存中
     * 初始化预警区域
     */
    public synchronized void initMapArea() {
        if (MapUtils.isNotEmpty(areaCacheMap)) {
            log.warn("预警区域已经初始化完成,不再执行!={}", areaCacheMap);
            return;
        }
        List<MapVariableApiJsfDTO> result = mapVariableManager.listAttentionAlarm();
        if (CollectionUtils.isEmpty(result)) {
            String msg = "未获取到地图预警区域";
            log.error(msg);
            return;
        }

        for (MapVariableApiJsfDTO mapVariable : result) {
            bindKeyToMapVariable(mapVariable);
        }
        log.info("动态关注预警区域初始化结果={}", JsonUtils.writeValueAsString(areaCacheMap));
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @EqualsAndHashCode
    public static class AreaLocation implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * id
         */
        private Integer id;

        /**
         * 类型
         */
        private String type;

        /**
         * 名称备注
         */
        private String remark;

        /**
         * wgs84坐标
         */
        private Polygon geometryWgs84;

        /**
         * 生效车辆类别
         */
        private String effectVehicleType;

        /**
         * 生效车辆列表
         */
        private List<String> vehicleLimit;

        /**
         * 星期限制
         */
        private List<String> weekLimit;

        /**
         * 时间限制
         */
        private List<MapVariableApiJsfDTO.TimeLimit> timeLimit;

    }
}
