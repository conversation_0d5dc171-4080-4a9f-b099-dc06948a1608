package com.jdx.rover.monitor.service.event;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.*;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.ScheduleTaskTypeEnum;
import com.jdx.rover.monitor.manager.vehicle.SystemStateManager;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeDrivableInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 影子单车页数据
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SingleVehicleHistoryService {
  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private VehicleStatusRepository vehicleStatusRepository;

  @Autowired
  private JmqProducerService jmqProducerService;

  /**
   * 1 通过车辆名称列表推送单车实时信息
   *
   * @return
   */
  public void pushSingleVehicleRealtime(List<VehicleRealtimeInfoDTO> vehicleList, Map<String, MonitorScheduleEntity> scheduleEntityMap) {
    vehicleList.stream().forEach(realtime -> {
      MonitorScheduleEntity scheduleEntity = scheduleEntityMap.get(realtime.getVehicleName());
      SingleVehicleRealDTO vehicleDto = buildSingleVehicleDTO(realtime, scheduleEntity);
      jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SINGLE_VEHICLE_REALTIME.getTopic(),realtime.getVehicleName(), vehicleDto);
    });
  }

  /**
   * 2 通过车辆名称推送单车调度信息
   *
   * @return
   */
  public void pushSingleVehicleSchedule(ScheduleTask scheduleTask, MonitorScheduleEntity scheduleEntity) {
    SingleVehicleScheduleDTO scheduleDTO = new SingleVehicleScheduleDTO();
    scheduleDTO.setVehicleName(scheduleTask.getVehicleName());
    scheduleDTO.setScheduleNo(scheduleTask.getScheduleName());
    scheduleDTO.setScheduleState(scheduleTask.getVehicleScheduleState());
    scheduleDTO.setRecordTime(scheduleTask.getRecordTime());
    scheduleDTO.setTaskType(scheduleTask.getScheduleTaskType());
    scheduleDTO.setGlobalMileage(scheduleTask.getGlobalMileage());
    scheduleDTO.setStartDateTime(scheduleTask.getStartDatetime());
    scheduleDTO.setEstDepartureTime(scheduleTask.getEstDepartureTime());
    scheduleDTO.setFinishedMileage(Optional.ofNullable(scheduleEntity).
            map(entity -> entity.getFinishedMileage()).orElse(0.0));
    if (scheduleEntity != null && scheduleEntity.getStop() != null) {
      List<SingleVehicleScheduleStopDTO> stopList = scheduleEntity.getStop().stream().map(stop -> {
        SingleVehicleScheduleStopDTO stopDTO = new SingleVehicleScheduleStopDTO();
        stopDTO.setId(stop.getId());
        stopDTO.setGoalId(stop.getGoalId());
        stopDTO.setName(stop.getName());
        stopDTO.setType(stop.getType());
        stopDTO.setArrivedTime(stop.getArrivedTime());
        stopDTO.setStopAction(stop.getStopAction());
        stopDTO.setDepartTime(stop.getDepartTime());
        stopDTO.setEstDepartTime(stop.getEstDepartTime());
        stopDTO.setTravelStatus(stop.getTravelStatus());
        stopDTO.setWaitingTime(stop.getWaitingTime());
        stopDTO.setGlobalMileage(stop.getGlobalMileage());
        return stopDTO;
      }).collect(Collectors.toList());
      scheduleDTO.setStopList(stopList);
    }
    String jsonStr = JsonUtils.writeValueAsString(scheduleDTO);
    log.info("Send single vehicle {} history schedule {}",scheduleTask.getVehicleName(), jsonStr);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_SINGLE_VEHICLE_SCHEDULE.getTopic(), scheduleTask.getVehicleName(), scheduleDTO);
  }

  /**
   * 3 通过车辆名称推送单车订单信息
   *
   */
  public void pushSingleVehicleScheduleOrder(MonitorScheduleEntity scheduleEntity) {

  }

  /**
   * 4 通过车辆名称推送单车异常信息
   *
   * @return
   */
  public void pushSingleVehicleException(MonitorScheduleEntity scheduleEntity) {


  }

  private Double getArrivedMileage(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
    Double finishedMileage = Optional.ofNullable(monitorScheduleEntity.getFinishedMileage()).orElse(0.0);
    if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(),
            VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(),
            VehicleScheduleState.RETURN.getVehicleScheduleState())) {
      Double currentStopFinishedMileage;
      if (realtime == null || realtime.getCurrentStopFinishedMileage() == null) {
        currentStopFinishedMileage = 0.0;
      } else {
        currentStopFinishedMileage = realtime.getCurrentStopFinishedMileage();
      }
      finishedMileage += currentStopFinishedMileage;
    }
    return finishedMileage;
  }

  /**
   * 构建使能信息
   *
   * @param realtime
   * @return
   */
  private SingleVehicleDrivableDirectionDTO buildVehicleDrivableDirectionDTO(VehicleRealtimeInfoDTO realtime) {
    VehicleRealtimeDrivableInfoDTO drivableDirection = realtime.getDrivableDirection();
    if (drivableDirection == null) {
      return null;
    }
    SingleVehicleDrivableDirectionDTO vehicleDrivableDirection = new SingleVehicleDrivableDirectionDTO();
    vehicleDrivableDirection.setEnableFront(drivableDirection.getEnableFront());
    vehicleDrivableDirection.setEnableBack(drivableDirection.getEnableBack());
    vehicleDrivableDirection.setEnableLeft(drivableDirection.getEnableLeft());
    vehicleDrivableDirection.setEnableRight(drivableDirection.getEnableRight());
    return vehicleDrivableDirection;
  }

  /**
   * 构建单车推送对象
   *
   * @param realtime
   * @return
   */
  private SingleVehicleRealDTO buildSingleVehicleDTO(VehicleRealtimeInfoDTO realtime, MonitorScheduleEntity monitorScheduleEntity) {
    SingleVehicleRealDTO vehicle = new SingleVehicleRealDTO();
    vehicle.setVehicleName(realtime.getVehicleName());
    vehicle.setRecordTime(realtime.getRecordTime());
    if (monitorScheduleEntity != null) {
      vehicle.setScheduleNo(monitorScheduleEntity.getScheduleNo());
      vehicle.setScheduleState(monitorScheduleEntity.getScheduleState());
      vehicle.setTaskType(monitorScheduleEntity.getTaskType());
      if (realtime != null) {
        vehicle.setCurrentStopFinishedMileage(realtime.getCurrentStopFinishedMileage());
        vehicle.setCurrentScheduleFinishedMileage(getArrivedMileage(monitorScheduleEntity, realtime));
      }
    } else {
      vehicle.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
      vehicle.setTaskType(ScheduleTaskTypeEnum.NOTASK.getValue());
    }

    if (realtime != null) {
      String systemState = realtime.getSystemState();
      vehicle.setSystemState(systemState);
      if (!SystemStateManager.isOfflineOrLost(systemState)) {
        vehicle.setVehicleState(realtime.getVehicleState());
        vehicle.setSpeed(realtime.getSpeed());
        vehicle.setPower(realtime.getPower());
        vehicle.setLatitude(realtime.getLat());
        vehicle.setLongitude(realtime.getLon());
        vehicle.setSteerZero(realtime.getSteerZero());
        List<VehicleAlarmDO.VehicleAlarmEventDO> alarmEventList = vehicleAlarmRepository.get(realtime.getVehicleName());
        if (!CollectionUtils.isEmpty(alarmEventList)) {
          vehicle.setAlarmEventList(alarmEventList.stream().map(alarm -> {
            SingleVehicleAlarmDTO vehicleAlarm = new SingleVehicleAlarmDTO();
            vehicleAlarm.setAlarmEvent(alarm.getType());
            vehicleAlarm.setReportTime(alarm.getReportTime());
            return vehicleAlarm;
          }).collect(Collectors.toList()));
        }
        SingleVehicleDrivableDirectionDTO vehicleDrivableDirection = buildVehicleDrivableDirectionDTO(realtime);
        vehicle.setVehicleDrivableDirection(vehicleDrivableDirection);
      }
    } else {
      vehicle.setSystemState(SystemStateEnum.OFFLINE.getSystemState());
    }
    VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(realtime.getVehicleName());
    if (!Objects.isNull(vehicleStatusDO) && StringUtils.isNotBlank(vehicleStatusDO.getSceneSignal())) {
      vehicle.setSceneSignal(Double.valueOf(vehicleStatusDO.getSceneSignal()));
    }
    return vehicle;
  }
}
