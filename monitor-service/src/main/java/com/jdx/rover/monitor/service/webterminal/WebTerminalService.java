/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.webterminal;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.VehicleOperateEventDTO;
import com.jdx.rover.monitor.api.domain.enums.OperateStateEnum;
import com.jdx.rover.monitor.api.domain.enums.VehicleOperateSourceEnum;
import com.jdx.rover.monitor.api.domain.enums.VehicleOperateTypeEnum;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.WebTerminalEntity;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.server.api.domain.dto.webterminal.WebTerminalResponseDTO;
import com.jdx.rover.server.api.domain.dto.webterminal.WebTerminalResponseHeader;
import com.jdx.rover.server.api.domain.enums.webterminal.WebTerminalMessageTypeEnum;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestHeader;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * web terminal service类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WebTerminalService {
    private final JmqProducerService jmqProducerService;

    private final SingleVehicleManager singleVehicleManager;

    private final UserStatusRepository userStatusRepository;

    /**
     * 发送车端
     *
     * @param webTerminalRequestVO
     */
    public void sendCommandToVehicle(WebTerminalRequestVO webTerminalRequestVO) {
        jmqProducerService.sendMessage(JmqProducerTopicEnum.SERVER_WEB_TERMINAL_COMMAND_DOWN.getTopic(), webTerminalRequestVO.getHeader().getVehicleName(), webTerminalRequestVO);
    }

    /**
     * 处理监听kafka消息
     *
     * @param message
     */
    public void handleMessage(String message) {
        WebTerminalRequestVO webTerminalRequestVO;
        log.info("web terminal 处理监听kafka消息message:{}", message);
        webTerminalRequestVO = JsonUtils.readValue(message, WebTerminalRequestVO.class);
        if (webTerminalRequestVO == null || webTerminalRequestVO.getHeader().getVehicleName() == null) {
            log.error("web terminal 读取异常信息为空!{}", message);
            return;
        }
        WebTerminalResponseDTO dto = setWebTerminalResponseDTO(webTerminalRequestVO);
        WebTerminalMessageTypeEnum messageType = WebTerminalMessageTypeEnum.of(webTerminalRequestVO.getMessageType());
        switch (messageType) {
            case OPEN_SESSION://创建会话
                setWebTerminalList(dto);
                break;
            case CLOSE_SESSION://关闭会话
                reSetWebTerminalList(dto);
                break;
            case COMMAND_DATA://指令内容
                sendMsgToMonitor(dto);
                break;
            case SERVER_TO_VEHICLE_FILE://下载文件
            case VEHICLE_TO_SERVER_FILE://上传文件
                dto = JsonUtils.readValue(message, WebTerminalResponseDTO.class);
                WsResult<WebTerminalResponseDTO> wsResult = WsResult.success(WebsocketEventTypeEnum.WEB_TERMINAL_COMMAND_DATA.getValue(), dto);
                singleVehicleManager.pushSingleVehiclePageData(dto.getHeader().getVehicleName(), wsResult);
            default:
        }
    }

    /**
     * 设置数据响应头信息
     *
     * @return
     */
    private WebTerminalResponseDTO setWebTerminalResponseDTO(WebTerminalRequestVO webTerminalRequestVO) {
        WebTerminalResponseHeader webTerminalResponseHeader = new WebTerminalResponseHeader();
        webTerminalResponseHeader.setResponseTime(System.currentTimeMillis());
        webTerminalResponseHeader.setTerminalName(webTerminalRequestVO.getHeader().getTerminalName());
        webTerminalResponseHeader.setVehicleName(webTerminalRequestVO.getHeader().getVehicleName());
        webTerminalResponseHeader.setRequestId(webTerminalRequestVO.getHeader().getRequestId());
        //webTerminalResponseHeader.setMessageState(webTerminalRequestVO.getHeader());
        WebTerminalResponseDTO webTerminalResponseDTO = new WebTerminalResponseDTO();
        webTerminalResponseDTO.setHeader(webTerminalResponseHeader);
        webTerminalResponseDTO.setMessageType(webTerminalRequestVO.getMessageType());
        webTerminalResponseDTO.setData(webTerminalRequestVO.getData());
        return webTerminalResponseDTO;
    }

    /**
     * 创建 Rover web terminal session 状态
     *
     * @param webTerminalResponseDTO
     */
    public void setWebTerminalList(WebTerminalResponseDTO webTerminalResponseDTO) {
        log.info("web terminal set redis webTerminalResponseDTO: {}", JsonUtils.writeValueAsString(webTerminalResponseDTO));
        String messageType = webTerminalResponseDTO.getMessageType();
        String redisKey = RedisKeyEnum.WEB_TERMINAL_SESSION_PREFIX.getValue() + webTerminalResponseDTO.getHeader().getVehicleName();
        if (StringUtils.equals(WebTerminalMessageTypeEnum.OPEN_SESSION.getValue(), messageType)) {
            RedissonUtils.setMapObject(redisKey, webTerminalResponseDTO.getHeader().getTerminalName(), 1);
        }
    }

    /**
     * 重置 Rover web terminal session 状态
     *
     * @param webTerminalResponseDTO
     */
    public void reSetWebTerminalList(WebTerminalResponseDTO webTerminalResponseDTO) {
        log.info("web terminal set redis reSetWebTerminalList: {}", JsonUtils.writeValueAsString(webTerminalResponseDTO));
        String messageType = webTerminalResponseDTO.getMessageType();
        String redisKey = RedisKeyEnum.WEB_TERMINAL_SESSION_PREFIX.getValue() + webTerminalResponseDTO.getHeader().getVehicleName();

        if (StringUtils.equals(WebTerminalMessageTypeEnum.CLOSE_SESSION.getValue(), messageType)) {
            RedissonUtils.setMapObject(redisKey, webTerminalResponseDTO.getHeader().getTerminalName(), 0);
        }
    }

    /**
     * 获取 Rover web terminal name session 状态
     *
     * @param vehiclName
     */
    public List<WebTerminalEntity> getWebTerminalList(String vehiclName) {
        log.info("web terminal set redis getWebTerminalList: {}", vehiclName);
        Map<String, Integer> map = null;
        List<WebTerminalEntity> wtList = new ArrayList<WebTerminalEntity>();
        if (StringUtils.isBlank(vehiclName)) {
            return wtList;
        }
        String redisKey = RedisKeyEnum.WEB_TERMINAL_SESSION_PREFIX.getValue() + vehiclName;
        map = RedissonUtils.getMap(redisKey);

        map.forEach((key, value) -> {
            WebTerminalEntity wtEntity = new WebTerminalEntity();
            wtEntity.setTerminalName(key);
            wtEntity.setStatus(value);
            wtList.add(wtEntity);
        });
        log.info("web terminal set redis getWebTerminalList: {}", JsonUtils.writeValueAsString(map));
        return wtList;
    }

    /**
     * 消息中转给 web monitor
     *
     * @param webTerminalResponseDTO
     */
    public void sendMsgToMonitor(WebTerminalResponseDTO webTerminalResponseDTO) {
        StringBuffer topicName = new StringBuffer(16);
        topicName.append(RedisTopicEnum.REMOTE_CONTROL_WEB_TERMINAL_PREFIX.getValue());
        topicName.append(webTerminalResponseDTO.getHeader().getVehicleName() + ":");
        topicName.append(webTerminalResponseDTO.getHeader().getTerminalName() + ":");
        topicName.append(webTerminalResponseDTO.getHeader().getRequestId());
        RTopic rTopic = RedissonUtils.getRTopic(topicName.toString());
        String msg = JsonUtils.writeValueAsString(webTerminalResponseDTO);
        log.info("web terminal set redis topic sendMsgToMonitor: {}", msg);
        rTopic.publish(msg);
    }

    public WebTerminalRequestVO sendTransferStatusToVehicle(WebTerminalRequestVO vo, WebsocketClientBO client) {
        WebTerminalRequestHeader webTerminalRequestHeader = new WebTerminalRequestHeader();
        webTerminalRequestHeader.setVehicleName(vo.getHeader().getVehicleName());
        webTerminalRequestHeader.setTerminalName(vo.getHeader().getTerminalName());
        webTerminalRequestHeader.setRequestId(vo.getHeader().getRequestId());
        webTerminalRequestHeader.setRequestTime(System.currentTimeMillis());
        webTerminalRequestHeader.setNeedResponse(true);
        webTerminalRequestHeader.setExpireMilliSecond(0);
        WebTerminalRequestVO webTerminalRequestVO = new WebTerminalRequestVO();
        webTerminalRequestVO.setHeader(webTerminalRequestHeader);
        webTerminalRequestVO.setMessageType(vo.getMessageType());
        webTerminalRequestVO.setUserName(client.getUsername());
        webTerminalRequestVO.setData(vo.getData());
        webTerminalRequestVO.setUserName(client.getUsername());
        sendCommandToVehicle(webTerminalRequestVO);
        return webTerminalRequestVO;
    }

    /**
     * 消息中转给 用户操作记录
     *
     * @param userName 操作用户
     * @param vehicleName 车辆
     */
    public void sendCommandToRecord(String userName, String vehicleName, String terminalName) {
        VehicleOperateEventDTO event = new VehicleOperateEventDTO();
        event.setRecordTime(new Date());
        event.setVehicleName(vehicleName);
        event.setUserName(userName);
        event.setOperateSource(VehicleOperateSourceEnum.MONITOR.getSource());
        event.setOperateState(OperateStateEnum.SUCCESS.getState());
        event.setOperateTime(new Date());
        event.setOperateType(VehicleOperateTypeEnum.REMOTE_REQUEST_WEB_TERMINAL.getValue());
        event.setOperateMessage("访问远程工具"+ terminalName + "端");
        UserStatusDO userStatus = userStatusRepository.get(userName);
        if (!Objects.isNull(userStatus)) {
            event.setCockpitNumber(userStatus.getCockpitNumber());
        }
        log.info("推送车辆用户操作{}", JsonUtils.writeValueAsString(event));
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_VEHICLE_COMMAND_OPERATION.getTopic(), vehicleName, event);
    }
}