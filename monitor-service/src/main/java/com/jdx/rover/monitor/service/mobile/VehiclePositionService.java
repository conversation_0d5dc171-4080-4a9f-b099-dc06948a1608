package com.jdx.rover.monitor.service.mobile;

import com.jdx.rover.monitor.common.utils.jts.GeometryUtils;
import com.jdx.rover.monitor.manager.mobile.VehiclePositionManager;
import com.jdx.rover.monitor.po.mobile.VehiclePosition;
import com.jdx.rover.monitor.service.vehicle.VehicleLocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: VehiclePositionService
 * @author: wangguotai
 * @create: 2024-07-22 11:23
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class VehiclePositionService {

    private final VehiclePositionManager vehiclePositionManager;

    /**
     * 保存运动中车辆位置数据
     */
    public void saveInMotionVehicle() {
        List<VehiclePosition> vehiclePositionList = new ArrayList<>();
        VehicleLocationService.CACHE.asMap().forEach((vehicleName, vehicleLocation) -> {
            VehiclePosition vehiclePosition = new VehiclePosition();
            vehiclePosition.setVehicleName(vehicleName);
            vehiclePosition.setPoint(GeometryUtils.createPoint(vehicleLocation.getLon(), vehicleLocation.getLat()));
            vehiclePositionList.add(vehiclePosition);
        });
        if (CollectionUtils.isEmpty(vehiclePositionList)) {
            return;
        }
        vehiclePositionManager.getBaseMapper().insertOrUpdateBatch(vehiclePositionList);
    }
}