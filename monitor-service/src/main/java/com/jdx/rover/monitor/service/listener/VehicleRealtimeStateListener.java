package com.jdx.rover.monitor.service.listener;

import lombok.extern.slf4j.Slf4j;

import com.jdx.rover.monitor.service.web.MonitorScheduleStopService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class VehicleRealtimeStateListener {

  @Autowired
  private MonitorScheduleStopService vehicleRealtimeStateService;

//   @KafkaListener(topics = {MonitorKafkaTopicConstant.SERVER_GUARDIAN_VEHICLE_STATE})
  // public void onMessage(ConsumerRecord<String, String> record) {
  //   Optional op = Optional.ofNullable(record);
  //   if (op.isPresent()) {
  //     return;
  //   }
  //   String message = record.value();
  //   log.info("Received topic={}, message={}", record.topic(), message);
  //   vehicleRealtimeStateService.handleVehicleRealtimeState(message);
  // }MonitorScheduleListener
}
