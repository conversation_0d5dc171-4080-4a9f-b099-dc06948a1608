package com.jdx.rover.monitor.service.event;

import com.jdx.rover.monitor.enums.mobile.MessageModuleEnum;
import com.jdx.rover.monitor.enums.mobile.MessageStatusEnum;
import com.jdx.rover.monitor.enums.mobile.MessageTypeEnum;
import com.jdx.rover.monitor.event.AccidentEvent;
import com.jdx.rover.monitor.manager.todo.UserTodoTaskManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.UserTodoTask;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 事故监控器
 */
@Component
@Slf4j
public class AccidentEventListener {

    @Resource
    private UserTodoTaskManager userTodoTaskManager;

    @EventListener
    public void handleEvent(AccidentEvent accidentEvent) {
        Accident accident = accidentEvent.getAccident();
        //已经推送过的事故不再推送
        if(userTodoTaskManager.existsTodoTask(MessageModuleEnum.ACCIDENT.getValue(), MessageTypeEnum.ACCIDENT.getValue(),
                accident.getAccidentNo())) {
            log.info("京小鸽待办列表已经存在消息：module={}, type={}, businessKey={}", MessageModuleEnum.ACCIDENT.getValue(),
                    MessageTypeEnum.ACCIDENT.getValue(), accident.getAccidentNo());
            return;
        }
        //新增一条事故消息
        UserTodoTask userTodoTask = new UserTodoTask();
        userTodoTask.setModule(MessageModuleEnum.ACCIDENT.getValue());
        userTodoTask.setType(MessageTypeEnum.ACCIDENT.getValue());
        userTodoTask.setVehicleName(accident.getVehicleName());
        userTodoTask.setStationName(accident.getStationName());
        userTodoTask.setPushTime(new Date());
        userTodoTask.setDescription(accident.getAccidentDesc());
        userTodoTask.setStatus(MessageStatusEnum.HANDLE.getValue());
        userTodoTask.setBusinessKey(accident.getAccidentNo());
        userTodoTask.setAddress(accident.getAccidentAddress());
        userTodoTask.setCreateUser(accident.getCreateUser());
        userTodoTask.setModifyUser(accident.getCreateUser());
        userTodoTaskManager.save(userTodoTask);
    }
}
