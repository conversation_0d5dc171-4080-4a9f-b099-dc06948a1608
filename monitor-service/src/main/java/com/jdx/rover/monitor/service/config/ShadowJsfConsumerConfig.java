/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import com.jdx.rover.shadow.api.jsf.ShadowDataCollectionService;
import com.jdx.rover.shadow.api.jsf.ShadowTrackingEventService;
import com.jdx.rover.shadow.api.jsf.ShadowVehicleRouteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 影子服务jsf配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@Component
public class ShadowJsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册 ShadowTrackingEventService
     */
    @Bean
    public ShadowTrackingEventService shadowTrackingEventService() {
        return jsfConsumerRegister.createConsumerConfig(ShadowTrackingEventService.class).refer();
    }

    /**
     * 注册 ShadowVehicleRouteService
     */
    @Bean
    public ShadowVehicleRouteService shadowVehicleRouteService() {
        return jsfConsumerRegister.createConsumerConfig(ShadowVehicleRouteService.class).refer();
    }

    /**
     * 注册 ShadowDataCollectionService
     */
    @Bean
    public ShadowDataCollectionService shadowDataCollectionService() {
        return jsfConsumerRegister.createConsumerConfig(ShadowDataCollectionService.class).refer();
    }

}