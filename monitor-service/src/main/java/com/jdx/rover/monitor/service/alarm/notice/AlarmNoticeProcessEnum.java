package com.jdx.rover.monitor.service.alarm.notice;

import cn.hutool.extra.spring.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.function.BiFunction;

/**
 * <p>
 * This is a alarm notice message enum.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@Getter
public enum AlarmNoticeProcessEnum {
  /**
   * <p>
   * The enumerate alarm notice type.
   * </p>
   */
  VEHICLE_CRASH_F_BUMPER("VEHICLE_CRASH","-1042", "【重要】bump碰撞提醒", "%s市%s站，无人车%s于%s触发bump碰撞提醒,请您尽快处理!", (alarm, vehicle) -> SpringUtil.getBean(VehicleCommonAlarmNoticeService.class).process(alarm, vehicle)),
  VEHICLE_CRASH_R_BUMPER("VEHICLE_CRASH","-1043", "【重要】bump碰撞提醒", "%s市%s站，无人车%s于%s触发bump碰撞提醒,请您尽快处理!", (alarm, vehicle) -> SpringUtil.getBean(VehicleCommonAlarmNoticeService.class).process(alarm, vehicle)),
  VEHICLE_CRASH_BUMP("VEHICLE_CRASH", "-1055", "【重要】bump碰撞提醒", "%s市%s站，无人车%s于%s触发bump碰撞提醒,请您尽快处理!",(alarm, vehicle) -> SpringUtil.getBean(VehicleCommonAlarmNoticeService.class).process(alarm, vehicle)),
  VEHICLE_CRASH_GUARDIAN("VEHICLE_CRASH", "-13601","【重要】系统碰撞提醒", "%s市%s站，无人车%s于%s触发系统碰撞,请您尽快处理!", (alarm, vehicle) -> SpringUtil.getBean(VehicleCommonAlarmNoticeService.class).process(alarm, vehicle)),
  VEHICLE_STOP_BUTTON("VEHICLE_STOP_BUTTON", "DRIVEMODE_TAKEOVER_EMERGENCY_STOP","车端按钮被拍停", "%s市%s站，无人车%s于%s车端按钮被拍停", (alarm, vehicle) -> SpringUtil.getBean(VehicleCommonAlarmNoticeService.class).process(alarm, vehicle)),
  BOOT_FAIL("BOOT_FAIL", null,"【%s】%s启动失败", "您所负责的【%s】%s模块启动失败(已启动持续时间%s s)，请您查看处理。\n" + "车牌号：%s；当前所处阶段为：%s；车辆启动耗时：%s s。日志详情：%s PC端链接地址：%s\n", (alarm, vehicle) -> SpringUtil.getBean(VehicleBootAlarmNoticeService.class).process(alarm, vehicle)),
  BOOT_TIMEOUT("BOOT_TIMEOUT", null,"【%s】%s启动过久", "您所负责的【%s】%s模块启动过久(已启动持续时间%s s)，请您查看处理。\n" + "车牌号：%s；当前所处阶段为：%s；车辆启动耗时：%s s。日志详情：%s PC端链接地址：%s\n", (alarm, vehicle) -> SpringUtil.getBean(VehicleBootAlarmNoticeService.class).process(alarm, vehicle)),
  BOOT_ABNORMAL_FAIL("BOOT_ABNORMAL_FAIL", null,"【%s】%s异常启动失败", "您所负责的【%s】%s模块启动失败(已启动持续时间%s s)，请您查看处理。\n" + "车牌号：%s；当前所处阶段为：%s；车辆启动耗时：%s s。日志详情：%s PC端链接地址：%s\n", (alarm, vehicle) -> SpringUtil.getBean(VehicleBootAlarmNoticeService.class).process(alarm, vehicle)),
  VEHICLE_DRIVING_LANE("VEHICLE_DRIVING_LANE", "VEHICLE_DRIVING_LANE", "机动车道行驶告警", "%s车辆%s于%s行驶至机动车道，请及时关注。", (alarm, vehicle) -> SpringUtil.getBean(VehicleDrivingLaneNoticeService.class).process(alarm, vehicle));
  /**
   * <p>
   * The alarm type.
   * </p>
   */
  private final String alarm;

  /**
   * <p>
   * The alarm code.
   * </p>
   */
  private final String alarmCode;

  /**
   * <p>
   * The notice title.
   * </p>
   */
  private final String title;

  /**
   * <p>
   * The notice content.
   * </p>
   */
  private final String content;

  private final BiFunction<AlarmNoticeProcessEnum, String, Boolean> processFunction;

  public static AlarmNoticeProcessEnum of(String alarm, String code) {
    for (AlarmNoticeProcessEnum em : AlarmNoticeProcessEnum.values()) {
      if (Objects.equals(alarm, em.getAlarm()) && Objects.equals(code, em.getAlarmCode())) {
        return em;
      }
    }
    return null;
  }




}
