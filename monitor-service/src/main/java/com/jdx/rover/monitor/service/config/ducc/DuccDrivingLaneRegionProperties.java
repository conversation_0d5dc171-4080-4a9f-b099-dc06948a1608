/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.config.ducc;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * DUCC配置行驶机动车道区域
 * </p>
 * 
 * <AUTHOR>
 * @date 2024/11/26
 * @version 1.0.0
 */
@Data
public class DuccDrivingLaneRegionProperties implements Serializable {
  private static final long serialVersionUID = 1L;
  /**
   * <p>
   * 最大纬度
   * </p>
   */
  private Double maxLatitude;

  /**
   * <p>
   * 最小纬度
   * </p>
   */
  private Double minLatitude;

  /**
   * <p>
   * 最大经度
   * </p>
   */
  private Double maxLongitude;

  /**
   * <p>
   * 最小经度
   * </p>
   */
  private Double minLongitude;

}
