package com.jdx.rover.monitor.service.cockpit;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.metadata.api.domain.enums.common.EnableEnum;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitBasicInfoDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamBasicInfoDTO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamBasicUserVO;
import com.jdx.rover.monitor.api.domain.dto.CockpitRealStatusDTO;
import com.jdx.rover.monitor.api.domain.enums.AcceptIssueStatusEnum;
import com.jdx.rover.monitor.api.domain.enums.CockpitModeEnum;
import com.jdx.rover.monitor.api.domain.enums.CockpitStatusEnum;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.entity.VehicleRemoteOperationStatusEnum;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.cockpit.CockpitManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamMqttManager;
import com.jdx.rover.monitor.manager.cockpit.WorkRecordManager;
import com.jdx.rover.monitor.manager.ticket.IssueQueryApiManager;
import com.jdx.rover.monitor.manager.user.UserInfoMqttManager;
import com.jdx.rover.monitor.po.WorkRecord;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.vo.cockpit.EnterCockpitVO;
import com.jdx.rover.monitor.vo.cockpit.SwitchModeVO;
import com.jdx.rover.monitor.vo.cockpit.SwitchStatusVO;
import com.jdx.rover.ticket.domain.dto.query.IssueCheckDTO;
import com.jdx.rover.ticket.api.kafka.IssueCockpitListDTO;
import com.jdx.rover.ticket.domain.vo.query.QueryCockpitVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @description: 座席模式切换Service
 * @author: wangguotai
 * @create: 2024-06-11 10:21
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class CockpitChangeService {

    private final UserStatusRepository userStatusRepository;

    private final CockpitStatusRepository cockpitStatusRepository;

    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    private final UserVehicleNameRepository userVehicleNameRepository;

    private final JmqProducerService jmqProducerService;

    private final IssueQueryApiManager issueQueryApiManager;

    private final WorkRecordManager workRecordManager;

    private final CockpitManager cockpitManager;

    private final CockpitTeamManager cockpitTeamManager;

    private final UserInfoMqttManager userInfoMqttManager;

    private final CockpitTeamMqttManager cockpitTeamMqttManager;

    /**
     * 1、入座座席
     *
     * @param enterCockpitVO enterCockpitVO
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum enterCockpit(EnterCockpitVO enterCockpitVO) {
        // 并发加锁，避免一个座席被多人入座
        String cockpitLockKey = RedisKeyEnum.COCKPIT_STATUS_UPDATE_LOCK_PREFIX.getValue() + enterCockpitVO.getCockpitNumber();
        if (RedissonUtils.tryLock(cockpitLockKey)) {
            try {
                String username = UserUtils.getAndCheckLoginUser();
                // 0、校验座席团队是否停用
                CockpitTeamBasicUserVO userVO = new CockpitTeamBasicUserVO();
                userVO.setUserName(username);
                CockpitTeamBasicInfoDTO teamInfoOfUserName = cockpitTeamManager.getTeamBasicInfoOfUserName(userVO);
                if (Objects.isNull(teamInfoOfUserName) || EnableEnum.DISABLE.getEnable().equals(teamInfoOfUserName.getEnable())) {
                    log.error("Current user cockpit team disable {}.", username);
                    return MonitorErrorEnum.ERROR_COCKPIT_TEAM_DISABLE;
                }
                // 1、校验当前用户是否正接管
                // 2、校验当前座席状态
                String cockpitNumber = enterCockpitVO.getCockpitNumber();
                CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(cockpitNumber);
                if (!Objects.isNull(cockpitStatusDO.getCockpitStatus()) && !CockpitStatusEnum.OFFLINE.getValue().equals(cockpitStatusDO.getCockpitStatus())) {
                    log.error("Current cockpit already occupied {}.", cockpitNumber);
                    return MonitorErrorEnum.ERROR_COCKPIT_OCCUPIED;
                }
                // 3、校验当前用户状态
                UserStatusDO userStatusDO = userStatusRepository.get(username);
                if (StringUtils.isNotBlank(userStatusDO.getCockpitNumber())) {
                    log.error("Current user already entered {}.", username);
                    return MonitorErrorEnum.ERROR_USER_ENTERED;
                }
                String acceptIssueStatus = CockpitModeEnum.FREE_MODE.getValue().equals(enterCockpitVO.getCockpitMode()) ? AcceptIssueStatusEnum.STOP.getValue() : AcceptIssueStatusEnum.ACCEPT.getValue();
                // 4、更新座席状态
                Map<String, Object> cockpitParamMap = new ParamMap<CockpitStatusDO>().addNonNullProperty(CockpitStatusDO::getCockpitUserName, username).addNonNullProperty(CockpitStatusDO::getCockpitStatus, CockpitStatusEnum.WORK.getValue()).addNonNullProperty(CockpitStatusDO::getCockpitMode, enterCockpitVO.getCockpitMode()).addNonNullProperty(CockpitStatusDO::getAcceptIssueStatus, acceptIssueStatus).toMap();
                cockpitStatusRepository.putAllMapObject(cockpitNumber, cockpitParamMap);
                // 5、更新用户状态
                CockpitBasicInfoDTO cockpitBasicInfo = cockpitManager.getCockpitBasicInfo(cockpitNumber);
                Map<String, Object> userParamMap = new ParamMap<UserStatusDO>().addNonNullProperty(UserStatusDO::getWorkStartTime, new Date()).addNonNullProperty(UserStatusDO::getWorkMode, cockpitBasicInfo.getCockpitType()).addNonNullProperty(UserStatusDO::getCockpitMode, enterCockpitVO.getCockpitMode()).addNonNullProperty(UserStatusDO::getCockpitNumber, cockpitNumber).toMap();
                userStatusRepository.putAllMapObject(username, userParamMap);
                // 6、新增工作记录
                WorkRecord workRecord = new WorkRecord();
                workRecord.setUserName(username);
                workRecord.setCockpitNumber(cockpitNumber);
                workRecord.setCockpitStatus(CockpitStatusEnum.WORK.getValue());
                workRecord.setCockpitMode(enterCockpitVO.getCockpitMode());
                workRecord.setStartTime(new Date());
                workRecord.setCockpitType(cockpitBasicInfo.getCockpitType());
                workRecord.setCockpitTeamNumber(cockpitBasicInfo.getCockpitTeamNumber());
                workRecordManager.save(workRecord);
                // 7、对外推送消息
                CockpitRealStatusDTO statusDTO = new CockpitRealStatusDTO();
                statusDTO.setCockpitNumber(cockpitNumber);
                statusDTO.setCockpitUserName(username);
                statusDTO.setCockpitStatus(CockpitStatusEnum.WORK.getValue());
                statusDTO.setAcceptIssueStatus(acceptIssueStatus);
                statusDTO.setCockpitMode(enterCockpitVO.getCockpitMode());
                statusDTO.setRecordTime(new Date());
                jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_COCKPIT_STATUS_CHANGE.getTopic(), statusDTO.getCockpitNumber(), statusDTO);
                // 8、推送mqtt消息
                userInfoMqttManager.pushUserInfo(username);
                cockpitTeamMqttManager.pushCockpitTeam(cockpitBasicInfo.getCockpitTeamNumber());
            } finally {
                RedissonUtils.unLock(cockpitLockKey);
            }
            return MonitorErrorEnum.OK;
        }
        return MonitorErrorEnum.ERROR_REPEAT_OPERATION;
    }

    /**
     * 2、退出座席
     *
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum quitCockpit() {
        String username = UserUtils.getAndCheckLoginUser();
        // 1、校验当前用户是否在座席
        UserStatusDO userStatusDO = userStatusRepository.get(username);
        String cockpitNumber = userStatusDO.getCockpitNumber();
        if (StringUtils.isBlank(cockpitNumber)) {
            log.error("Current user {} already exited.", username);
            return MonitorErrorEnum.ERROR_USER_EXITED;
        }
        // 2、校验当前用户是否正接管
        // 3、校验当前座席是否有工单
        if (checkHasIssue(cockpitNumber)) {
            log.error("Current cockpit has issue {}.", cockpitNumber);
            return MonitorErrorEnum.ERROR_COCKPIT_HAS_ISSUE;
        }
        // 4、退出
        quit(username, cockpitNumber);
        return MonitorErrorEnum.OK;
    }

    /**
     * 3、切换模式
     *
     * @param switchModeVO switchModeVO
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum switchMode(SwitchModeVO switchModeVO) {
        String username = UserUtils.getAndCheckLoginUser();
        // 1、校验当前用户是否在座席
        UserStatusDO userStatusDO = userStatusRepository.get(username);
        String cockpitNumber = userStatusDO.getCockpitNumber();
        if (StringUtils.isBlank(cockpitNumber) || !switchModeVO.getCockpitNumber().equals(cockpitNumber)) {
            log.error("Current user cockpit not matched {}.", username);
            return MonitorErrorEnum.ERROR_USER_COCKPIT_MATCH;
        }
        // 2、校验当前用户是否正接管
        if (checkTakeOver(username)) {
            log.error("Current user taking over {}.", username);
            return MonitorErrorEnum.ERROR_USER_TAKING_OVER;
        }
        // 3、校验当前座席是否有工单
        if (checkHasIssue(cockpitNumber)) {
            log.error("Current cockpit has issue {}.", cockpitNumber);
            return MonitorErrorEnum.ERROR_COCKPIT_HAS_ISSUE;
        }
        // 4、更新用户状态
        userStatusRepository.putMapValue(username, UserStatusDO::getCockpitMode, switchModeVO.getCockpitMode());
        ParamMap<CockpitStatusDO> cockpitParamMap = new ParamMap<CockpitStatusDO>().addNonNullProperty(CockpitStatusDO::getCockpitUserName, username).addNonNullProperty(CockpitStatusDO::getCockpitMode, switchModeVO.getCockpitMode());
        // 自由模式下更新座席接单状态
        if (CockpitModeEnum.FREE_MODE.getValue().equals(switchModeVO.getCockpitMode())) {
            cockpitParamMap.addNonNullProperty(CockpitStatusDO::getAcceptIssueStatus, AcceptIssueStatusEnum.STOP.getValue());
        }
        // 5、更新座席状态
        cockpitStatusRepository.putAllMapObject(cockpitNumber, cockpitParamMap.toMap());
        // 6、更新+新增工作记录
        CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(cockpitNumber);
        CockpitBasicInfoDTO cockpitBasicInfo = cockpitManager.getCockpitBasicInfo(cockpitNumber);
        Date date = new Date();
        workRecordManager.lambdaUpdate().eq(WorkRecord::getUserName, username).eq(WorkRecord::getCockpitNumber, cockpitNumber).isNull(WorkRecord::getEndTime).set(WorkRecord::getEndTime, date).update();
        WorkRecord workRecord = new WorkRecord();
        workRecord.setUserName(username);
        workRecord.setCockpitNumber(cockpitNumber);
        workRecord.setCockpitStatus(cockpitStatusDO.getCockpitStatus());
        workRecord.setCockpitMode(switchModeVO.getCockpitMode());
        workRecord.setStartTime(date);
        workRecord.setCockpitType(cockpitBasicInfo.getCockpitType());
        workRecord.setCockpitTeamNumber(cockpitBasicInfo.getCockpitTeamNumber());
        workRecordManager.save(workRecord);
        // 7、对外推送消息
        String acceptIssueStatus = CockpitModeEnum.FREE_MODE.getValue().equals(switchModeVO.getCockpitMode()) ? AcceptIssueStatusEnum.STOP.getValue() : cockpitStatusDO.getAcceptIssueStatus();
        CockpitRealStatusDTO statusDTO = new CockpitRealStatusDTO();
        statusDTO.setCockpitNumber(cockpitNumber);
        statusDTO.setCockpitUserName(username);
        statusDTO.setCockpitStatus(cockpitStatusDO.getCockpitStatus());
        statusDTO.setAcceptIssueStatus(acceptIssueStatus);
        statusDTO.setCockpitMode(switchModeVO.getCockpitMode());
        statusDTO.setRecordTime(new Date());
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_COCKPIT_STATUS_CHANGE.getTopic(), statusDTO.getCockpitNumber(), statusDTO);
        // 8、推送mqtt消息
        userInfoMqttManager.pushUserInfo(username);
        cockpitTeamMqttManager.pushCockpitTeam(cockpitBasicInfo.getCockpitTeamNumber());
        return MonitorErrorEnum.OK;
    }

    /**
     * 4、切换状态
     *
     * @param switchStatusVO switchStatusVO
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum switchStatus(SwitchStatusVO switchStatusVO) {
        String username = UserUtils.getAndCheckLoginUser();
        String cockpitNumber = switchStatusVO.getCockpitNumber();
        CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(cockpitNumber);
        UserStatusDO userStatusDO = userStatusRepository.get(username);
        CockpitBasicInfoDTO cockpitBasicInfo = cockpitManager.getCockpitBasicInfo(cockpitNumber);
        // 判断重新工作
        if (CockpitStatusEnum.REST.getValue().equals(cockpitStatusDO.getCockpitStatus()) && CockpitStatusEnum.WORK.getValue().equals(switchStatusVO.getCockpitStatus()) && AcceptIssueStatusEnum.ACCEPT.getValue().equals(switchStatusVO.getAcceptIssueStatus())) {
            log.info("Current cockpit switch status to WORK {}.", cockpitNumber);
            Date date = new Date();
            // 更新用户状态
            userStatusRepository.putMapValue(username, UserStatusDO::getWorkStartTime, date);
            // 更新+新增工作记录
            workRecordManager.lambdaUpdate().eq(WorkRecord::getUserName, username).eq(WorkRecord::getCockpitNumber, cockpitNumber).isNull(WorkRecord::getEndTime).set(WorkRecord::getEndTime, date).update();
            WorkRecord workRecord = new WorkRecord();
            workRecord.setUserName(username);
            workRecord.setCockpitNumber(cockpitNumber);
            workRecord.setCockpitStatus(switchStatusVO.getCockpitStatus());
            workRecord.setCockpitMode(userStatusDO.getCockpitMode());
            workRecord.setStartTime(date);
            workRecord.setCockpitType(cockpitBasicInfo.getCockpitType());
            workRecord.setCockpitTeamNumber(cockpitBasicInfo.getCockpitTeamNumber());
            workRecordManager.save(workRecord);
        }
        // 判断休息
        if (CockpitStatusEnum.REST.getValue().equals(switchStatusVO.getCockpitStatus())) {
            log.info("Current cockpit switch status to REST {}.", cockpitNumber);
            // 更新+新增工作记录
            Date date = new Date();
            workRecordManager.lambdaUpdate().eq(WorkRecord::getUserName, username).eq(WorkRecord::getCockpitNumber, cockpitNumber).isNull(WorkRecord::getEndTime).set(WorkRecord::getEndTime, date).update();
            WorkRecord workRecord = new WorkRecord();
            workRecord.setUserName(username);
            workRecord.setCockpitNumber(cockpitNumber);
            workRecord.setCockpitStatus(switchStatusVO.getCockpitStatus());
            workRecord.setCockpitMode(userStatusDO.getCockpitMode());
            workRecord.setStartTime(date);
            workRecord.setCockpitType(cockpitBasicInfo.getCockpitType());
            workRecord.setCockpitTeamNumber(cockpitBasicInfo.getCockpitTeamNumber());
            workRecordManager.save(workRecord);
            // 清空工作开始时间+刷新历史工作时长
            refreshWorkTimeTotalHistory(username, cockpitNumber);
        }
        // 判断结单后休息
        if (CockpitStatusEnum.WORK.getValue().equals(cockpitStatusDO.getCockpitStatus()) && CockpitStatusEnum.WORK.getValue().equals(switchStatusVO.getCockpitStatus()) && AcceptIssueStatusEnum.STOP.getValue().equals(switchStatusVO.getAcceptIssueStatus())) {
            log.info("Current cockpit switch status to AFTER REST {}.", cockpitNumber);
            String triggerKey = RedisKeyEnum.COCKPIT_REST_AFTER_ISSUE_PREFIX.getValue() + cockpitNumber;
            RedissonUtils.setObject(triggerKey, System.currentTimeMillis());
        }
        // 判断取消结单后休息
        if (CockpitStatusEnum.WORK.getValue().equals(cockpitStatusDO.getCockpitStatus()) && CockpitStatusEnum.WORK.getValue().equals(switchStatusVO.getCockpitStatus()) && AcceptIssueStatusEnum.ACCEPT.getValue().equals(switchStatusVO.getAcceptIssueStatus())) {
            log.info("Current cockpit switch status to CANCEL AFTER REST {}.", cockpitNumber);
            String cancelKey = RedisKeyEnum.COCKPIT_REST_AFTER_ISSUE_PREFIX.getValue() + cockpitNumber;
            RedissonUtils.deleteObject(cancelKey);
        }
        // 1、更新座席状态
        Map<String, Object> cockpitParamMap = new ParamMap<CockpitStatusDO>().addNonNullProperty(CockpitStatusDO::getCockpitStatus, switchStatusVO.getCockpitStatus()).addNonNullProperty(CockpitStatusDO::getAcceptIssueStatus, switchStatusVO.getAcceptIssueStatus()).toMap();
        cockpitStatusRepository.putAllMapObject(switchStatusVO.getCockpitNumber(), cockpitParamMap);
        // 2、对外推送消息
        CockpitRealStatusDTO statusDTO = new CockpitRealStatusDTO();
        statusDTO.setCockpitNumber(cockpitNumber);
        statusDTO.setCockpitUserName(username);
        statusDTO.setCockpitStatus(switchStatusVO.getCockpitStatus());
        statusDTO.setAcceptIssueStatus(switchStatusVO.getAcceptIssueStatus());
        statusDTO.setCockpitMode(userStatusDO.getCockpitMode());
        statusDTO.setRecordTime(new Date());
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_COCKPIT_STATUS_CHANGE.getTopic(), statusDTO.getCockpitNumber(), statusDTO);
        // 3、推送mqtt消息
        userInfoMqttManager.pushUserInfo(username);
        cockpitTeamMqttManager.pushCockpitTeam(cockpitBasicInfo.getCockpitTeamNumber());
        return MonitorErrorEnum.OK;
    }

    /**
     * 5、进入座席模式（按钮点击校验）
     *
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum enterMode() {
        String username = UserUtils.getAndCheckLoginUser();
        // 1、校验当前用户是否正接管
        if (checkTakeOver(username)) {
            log.error("Current user taking over {}.", username);
            return MonitorErrorEnum.ERROR_USER_TAKING_OVER;
        }
        return MonitorErrorEnum.OK;
    }

    /**
     * 6、结单后休息判断
     *
     * @param issueCockpitListDTO issueCockpitListDTO
     */
    public void checkRest(IssueCockpitListDTO issueCockpitListDTO) {
        if (CollectionUtils.isEmpty(issueCockpitListDTO.getList())) {
            String userName = issueCockpitListDTO.getUserName();
            String cockpitNumber = issueCockpitListDTO.getCockpitNumber();
            String checkKey = RedisKeyEnum.COCKPIT_REST_AFTER_ISSUE_PREFIX.getValue() + cockpitNumber;
            if (RedissonUtils.hasKey(checkKey)) {
                log.info("CheckRest trigger success {}.", cockpitNumber);
                // 删除结单后休息缓存标识
                RedissonUtils.deleteObject(checkKey);
                CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(cockpitNumber);
                if (!CockpitStatusEnum.WORK.getValue().equals(cockpitStatusDO.getCockpitStatus())) {
                    log.error("Cockpit status not match {} {}.", cockpitNumber, cockpitStatusDO.getCockpitStatus());
                    return;
                }
                // 1、更新座席状态
                cockpitStatusRepository.putMapValue(cockpitNumber, CockpitStatusDO::getCockpitStatus, CockpitStatusEnum.REST.getValue());
                // 2、更新+新增工作记录
                CockpitBasicInfoDTO cockpitBasicInfo = cockpitManager.getCockpitBasicInfo(cockpitNumber);
                UserStatusDO userStatusDO = userStatusRepository.get(userName);
                Date date = new Date();
                workRecordManager.lambdaUpdate().eq(WorkRecord::getUserName, userName).eq(WorkRecord::getCockpitNumber, cockpitNumber).isNull(WorkRecord::getEndTime).set(WorkRecord::getEndTime, date).update();
                WorkRecord workRecord = new WorkRecord();
                workRecord.setUserName(userName);
                workRecord.setCockpitNumber(cockpitNumber);
                workRecord.setCockpitStatus(CockpitStatusEnum.REST.getValue());
                workRecord.setCockpitMode(userStatusDO.getCockpitMode());
                workRecord.setStartTime(date);
                workRecord.setCockpitType(cockpitBasicInfo.getCockpitType());
                workRecord.setCockpitTeamNumber(cockpitBasicInfo.getCockpitTeamNumber());
                workRecordManager.save(workRecord);
                // 清空工作开始时间+刷新历史工作时长
                refreshWorkTimeTotalHistory(userName, cockpitNumber);
                // 3、对外推送消息
                CockpitRealStatusDTO statusDTO = new CockpitRealStatusDTO();
                statusDTO.setCockpitNumber(cockpitNumber);
                statusDTO.setCockpitUserName(userName);
                statusDTO.setCockpitStatus(CockpitStatusEnum.REST.getValue());
                statusDTO.setAcceptIssueStatus(AcceptIssueStatusEnum.STOP.getValue());
                statusDTO.setCockpitMode(userStatusDO.getCockpitMode());
                statusDTO.setRecordTime(new Date());
                jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_COCKPIT_STATUS_CHANGE.getTopic(), statusDTO.getCockpitNumber(), statusDTO);
                // 4、推送mqtt消息
                userInfoMqttManager.pushUserInfo(userName);
                cockpitTeamMqttManager.pushCockpitTeam(cockpitBasicInfo.getCockpitTeamNumber());
            }
        }
    }

    /**
     * 7、60s强制退出座席
     */
    public MonitorErrorEnum forceQuitCockpit(String userName) {
        log.info("ForceQuitCockpit trigger success {}.", userName);
        // 1、获取当前用户座席状态
        UserStatusDO userStatusDO = userStatusRepository.get(userName);
        String cockpitNumber = userStatusDO.getCockpitNumber();
        if (StringUtils.isBlank(cockpitNumber)) {
            log.error("Current user {} already exited.", userName);
            return MonitorErrorEnum.OK;
        }
        // 2、退出
        quit(userName, cockpitNumber);
        return MonitorErrorEnum.OK;
    }

    /**
     * 退出
     *
     * @param userName      userName
     * @param cockpitNumber cockpitNumber
     */
    private void quit(String userName, String cockpitNumber) {
        // 删除结单后休息缓存标识
        String checkKey = RedisKeyEnum.COCKPIT_REST_AFTER_ISSUE_PREFIX.getValue() + cockpitNumber;
        if (RedissonUtils.hasKey(checkKey)) {
            RedissonUtils.deleteObject(checkKey);
        }
        // 更新座席状态
        cockpitStatusRepository.fastRemoveMapKey(cockpitNumber, CockpitStatusDO::getCockpitUserName, CockpitStatusDO::getCockpitMode);
        Map<String, Object> cockpitParamMap = new ParamMap<CockpitStatusDO>().addNonNullProperty(CockpitStatusDO::getCockpitStatus, CockpitStatusEnum.OFFLINE.getValue()).addNonNullProperty(CockpitStatusDO::getAcceptIssueStatus, AcceptIssueStatusEnum.STOP.getValue()).toMap();
        cockpitStatusRepository.putAllMapObject(cockpitNumber, cockpitParamMap);
        // 更新用户状态
        userStatusRepository.fastRemoveMapKey(userName, UserStatusDO::getWorkMode, UserStatusDO::getCockpitMode, UserStatusDO::getCockpitNumber);
        userStatusRepository.putMapValue(userName, UserStatusDO::getLastCockpitNumber, cockpitNumber);
        // 更新工作记录
        workRecordManager.lambdaUpdate().eq(WorkRecord::getUserName, userName).eq(WorkRecord::getCockpitNumber, cockpitNumber).isNull(WorkRecord::getEndTime).set(WorkRecord::getEndTime, new Date()).update();
        // 清空工作开始时间+刷新历史工作时长
        refreshWorkTimeTotalHistory(userName, cockpitNumber);
        // 工单系统推送消息，座席下工单要返回池子
        CockpitRealStatusDTO statusDTO = new CockpitRealStatusDTO();
        statusDTO.setCockpitNumber(cockpitNumber);
        statusDTO.setCockpitUserName(null);
        statusDTO.setCockpitStatus(CockpitStatusEnum.OFFLINE.getValue());
        statusDTO.setAcceptIssueStatus(AcceptIssueStatusEnum.STOP.getValue());
        statusDTO.setCockpitMode(null);
        statusDTO.setRecordTime(new Date());
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_COCKPIT_STATUS_CHANGE.getTopic(), statusDTO.getCockpitNumber(), statusDTO);
        // 推送mqtt消息
        userInfoMqttManager.pushUserInfo(userName);
        cockpitTeamMqttManager.pushCockpitTeamByCockpit(cockpitNumber);
    }

    /**
     * 清空工作开始时间+刷新历史工作时长
     *
     * @param username      username
     * @param cockpitNumber cockpitNumber
     */
    private void refreshWorkTimeTotalHistory(String username, String cockpitNumber) {
        userStatusRepository.fastRemoveMapKey(username, UserStatusDO::getWorkStartTime);
        Integer workTime = workRecordManager.getBaseMapper().getWorkTime(username, CockpitStatusEnum.WORK.getValue());
        userStatusRepository.putMapValue(username, UserStatusDO::getWorkTimeTotalHistory, workTime == null ? 0 : workTime);
    }

    /**
     * 判断用户是否正接管
     *
     * @param username username
     * @return boolean
     */
    private boolean checkTakeOver(String username) {
        Set<String> userVehicleList = userVehicleNameRepository.get(username);
        if (CollectionUtils.isEmpty(userVehicleList)) {
            return false;
        }
        List<VehicleTakeOverEntity> takeOverEntityList = vehicleTakeOverRepository.getTakeOverVehicle(username, Lists.newArrayList(userVehicleList));
        List<VehicleTakeOverEntity> filterList = takeOverEntityList.stream().filter(v -> VehicleRemoteOperationStatusEnum.TAKEOVER.getOperationStatus().equals(v.getOperationStatus())).toList();
        return !CollectionUtils.isEmpty(filterList);
    }

    /**
     * 判断座席是否有工单
     *
     * @param cockpitNumber cockpitNumber
     * @return boolean
     */
    private boolean checkHasIssue(String cockpitNumber) {
        QueryCockpitVO queryCockpitVO = new QueryCockpitVO();
        queryCockpitVO.setCockpitNumber(cockpitNumber);
        IssueCheckDTO issueCheckDTO = issueQueryApiManager.cockpitHasIssue(queryCockpitVO);
        return issueCheckDTO.getHasIssue();
    }

    /**
     * 8、定时修改用户时长
     */
    public void userTimeChangeTask() {
        log.info("Trigger user time change task.");
        // 加锁避免并发执行数据不一致
        String taskLockKey = RedisKeyEnum.COCKPIT_TIME_CHANGE_TASK_LOCK.getValue();
        if (RedissonUtils.tryLock(taskLockKey)) {
            try {
                Date date = new Date();
                // 昨天工作过的用户
                List<WorkRecord> workRecordList = workRecordManager.lambdaQuery().select(WorkRecord::getUserName).between(WorkRecord::getStartTime, DateUtil.beginOfDay(DateUtil.yesterday()), date).list();
                List<String> userNameList = workRecordList.stream().map(WorkRecord::getUserName).distinct().toList();
                if (CollectionUtils.isEmpty(userNameList)) {
                    log.info("Skip user time change task.");
                    return;
                }
                for (String userName : userNameList) {
                    try {
                        UserStatusDO userStatusDO = userStatusRepository.get(userName);
                        userStatusRepository.putMapValue(userName, UserStatusDO::getCompleteIssueCount, 0);
                        // 判断用户是否入座座席
                        if (StringUtils.isBlank(userStatusDO.getCockpitNumber())) {
                            userStatusRepository.putMapValue(userName, UserStatusDO::getWorkTimeTotalHistory, 0);
                            continue;
                        }
                        // 判断坐席状态
                        CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(userStatusDO.getCockpitNumber());
                        if (CockpitStatusEnum.REST.getValue().equals(cockpitStatusDO.getCockpitStatus())) {
                            userStatusRepository.putMapValue(userName, UserStatusDO::getWorkTimeTotalHistory, 0);
                        } else if (CockpitStatusEnum.WORK.getValue().equals(cockpitStatusDO.getCockpitStatus())) {
                            userStatusRepository.putMapValue(userName, UserStatusDO::getWorkTimeTotalHistory, 0);
                            userStatusRepository.putMapValue(userName, UserStatusDO::getWorkStartTime, date);
                        }
                        // 更新+新增工作记录
                        CockpitBasicInfoDTO cockpitBasicInfo = cockpitManager.getCockpitBasicInfo(cockpitStatusDO.getCockpitNumber());
                        workRecordManager.lambdaUpdate().eq(WorkRecord::getUserName, userName).eq(WorkRecord::getCockpitNumber, cockpitStatusDO.getCockpitNumber()).isNull(WorkRecord::getEndTime).set(WorkRecord::getEndTime, date).update();
                        WorkRecord workRecord = new WorkRecord();
                        workRecord.setUserName(userName);
                        workRecord.setCockpitNumber(cockpitStatusDO.getCockpitNumber());
                        workRecord.setCockpitStatus(cockpitStatusDO.getCockpitStatus());
                        workRecord.setCockpitMode(userStatusDO.getCockpitMode());
                        workRecord.setStartTime(date);
                        workRecord.setCockpitType(cockpitStatusDO.getCockpitType());
                        workRecord.setCockpitTeamNumber(cockpitBasicInfo.getCockpitTeamNumber());
                        workRecordManager.save(workRecord);
                        // 推送mqtt消息
                        userInfoMqttManager.pushUserInfo(userName);
                        cockpitTeamMqttManager.pushCockpitTeam(cockpitBasicInfo.getCockpitTeamNumber());
                    } catch (Exception e) {
                        log.error("Handle {} user time change task exception.", userName, e);
                    }
                }
                log.info("Finish user time change task.");
            } finally {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error("Sleep user time change task error.");
                }
                RedissonUtils.unLock(taskLockKey);
            }
        }
    }

    /**
     * 手动同步刷新用户座席(OPS接口)
     *
     * @param userName userName
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum refresh(String userName) {
        UserStatusDO userStatusDO = userStatusRepository.get(userName);
        String cockpitNumber = userStatusDO.getCockpitNumber();
        if (StringUtils.isNotBlank(cockpitNumber)) {
            CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(cockpitNumber);
            CockpitBasicInfoDTO cockpitBasicInfo = cockpitManager.getCockpitBasicInfo(cockpitNumber);
            // 对外推送消息
            CockpitRealStatusDTO statusDTO = new CockpitRealStatusDTO();
            statusDTO.setCockpitNumber(cockpitStatusDO.getCockpitNumber());
            statusDTO.setCockpitUserName(cockpitStatusDO.getCockpitUserName());
            statusDTO.setCockpitStatus(cockpitStatusDO.getCockpitStatus());
            statusDTO.setAcceptIssueStatus(cockpitStatusDO.getAcceptIssueStatus());
            statusDTO.setCockpitMode(cockpitStatusDO.getCockpitMode());
            statusDTO.setRecordTime(new Date());
            jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_COCKPIT_STATUS_CHANGE.getTopic(), statusDTO.getCockpitNumber(), statusDTO);
            // 推送mqtt消息
            userInfoMqttManager.pushUserInfo(userName);
            cockpitTeamMqttManager.pushCockpitTeam(cockpitBasicInfo.getCockpitTeamNumber());
        }
        return MonitorErrorEnum.OK;
    }
}