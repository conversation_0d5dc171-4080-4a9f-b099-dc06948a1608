/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot.alarm;

import com.jdx.k2.indoor.map.jsf.domain.dto.mapInfo.PointInfoDTO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO.DeviceAlarmEventDO;
import com.jdx.rover.monitor.entity.device.RobotTaskGoalDO;
import com.jdx.rover.monitor.enums.AlarmLevelEnum;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCodeEnum;
import com.jdx.rover.monitor.enums.device.DeviceTaskActionEnum;
import com.jdx.rover.monitor.repository.redis.robot.RobotAlarmRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotTaskGoalRepository;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import io.netty.util.HashedWheelTimer;
import io.netty.util.Timeout;
import io.netty.util.TimerTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 机器人调度检测服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotScheduleAlarmService {

    /**
     * 机器人告警缓存服务。
     */
    private final RobotAlarmRepository robotAlarmRepository;


    /**
     * 机器人任务目标缓存。
     */
    private final RobotTaskGoalRepository taskGoalRepository;

    /**
     * 机器人告警服务。
     */
    private final RobotAlarmService robotAlarmService;

    /**
     * wheelTimer
     */
    private final HashedWheelTimer wheelTimer = new HashedWheelTimer(2, TimeUnit.SECONDS, 20);

    /**
     * 处理设备业务监测告警。
     */
    public void processReportTaskState(String deviceName, Date startTime, Date endTime) {
        Map<String, DeviceAlarmEventDO> alarmMap = robotAlarmRepository.get(deviceName);
        RobotAlarmDO.DeviceAlarmEventDO taskTimeoutAlarm = alarmMap.get(DeviceAlarmCodeEnum.VEHICLE_TASK_TIMEOUT.getAlarmCode());
        Date currentDate = new Date();
        // 1、不管前往和到达，如果有告警，先消除
        if (Objects.nonNull(taskTimeoutAlarm) && Objects.nonNull(endTime)) {
            // 发送告警消失
            alarmMap.remove(DeviceAlarmCodeEnum.VEHICLE_TASK_TIMEOUT.getAlarmCode());
            robotAlarmRepository.fastRemoveMapKey(deviceName, Arrays.asList(DeviceAlarmCodeEnum.VEHICLE_TASK_TIMEOUT.getAlarmCode()));
            robotAlarmService.endDeviceAbnormal(deviceName, ProductTypeEnum.INTEGRATE.getValue(), DeviceAlarmCodeEnum.VEHICLE_TASK_TIMEOUT.getAlarmCode());
            // 发送告警变化通知
            robotAlarmService.sendJmqMessage(deviceName, alarmMap.values());
        } else if (Objects.nonNull(startTime) && Objects.isNull(endTime)) {
            // 告警产生
            DeviceAlarmEventDO deviceAlarmEventDo = new DeviceAlarmEventDO();
            deviceAlarmEventDo.setReportTime(currentDate);
            deviceAlarmEventDo.setAbnormalModule("");
            deviceAlarmEventDo.setErrorCode(DeviceAlarmCodeEnum.VEHICLE_TASK_TIMEOUT.getAlarmCode());
            deviceAlarmEventDo.setErrorLevel(AlarmLevelEnum.ALARM_NORMAL.getValue());
            deviceAlarmEventDo.setErrorMsg(DeviceAlarmCodeEnum.VEHICLE_TASK_TIMEOUT.getAlarmMsg());
            alarmMap.put(deviceAlarmEventDo.getErrorCode(), deviceAlarmEventDo);
            robotAlarmRepository.add(deviceName, DeviceAlarmCodeEnum.VEHICLE_TASK_TIMEOUT.getAlarmCode(), deviceAlarmEventDo);
            robotAlarmService.addDeviceAbnormal(deviceName, ProductTypeEnum.INTEGRATE.getValue(), deviceAlarmEventDo);
            // 发送告警变化通知
            robotAlarmService.sendJmqMessage(deviceName, alarmMap.values());
        }
  }

    /**
     * 检查机器人调度报警状态并处理相应的操作。
     * @param deviceName 设备名称。
     * @param taskStatus 任务状态。
     * @param pointInfoDto 点信息DTO。
     */
    public void checkRobotScheduleAlarm(String deviceName, String taskStatus, String taskId, PointInfoDTO pointInfoDto) {
        DeviceTaskActionEnum taskActionEnum = DeviceTaskActionEnum.getByValue(taskStatus);
        if (Objects.isNull(taskActionEnum)) {
            return;
        }
        // 先结束当前告警
        processReportTaskState(deviceName, new Date(), new Date());
        if (taskActionEnum == DeviceTaskActionEnum.STATUS_START) {
            // 前往的时候缓存数据，并5min定时检测是否未结束
            RobotTaskGoalDO robotTaskGoalDo = taskGoalRepository.get(deviceName);
            if (Objects.nonNull(robotTaskGoalDo) && StringUtils.equals(robotTaskGoalDo.getTaskId(), taskId)
                    && Objects.equals(robotTaskGoalDo.getStopId(), pointInfoDto.getId())) {
                return;
            }
            RobotTaskGoalDO taskGoalDo = new RobotTaskGoalDO();
            taskGoalDo.setTaskId(taskId);
            taskGoalDo.setStopId(pointInfoDto.getId());
            taskGoalDo.setStartTime(new Date());
            taskGoalRepository.add(deviceName, taskGoalDo);
            wheelTimer.newTimeout(new TimerTask() {
                @Override
                public void run(Timeout timeout) throws Exception {
                    RobotTaskGoalDO pointInfo = taskGoalRepository.get(deviceName);
                    if (Objects.nonNull(pointInfo) && StringUtils.equals(pointInfo.getTaskId(), taskId)
                            && Objects.equals(pointInfo.getStopId(), pointInfoDto.getId())) {
                        // 发送告警
                        processReportTaskState(deviceName, new Date(), null);
                    }
                }
            }, 5, TimeUnit.MINUTES);
        } else if (taskActionEnum == DeviceTaskActionEnum.STATUS_END) {
            taskGoalRepository.remove(deviceName);
        }
    }
}
