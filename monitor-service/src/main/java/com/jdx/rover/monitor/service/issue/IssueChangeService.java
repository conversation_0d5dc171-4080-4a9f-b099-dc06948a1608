/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.issue;

import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.service.web.MonitorAlarmService;
import com.jdx.rover.monitor.service.web.MonitorGuardianInfoService;
import com.jdx.rover.ticket.api.enums.IssueStatusEnum;
import com.jdx.rover.ticket.api.kafka.IssueEventDTO;
import com.jdx.rover.ticket.api.kafka.IssueFullInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 工单变化处理服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class IssueChangeService {

    private final MonitorAlarmService alarmService;

    private final MonitorGuardianInfoService guardianInfoService;

    private final AccidentManager accidentManager;

    /**
     * 工单变化处理接口
     */
    public void handleIssueChange(IssueFullInfoDTO issueFullInfo) {
        if (!StringUtils.equalsAny(issueFullInfo.getIssueStatus(), IssueStatusEnum.COMPLETE.getCode(),
                IssueStatusEnum.FORCE_CLOSE.getCode(), IssueStatusEnum.AUTO_CLOSE.getCode())) {
            return;
        }
        String issueNumber = issueFullInfo.getIssueNumber();
        String vehicleName = issueFullInfo.getVehicleName();
        List<String> alarmNumberList = new ArrayList<>();
        for(IssueEventDTO event : issueFullInfo.getEventList()) {
            if (StringUtils.equals(event.getEventType(), VehicleAlarmEnum.MANUAL_REPORT.getValue())) {
                // 结束人工告警并发送告警变化
                alarmService.endManualAlarm(issueFullInfo.getVehicleName(), issueFullInfo.getIssueEndTime());
            } else if (StringUtils.equals(event.getEventType(), VehicleAlarmEnum.VEHICLE_CRASH.getValue())) {
                // 关联事故与工单
                accidentManager.associateIssueNumber(event.getEventId(), issueNumber);
            }
            alarmNumberList.add(event.getEventId());
        }
        // 关联告警与工单
        guardianInfoService.updateAlarmIssueNo(vehicleName, alarmNumberList, issueNumber);
    }
}

