/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <p>
 * This is static class that provides configuration properties for assist jira record.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "assist-jira-record")
@Data
@Validated
public class AssistJiraRecordProperties {

  /**
   * <p>
   * The project key.
   * </p>
   */
  @NotNull
  @NotBlank
  private String projectKey = "JDXAD";

  /**
   * <p>
   * The issue type id.
   * </p>
   */
  @Min(0)
  @NotNull
  private Integer issueTypeId = 1;

  /**
   * <p>
   * The priority id.
   * </p>
   */
  @Min(0)
  @NotNull
  private Integer priorityId = 3;

  /**
   * <p>
   * Represents the defect type id of assist jira record.
   * </p>
   */
  @Min(0)
  @NotNull
  private Integer defectTypeId = 11505;

  /**
   * <p>
   * Represents the defect type value of assist jira record.
   * </p>
   */
  @NotNull
  @NotBlank
  private String defectTypeValue = "用户体验问题/优化提升";

  /**
   * <p>
   * Represents the severityId of assist jira record.
   * </p>
   */
  @Min(0)
  @NotNull
  private Integer severityId = 11511;

  /**
   * <p>
   * Represents the severity value of assist jira record.
   * </p>
   */
  @NotNull
  @NotBlank
  private String severityValue = "Major（一般缺陷）";

  /**
   * <p>
   * Represents the discovery phase id of assist jira record.
   * </p>
   */
  @Min(0)
  @NotNull
  private Integer discoveryPhaseId = 12005;

  /**
   * <p>
   * Represents the discovery phase value of assist jira record.
   * </p>
   */
  @NotNull
  @NotBlank
  private String discoveryPhaseValue = "用户反馈";

  /**
   * <p>
   * Represents the discover situation id of assist jira record.
   * </p>
   */
  @Min(0)
  @NotNull
  private Integer discoverSituationId = 11529;

  /**
   * <p>
   * Represents the discover situation value of assist jira record.
   * </p>
   */
  @NotNull
  @NotBlank
  private String discoverSituationValue = "否";

  /**
   * <p>
   * Represents the component id of assist jira record.
   * </p>
   */
  @Min(0)
  @NotNull
  private Integer componentId = 27791;

  /**
   * <p>
   * Represents the component name of assist jira record.
   * </p>
   */
  @NotNull
  private String componentName = "待分发";

  /**
   * <p>
   * Represents the scenario of assist jira record.
   * </p>
   */
  @NotNull
  @NotBlank
  private String scenario = "社道";

  /**
   * <p>
   * Represents the use case of assist jira record.
   * </p>
   */
  @NotNull
  @NotBlank
  private String useCase = "运营";

  /**
   * <p>
   * Represents the version id of assist jira record.
   * </p>
   */
  @Min(0)
  @NotNull
  private Integer versionId = 22084;

  /**
   * <p>
   * Represents the version value of assist jira record.
   * </p>
   */
  @NotNull
  @NotBlank
  private String versionValue = "unkown";
}
