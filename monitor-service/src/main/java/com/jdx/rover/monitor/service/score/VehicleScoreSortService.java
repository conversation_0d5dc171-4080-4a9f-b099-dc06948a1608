package com.jdx.rover.monitor.service.score;

import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleSortTypeEnum;
import com.jdx.rover.monitor.repository.redis.AllStationSortRepository;
import com.jdx.rover.monitor.repository.redis.AllVehicleNameSortRepository;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.util.vehicle.VehicleRealtimeUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleScoreSortService {

  /**
   * 用于访问和操作基础信息
   */
  private final VehicleBasicRepository vehicleBasicRepository;

  /**
   * 用于访问和操作站点信息
   */
  private final AllStationSortRepository allStationSortRepository;

  /**
   * 用于访问和操作全部车辆信息
   */
  private final AllVehicleNameSortRepository allVehicleNameSortRepository;

  /**
   * 用于访问和操作车辆告警信息
   */
  private final VehicleAlarmRepository vehicleAlarmRepository;

  /**
   * 用于访问和操作车辆调度信息
   */
  private final VehicleScheduleRepository vehicleScheduleRepository;

  /**
   * 初始化辆实时数据
   */
  public void updateVehicleRealtimeInfo(VehicleRealtimeInfoDTO vehicleRealtimeInfo) {
    String vehicleName = vehicleRealtimeInfo.getVehicleName();
    log.info("初始化单个实时信息开始!={}", vehicleName);
    VehicleBasicDTO dto = vehicleBasicRepository.get(vehicleName);
    List<String> stationSortList = allStationSortRepository.get();
    List<String> vehicleNameSortList = allVehicleNameSortRepository.get();
    updateRealtimeScore(vehicleRealtimeInfo, dto, vehicleNameSortList, stationSortList);
    log.info("初始化单个实时信息结束!={}", vehicleName);
  }

  /**
   * 初始化车辆基础数据
   */
  public void updateRealtimeScore(VehicleRealtimeInfoDTO vehicleRealtimeInfo, VehicleBasicDTO dto, List<String> vehicleNameSortList, List<String> stationSortList) {
    if (dto == null || dto.getName() == null) {
      log.error("dto对象为空,不更新分数,直接返回!");
      return;
    }
    String vehicleName = dto.getName();
    String systemState = VehicleRealtimeUtils.getSystemState(vehicleRealtimeInfo);
    String alarmEvent = vehicleAlarmRepository.getHighPriorityAlarm(vehicleName);
    String scheduleState = vehicleScheduleRepository.getScheduleState(vehicleName);
    double power = VehicleRealtimeUtils.getPower(vehicleRealtimeInfo);

    VehicleScoreBO bo = new VehicleScoreBO();
    bo.setVehicleName(vehicleName);
    bo.setSystemState(systemState);
    bo.setBusinessType(dto.getBusinessType());
    bo.setStationName(Optional.ofNullable(dto.getStationName()).orElse(""));
    bo.setPower(power);
    bo.setAlarmEvent(alarmEvent);
    bo.setVehicleNameSortList(vehicleNameSortList);
    bo.setScheduleState(scheduleState);
    bo.setStationSortList(stationSortList);
    updateRealtimeScore(bo);
  }

  /**
   * 更新分数
   */
  private void updateRealtimeScore(VehicleScoreBO bo) {
    for (VehicleSortTypeEnum itemEnum : VehicleSortTypeEnum.values()) {
      bo.setScoreType(itemEnum.getValue());
      //调用对应的策略方法
      UpdateScoreTypeEnum em = UpdateScoreTypeEnum.valueOf(bo.getScoreType());
      Consumer<VehicleScoreBO> consumer = em.getConsumer();
      if (Objects.isNull(consumer)) {
        log.error("车辆分数类型不存在={}", bo.getVehicleName());
        return;
      }
      consumer.accept(bo);
    }
  }
}
