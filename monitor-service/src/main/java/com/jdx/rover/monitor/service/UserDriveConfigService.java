/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service;

import com.jdx.rover.monitor.api.domain.web.jsf.dto.user.MonitorUserDriveConfigJsfDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.user.GetUserDriveConfigJsfVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.user.SaveUserDriveConfigJsfVO;
import com.jdx.rover.monitor.manager.UserDriveConfigManager;
import com.jdx.rover.monitor.po.user.UserDriveConfig;
import com.jdx.rover.monitor.repository.mapper.UserDriveConfigMapper;
import com.jdx.rover.monitor.service.base.BaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 用户配置service类
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@RequiredArgsConstructor
@Service
public class UserDriveConfigService extends BaseService<UserDriveConfigMapper, UserDriveConfig> {
    /**
     * 用户配置manager类
     */
    private final UserDriveConfigManager userDriveConfigManager;

    /**
     * 默认速度为15km/h
     */
    public static final Integer SPEED_LIMIT_DEFAULT = 15;

    /**
     * 保存用户配置
     */
    public Boolean saveOrUpdateUserDriveConfig(SaveUserDriveConfigJsfVO saveUserDriveConfigJsfVO) {
        UserDriveConfig userDriveConfig = new UserDriveConfig();
        userDriveConfig.setUserName(saveUserDriveConfigJsfVO.getUserName());
        userDriveConfig.setSpeedLimit(saveUserDriveConfigJsfVO.getSpeedLimit());

        UserDriveConfig userDriveConfigDb = userDriveConfigManager.getByUserName(saveUserDriveConfigJsfVO.getUserName());
        if (Objects.nonNull(userDriveConfigDb)) {
            userDriveConfig.setId(userDriveConfigDb.getId());
        }

        return this.saveOrUpdate(userDriveConfig);
    }

    /**
     * 获取用户配置
     */
    public MonitorUserDriveConfigJsfDTO getUserDriveConfig(GetUserDriveConfigJsfVO getUserDriveConfigJsfVO) {
        UserDriveConfig userDriveConfigDb = userDriveConfigManager.getByUserName(getUserDriveConfigJsfVO.getUserName());
        MonitorUserDriveConfigJsfDTO dto = new MonitorUserDriveConfigJsfDTO();
        dto.setUserName(getUserDriveConfigJsfVO.getUserName());
        if (Objects.isNull(userDriveConfigDb) || Objects.isNull(userDriveConfigDb.getSpeedLimit())) {
            dto.setSpeedLimit(SPEED_LIMIT_DEFAULT);
        } else {
            dto.setSpeedLimit(userDriveConfigDb.getSpeedLimit());
        }
        return dto;
    }
}