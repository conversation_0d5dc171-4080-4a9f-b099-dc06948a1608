/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorBasicDataInfoUnderUseDTO;

import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * This is a monitor user data info service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class MonitorUserDataInfoService {

  /**
   * <p>
   * Search monitor data info under user.
   * </p>
   * 
   * @param
   */
  public HttpResult<MonitorBasicDataInfoUnderUseDTO> getDataInfoByIdentity() {
    /*
     1 获取用户下所有车辆 
     2 遍历车辆，构造城市 和站点
     3 返回城市 站点 车辆树形结构
     */
    return null;
  }

}
