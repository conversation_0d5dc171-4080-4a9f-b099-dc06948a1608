/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.jira;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.jdx.common.exception.RoverRuntimeException;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.warehouse.vehicle.VehicleRealtimeJsfDTO;
import com.jdx.rover.datacenter.domain.vo.warehouse.vehicle.SingleVehicleJsfVo;
import com.jdx.rover.infrastructure.api.domain.entity.dto.xingyun.CreateBugDTO;
import com.jdx.rover.infrastructure.api.domain.entity.vo.xingyun.CreateBugWithAttachmentVO;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.ModuleEnum;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.SeverityEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.constant.BugConstant;
import com.jdx.rover.monitor.dto.jira.JiraAlarmListDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.jira.JiraSourceEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperationStatusEnum;
import com.jdx.rover.monitor.event.AccidentEvent;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.bug.BugReportManager;
import com.jdx.rover.monitor.manager.datacenter.DataCenterRealtimeApiManager;
import com.jdx.rover.monitor.manager.issue.IssueManager;
import com.jdx.rover.monitor.manager.ota.OtaApplicationVersionManager;
import com.jdx.rover.monitor.manager.ticket.IssueQueryApiManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.manager.xingyun.InfrastructureBugApiManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AssistJiraDebugTime;
import com.jdx.rover.monitor.po.AssistJiraRecord;
import com.jdx.rover.monitor.po.BugRecord;
import com.jdx.rover.monitor.po.GuardianVehicleAlarmInfo;
import com.jdx.rover.monitor.po.GuardianVehicleExceptionInfo;
import com.jdx.rover.monitor.repository.mapper.AssistJiraAttachmentMapper;
import com.jdx.rover.monitor.repository.mapper.AssistJiraDebugTimeMapper;
import com.jdx.rover.monitor.repository.mapper.AssistJiraRecordMapper;
import com.jdx.rover.monitor.repository.mapper.GuardianVehicleAlarmInfoMapper;
import com.jdx.rover.monitor.repository.mapper.GuardianVehicleExceptionInfoMapper;
import com.jdx.rover.monitor.repository.mapper.IssueRecordMapper;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.service.config.AssistJiraRecordProperties;
import com.jdx.rover.monitor.service.config.JiraRestServiceProperties;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.jdme.JdmeMessagePushService;
import com.jdx.rover.monitor.service.web.MonitorAccidentService;
import com.jdx.rover.monitor.vo.BugAddVO;
import com.jdx.rover.monitor.vo.BugUserDeleteVO;
import com.jdx.rover.monitor.vo.BugUserVO;
import com.jdx.rover.monitor.vo.BugWithAttachmentAddVO;
import com.jdx.rover.monitor.vo.SemiJiraAddVO;
import com.jdx.rover.monitor.vo.VehicleRealtimeVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import com.jdx.rover.ticket.domain.dto.query.IssueEventDTO;
import com.jdx.rover.ticket.domain.dto.query.VehicleIssuesDTO;
import com.jdx.rover.ticket.domain.vo.query.QueryIssueVO;
import com.jdx.rover.ticket.domain.vo.query.QueryVehicleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RSet;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.jdx.rover.monitor.enums.redis.RedisKeyEnum.BUG_USER_MAPPING;
import static com.jdx.rover.monitor.enums.redis.RedisKeyEnum.BUG_USER_SET;

/**
 * <p>
 * This is a jira service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MonitorJiraService implements ApplicationContextAware {
  /**
   * <p>
   * The configuration of jira service.
   * </p>
   */
  private final JiraRestServiceProperties jiraRestServiceProperties;

  private final AssistJiraRecordProperties assistJiraRecordProperties;

  /**
   * <p>
   * Represents the assist jira record repository.
   * </p>
   */
  private final AssistJiraRecordMapper assistJiraRecordMapper;

  private final S3Properties s3Properties;

  /**
   * <p>
   * Represents the assist jira debug time repository.
   * </p>
   */
  private final AssistJiraDebugTimeMapper assistJiraDebugTimeMapper;

  /**
   * <p>
   * Represents the assist jira debug time repository.
   * </p>
   */
  private final AssistJiraAttachmentMapper assistJiraAttachmentMapper;

  private final IssueRecordMapper issueRecordMapper;

  private final IssueManager issueManager;

  private final MonitorAccidentService monitorAccidentService;

  private final VehicleManager monitorVehicleManager;

  private final SingleVehicleManager singleVehicleManager;

  private final TrackingEventCollectService eventCollectService;

  private final GuardianVehicleExceptionInfoMapper guardianVehicleExceptionInfoMapper;

  private final GuardianVehicleAlarmInfoMapper guardianVehicleAlarmInfoMapper;

  private final InfrastructureBugApiManager infrastructureBugApiManager;

  private final OtaApplicationVersionManager otaApplicationVersionManager;

  private final MetadataUserApiManager metadataUserApiManager;

  private final IssueQueryApiManager issueQueryApiManager;

  private final AccidentManager accidentManager;

  private final JdmeMessagePushService jdmeMessagePushService;

  private final BugReportManager bugReportManager;

  private final DataCenterRealtimeApiManager dataCenterRealtimeApiManager;

  private ApplicationContext applicationContext;

  public HttpResult<String> reportJira(BugAddVO bugAddVO) {
    String userName = Optional.ofNullable(UserUtils.getLoginUser()).orElse(BugConstant.userName);
    // 默认设置为待分发
    if (StrUtil.isBlank(bugAddVO.getModule())) {
      bugAddVO.setModule(ModuleEnum.DISTRIBUTED.getValue());
    }
    VehicleBasicDTO vehicleBasicDto = monitorVehicleManager.getBasicByName(bugAddVO.getVehicleName());
    ParameterCheckUtility.checkNotNull(vehicleBasicDto, "bugAddVO#vehicle");
    //填充BUG描述信息
    Accident accident = null;
    if (Strings.isNotBlank(bugAddVO.getIssueNo()) && StringUtils.equals(JiraSourceEnum.ACCIDENT.getValue(), bugAddVO.getJiraSource())) {
      accident = accidentManager.selectByAccidentNo(bugAddVO.getIssueNo());
    }
    String accidentStationName = null == accident ? "" : accident.getStationName();
    setBugDescription(bugAddVO, StrUtil.isBlank(accidentStationName) ? vehicleBasicDto.getStationName() : accidentStationName, userName);
    //创建行云缺陷
    CreateBugDTO createBugDTO = createBug(bugAddVO, vehicleBasicDto, userName);
    if (createBugDTO == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
    }
    //保存bug数据
    saveBugData(bugAddVO, createBugDTO, userName);
    if (accident != null) {
      //京ME推送消息落库
      jdmeMessagePushService.savePushMsg(bugAddVO, vehicleBasicDto, createBugDTO, userName);
      monitorAccidentService.reportJiraOrXata(bugAddVO.getIssueNo(), null, createBugDTO.getBugCode(),userName
              , bugAddVO, AccidentOperationStatusEnum.NO_OPS.getValue().equals(accident.getOperationStatus()));
      singleVehicleManager.pushSingleVehicleAccident(accident.getVehicleName());
      //推送京小鸽待办
      AccidentEvent accidentEvent = new AccidentEvent(this, accident);
      applicationContext.publishEvent(accidentEvent);
    }
    eventCollectService.pushJiraEvent(bugAddVO, createBugDTO.getBugCode(), userName);
    return HttpResult.success(createBugDTO.getBugCode());
  }

  /**
   * 构建并设置缺陷描述信息
   * @param bugAddVO
   * @param stationName
   */
  private void setBugDescription(BugAddVO bugAddVO, String stationName, String userName) {
    //事故发生时的状态有延时，此处回退3秒进行弥补
    Date date = bugAddVO.getDebugTime().get(0);
    date = new Date(date.getTime());

    VehicleRealtimeVO vehicleRealtimeVO = new VehicleRealtimeVO();
    vehicleRealtimeVO.setVehicleName(bugAddVO.getVehicleName());
    vehicleRealtimeVO.setDate(date);
    VehicleRealtimeJsfDTO vehicleRealtimeDto = getVehicleRealtimeInfo(vehicleRealtimeVO);
    String systemStatusName = "--";
    for(SystemStateEnum systemStatus : SystemStateEnum.values()) {
      if(systemStatus.getSystemState().equals(vehicleRealtimeDto.getSystemState())) {
        systemStatusName = systemStatus.getSystemStateName();
        break;
      }
    }
    String vehicleStatusName = "--";
    for(VehicleStateEnum vehicleState : VehicleStateEnum.values()) {
      if(vehicleState.getVehicleState().equals(vehicleRealtimeDto.getVehicleState())) {
        vehicleStatusName = vehicleState.getVehicleStateName();
      }
    }
    StringBuilder descriptionBuilder = new StringBuilder();
    descriptionBuilder.append("[提报人]:");
    descriptionBuilder.append(userName);
    descriptionBuilder.append("  [车辆模式]:");
    descriptionBuilder.append(vehicleStatusName);
    descriptionBuilder.append("  [系统状态]:");
    descriptionBuilder.append(systemStatusName);
    descriptionBuilder.append("  [详情描述]:");
    if(StrUtil.isNotBlank(stationName)) {
      descriptionBuilder.append("【");
      descriptionBuilder.append(stationName);
      descriptionBuilder.append("】");
    }
    descriptionBuilder.append(bugAddVO.getDescription());
    bugAddVO.setDescription(descriptionBuilder.toString());
  }

  /**
   * 保存bug数据
   * @param bugAddVO
   * @param createBugDTO
   */
  private void saveBugData(BugAddVO bugAddVO, CreateBugDTO createBugDTO, String userName) {
    BugRecord bugRecord = new BugRecord();
    bugRecord.setAccidentNo(bugRecord.getAccidentNo());
    bugRecord.setBugCode(createBugDTO.getBugCode());
    bugRecord.setTopic(bugAddVO.getTopic());
    bugRecord.setDescription(bugAddVO.getDescription());
    bugRecord.setVehicleName(bugAddVO.getVehicleName());
    bugRecord.setSeverity(bugAddVO.getSeverity());
    bugRecord.setModule(bugAddVO.getModule());
    bugRecord.setVersion(bugAddVO.getVersion());
    bugRecord.setLevel(bugAddVO.getLevel());
    if (CollectionUtils.isNotEmpty(bugAddVO.getDebugTime())) {
      bugRecord.setDebugTime(bugAddVO.getDebugTime().toString());
    }
    bugRecord.setJiraSource(bugAddVO.getJiraSource());
    bugRecord.setIssueNo(bugAddVO.getIssueNo());
    bugRecord.setCreateUser(userName);
    bugRecord.setModifyUser(userName);
    bugReportManager.save(bugRecord);
  }

  /**
   * 根据事故群号发送卡片消息
   * @param paramMap
   * @throws Exception
   */
  public void sendJdmeCardMessage(Map<String, Object> paramMap) throws Exception {
    if(paramMap.containsKey("challenge")) {
      log.info("用户群入京ME群消息推送消息是验证消息，忽略逻辑处理！");
      return;
    }
    //chat_add_member 向群组添加成员
    String eventType = (String) paramMap.get("eventType");
    if("chat_add_member".equals(eventType)) {
      Map<String, Object> event = (Map<String, Object>) paramMap.get("event");
      //群号
      String groupId = (String) event.get("groupId");
      jdmeMessagePushService.sendJdmeCardMessage(groupId);
    }
  }

  /**
   * 增加车端报警
   * @param bugAddVO
   */
  private void addVehicleAlarm(BugAddVO bugAddVO) {
    try{
      String userName = UserUtils.getLoginUser();
      GuardianVehicleAlarmInfo alarmInfoPo = new GuardianVehicleAlarmInfo();
      alarmInfoPo.setComponentId(bugAddVO.getModule());
      alarmInfoPo.setComponentName(ModuleEnum.getNameByValue(bugAddVO.getModule()));
      alarmInfoPo.setTopic(bugAddVO.getTopic());
      alarmInfoPo.setDescription(bugAddVO.getDescription());
      alarmInfoPo.setVehicleVersion(bugAddVO.getVersion());
      alarmInfoPo.setAlarmEvent(bugAddVO.getSelectedAlarm());
      VehicleBasicDTO vehicleBasicDto = monitorVehicleManager.getBasicByName(bugAddVO.getVehicleName());
      if (vehicleBasicDto != null) {
        alarmInfoPo.setStationName(vehicleBasicDto.getStationName());
        alarmInfoPo.setCityName(vehicleBasicDto.getCityName());
        alarmInfoPo.setUseCase(vehicleBasicDto.getOwnerUseCase());
      }
      alarmInfoPo.setVehicleName(bugAddVO.getVehicleName());
      alarmInfoPo.setReportUserName(Optional.ofNullable(userName).orElse(BugConstant.userName));
      alarmInfoPo.setReportTimestamp(new Date());
      alarmInfoPo.setOperateTimestamp(new Date());
      guardianVehicleAlarmInfoMapper.insert(alarmInfoPo);
    }catch (Exception e) {
      log.error("Report jira add vehicle alarm exception", e);
    }
  }

  /**
   * <p>
   * Post semi issues.
   * </p>
   * 
   * @param semiJiraAddVO add view object of jira issue
   * @return BasicDto
   */
  public HttpResult semiReportJira(SemiJiraAddVO semiJiraAddVO) {
    String userName = UserUtils.getLoginUser();
    ParameterCheckUtility.checkNotNull(semiJiraAddVO, "SemiJiraAddVO");
    GuardianVehicleAlarmInfo vehicleAlarmInfo = guardianVehicleAlarmInfoMapper.selectById(semiJiraAddVO.getId());
    ParameterCheckUtility.checkNotNull(vehicleAlarmInfo, "VehicleAlarmInfo");
    VehicleBasicDTO vehicleBasicDto = monitorVehicleManager.getBasicByName(vehicleAlarmInfo.getVehicleName());
    ParameterCheckUtility.checkNotNull(vehicleBasicDto, "monitorJiraAddVo#vehicle");
    BugAddVO bugAddVO = createBugAddVO(vehicleAlarmInfo, userName);
    CreateBugDTO bugDTO = createBug(bugAddVO, vehicleBasicDto, userName);
    if (bugDTO == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
    }
    updateVehicleAlarmInfo(vehicleAlarmInfo, userName);
    eventCollectService.pushJiraEvent(bugAddVO, bugDTO.getBugCode(), userName);
    return HttpResult.success(bugDTO.getBugCode());
  }

  private void updateVehicleAlarmInfo(GuardianVehicleAlarmInfo vehicleAlarmInfo, String userName) {
    vehicleAlarmInfo.setReportTimestamp(new Date());
    vehicleAlarmInfo.setReportUserName(userName);
    guardianVehicleAlarmInfoMapper.updateById(vehicleAlarmInfo);
  }

  private BugAddVO createBugAddVO(GuardianVehicleAlarmInfo vehicleAlarmInfo, String userName) {
    BugAddVO bugAddVO = new BugAddVO();
    bugAddVO.setVehicleName(vehicleAlarmInfo.getVehicleName());
    StringBuilder exceptionBuilder = new StringBuilder();
    if (vehicleAlarmInfo.getExceptionInfoId() != null) {
      GuardianVehicleExceptionInfo exceptionLog = guardianVehicleExceptionInfoMapper
              .selectById(vehicleAlarmInfo.getExceptionInfoId());
      if (exceptionLog != null) {
        exceptionBuilder.append(" errorCode:").append(exceptionLog.getErrorCode()).append(" errorLevel: ")
                .append(exceptionLog.getErrorLevel()).append(" errorMsg: ").append(exceptionLog.getErrorMessage());
      }
    }
    StringBuilder descriptionBuilder = new StringBuilder("提报人账号[");
    descriptionBuilder.append(Optional.ofNullable(userName).orElse(BugConstant.userName)).append("], 系统状态[").append(vehicleAlarmInfo.getSystemState())
            .append("], 报警事件[").append(vehicleAlarmInfo.getAlarmEvent()).append("], 问题描述[")
            .append(vehicleAlarmInfo.getDescription()).append("], 错误信息[").append(exceptionBuilder.toString()).append(" ]");
    String topic = vehicleAlarmInfo.getDescription();
    if (topic.length() > 200) {
      bugAddVO.setTopic(topic.substring(0, 200));
    } else {
      bugAddVO.setTopic(topic);
    }
    bugAddVO.setDescription(descriptionBuilder.toString());
    bugAddVO.setVersion(vehicleAlarmInfo.getVehicleVersion());
    bugAddVO.setModule(vehicleAlarmInfo.getComponentId());
    bugAddVO.setSeverity(SeverityEnum.MINOR.getValue());
    bugAddVO.setDebugTime(Lists.newArrayList(vehicleAlarmInfo.getCreatedTime()));
    return bugAddVO;
  }

  /**
   * <p>
   * add record to AssistJiraRecordRepository, AssistJiraDebugTimeRepository,
   * AssistJiraAttachmentRepository
   * </p>
   *
   */
  private void addRecord(BugAddVO bugAddVO, VehicleBasicDTO vehicleBasicDto) {
    AssistJiraRecord assistJiraRecord = new AssistJiraRecord();
    assistJiraRecord.setIsReported(true);
    assistJiraRecord.setIssueTypeId(1);
    assistJiraRecord.setTopic(bugAddVO.getTopic());
    assistJiraRecord.setDescription(bugAddVO.getDescription());
    assistJiraRecord.setSeverityId(bugAddVO.getSeverity());
    assistJiraRecord.setSeverityValue(SeverityEnum.getNameByValue(bugAddVO.getSeverity()));
    assistJiraRecord.setComponentId(bugAddVO.getModule());
    assistJiraRecord.setComponentName(ModuleEnum.getNameByValue(bugAddVO.getModule()));
    // 获取请求用户
    assistJiraRecord.setCityName(vehicleBasicDto.getCityName());
    assistJiraRecord.setStationName(vehicleBasicDto.getStationName());
    assistJiraRecord.setReportUser(UserUtils.getAndCheckLoginUser());
    assistJiraRecord.setUseCase(vehicleBasicDto.getOwnerUseCase());
    assistJiraRecord.setVersionId(bugAddVO.getVersion());
    assistJiraRecord.setVersionValue(bugAddVO.getVersion());
    assistJiraRecord.setVehicleName(bugAddVO.getVehicleName());
    assistJiraRecord.setOperateTime(new Date());
    if (bugAddVO.getSelectedAlarm() != null) {
      assistJiraRecord.setAlarmEvent(bugAddVO.getSelectedAlarm());
    }
    assistJiraRecordMapper.insert(assistJiraRecord);
    for (Date date : bugAddVO.getDebugTime()) {
      AssistJiraDebugTime assistJiraDebugTime = new AssistJiraDebugTime();
      assistJiraDebugTime.setJiraRecordId(assistJiraRecord.getId());
      assistJiraDebugTime.setDebugTime(date);
      assistJiraDebugTimeMapper.insert(assistJiraDebugTime);
    }
  }

  /**
   * 创建行云缺陷
   * @param bugAddVO
   * @param vehicleBasicDTO
   */
  private CreateBugDTO createBug(BugAddVO bugAddVO, VehicleBasicDTO vehicleBasicDTO, String userName) {
    CreateBugWithAttachmentVO createBugVO = new CreateBugWithAttachmentVO();
    List<Date> jiraDebugTimeList = bugAddVO.getDebugTime();
    StringBuilder summaryBuilder = new StringBuilder();
    summaryBuilder.append(bugAddVO.getVehicleName()).append("|")
            .append(VehicleOwnerUseCaseEnum.getNameByValue(vehicleBasicDTO.getOwnerUseCase())).append("|").append("【").append(vehicleBasicDTO.getStationName()).append("】").append(bugAddVO.getTopic());
    if (jiraDebugTimeList != null && !jiraDebugTimeList.isEmpty()) {
      SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMdd-HH-mm-ss");
      StringBuilder jiraDebugTime = new StringBuilder();
      for (Date debugTime : jiraDebugTimeList) {
        jiraDebugTime.append(dateTimeFormat.format(debugTime) + ";");
      }
      String debugTimeString = jiraDebugTime.toString();
      createBugVO.setTitle(debugTimeString.substring(0, debugTimeString.length() - 1) + "|" + summaryBuilder.toString());
    }
    createBugVO.setDescription(bugAddVO.getDescription());
    createBugVO.setSeverity(bugAddVO.getSeverity());
    createBugVO.setModule(bugAddVO.getModule());
    createBugVO.setVersion(bugAddVO.getVersion());
    createBugVO.setLevel(bugAddVO.getLevel());
    createBugVO.setAttachment(bugAddVO.getAttachmentList());
    createBugVO.setAccidentLevel(bugAddVO.getAccidentLevel());
    createBugVO.setAccidentType(bugAddVO.getAccidentType());

    //userName转化为erp
    UserExtendInfoDTO userExtendInfoDTO = metadataUserApiManager.getUserExtendInfoByName(userName);
    if (userExtendInfoDTO == null || userExtendInfoDTO.getJdErp() == null) {
      throw new RoverRuntimeException("提报人erp不存在");
    }
    String jdErp = userExtendInfoDTO.getJdErp();
    String proposer = jdErp;
    String assignee = jdErp;
    RSet<Object> set = RedissonUtils.getRedissonClient().getSet(BUG_USER_SET.getValue());
    if (set.contains(jdErp)) {
      proposer = RedissonUtils.getObject(BUG_USER_MAPPING.getValue() + jdErp);
      if (ModuleEnum.DISTRIBUTED.getValue().equals(bugAddVO.getModule())) {
        assignee = proposer;
      }
    }
    if (!ModuleEnum.DISTRIBUTED.getValue().equals(bugAddVO.getModule())) {
      assignee = ModuleEnum.getOwnerByValue(bugAddVO.getModule());
    }
    createBugVO.setProposer(proposer);
    createBugVO.setAssignee(assignee);
    CreateBugDTO createBugDTO = infrastructureBugApiManager.createBug(createBugVO);
    return createBugDTO;
  }

  /**
   * 获取可提报缺陷的告警列表
   * @return
   */
  public JiraAlarmListDTO getAvailableReportAlarmList(String vehicleName) {
    JiraAlarmListDTO jiraAlarmListDto = new JiraAlarmListDTO();
    jiraAlarmListDto.setVehicleName(vehicleName);
    jiraAlarmListDto.setRoverVersion("");
    List<JiraAlarmListDTO.AlarmEvent> alarmList = new ArrayList<>();
    // 1：有处理中运维工单，展示工单下事件列表
    QueryVehicleVO queryVehicleVo = new QueryVehicleVO();
    queryVehicleVo.setVehicleName(vehicleName);
    VehicleIssuesDTO issuesDto = issueQueryApiManager.queryIssueByVehicle(queryVehicleVo);
    if(!Objects.isNull(issuesDto)) {
      List<String> issueNumberList = issuesDto.getIssueList().stream().map(issue -> issue.getIssueNumber()).collect(Collectors.toList());
      if (CollectionUtils.isNotEmpty(issueNumberList)) {
        QueryIssueVO queryIssueVo = new QueryIssueVO();
        queryIssueVo.setIssueNumber(issueNumberList.get(0));
        IssueEventDTO issueEventDto = issueQueryApiManager.queryIssueEventByIssue(queryIssueVo);
        issueEventDto.getEventList().stream().forEach(issueEvent ->{
          JiraAlarmListDTO.AlarmEvent alarmEvent = new JiraAlarmListDTO.AlarmEvent();
          alarmEvent.setAlarmEvent(issueEvent.getEventType());
          alarmEvent.setReportTime(issueEvent.getEventTime());
          alarmEvent.setAlarmMessage(issueEvent.getEventDescription());
          alarmEvent.setAlarmNumber(String.valueOf(issueEvent.getEventTime().getTime()));
          alarmList.add(alarmEvent);
        });
      }
    }
    jiraAlarmListDto.setAlarmList(alarmList);
    return jiraAlarmListDto;
  }

  /**
   * 通过OTA获取最新版本信息
   * @return
   */
  public HttpResult<List<String>> getRoverList() {
    List<String> roverVersionList = otaApplicationVersionManager.getRoverVersionList();
    return HttpResult.success(roverVersionList);
  }

  /**
   * 添加人员映射关系
   * @param bugUserVO
   * @return
   */
  public HttpResult addUserMapping(BugUserVO bugUserVO) {
    log.info("添加bug人员映射关系,userNameList:{}, mappingUserName:{}", bugUserVO.getReportUserNameList(), bugUserVO.getMappingUserName());
    RSet<Object> set = RedissonUtils.getRedissonClient().getSet(BUG_USER_SET.getValue());
    set.addAll(bugUserVO.getReportUserNameList());
    for (String userName : bugUserVO.getReportUserNameList()) {
      RedissonUtils.setObject(BUG_USER_MAPPING.getValue() + userName, bugUserVO.getMappingUserName());
    }
    return HttpResult.success();
  }

  /**
   * 删除人员映射关系
   */
  public HttpResult deleteUserMapping(BugUserDeleteVO bugUserDeleteVO) {
    log.info("删除bug人员映射关系,useNameList:{}", bugUserDeleteVO.getUserNameList());
    RSet<Object> set = RedissonUtils.getRedissonClient().getSet(BUG_USER_SET.getValue());
    set.removeAll(bugUserDeleteVO.getUserNameList());
    return HttpResult.success();
  }

  /**
   * 获取车辆历史实时信息
   * @param vehicleRealtimeVO vehicleRealtimeVO
   */
  public VehicleRealtimeJsfDTO getVehicleRealtimeInfo(VehicleRealtimeVO vehicleRealtimeVO) {
    VehicleRealtimeJsfDTO vehicleRealtimeDto = new VehicleRealtimeJsfDTO();
    SingleVehicleJsfVo requestDto = new SingleVehicleJsfVo();
    requestDto.setVehicleName(vehicleRealtimeVO.getVehicleName());
    requestDto.setStartTime(vehicleRealtimeVO.getDate());
    requestDto.setEndTime(vehicleRealtimeVO.getDate());
    List<VehicleRealtimeJsfDTO> vehicleRealtimeDtoList = dataCenterRealtimeApiManager.queryRealtimeInfo(requestDto);
    if (CollectionUtils.isNotEmpty(vehicleRealtimeDtoList)) {
      vehicleRealtimeDto = vehicleRealtimeDtoList.get(0);
    }
    return vehicleRealtimeDto;
  }
  /**
   * 回调,设置上下文
   * @param applicationContext
   * @throws BeansException
   */
  @Override
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    this.applicationContext = applicationContext;
  }

  /**
   * 适配gateway提交缺陷未迁移接口，后续前端支持后下线
   * @param bugAddVO
   * @return
   */
  @Deprecated
  public HttpResult reportJiraWithAttachment(BugWithAttachmentAddVO bugAddVO) {
    String userName = Optional.ofNullable(UserUtils.getLoginUser()).orElse(BugConstant.userName);
    // 默认设置为待分发
    if (StrUtil.isBlank(bugAddVO.getModule())) {
      bugAddVO.setModule(ModuleEnum.DISTRIBUTED.getValue());
    }
    VehicleBasicDTO vehicleBasicDto = monitorVehicleManager.getBasicByName(bugAddVO.getVehicleName());
    ParameterCheckUtility.checkNotNull(vehicleBasicDto, "bugAddVO#vehicle");
    //填充BUG描述信息
    Accident accident = null;
    if (Strings.isNotBlank(bugAddVO.getIssueNo()) && StringUtils.equals(JiraSourceEnum.ACCIDENT.getValue(), bugAddVO.getJiraSource())) {
      accident = accidentManager.selectByAccidentNo(bugAddVO.getIssueNo());
    }
    String accidentStationName = null == accident ? "" : accident.getStationName();
    setBugDescription(bugAddVO, StrUtil.isBlank(accidentStationName) ? vehicleBasicDto.getStationName() : accidentStationName, userName);
    //创建行云缺陷
    CreateBugDTO createBugDTO = createBugWithAttachment(bugAddVO, vehicleBasicDto, userName);
    if (createBugDTO == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
    }
    //保存bug数据
    saveBugData(bugAddVO, createBugDTO, userName);
    if (accident != null) {
      //京ME推送消息落库
      jdmeMessagePushService.savePushMsg(bugAddVO, vehicleBasicDto, createBugDTO, userName);
      monitorAccidentService.reportJiraOrXata(bugAddVO.getIssueNo(), null, createBugDTO.getBugCode(),userName
              , bugAddVO, AccidentOperationStatusEnum.NO_OPS.getValue().equals(accident.getOperationStatus()));
      singleVehicleManager.pushSingleVehicleAccident(accident.getVehicleName());
      //推送京小鸽待办
      AccidentEvent accidentEvent = new AccidentEvent(this, accident);
      applicationContext.publishEvent(accidentEvent);
    }
    eventCollectService.pushJiraEvent(bugAddVO, createBugDTO.getBugCode(), userName);
    return HttpResult.success(createBugDTO.getBugCode());
  }

  /**
   * 创建带附件的bug，并将其添加到JIRA系统中。
   * @param bugAddVO 包含bug信息的VO对象。
   * @param vehicleBasicDTO 车辆基本信息的DTO对象。
   * @param userName 提报人的用户名。
   * @return 创建的bug的DTO对象。
   */
  @Deprecated
  private CreateBugDTO createBugWithAttachment(BugWithAttachmentAddVO bugAddVO, VehicleBasicDTO vehicleBasicDTO, String userName) {
    CreateBugWithAttachmentVO createBugVO = new CreateBugWithAttachmentVO();
    List<Date> jiraDebugTimeList = bugAddVO.getDebugTime();
    StringBuilder summaryBuilder = new StringBuilder();
    summaryBuilder.append(bugAddVO.getVehicleName()).append("|")
            .append(VehicleOwnerUseCaseEnum.getNameByValue(vehicleBasicDTO.getOwnerUseCase())).append("|").append("【").append(vehicleBasicDTO.getStationName()).append("】").append(bugAddVO.getTopic());
    if (jiraDebugTimeList != null && !jiraDebugTimeList.isEmpty()) {
      SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMdd-HH-mm-ss");
      StringBuilder jiraDebugTime = new StringBuilder();
      for (Date debugTime : jiraDebugTimeList) {
        jiraDebugTime.append(dateTimeFormat.format(debugTime) + ";");
      }
      String debugTimeString = jiraDebugTime.toString();
      createBugVO.setTitle(debugTimeString.substring(0, debugTimeString.length() - 1) + "|" + summaryBuilder.toString());
    }
    createBugVO.setDescription(bugAddVO.getDescription());
    createBugVO.setSeverity(bugAddVO.getSeverity());
    createBugVO.setModule(bugAddVO.getModule());
    createBugVO.setVersion(bugAddVO.getVersion());
    createBugVO.setLevel(bugAddVO.getLevel());
    if (Objects.nonNull(bugAddVO.getAttachment())) {
      List<String> attachementList = new ArrayList<>();
      for (MultipartFile file : bugAddVO.getAttachment()) {
        String fileKey = S3Utils.uploadFile(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getEndpoint(),
                jiraRestServiceProperties.getAttachmentBucketName(), file);
        attachementList.add(fileKey);
      }
      createBugVO.setAttachment(attachementList);
    }
    createBugVO.setAccidentLevel(bugAddVO.getAccidentLevel());
    createBugVO.setAccidentType(bugAddVO.getAccidentType());

    //userName转化为erp
    UserExtendInfoDTO userExtendInfoDTO = metadataUserApiManager.getUserExtendInfoByName(userName);
    if (userExtendInfoDTO == null || userExtendInfoDTO.getJdErp() == null) {
      throw new RoverRuntimeException("提报人erp不存在");
    }
    String jdErp = userExtendInfoDTO.getJdErp();
    String proposer = jdErp;
    String assignee = jdErp;
    RSet<Object> set = RedissonUtils.getRedissonClient().getSet(BUG_USER_SET.getValue());
    if (set.contains(jdErp)) {
      proposer = RedissonUtils.getObject(BUG_USER_MAPPING.getValue() + jdErp);
      if (ModuleEnum.DISTRIBUTED.getValue().equals(bugAddVO.getModule())) {
        assignee = proposer;
      }
    }
    if (!ModuleEnum.DISTRIBUTED.getValue().equals(bugAddVO.getModule())) {
      assignee = ModuleEnum.getOwnerByValue(bugAddVO.getModule());
    }
    createBugVO.setProposer(proposer);
    createBugVO.setAssignee(assignee);
    CreateBugDTO createBugDTO = infrastructureBugApiManager.createBug(createBugVO);
    return createBugDTO;
  }
}
