/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;

/**
 * 地图服务配置
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "rover-map")
@Data
@Validated
public class RoverMapServiceProperties {

  /**
   * <p>
   * 地图bucket
   * </p>
   */
  @NotBlank
  private String roverMapBucketName = "rover-map";

  /**
   * <p>
   * 监控服务bucket
   * </p>
   */
  @NotBlank
  private String attachmentBucketName = "rover-jira";
}
