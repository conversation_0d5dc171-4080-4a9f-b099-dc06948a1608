/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.issue.IssueDetailDTO;
import com.jdx.rover.monitor.dto.issue.IssueRecordDTO;
import com.jdx.rover.monitor.entity.IssueCacheEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.issue.IssueStateEnum;
import com.jdx.rover.monitor.manager.issue.IssueManager;
import com.jdx.rover.monitor.manager.issue.IssueOperateHistoryManager;
import com.jdx.rover.monitor.repository.redis.IssueCacheRepository;
import com.jdx.rover.monitor.repository.redis.IssueNoCacheRepository;
import com.jdx.rover.monitor.search.IssueRecordSearch;
import com.jdx.rover.monitor.vo.MiniMonitorAlarmAttentionAddVO;
import com.jdx.rover.monitor.vo.MiniMonitorAlarmSubmissionAddVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * This is a mini monitor issue function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
@Slf4j
public class MiniMonitorIssueService {

  @Autowired
  private IssueCacheRepository issueCacheRepository;

  @Autowired
  private IssueNoCacheRepository issueNoCacheRepository;

  @Autowired
  private IssueManager issueManager;

  @Autowired
  private IssueOperateHistoryManager issueOperateHistoryManager;

  /**
   * <p>
   * Post a mini monitor alarm.
   * </p>
   *
   * @param miniMonitorAlarmSubmissionAddVo the view object of mini monitor alarm
   * @return MiniMonitorReportAlarmResponseDto
   */
  public HttpResult reportAlarm(MiniMonitorAlarmSubmissionAddVO miniMonitorAlarmSubmissionAddVo) {
    String userName = LoginUtils.getUsername();
    if (StringUtils.isBlank(userName)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    Boolean hasIssue =
            issueCacheRepository.hasKey(miniMonitorAlarmSubmissionAddVo.getVehicleName());
    if (hasIssue) {
      return  HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_ISSUE_AVAILABLE.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_ISSUE_AVAILABLE.getMessage());
    }
    String issueNo = issueNoCacheRepository.getIssueNo();
    IssueCacheEntity issueCacheEntity = new IssueCacheEntity();
    issueCacheEntity.setIssueNo(issueNo);
    issueCacheEntity.setIssueState(IssueStateEnum.WAITING.getIssueState());
    issueCacheRepository.save(miniMonitorAlarmSubmissionAddVo.getVehicleName(), issueCacheEntity);
    issueManager.addSubmissionAlarmIssue(miniMonitorAlarmSubmissionAddVo, userName, issueNo);
    return HttpResult.success(issueCacheEntity.getIssueNo());
  }

  /**
   * <p>
   * Post a mini monitor alarm attention.
   * </p>
   *
   * @param miniMonitorAlarmAttentionAddVo the view object of mini monitor alarm attention
   * @return MiniMonitorReportAlarmResponseDto
   */
  public HttpResult reportAlarmAttention(MiniMonitorAlarmAttentionAddVO miniMonitorAlarmAttentionAddVo) {
    String userName = LoginUtils.getUsername();
    if (StringUtils.isBlank(userName)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    Boolean hasIssue = issueCacheRepository.hasKey(miniMonitorAlarmAttentionAddVo.getVehicleName());
    if (hasIssue) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_ISSUE_AVAILABLE.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_ISSUE_AVAILABLE.getMessage());
    }
    String issueNo = issueNoCacheRepository.getIssueNo();
    IssueCacheEntity issueCacheEntity = new IssueCacheEntity();
    issueCacheEntity.setIssueNo(issueNo);
    issueCacheEntity.setIssueState(IssueStateEnum.WAITING.getIssueState());
    issueCacheRepository.save(miniMonitorAlarmAttentionAddVo.getVehicleName(), issueCacheEntity);
    issueManager.addAttentionAlarmIssue(miniMonitorAlarmAttentionAddVo, userName, issueNo);
    return HttpResult.success(issueCacheEntity.getIssueNo());
  }

  /**
   * <p>
   * Get issue by issue no.
   * </p>
   *
   * @param issueRecordSearch the view object of mini monitor
   * @return IssueRecordDto
   */
  public HttpResult getByIssueNo(final IssueRecordSearch issueRecordSearch) {
    IssueDetailDTO record = issueManager.getIssueDetailFromDb(issueRecordSearch.getIssueNo());
    if (StringUtils.isBlank(record.getIssueNo())) {
      return HttpResult.error(MonitorErrorEnum.ERROR_ISSUE_ABSENT.getCode(), MonitorErrorEnum.ERROR_ISSUE_ABSENT.getMessage());
    }
    if (!StringUtils.equals(record.getIssueState(), IssueStateEnum.WAITING.getIssueState())) {
      record.setHistoryList(issueOperateHistoryManager.listOperateHistoryByIssueNo(record.getIssueNo()));
    }
    return HttpResult.success(record);
  }

  /**
   * <p>
   * Page search Issue record.
   * </p>
   */
  public HttpResult search(PageVO pageableVo, IssueRecordSearch issueRecordSearch) {
    String userName = LoginUtils.getUsername();
    if (StringUtils.isBlank(userName)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    issueRecordSearch.setReportUser(userName);
    PageDTO<IssueRecordDTO> pageResult = issueManager.pageSearch(pageableVo, issueRecordSearch);
    return HttpResult.success(pageResult);
  }

}
