/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.monitor.dto.MonitorScheduleStopDetailDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopOrderEntity;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleStopOrderRepository;
import com.jdx.rover.monitor.vo.MonitorScheduleStopRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * This is a vehicle schedule stop service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class MonitorScheduleStopService {

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private VehicleScheduleStopOrderRepository vehicleScheduleStopOrderRepository;

  /**
   * <p>
   * 获取调度停靠点实时信息。
   * </p>
   *
   */
  public MonitorScheduleStopDetailDTO getScheduleStopState(MonitorScheduleStopRequestVO scheduleStopRequestVO) {
    ParameterCheckUtility.checkNotNull(scheduleStopRequestVO, "scheduleStopRequestVO");
    ParameterCheckUtility.checkNotNull(scheduleStopRequestVO.getVehicleName(), "scheduleStopRequestVO#vehicleName");
    ParameterCheckUtility.checkNotNull(scheduleStopRequestVO.getStopId(), "scheduleStopRequestVO#stopId");
    ParameterCheckUtility.checkNotNull(scheduleStopRequestVO.getStopAction(), "scheduleStopRequestVO#stopAction");
    MonitorScheduleStopDetailDTO scheduleStopDto = new MonitorScheduleStopDetailDTO();
    MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.get(scheduleStopRequestVO.getVehicleName());
    if (scheduleEntity == null || scheduleEntity.getScheduleNo() == null) {
      return scheduleStopDto;
    }
    Optional<MonitorScheduleStopEntity> op = scheduleEntity.getStop().stream().filter(stop ->
            stop.getId().equals(scheduleStopRequestVO.getStopId()) &&
                    StringUtils.equals(scheduleStopRequestVO.getStopAction(), stop.getStopAction())).findFirst();
    if (op.isPresent()) {
      MonitorScheduleStopEntity stopEntity = op.get();
      scheduleStopDto.setId(stopEntity.getId());
      MonitorScheduleStopOrderEntity stopOrderEntity =
              vehicleScheduleStopOrderRepository.get(scheduleStopRequestVO.getVehicleName(),
                      scheduleStopRequestVO.getStopId(), scheduleStopRequestVO.getStopAction());
      scheduleStopDto.setCanceledCollectOrderNum(stopOrderEntity.getCanceledCollectOrderNum());
      scheduleStopDto.setFinishedCollectOrderNum(stopOrderEntity.getFinishedCollectOrderNum());
      scheduleStopDto.setTotalCollectOrderNum(stopOrderEntity.getTotalCollectOrderNum());
      scheduleStopDto.setCanceledDeliveryOrderNum(stopOrderEntity.getCanceledDeliveryOrderNum());
      scheduleStopDto.setFinishedDeliveryOrderNum(stopOrderEntity.getFinishedDeliveryOrderNum());
      scheduleStopDto.setTotalDeliveryOrderNum(stopOrderEntity.getTotalDeliveryOrderNum());
      scheduleStopDto.setArrivedTime(stopEntity.getArrivedTime());
      scheduleStopDto.setWaitingTime(stopEntity.getWaitingTime());
      scheduleStopDto.setDepartTime(stopEntity.getDepartTime());
    }
    return scheduleStopDto;
  }

}

