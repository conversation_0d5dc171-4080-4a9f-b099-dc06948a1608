package com.jdx.rover.monitor.service.vehicle;

import com.jdx.rover.monitor.dto.vehicle.*;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.abnormal.GuardianVehicleAbnormalManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.manager.vehicle.SystemStateManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSpeedLimitRepository;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import com.jdx.rover.schedule.api.domain.enums.StopTravelStatus;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeDrivableInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RScoredSortedSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CancellationException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 单车页服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SingleVehicleService {
  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private SingleVehicleManager singleVehicleManager;

  @Autowired
  private GuardianVehicleAbnormalManager guardianVehicleAbnormalManager;

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private DuccConfigProperties duccConfigProperties;

    @Autowired
    private VehicleSpeedLimitRepository vehicleSpeedLimitRepository;

  @Value("${project.push.interval.singleVehicle:5000}")
  private Integer pushInterval;

  @Value("${project.push.batch.singleVehicle:5}")
  private Integer pushBatch;

  /**
   * 获取待推送的车辆,并更新对象分数排序set
   *
   * @return
   */
  public Collection<String> getAndUpdatePushVehicleCollection() {
    Collection<String> vehicleNameCollection = null;
    Map<String, Double> nextPushMap = new HashMap<>(pushBatch);
    long currentTime = System.currentTimeMillis();
    double nextPushTime = currentTime + pushInterval;
    boolean lockFlag = false;
    // 任务必须加锁，因为同一任务可能被多个实例所执行
    RLock lock = RedissonUtils.getRedissonClient().getLock(RedisKeyEnum.SORTED_SET_PUSH_SINGLE_VEHICLE.getValue() + ":lock");
    try {
      //尝试获取锁,超过2秒退出,等下次循环
      lockFlag = lock.tryLock(2L, TimeUnit.SECONDS);
      if (!lockFlag) {
        return null;
      }
      //获取到锁,正常执行关单操作
      RScoredSortedSet<String> scoredSortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(RedisKeyEnum.SORTED_SET_PUSH_SINGLE_VEHICLE.getValue());
      vehicleNameCollection = scoredSortedSet.valueRange(0, true, currentTime, false, 0, pushBatch);
      if (CollectionUtils.isEmpty(vehicleNameCollection)) {
        return null;
      }
      for (String vehicleName : vehicleNameCollection) {
        nextPushMap.put(vehicleName, nextPushTime);
        scoredSortedSet.addAll(nextPushMap);
      }
    } catch (CancellationException e) {
      // 未获取到锁,直接忽略,退出
      log.info("未成功获取锁,直接退出CancellationException");
    } catch (Exception e) {
      log.error("推送单车数据异常", e);
    } finally {
      if (lockFlag) {
        lock.unlock();
      }
    }
    return vehicleNameCollection;
  }

  /**
   * 通过车辆名称获取对象
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public WsResult<SingleVehicleDTO> listSingleVehicle(String vehicleName) {
    VehicleRealtimeInfoDTO realtime = vehicleRealtimeRepository.get(vehicleName);
    SingleVehicleDTO vehicle = buildSingleVehicleDTO(vehicleName, realtime, Boolean.FALSE);
    WsResult<SingleVehicleDTO> wsResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE.getValue(), vehicle);
    return wsResult;
  }

  /**
   * 当前调度完成里程
   *
   * @param monitorScheduleEntity
   * @param realtime
   * @return
   */
  private Double getArrivedMileage(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
    Double finishedMileage = Optional.ofNullable(monitorScheduleEntity.getFinishedMileage()).orElse(0.0);
    if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(),
            VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(),
            VehicleScheduleState.RETURN.getVehicleScheduleState())) {
      Double currentStopFinishedMileage;
      if (realtime == null || realtime.getCurrentStopFinishedMileage() == null) {
        currentStopFinishedMileage = 0.0;
      } else {
        currentStopFinishedMileage = realtime.getCurrentStopFinishedMileage();
      }
      finishedMileage += currentStopFinishedMileage;
    }
    return finishedMileage;
  }

  /**
   * 通过车辆名称获取单车页调度信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public WsResult<SingleVehicleScheduleDTO> listSingleVehicleSchedule(String vehicleName) {
    MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.get(vehicleName);
    if (scheduleEntity == null) {
      return WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_SCHEDULE.getValue());
    }

    SingleVehicleScheduleDTO scheduleDTO = new SingleVehicleScheduleDTO();
    scheduleDTO.setVehicleName(vehicleName);
    scheduleDTO.setScheduleNo(scheduleEntity.getScheduleNo());
    scheduleDTO.setGlobalMileage(scheduleEntity.getGlobalMileage());
    List<SingleVehicleScheduleStopDTO> stopList = scheduleEntity.getStop().stream().map(stop -> {
      SingleVehicleScheduleStopDTO stopDTO = new SingleVehicleScheduleStopDTO();
      stopDTO.setId(stop.getId());
      stopDTO.setGoalId(stop.getGoalId());
      stopDTO.setName(stop.getName());
      stopDTO.setType(stop.getType());
      stopDTO.setArrivedTime(stop.getArrivedTime());
      stopDTO.setStopAction(stop.getStopAction());
      stopDTO.setDepartTime(stop.getDepartTime());
      stopDTO.setEstDepartTime(stop.getEstDepartTime());
      stopDTO.setTravelStatus(stop.getTravelStatus());
      stopDTO.setWaitingTime(stop.getWaitingTime());
      if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.START.getTravelStatus())) {
        stopDTO.setStartTime(stop.getStartTime());
        stopDTO.setGlobalMileage(stop.getGlobalMileage());
      }
      return stopDTO;
    }).collect(Collectors.toList());
    scheduleDTO.setStopList(stopList);
    WsResult<SingleVehicleScheduleDTO> wsResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_SCHEDULE.getValue(), scheduleDTO);
    return wsResult;
  }

  /**
   * 构建使能信息
   *
   * @param realtime
   * @return
   */
  private VehicleDrivableDirectionDTO buildVehicleDrivableDirectionDTO(VehicleRealtimeInfoDTO realtime) {
    VehicleRealtimeDrivableInfoDTO drivableDirection = realtime.getDrivableDirection();
    if (drivableDirection == null) {
      return null;
    }
    VehicleDrivableDirectionDTO vehicleDrivableDirection = new VehicleDrivableDirectionDTO();
    vehicleDrivableDirection.setEnableFront(drivableDirection.getEnableFront());
    vehicleDrivableDirection.setEnableBack(drivableDirection.getEnableBack());
    vehicleDrivableDirection.setEnableLeft(drivableDirection.getEnableLeft());
    vehicleDrivableDirection.setEnableRight(drivableDirection.getEnableRight());
    return vehicleDrivableDirection;
  }

  /**
   * 推送调度消息
   *
   * @param vehicleName
   */
  public void pushSingleVehicleSchedule(String vehicleName) {
    WsResult<SingleVehicleScheduleDTO> wsResult = this.listSingleVehicleSchedule(vehicleName);
    singleVehicleManager.pushSingleVehiclePageData(vehicleName, wsResult);
  }

  /**
   * 通过车辆名称列表推送获取对象
   *
   * @param vehicleNameList 车辆名称列表
   * @return
   */
  public void pushSingleVehicleRealtime(List<String> vehicleNameList) {
    Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
    realtimeMap.forEach((vehicleName, realtime) -> {
      SingleVehicleDTO vehicle = buildSingleVehicleDTO(vehicleName, realtime, Boolean.TRUE);
      WsResult<SingleVehicleDTO> wsResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE.getValue(), vehicle);
      singleVehicleManager.pushSingleVehiclePageData(vehicleName, wsResult);
    });
  }

  /**
   * 通过车辆实时列表推送获取对象
   *
   * @param vehicleList 车辆实时列表
   * @return
   */
  public void pushSingleVehicle(List<VehicleRealtimeInfoDTO> vehicleList, boolean isContainAlarm) {
    vehicleList.stream().forEach(realtime -> {
      SingleVehicleDTO vehicle = buildSingleVehicleDTO(realtime.getVehicleName(), realtime, isContainAlarm);
      WsResult<SingleVehicleDTO> wsResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE.getValue(), vehicle);
      singleVehicleManager.pushSingleVehiclePageData(realtime.getVehicleName(), wsResult);
    });
  }

  /**
   * 构建单车推送对象
   *
   * @param vehicleName
   * @param realtime
   * @return
   */
  private SingleVehicleDTO buildSingleVehicleDTO(String vehicleName, VehicleRealtimeInfoDTO realtime, boolean isContainAlarm) {
    SingleVehicleDTO vehicle = new SingleVehicleDTO();
    MonitorScheduleEntity monitorScheduleEntity = vehicleScheduleRepository.get(vehicleName);
    if (monitorScheduleEntity != null) {
      vehicle.setScheduleState(monitorScheduleEntity.getScheduleState());
      vehicle.setTaskType(monitorScheduleEntity.getTaskType());
      if (realtime != null) {
        vehicle.setCurrentStopFinishedMileage(realtime.getCurrentStopFinishedMileage());
        vehicle.setCurrentScheduleFinishedMileage(getArrivedMileage(monitorScheduleEntity, realtime));
      }
    } else {
      vehicle.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
    }

    if (realtime != null) {
      String systemState = realtime.getSystemState();
      vehicle.setSystemState(systemState);
      if (SystemStateEnum.CONNECTION_LOST.getSystemState().equals(vehicle.getSystemState())) {
        vehicle.setRecordTime(realtime.getRecordTime());
      }
      if (!SystemStateManager.isOfflineOrLost(systemState)) {
        vehicle.setVehicleState(realtime.getVehicleState());
        vehicle.setSpeed(realtime.getSpeed());
        vehicle.setPower(realtime.getPower());
        if(isContainAlarm) {
          List<VehicleAlarmDO.VehicleAlarmEventDO> alarmEventDTOList = vehicleAlarmRepository.get(vehicleName);
          List<AlarmEventDTO> alarmEventList = alarmEventDTOList.stream().map(tmp -> {
            AlarmEventDTO alarmEventDTO = new AlarmEventDTO();
            alarmEventDTO.setAlarmEvent(tmp.getType());
            alarmEventDTO.setReportTime(tmp.getReportTime());
            return alarmEventDTO;
          }).collect(Collectors.toList());
          vehicle.setAlarmEventList(alarmEventList);
        }
        VehicleDrivableDirectionDTO vehicleDrivableDirection = buildVehicleDrivableDirectionDTO(realtime);
        vehicle.setVehicleDrivableDirection(vehicleDrivableDirection);
      }
    } else {
      vehicle.setSystemState(SystemStateEnum.OFFLINE.getSystemState());
    }

    vehicle.setIsSpeedLimitEnabled(vehicleSpeedLimitRepository.existsSpeedLimit(vehicleName));
    return vehicle;
  }
}
