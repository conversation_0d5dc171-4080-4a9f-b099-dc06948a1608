package com.jdx.rover.monitor.service.score;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 更新
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum UpdateScoreTypeEnum {
  VEHICLE_NAME(SortScoreUtils::getVehicleNameScore, SpringUtil.getBean(SortScoreTypeService.class)::updateVehicleNameScore, "车号"),
  BUSINESS_TYPE(SortScoreUtils::getBusinessTypeScore, SpringUtil.getBean(SortScoreTypeService.class)::updateBusinessTypeScore, "业务"),
  POWER(SortScoreUtils::getPowerScore, SpringUtil.getBean(SortScoreTypeService.class)::updatePowerScore, "电量"),
  STATION(SortScoreUtils::getStationScore, Injector.getSortService()::updateStationScore, "站点"),
  ;

  private final Function<VehicleScoreBO, Double> function;

  private final Consumer<VehicleScoreBO> consumer;

  private String title;

  @Component
  static class Injector {
    private static SortScoreTypeService sortScoreTypeService;

    @Autowired
    public void setSortService(SortScoreTypeService sortScoreTypeService) {
      Injector.sortScoreTypeService = sortScoreTypeService;
    }

    static SortScoreTypeService getSortService() {
      return sortScoreTypeService;
    }
  }
}
