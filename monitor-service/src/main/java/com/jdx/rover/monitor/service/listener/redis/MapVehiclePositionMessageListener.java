package com.jdx.rover.monitor.service.listener.redis;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.MonitorVehiclePositionDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;

/**
 * 地图页车辆位置信息更新推送
 *
 * <AUTHOR>
 */
@Slf4j
public class MapVehiclePositionMessageListener implements MessageListener<String> {
  private Session session;

  public MapVehiclePositionMessageListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    log.info("Send vehicle position info {}", msg);
    WsResult<MonitorVehiclePositionDTO> dto = JsonUtils.readValue(msg, WsResult.class);
    if (dto == null) {
      return;
    }
    synchronized (session) {
      try {
        this.session.getBasicRemote().sendText(JsonUtils.writeValueAsString(dto));
      } catch (Exception e) {
        log.error("Send vehicle position exception", e);
      }
    }

  }
}
