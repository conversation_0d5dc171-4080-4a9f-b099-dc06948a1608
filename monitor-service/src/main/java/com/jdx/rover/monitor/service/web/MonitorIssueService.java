/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.google.common.collect.Lists;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.issue.IssueAlarmDTO;
import com.jdx.rover.monitor.dto.issue.IssueChangeDTO;
import com.jdx.rover.monitor.dto.issue.IssueDetailDTO;
import com.jdx.rover.monitor.dto.issue.IssuePendingDTO;
import com.jdx.rover.monitor.dto.issue.IssueRecordDTO;
import com.jdx.rover.monitor.entity.IssueCacheEntity;
import com.jdx.rover.monitor.enums.AlarmEventErrorCodeEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.issue.IssueOperateResultEnum;
import com.jdx.rover.monitor.enums.issue.IssueStateEnum;
import com.jdx.rover.monitor.manager.issue.IssueAlarmManager;
import com.jdx.rover.monitor.manager.issue.IssueManager;
import com.jdx.rover.monitor.manager.issue.IssueOperateHistoryManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.po.IssueAlarmRecord;
import com.jdx.rover.monitor.repository.redis.IssueCacheRepository;
import com.jdx.rover.monitor.repository.redis.IssueNoCacheRepository;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.search.IssueRecordSearch;
import com.jdx.rover.monitor.vo.IssueRecordListRequestVO;
import com.jdx.rover.monitor.vo.IssueVO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a issue service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class MonitorIssueService {

  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;

  @Autowired
  private IssueManager issueManager;

  @Autowired
  private IssueOperateHistoryManager issueOperateHistoryManager;

  @Autowired
  private IssueAlarmManager issueAlarmManager;

  @Autowired
  private IssueNoCacheRepository issueNoCacheRepository;

  @Autowired
  private IssueCacheRepository issueCacheRepository;

  @Autowired
  private SingleVehicleManager singleVehicleManager;

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;

  /**
   * <p>
   * 获取工单详情
   * </p>
   */
  public HttpResult<IssueDetailDTO> getIssueDetail(String vehicleName, String issueNo) {
    ParameterCheckUtility.checkNotNullNorEmpty(vehicleName, "vehicleName");
    if (StringUtils.isEmpty(issueNo)) {
      IssueCacheEntity issueCacheEntity = issueCacheRepository.getByKey(vehicleName);
      if (issueCacheEntity == null) {
        return HttpResult.error(MonitorErrorEnum.ERROR_ISSUE_ABSENT.getCode(), MonitorErrorEnum.ERROR_ISSUE_ABSENT.getMessage());
      }
      issueNo = issueCacheEntity.getIssueNo();
    }
    IssueDetailDTO issueDetailDto = issueManager.getIssueDetailFromDb(issueNo);
    issueDetailDto.setAlarmList(issueAlarmManager.getAlarmListByIssueNo(issueNo, StringUtils.equals(issueDetailDto.getIssueState(),
            IssueStateEnum.FINISH.getIssueState())).stream().map(alarm -> alarm.toIssAlarmDto()).collect(Collectors.toList()));
    issueDetailDto.setHistoryList(issueOperateHistoryManager.listOperateHistoryByIssueNo(issueNo));
    return HttpResult.success(issueDetailDto);
  }

  /**
   * <p>
   * 获取当前可操作工单或者车辆告警
   * </p>
   */
  public HttpResult<IssueDetailDTO> getAvailibleIssue(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmpty(vehicleName, "vehicleName");
    IssueDetailDTO monitroIssueDetailDto = new IssueDetailDTO();
    IssueCacheEntity issueCacheEntity = issueCacheRepository.getByKey(vehicleName);
    if (issueCacheEntity != null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_ISSUE_AVAILABLE.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_ISSUE_AVAILABLE.getMessage());
    }
      String systemState = vehicleRealtimeRepository.getSystemState(vehicleName);
      if (StringUtils.equalsAny(systemState, SystemStateEnum.CONNECTION_LOST.getSystemState(),  SystemStateEnum.OFFLINE.getSystemState())) {
        return HttpResult.success(monitroIssueDetailDto);
      }
      monitroIssueDetailDto.setAlarmList(vehicleAlarmRepository.get(vehicleName).stream().map(alarm -> {
        IssueAlarmDTO issueAlarmDto = new IssueAlarmDTO();
        issueAlarmDto.setType(alarm.getType());
        issueAlarmDto.setStartTimestamp(alarm.getReportTime());
        AlarmEventErrorCodeEnum alarmEnum = AlarmEventErrorCodeEnum.of(alarm.getType());
        if (alarmEnum != null) {
          issueAlarmDto.setDescription(alarmEnum.getErrorCode());
        } else {
          issueAlarmDto.setDescription(alarm.getType());
        }
        issueAlarmDto.setTitle(alarm.getErrorCode());
        return issueAlarmDto;
      }).collect(Collectors.toList()));
      return HttpResult.success(monitroIssueDetailDto);
    }

  /**
   * <p>
   * 分页获取工单
   * </p>
   */
  public PageDTO<IssueRecordDTO> search(IssueRecordSearch issueRecordSearch) {
    if (issueRecordSearch.getPageNum() == null || issueRecordSearch.getPageSize() == null) {
      issueRecordSearch.setPageNum(1);
      issueRecordSearch.setPageSize(20);
    }
    return issueManager.pageSearch(issueRecordSearch, issueRecordSearch);
  }

  /**
   * <p>
   * 查询工单列表
   * </p>
   */
  public HttpResult listIssueRecord(IssueRecordListRequestVO issueRecordListRequestVo) {
    List<IssueDetailDTO> recordList = issueManager.listIssue(issueRecordListRequestVo);
    if (CollectionUtils.isEmpty(recordList)) {
      return HttpResult.success(new ArrayList<>());
    }
    if (issueRecordListRequestVo.getIsNeedAlarm() != null && issueRecordListRequestVo.getIsNeedAlarm()) {
      for (IssueDetailDTO detailDto : recordList) {
        detailDto.setAlarmList(listAlarmByIssueNo(detailDto.getIssueNo(), true));
      }
    }
    return HttpResult.success(recordList);
  }

  /**
   * <p>
   * 查询小铃铛里工单列表
   * </p>
   */
  public IssuePendingDTO listPendingIssue(IssueRecordListRequestVO issueRecordListRequestVo, String userName) {
    IssuePendingDTO issuePendingDto = new IssuePendingDTO();
    Set<String> vehicleSetList = userVehicleNameRepository.get(userName);
    if (CollectionUtils.isEmpty(vehicleSetList)) {
      return issuePendingDto;
    }
    issueRecordListRequestVo.setIsReported(false);
    issueRecordListRequestVo.setStateList(Lists.newArrayList(IssueStateEnum.FINISH.getIssueState()));
    List<IssueDetailDTO> finishedRecordList = issueManager.listIssue(issueRecordListRequestVo);
    if (!CollectionUtils.isEmpty(finishedRecordList)) {
      finishedRecordList.stream().filter(record -> vehicleSetList.contains(record.getVehicleName())).forEach(record -> {
        IssueChangeDTO issueDto = new IssueChangeDTO();
        issueDto.setVehicleName(record.getVehicleName());
        issueDto.setIssueNo(record.getIssueNo());
        issueDto.setIssueState(record.getIssueState());
        issueDto.setStartTime(record.getFinishTime());
        issueDto.setOperateResultName(record.getOperateResultName());
        issuePendingDto.getFinished().add(issueDto);
      });
    }
    issueRecordListRequestVo = new IssueRecordListRequestVO();
    issueRecordListRequestVo.setStateList(Lists.newArrayList(IssueStateEnum.WAITING.getIssueState(),IssueStateEnum.PROCESS.getIssueState()));
    List<IssueDetailDTO> processRecordList = issueManager.listIssue(issueRecordListRequestVo);
    if (!CollectionUtils.isEmpty(processRecordList)) {
      processRecordList.stream().filter(record -> vehicleSetList.contains(record.getVehicleName())).forEach(record -> {
        IssueChangeDTO issueDto = new IssueChangeDTO();
        issueDto.setVehicleName(record.getVehicleName());
        issueDto.setAlarmType(record.getAlarmType());
        issueDto.setIssueNo(record.getIssueNo());
        issueDto.setIssueState(record.getIssueState());
        IssueAlarmRecord alarmRecord = issueAlarmManager.getAlarmByIssueNoAndAlarmType(record.getIssueNo(), record.getAlarmType(), true);
        issueDto.setStartTime(Optional.ofNullable(alarmRecord).map(alarm -> alarm.getTimestamp()).orElse(record.getReportTime()));
        issuePendingDto.getProcess().add(issueDto);
      });
    }
    return issuePendingDto;
  }

  /**
   * <p>
   * 技术支持创建工单
   * </p>
   */
  public HttpResult operateAddIssue(IssueVO monitorIssueVo, String userName) {
    String issueNo = issueNoCacheRepository.getIssueNo();
    IssueCacheEntity issueCacheEntity = new IssueCacheEntity();
    issueCacheEntity.setIssueNo(issueNo);
    issueCacheEntity.setListenerId(issueManager.subscribeIssueVehicleAlarm(monitorIssueVo.getVehicleName(), issueNo));
    issueCacheEntity.setIssueState(IssueStateEnum.PROCESS.getIssueState());
    issueCacheRepository.save(monitorIssueVo.getVehicleName(), issueCacheEntity);
    issueManager.addOperateAlarmIssue(monitorIssueVo, userName, issueNo);
    singleVehicleManager.pushSingleVehicleTakeOverAndIssue(monitorIssueVo.getVehicleName());
    return HttpResult.success(issueNo);
  }

  /**
   * <p>
   * 处理工单
   * </p>
   *
   */
  public HttpResult update(IssueVO monitorIssueVo) {
    log.info("Received monitor issue update request: {}", monitorIssueVo);
    String userName = UserUtils.getLoginUser();
    IssueCacheEntity issueCacheEntity = issueCacheRepository.getByKey(monitorIssueVo.getVehicleName());
    if (issueCacheEntity == null && monitorIssueVo.getIssueNo() != null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_ISSUE_ABSENT.getCode(), MonitorErrorEnum.ERROR_ISSUE_ABSENT.getMessage());
    }
    if (issueCacheEntity == null && monitorIssueVo.getIssueNo() == null) {
      if (CollectionUtils.isEmpty(monitorIssueVo.getAlarmList()) || CollectionUtils.isEmpty(monitorIssueVo.getIssueHistoryList())) {
        return HttpResult.error(MonitorErrorEnum.ERROR_ISSUE_CRRATE.getCode(), MonitorErrorEnum.ERROR_ISSUE_CRRATE.getMessage());
      }
      return operateAddIssue(monitorIssueVo, userName);
    }
    if (StringUtils.equals(issueCacheEntity.getIssueState(), IssueStateEnum.WAITING.getIssueState())) {
      if (StringUtils.equalsAny(monitorIssueVo.getOperateResult(), IssueOperateResultEnum.OPERATION_RESUME.getValue(), IssueOperateResultEnum.OPERATION_ABORT.getValue())) {
        return HttpResult.error(MonitorErrorEnum.ERROR_ISSUE_STATE.getCode(), MonitorErrorEnum.ERROR_ISSUE_STATE.getMessage());
      }
      issueCacheEntity.setIssueState(IssueStateEnum.PROCESS.getIssueState());
      issueCacheEntity.setListenerId(issueManager.subscribeIssueVehicleAlarm(monitorIssueVo.getVehicleName(), issueCacheEntity.getIssueNo()));
      issueCacheRepository.save(monitorIssueVo.getVehicleName(), issueCacheEntity);
    }
    if (StringUtils.isNotBlank(monitorIssueVo.getOperateResult())) {
      IssueOperateResultEnum operateResultEnum =
              IssueOperateResultEnum.of(monitorIssueVo.getOperateResult());
      if (IssueOperateResultEnum.OPERATION_RESUME == operateResultEnum
              || IssueOperateResultEnum.OPERATION_ABORT == operateResultEnum) {
        issueManager.unSubscribeIssueVehicleAlarm(monitorIssueVo.getVehicleName());
        issueCacheRepository.remove(monitorIssueVo.getVehicleName());
      }
    }
    monitorIssueVo.setIssueNo(issueCacheEntity.getIssueNo());
    issueManager.update(monitorIssueVo, userName);
    singleVehicleManager.pushSingleVehicleTakeOverAndIssue(monitorIssueVo.getVehicleName());
    return HttpResult.success(issueCacheEntity.getIssueNo());
  }

  /**
   * <p>
   * 获取工单相应的告警.
   * </p>
   */
  public List<IssueAlarmDTO> listAlarmByIssueNo(String issueNo, boolean isSelected) {
    ParameterCheckUtility.checkNotNullNorEmpty(issueNo, "issueNo");
    List<IssueAlarmRecord> alarmRecordList = issueAlarmManager.getAlarmListByIssueNo(issueNo, isSelected);
    return alarmRecordList.stream().map(IssueAlarmRecord :: toIssAlarmDto).collect(Collectors.toList());
  }

}
