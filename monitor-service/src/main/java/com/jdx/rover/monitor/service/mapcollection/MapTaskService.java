/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.mapcollection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.datacenter.domain.dto.map.PointQueryDTO;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.mapcollection.MonitorTaskSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.PointInfoDTO;
import com.jdx.rover.monitor.dto.mapcollection.StationStopRangeSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskCreateDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO.FourLaneDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO.PositionDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskSearchDTO.TaskDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mapcollection.TabTypeEnum;
import com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskManager;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTask;
import com.jdx.rover.monitor.repository.redis.mapcollection.MapCollectionTaskRedis;
import com.jdx.rover.monitor.service.config.ducc.DuccMobileProperties;
import com.jdx.rover.monitor.vo.mapcollection.MonitorTaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskCreateVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskDeleteVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskRouteSaveVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskUpdateVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/12 16:48
 * @description 采集任务服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MapTaskService {

    /**
     * MapCollectionTaskManager
     */
    private final MapCollectionTaskManager mapCollectionTaskManager;

    /**
     * MapCollectionRedis
     */
    private final MapCollectionTaskRedis mapCollectionTaskRedis;

    /**
     * DuccMobileProperties
     */
    private final DuccMobileProperties duccMobileProperties;

    /**
     * MetadataStationApiManager
     */
    private final MetadataStationApiManager metadataStationApiManager;

    /**
     * PointQueryService
     */
    private final PointQueryService pointQueryService;

    /**
     * 获取勘查任务
     * 1. 默认全部查询时，stationId和username均不需要填入
     * 2. 筛选条件选择全部用户，仅需填入stationId
     * 3. 筛选条件选择创建人，填入username，不需要填入stationId
     *
     * @param taskSearchVO taskSearchVO
     * @return TaskSearchDTO
     */
    public TaskSearchDTO getTaskList(TaskSearchVO taskSearchVO) {
        TaskSearchDTO taskSearchDTO = new TaskSearchDTO();

        // 数据查询
        List<MapCollectionTask> mapCollectionTasks = mapCollectionTaskManager.queryTaskList(taskSearchVO.getStationId(), taskSearchVO.getUsername(), taskSearchVO.getTabType());
        if (CollUtil.isEmpty(mapCollectionTasks)) {
            taskSearchDTO.setTaskList(Collections.emptyList());
            return taskSearchDTO;
        }

        // 组装数据
        List<TaskDTO> taskList = Lists.newArrayListWithCapacity(mapCollectionTasks.size());
        taskSearchDTO.setTaskList(taskList);
        mapCollectionTasks.forEach(mapCollectionTask -> {
            TaskDTO taskDTO = new TaskDTO();
            taskDTO.setTaskId(mapCollectionTask.getId());
            taskDTO.setTaskName(mapCollectionTask.getTaskName());
            taskDTO.setTaskCreator(mapCollectionTask.getTaskCreator());
            taskDTO.setCreateTime(mapCollectionTask.getCreateTime());
            taskDTO.setTaskStatus(mapCollectionTask.getTaskStatus());
            taskDTO.setCityId(mapCollectionTask.getCityId());
            taskDTO.setStationId(mapCollectionTask.getStationId());
            taskDTO.setVehicleName(mapCollectionTask.getVehicleName());
            taskList.add(taskDTO);
        });

        // 时间逆序排序
        taskList.sort(Comparator.comparing(TaskDTO::getCreateTime).reversed());
        return taskSearchDTO;
    }

    /**
     * 创建勘查任务
     *
     * @param taskCreateVO taskCreateVO
     * @return TaskCreateDTO
     */
    public TaskCreateDTO createTask(TaskCreateVO taskCreateVO) {
        checkUsernameExist();

        // 返回插入任务ID
        int taskId = mapCollectionTaskManager.createTask(taskCreateVO.getTaskName(), taskCreateVO.getCityId(), taskCreateVO.getStationId(), JsfLoginUtil.getUsername());
        TaskCreateDTO taskCreateDTO = new TaskCreateDTO();
        taskCreateDTO.setTaskId(taskId);
        return taskCreateDTO;
    }

    /**
     * 编辑勘查任务
     *
     * @param taskUpdateVO taskUpdateVO
     */
    public void updateTask(TaskUpdateVO taskUpdateVO) {
        String taskStatus = queryTaskStatus(taskUpdateVO.getTaskId());

        // 非勘查中任务无法编辑
        if (!TaskStatusEnum.UNDER_EXPLORATION.getCode().equals(taskStatus)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getMessage());
        }
        mapCollectionTaskManager.updateTask(taskUpdateVO.getTaskId(), taskUpdateVO.getTaskName(), taskUpdateVO.getCityId(), taskUpdateVO.getStationId());
    }

    /**
     * 删除勘查任务
     *
     * @param taskDeleteVO taskDeleteVO
     */
    public void deleteTask(TaskDeleteVO taskDeleteVO) {
        checkUsernameExist();

        // 非勘查中、待认领状态无法删除
        String taskStatus = queryTaskStatus(taskDeleteVO.getTaskId());
        if (!TaskStatusEnum.UNDER_EXPLORATION.getCode().equals(taskStatus) && !TaskStatusEnum.PENDING_CLAIM.getCode().equals(taskStatus)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_CANNOT_DELETE.getCode(), MonitorErrorEnum.ERROR_TASK_CANNOT_DELETE.getMessage());
        }

        // 任务状态发生变更，无法删除（根据入参Tab类型做任务状态校验）
        if (!TabTypeEnum.of(taskDeleteVO.getTabType()).getTaskStatusEnums().contains(TaskStatusEnum.of(taskStatus))) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_STATUS_CHANGE.getCode(), MonitorErrorEnum.ERROR_TASK_STATUS_CHANGE.getMessage());
        }

        // 如果待勘查任务此时有用户正在勘查中，则任务无法删除
        if (TaskStatusEnum.UNDER_EXPLORATION.getCode().equals(taskStatus)) {
            checkSameProcessor(taskDeleteVO.getTaskId());
        }

        // 更新任务状态为任务关闭
        mapCollectionTaskManager.updateTaskStatus(taskDeleteVO.getTaskId(), TaskStatusEnum.TASK_DELETED);
    }

    /**
     * 开始勘查
     *
     * @param taskBaseVO taskBaseVO
     */
    public void processStartTask(TaskBaseVO taskBaseVO) {
        checkUsernameExist();

        String taskStatus = queryTaskStatus(taskBaseVO.getTaskId());
        // 非勘查中任务无法编辑
        if (!TaskStatusEnum.UNDER_EXPLORATION.getCode().equals(taskStatus)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getMessage());
        }

        // 对任务加锁，防止并发出现数据不一致
        boolean locked = mapCollectionTaskRedis.lockTask(taskBaseVO.getTaskId());
        if (!locked) {
            log.warn("开始勘查任务加锁失败，taskId:{}", taskBaseVO.getTaskId());
            throw new AppException(MonitorErrorEnum.ERROR_REPEAT_OPERATION.getCode(), MonitorErrorEnum.ERROR_REPEAT_OPERATION.getMessage());
        }

        try {
            String username = JsfLoginUtil.getUsername();
            String processor = mapCollectionTaskRedis.processTask(taskBaseVO.getTaskId(), username);

            // 如果该任务存在绑定用户且绑定用户与请求人不一致，返回错误
            if (StrUtil.isNotBlank(processor) && !username.equals(processor)) {
                throw new AppException(MonitorErrorEnum.ERROR_TASK_CANNOT_PROCESS.getCode(), String.format(MonitorErrorEnum.ERROR_TASK_CANNOT_PROCESS.getMessage(), processor));
            }
        } finally {
            mapCollectionTaskRedis.unlockTask(taskBaseVO.getTaskId());
        }
    }

    /**
     * 结束勘查
     *
     * @param taskBaseVO taskBaseVO
     */
    public void processEndTask(TaskBaseVO taskBaseVO) {
        // 对任务加锁，防止并发出现数据不一致
        boolean locked = mapCollectionTaskRedis.lockTask(taskBaseVO.getTaskId());
        if (!locked) {
            log.warn("结束勘查任务加锁失败，taskId:{}", taskBaseVO.getTaskId());
            throw new AppException(MonitorErrorEnum.ERROR_REPEAT_OPERATION.getCode(), MonitorErrorEnum.ERROR_REPEAT_OPERATION.getMessage());
        }

        // 解绑任务
        try {
            // 检查任务处理人和接口调用人是否一致
            checkSameProcessor(taskBaseVO.getTaskId());

            mapCollectionTaskRedis.endTask(taskBaseVO.getTaskId());
        } finally {
            mapCollectionTaskRedis.unlockTask(taskBaseVO.getTaskId());
        }
    }

    /**
     * 提交勘查任务
     *
     * @param taskBaseVO taskBaseVO
     */
    public void submitTask(TaskBaseVO taskBaseVO) {
        String taskStatus = queryTaskStatus(taskBaseVO.getTaskId());
        // 非勘查中任务无法编辑
        if (!TaskStatusEnum.UNDER_EXPLORATION.getCode().equals(taskStatus)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getMessage());
        }

        // 检查任务处理人和接口调用人是否一致
        checkSameProcessor(taskBaseVO.getTaskId());

        // 更新任务状态为待认领
        mapCollectionTaskManager.submitTask(taskBaseVO.getTaskId(), JsfLoginUtil.getUsername());
    }

    /**
     * 获取附近站点和停靠点
     *
     * @param positionVO positionVO
     * @return StationStopRangeSearchDTO
     */
    public StationStopRangeSearchDTO getInitialPoints(PositionVO positionVO) {
        StationStopRangeSearchDTO stationStopRangeSearchDTO = new StationStopRangeSearchDTO();
        PointQueryDTO pointQueryDTO = pointQueryService.queryStationAndStopByRadiusAndUser(positionVO.getLatitude(), positionVO.getLongitude(),
            duccMobileProperties.getInitialPointRadius(), UserUtils.getAndCheckLoginUser());
        if (Objects.isNull(pointQueryDTO) || CollUtil.isEmpty(pointQueryDTO.getPointList())) {
            stationStopRangeSearchDTO.setPointList(Collections.emptyList());
            return stationStopRangeSearchDTO;
        }

        List<PointInfoDTO> pointList = Lists.newArrayListWithCapacity(pointQueryDTO.getPointList().size());
        stationStopRangeSearchDTO.setPointList(pointList);
        pointQueryDTO.getPointList().forEach(point -> {
            PointInfoDTO pointInfoDTO = new PointInfoDTO();
            pointInfoDTO.setPointId(point.getPointId());
            pointInfoDTO.setPointNumber(point.getPointNumber());
            pointInfoDTO.setPointName(point.getPointName());
            pointInfoDTO.setPointType(point.getPointType());
            pointInfoDTO.setLatitude(point.getLatitude());
            pointInfoDTO.setLongitude(point.getLongitude());
            pointInfoDTO.setEnabled(point.getEnabled());
            pointList.add(pointInfoDTO);
        });
        return stationStopRangeSearchDTO;
    }

    /**
     * 暂存勘查线路
     *
     * @param taskRouteSaveVO taskRouteSaveVO
     */
    public void saveTaskRoute(TaskRouteSaveVO taskRouteSaveVO) {
        String taskStatus = queryTaskStatus(taskRouteSaveVO.getTaskId());
        // 非勘查中任务无法编辑
        if (!TaskStatusEnum.UNDER_EXPLORATION.getCode().equals(taskStatus)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getMessage());
        }

        // 检查任务处理人和接口调用人是否一致
        checkSameProcessor(taskRouteSaveVO.getTaskId());

        // 更新线路
        mapCollectionTaskManager.saveTaskRoute(taskRouteSaveVO);
    }

    /**
     * 获取勘查任务关联线路
     *
     * @param taskBaseVO taskBaseVO
     * @return TaskRouteDTO
     */
    public TaskRouteDTO getTaskRoute(TaskBaseVO taskBaseVO) {
        if (Objects.isNull(mapCollectionTaskManager.queryTaskStatus(taskBaseVO.getTaskId()))) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EXIST.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EXIST.getMessage());
        }

        MapCollectionTask mapCollectionTask = mapCollectionTaskManager.queryTask(taskBaseVO.getTaskId());
        if (Objects.isNull(mapCollectionTask)) {
            return null;
        }

        TaskRouteDTO taskRouteDTO = new TaskRouteDTO();
        taskRouteDTO.setTaskName(mapCollectionTask.getTaskName());
        taskRouteDTO.setTotalMileage(mapCollectionTask.getTotalMileage());
        taskRouteDTO.setTaskRouteColor(mapCollectionTask.getRouteColor());

        // 处理线路点位
        if (CollUtil.isNotEmpty(mapCollectionTask.getTaskRoute())) {
            List<PositionDTO> taskRouteList = Lists.newArrayListWithCapacity(mapCollectionTask.getTaskRoute().size());
            mapCollectionTask.getTaskRoute().forEach(taskRoute -> {
                PositionDTO positionDTO = new PositionDTO();
                positionDTO.setLatitude(taskRoute.getLatitude());
                positionDTO.setLongitude(taskRoute.getLongitude());
                taskRouteList.add(positionDTO);
            });
            taskRouteDTO.setTaskRouteList(taskRouteList);
        } else {
            taskRouteDTO.setTaskRouteList(Lists.newArrayList());
        }

        // 处理路名
        if (CollUtil.isNotEmpty(mapCollectionTask.getRoadNames())) {
            taskRouteDTO.setRoadNameList(mapCollectionTask.getRoadNames());
        } else {
            taskRouteDTO.setRoadNameList(Lists.newArrayList());
        }

        // 处理四车道
        if (CollUtil.isNotEmpty(mapCollectionTask.getFourLane())) {
            List<FourLaneDTO> fourLaneList = Lists.newArrayListWithCapacity(mapCollectionTask.getFourLane().size());
            mapCollectionTask.getFourLane().forEach(fourLane -> {
                FourLaneDTO fourLaneDTO = new FourLaneDTO();
                fourLaneDTO.setStartAddress(fourLane.getStartAddress());
                fourLaneDTO.setEndAddress(fourLane.getEndAddress());
                fourLaneDTO.setRouteIndexList(fourLane.getRouteIndexList());
                fourLaneList.add(fourLaneDTO);
            });
            taskRouteDTO.setFourLaneList(fourLaneList);
        } else {
            taskRouteDTO.setFourLaneList(Lists.newArrayList());
        }
        return taskRouteDTO;
    }

    /**
     * 获取采集任务列表
     *
     * @param monitorTaskSearchVO monitorTaskSearchVO
     * @return PageDTO<MonitorTaskSearchDTO>
     */
    public PageDTO<MonitorTaskSearchDTO> getExplorationTaskList(MonitorTaskSearchVO monitorTaskSearchVO) {
        QueryWrapper<MapCollectionTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StrUtil.isNotBlank(monitorTaskSearchVO.getTaskName()), MapCollectionTask::getTaskName, StrUtil.trim(monitorTaskSearchVO.getTaskName()));
        queryWrapper.lambda().eq(ObjectUtil.isNotNull(monitorTaskSearchVO.getStationId()), MapCollectionTask::getStationId, monitorTaskSearchVO.getStationId());
        queryWrapper.lambda().eq(MapCollectionTask::getTaskStatus, TaskStatusEnum.PENDING_CLAIM.getCode());
        queryWrapper.lambda().isNotNull(MapCollectionTask::getTaskSubmitTime);
        if (StrUtil.isNotBlank(monitorTaskSearchVO.getVehicleName())) {
            queryWrapper.lambda().or(wp -> wp.eq(MapCollectionTask::getVehicleName, monitorTaskSearchVO.getVehicleName()).eq(MapCollectionTask::getTaskStatus, TaskStatusEnum.COLLECTING.getCode()));
            queryWrapper.lambda().orderByAsc(MapCollectionTask::getTaskStatus);
        }
        queryWrapper.lambda().orderByDesc(MapCollectionTask::getTaskSubmitTime);
        Page<MapCollectionTask> page = new Page<>(monitorTaskSearchVO.getPageNum(), monitorTaskSearchVO.getPageSize());
        Page<MapCollectionTask> mapCollectionTaskPage = mapCollectionTaskManager.getBaseMapper().selectPage(page, queryWrapper);
        List<MapCollectionTask> mapCollectionTaskList = mapCollectionTaskPage.getRecords();

        // 组装数据
        PageDTO<MonitorTaskSearchDTO> pageDTO = new PageDTO<>();
        List<MonitorTaskSearchDTO> result = Lists.newArrayList();
        pageDTO.setPageNum((int) mapCollectionTaskPage.getCurrent());
        pageDTO.setPageSize((int) mapCollectionTaskPage.getSize());
        pageDTO.setPages((int) mapCollectionTaskPage.getPages());
        pageDTO.setTotal(mapCollectionTaskPage.getTotal());
        pageDTO.setList(result);
        if (CollUtil.isEmpty(mapCollectionTaskList)) {
            return pageDTO;
        }

        // 过滤站点ID，获取站点详细信息
        List<Integer> stationIdSet = mapCollectionTaskList.stream().map(MapCollectionTask::getStationId).distinct().toList();
        Map<Integer, StationBasicDTO> stationBasicDTOMap = metadataStationApiManager.getStationById(stationIdSet.toArray(new Integer[0]));

        // 组装数据
        for (MapCollectionTask mapCollectionTask : mapCollectionTaskList) {
            MonitorTaskSearchDTO monitorTaskDTO = new MonitorTaskSearchDTO();
            monitorTaskDTO.setTaskId(mapCollectionTask.getId());
            monitorTaskDTO.setTaskName(mapCollectionTask.getTaskName());
            monitorTaskDTO.setTaskStatus(mapCollectionTask.getTaskStatus());
            monitorTaskDTO.setTotalMileage(mapCollectionTask.getTotalMileage());
            monitorTaskDTO.setTaskSubmitTime(mapCollectionTask.getTaskSubmitTime());
            if (stationBasicDTOMap.containsKey(mapCollectionTask.getStationId())) {
                monitorTaskDTO.setStationName(stationBasicDTOMap.get(mapCollectionTask.getStationId()).getStationName());
            }
            result.add(monitorTaskDTO);
        }
        return pageDTO;
    }

    /**
     * 删除勘查任务线路
     *
     * @param taskBaseVO taskBaseVO
     */
    public void deleteTaskRoute(TaskBaseVO taskBaseVO) {
        String taskStatus = queryTaskStatus(taskBaseVO.getTaskId());
        // 非勘查中任务无法编辑
        if (!TaskStatusEnum.UNDER_EXPLORATION.getCode().equals(taskStatus)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EDITABLE.getMessage());
        }

        // 检查任务处理人和接口调用人是否一致
        checkSameProcessor(taskBaseVO.getTaskId());

        // 删除线路相关数据
        mapCollectionTaskManager.deleteTaskRoute(taskBaseVO.getTaskId());
    }

    /**
     * 获取任务状态
     *
     * @param taskId taskId
     * @return taskStatus
     */
    private String queryTaskStatus(Integer taskId) {
        MapCollectionTask mapCollectionTask = mapCollectionTaskManager.queryTaskStatus(taskId);
        if (Objects.isNull(mapCollectionTask)) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_NOT_EXIST.getCode(), MonitorErrorEnum.ERROR_TASK_NOT_EXIST.getMessage());
        }
        return mapCollectionTask.getTaskStatus();
    }

    /**
     * 检查任务处理人和接口调用人是否一致
     *
     * @param taskId taskId
     */
    private void checkSameProcessor(Integer taskId) {
        String processor = mapCollectionTaskRedis.getTaskProcessor(taskId);
        if (StrUtil.isNotBlank(processor) && !StrUtil.equals(processor, JsfLoginUtil.getUsername())) {
            throw new AppException(MonitorErrorEnum.ERROR_TASK_CANNOT_PROCESS.getCode(), String.format(MonitorErrorEnum.ERROR_TASK_CANNOT_PROCESS.getMessage(), processor));
        }
    }

    /**
     * 检查JSF调用用户是否存在
     */
    private void checkUsernameExist() {
        if (StrUtil.isBlank(JsfLoginUtil.getUsername())) {
            throw new AppException(MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getCode(), MonitorErrorEnum.ERROR_USER_PHONE_ABSENT.getMessage());
        }
    }
}
