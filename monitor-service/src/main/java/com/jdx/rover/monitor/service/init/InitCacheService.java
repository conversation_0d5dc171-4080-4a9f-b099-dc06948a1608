/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.init;

import com.jdx.rover.metadata.domain.dto.cockpit.CockpitInfoDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamBasicInfoDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamInfoDTO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamBasicUserVO;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.cockpit.CockpitTeamStatusDO;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamMqttManager;
import com.jdx.rover.monitor.manager.drawer.DrawerMqttManager;
import com.jdx.rover.monitor.manager.user.UserInfoMqttManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitTeamStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class InitCacheService {
    private final CockpitTeamManager cockpitTeamManager;

    private final CockpitTeamStatusRepository cockpitTeamStatusRepository;

    private final CockpitStatusRepository cockpitStatusRepository;

    private final UserInfoMqttManager userInfoMqttManager;

    private final CockpitTeamMqttManager cockpitTeamMqttManager;

    private final DrawerMqttManager drawerMqttManager;

    /**
     * 初始化平行驾驶所有车辆基础数据
     */
    public void initAllCockpitTeam() {
        log.info("初始化驾驶舱团队开始!");
        StopWatch sw = new StopWatch();
        sw.start("获取所有驾驶团队信息");
        List<CockpitTeamInfoDTO> cockpitTeamInfoList = cockpitTeamManager.getCockpitTeamInfoList();
        sw.stop();

        sw.start("保存驾驶团队信息");
        for (CockpitTeamInfoDTO cockpitTeamInfoDTO : cockpitTeamInfoList) {
            if (CollectionUtils.isEmpty(cockpitTeamInfoDTO.getCockpitInfoList())) {
                continue;
            }
            List<String> cockpitList = cockpitTeamInfoDTO.getCockpitInfoList().stream().map(CockpitInfoDTO::getCockpitNumber)
                    .collect(Collectors.toList());
            Map<String, Object> paramMap = new ParamMap<CockpitTeamStatusDO>()
                    .addProperty(CockpitTeamStatusDO::getCockpitTeamName, cockpitTeamInfoDTO.getCockpitTeamName())
                    .addProperty(CockpitTeamStatusDO::getCockpitList, cockpitList)
                    .toMap();
            cockpitTeamStatusRepository.putAllMapObject(cockpitTeamInfoDTO.getCockpitTeamNumber(), paramMap);
        }
        // 删除实际不存在的缓存key
        List<String> cockpitTeamKeyList = RedissonUtils.getRedissonClient().getKeys().getKeysStreamByPattern(CockpitTeamStatusRepository.CACHE_NAME + "*").toList();
        if (CollectionUtils.isNotEmpty(cockpitTeamKeyList) && cockpitTeamKeyList.size() != cockpitTeamInfoList.size()) {
            List<String> existsKeyList = cockpitTeamInfoList.stream().map(
                    item -> CockpitTeamStatusRepository.CACHE_NAME + item.getCockpitTeamNumber()).toList();
            for (String key : cockpitTeamKeyList) {
                if (existsKeyList.contains(key)) {
                    continue;
                }
                RedissonUtils.getRedissonClient().getMap(key).delete();
            }
        }
        sw.stop();

        sw.start("保存驾驶舱信息");
        List<String> existsCockpitKeyList = new ArrayList<>();
        for (CockpitTeamInfoDTO cockpitTeamInfoDTO : cockpitTeamInfoList) {
            if (CollectionUtils.isEmpty(cockpitTeamInfoDTO.getCockpitInfoList())) {
                continue;
            }
            for (CockpitInfoDTO cockpitInfoDTO : cockpitTeamInfoDTO.getCockpitInfoList()) {
                cockpitStatusRepository.putMapValue(cockpitInfoDTO.getCockpitNumber(), CockpitStatusDO::getCockpitType, cockpitInfoDTO.getCockpitType());
                existsCockpitKeyList.add(CockpitStatusRepository.CACHE_NAME + cockpitInfoDTO.getCockpitNumber());
            }
        }
        // 删除实际不存在的缓存key
        List<String> cockpitKeyList = RedissonUtils.getRedissonClient().getKeys().getKeysStreamByPattern(CockpitStatusRepository.CACHE_NAME + "*").toList();
        if (CollectionUtils.isNotEmpty(cockpitKeyList) && cockpitKeyList.size() != existsCockpitKeyList.size()) {
            for (String key : cockpitKeyList) {
                if (existsCockpitKeyList.contains(key)) {
                    continue;
                }
                RedissonUtils.getRedissonClient().getMap(key).delete();
            }
        }
        sw.stop();

        log.info("初始化驾驶舱团队耗时={}", sw.prettyPrint());
        log.info("初始化驾驶舱团队结束!");
    }

    /**
     * 刷新用户mqtt消息
     */
    public void refreshUserMqtt(String userName) {
        userInfoMqttManager.pushUserInfo(userName);

        CockpitTeamBasicUserVO userVO = new CockpitTeamBasicUserVO();
        userVO.setUserName(userName);
        CockpitTeamBasicInfoDTO teamInfoOfUserName = cockpitTeamManager.getTeamBasicInfoOfUserName(userVO);
        cockpitTeamMqttManager.pushCockpitTeam(teamInfoOfUserName.getCockpitTeamNumber());

        drawerMqttManager.pushDrawer(userName);
    }
}
