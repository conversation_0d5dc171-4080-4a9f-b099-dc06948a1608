/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.cockpit;

import com.jdx.rover.local.cache.autoconfigure.service.LocalCacheEvictService;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamInfoDTO;
import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.manager.cockpit.CockpitManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.repository.redis.AllVehicleNameSortRepository;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.sort.CockpitScoreSortedSetRepository;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 初始化分数
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class CockpitInitService {
    private final VehicleScheduleRepository vehicleScheduleRepository;
    private final VehicleRealtimeRepository vehicleRealtimeRepository;
    private final MetadataVehicleApiManager metadataVehicleApiManager;
    private final AllVehicleNameSortRepository allVehicleNameSortRepository;
    private final LocalCacheEvictService localCacheEvictService;
    private final CockpitManager cockpitManager;
    private final CockpitTeamManager cockpitTeamManager;
    private final CockpitScoreService cockpitScoreService;

    /**
     * 初始化所有车辆基础数据
     */
    public void initAllVehicleBasicInfo() {
        log.info("初始化所有基础信息开始!");
        StopWatch sw = new StopWatch();
        sw.start("Feign获取基础信息");
        List<VehicleBasicDTO> vehicleList = metadataVehicleApiManager.listAll();
        sw.stop();

        sw.start("初始化所有分数");
        List<String> vehicleNameSortList = vehicleList.stream().map(VehicleBasicDTO::getName).sorted().collect(Collectors.toList());
        initScore(vehicleList, vehicleNameSortList);
        sw.stop();
        log.info("初始化所有基础信息和分数耗时={}", sw.prettyPrint());

        log.info("初始化所有基础信息和分数结束!");
    }

    /**
     * 初始化所有车辆基础数据
     */
    public void initScore(List<VehicleBasicDTO> vehicleList, List<String> vehicleNameSortList) {
        log.info("初始化所有分数开始!");

        StopWatch sw = new StopWatch();
        sw.start("获取调度信息");
        Map<String, MonitorScheduleEntity> scheduleMap = vehicleScheduleRepository.listMap(vehicleNameSortList);

        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameSortList);

        sw.stop();
        sw.start("构建BO对象");
        List<VehicleScoreBO> vehicleScoreBOList = new ArrayList<>(vehicleList.size());
        for (VehicleBasicDTO dto : vehicleList) {
            VehicleScoreBO bo = new VehicleScoreBO();
            bo.setVehicleName(dto.getName());
            String scheduleState = getScheduleState(bo.getVehicleName(), scheduleMap);
            bo.setScheduleState(scheduleState);

            String systemState = getSystemState(bo.getVehicleName(), realtimeMap);
            bo.setSystemState(systemState);

            bo.setVehicleNameSortList(vehicleNameSortList);
            vehicleScoreBOList.add(bo);
        }

        sw.stop();
        sw.start("通过调度状态排序");
        // 通过调度状态排序
        updateScheduleScore(vehicleScoreBOList);
        sw.stop();
        log.info("初始化所有分数耗时={}", sw.prettyPrint());

        log.info("初始化所有分数结束!");
    }

    /**
     * 初始化平行驾驶车辆基础数据
     */
    public void initCockpitTeam(String cockpitTeamNumber) {
        log.info("初始化驾驶舱团队开始!");
        StopWatch sw = new StopWatch();
        sw.start("获取车辆信息");
        CockpitTeamInfoDTO vehicleInfoList = cockpitTeamManager.getTeamVehicleList(cockpitTeamNumber);
        sw.stop();

        log.info("初始化驾驶舱团队耗时={}", sw.prettyPrint());
        log.info("初始化驾驶舱团队结束!");
    }


    /**
     * 通过类型更新分数
     */
    private void updateScheduleScore(List<VehicleScoreBO> vehicleScoreBOList) {
        Map<String, Double> scoreMap = new HashMap<>(vehicleScoreBOList.size());
        String cacheName = CockpitScoreSortedSetRepository.CACHE_NAME;
        RScoredSortedSet<String> sortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(cacheName);
        sortedSet.clear();
        // 通过字母对站点名称排序
        for (VehicleScoreBO bo : vehicleScoreBOList) {
            double score = CockpitScoreService.getScheduleStateScore(bo);
            scoreMap.put(bo.getVehicleName(), score);
        }
        sortedSet.addAll(scoreMap);
        localCacheEvictService.publishByName(new String[]{cacheName});
    }

    /**
     * 获取调度状态
     *
     * @param vehicleName
     * @param scheduleMap
     * @return
     */
    private String getScheduleState(String vehicleName, Map<String, MonitorScheduleEntity> scheduleMap) {
        MonitorScheduleEntity schedule = scheduleMap.get(vehicleName);
        if (schedule == null || schedule.getScheduleState() == null) {
            return VehicleScheduleState.WAITING.getVehicleScheduleState();
        }
        return schedule.getScheduleState();
    }

    /**
     * 获取系统状态
     *
     * @param vehicleName
     * @param realtimeMap
     * @return
     */
    private String getSystemState(String vehicleName, Map<String, VehicleRealtimeInfoDTO> realtimeMap) {
        VehicleRealtimeInfoDTO systemState = realtimeMap.get(vehicleName);
        if (systemState == null || systemState.getSystemState() == null) {
            return SystemStateEnum.OFFLINE.getSystemState();
        }
        return systemState.getSystemState();
    }
}
