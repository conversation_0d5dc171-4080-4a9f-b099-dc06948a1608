/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.tencent;

import cn.hutool.core.util.CoordinateUtil;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.infrastructure.jsf.domain.dto.TencentCrossAddressDTO;
import com.jdx.rover.infrastructure.jsf.domain.vo.TencentLocationVO;
import com.jdx.rover.infrastructure.jsf.service.HttpProxyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 腾讯地图操作工具类
 * 腾讯地图文档：https://lbs.qq.com/service/webService/webServiceGuide/webServiceGeocoder
 * 在线坐标转换工具： https://tool.lu/coordinate/
 *
 * <AUTHOR>
 * @date 2023/02/08
 */
@Slf4j
@Service
public class TencentMapService {

    /**
     * 腾讯接口地址
     */
    private static final String LOCATION_ADDRESS = "https://apis.map.qq.com/ws/geocoder/v1/?";

    /**
     * 默认的腾讯地图使用的key
     */
    private static final String DEFAULT_KEY = "67ABZ-HLPRQ-XZM53-GNCSR-GNSXF-DSBDK";

    /**
     * 方向最大距离,默认为50m,超过不返回
     */
    private static final double MAX_DIRECTION_DISTANCE = 50;

    /**
     * 外放访问服务
     */
    @Autowired
    private HttpProxyService httpProxyService;

    /**
     * 腾讯地图wgs84坐标转地址
     *
     * @param latitude  纬度
     * @param longitude 经度
     * @return 经纬度信息
     */
    public String wgs84ToAddress(double latitude, double longitude) {
        CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(longitude, latitude);
        return gcj02ToAddress(coordinate.getLat(), coordinate.getLng());
    }

    /**
     * 腾讯地图wGCJ02坐标（火星）转地址
     *
     * @param latitude  纬度
     * @param longitude 经度
     * @return 经纬度信息
     */
    private String gcj02ToAddress(double latitude, double longitude) {
        TencentLocationVO tencentLocationVo = new TencentLocationVO();
        tencentLocationVo.setLatitude(latitude);
        tencentLocationVo.setLongitude(longitude);
        try {
            log.info("[腾讯地图]通过经纬度获取地址信息:latitude={},longitude={}", latitude, longitude);
            HttpResult<TencentCrossAddressDTO> httpResult = httpProxyService.getAddressByLocation(tencentLocationVo);
            if (HttpResult.isSuccess(httpResult) && Objects.nonNull(httpResult.getData())) {
                TencentCrossAddressDTO addressDto = httpResult.getData();
                String address = buildResult(addressDto);
                log.info("通过经纬度获取地址最终结果:{}", address);
                return address;
            }
            return null;
        } catch (Exception ex) {
            log.error("获取腾讯地图停靠点信息失败：", ex);
            return null;
        }
    }

    /**
     * 通过腾讯返回结构,构建需要的路口地址
     *
     * @param tencentResult
     * @return
     */
    private String buildResult(TencentCrossAddressDTO tencentResult) {
        if (tencentResult == null || StringUtils.isBlank(tencentResult.getAddress())) {
            return null;
        }
        String addressResult = tencentResult.getAddress();
        if (tencentResult.getAddress_reference() == null || tencentResult.getAddress_reference().getCrossroad() == null) {
            return addressResult;
        }
        StringBuilder responseBuilder = new StringBuilder(addressResult);
        TencentCrossAddressDTO.Area crossroad = tencentResult.getAddress_reference().getCrossroad();
        if (StringUtils.isNotBlank(crossroad.getTitle())) {
            responseBuilder.append("-").append(crossroad.getTitle());
        }
        if (StringUtils.isNotBlank(crossroad.get_dir_desc()) && crossroad.get_distance() < MAX_DIRECTION_DISTANCE) {
            responseBuilder.append("-").append(crossroad.get_dir_desc());
        }
        return responseBuilder.toString();
    }

}
