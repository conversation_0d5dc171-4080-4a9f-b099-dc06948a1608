/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <p>
 * This is static class that provides configuration properties for impassable area.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "impassable-area")
@Data
@Validated
public class ImpassableAreaProperties {
  /**
   * <p>
   * Represents the url to add area.
   * </p>
   */
  @NotNull
  @NotBlank
  private String addUrl = "http://192.168.180.123:8082/AddTnta";
  /**
   * <p>
   * Represents the url to del area.
   * </p>
   */
  @NotNull
  @NotBlank
  private String delUrl = "http://192.168.180.123:8082/DelTnta";
  /**
   * <p>
   * Represents the url to get area.
   * </p>
   */
  @NotNull
  @NotBlank
  private String searchUrl = "http://192.168.180.123:8082/ReqTntaByPos";

  /**
   * <p>
   * Represents the url to update area.
   * </p>
   */
  @NotNull
  @NotBlank
  private String updateUrl = "http://192.168.180.123:8082/UpdateTntaByEffectDate";


  /**
   * <p>
   * Represents the url to translate area.
   * </p>
   */
  @NotNull
  @NotBlank
  private String utmUrl = "http://10.182.69.128:22005/xcss/v0/translate";

}
