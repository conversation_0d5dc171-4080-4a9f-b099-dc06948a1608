package com.jdx.rover.monitor.service.mapcollection;

import cn.hutool.core.util.CoordinateUtil;
import cn.hutool.core.util.StrUtil;
import com.jdx.rover.monitor.common.utils.jts.GeoUtils;
import com.jdx.rover.monitor.dataobject.mapcollection.VehicleDistanceDO;
import com.jdx.rover.monitor.dataobject.mapcollection.VehiclePointDO;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.mapcollection.CollectionModeEnum;
import com.jdx.rover.monitor.enums.mapcollection.CollectionStatusEnum;
import com.jdx.rover.monitor.repository.redis.VehicleMapDistanceRepository;
import com.jdx.rover.monitor.repository.redis.VehicleMapRealRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @description: 地图采集服务
 * @author: wangguotai
 * @create: 2024-12-19 21:13
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class MapCollectionService {

    private final VehicleStatusRepository vehicleStatusRepository;

    private final VehicleMapRealRouteRepository vehicleMapRealRouteRepository;

    private final VehicleMapDistanceRepository vehicleMapDistanceRepository;

    private final DuccConfigProperties duccConfigProperties;

    /**
     * 处理地图采集路线+行驶距离
     */
    public void handleRouteAndDistance(VehicleRealtimeInfoDTO dto) {
        // 是否处理
        if (ignore(dto)) {
            return;
        }
        // 处理行驶距离
        handleDistance(dto);
        // 处理路线
        handleRoute(dto);
    }

    /**
     * 处理行驶距离
     */
    private void handleDistance(VehicleRealtimeInfoDTO dto) {
        VehicleDistanceDO distanceDO = vehicleMapDistanceRepository.get(dto.getVehicleName());
        if (Objects.isNull(distanceDO)) {
            distanceDO = new VehicleDistanceDO();
            distanceDO.setLongitude(dto.getLon());
            distanceDO.setLatitude(dto.getLat());
            distanceDO.setDistance(0.0);
            distanceDO.setTotalDistance(0.0);
        } else {
            double distance = GeoUtils.haversineDistance(dto.getLat(), dto.getLon(), distanceDO.getLatitude(), distanceDO.getLongitude()) * 1000;
            distanceDO.setLongitude(dto.getLon());
            distanceDO.setLatitude(dto.getLat());
            if (distance > 0 && distance < duccConfigProperties.getMaxDrivingDistance()) {
                distanceDO.setDistance(distance + distanceDO.getDistance());
                distanceDO.setTotalDistance(distance + distanceDO.getTotalDistance());
            }
        }
        vehicleMapDistanceRepository.set(dto.getVehicleName(), distanceDO);
    }

    /**
     * 处理路线
     */
    private void handleRoute(VehicleRealtimeInfoDTO dto) {
        VehiclePointDO pointDO = new VehiclePointDO();
        pointDO.setLongitude(dto.getLon());
        pointDO.setLatitude(dto.getLat());
        vehicleMapRealRouteRepository.push(dto.getVehicleName(), pointDO);
    }

    /**
     * 是否处理
     */
    private boolean ignore(VehicleRealtimeInfoDTO dto) {
        if (Objects.isNull(dto) || StrUtil.isBlank(dto.getVehicleName()) || Objects.isNull(dto.getLon()) || Objects.isNull(dto.getLat())) {
            log.error("[告警]guardian实时数据内容异常.");
            return true;
        }

        if (Objects.isNull(dto.getSpeed()) || Double.compare(Math.abs(dto.getSpeed()), duccConfigProperties.getMinDrivingSpeed()) <= 0) {
            return true;
        }

        CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(dto.getLon(), dto.getLat());
        if (CoordinateUtil.outOfChina(coordinate.getLng(), coordinate.getLat())) {
            return true;
        }

        VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(dto.getVehicleName());
        if (!CollectionModeEnum.COLLECTION.getCollectionMode().equals(vehicleStatusDO.getCollectionMode())) {
            return true;
        }
        if (!CollectionStatusEnum.PROCESSING.getCollectionStatus().equals(vehicleStatusDO.getCollectionStatus())) {
            return true;
        }

        return false;
    }
}