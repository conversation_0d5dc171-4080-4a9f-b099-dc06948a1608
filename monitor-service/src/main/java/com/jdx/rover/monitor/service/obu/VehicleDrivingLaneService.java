/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.obu;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.map.api.domain.dto.GetLaneAndRoadTypeDTO;
import com.jdx.rover.map.api.domain.enums.road.MapRoadTypeEnum;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.constant.BugConstant;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePncTaskAndTrafficLightDTO;
import com.jdx.rover.monitor.entity.alarm.DrivingLaneAlarmDO;
import com.jdx.rover.monitor.entity.vehicle.VehiclePlanningStatusDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.vehicle.VehicleDrivingSecneManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehiclePlanningStatusRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.alarm.DrivingLaneRepository;
import com.jdx.rover.monitor.service.alarm.notice.AlarmNoticeProcessEnum;
import com.jdx.rover.monitor.service.alarm.notice.AlarmNoticeProcessInstance;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import com.jdx.rover.monitor.service.config.ducc.DuccDrivingLaneRegionProperties;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.obu.VehicleDriverDataDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import com.jdx.rover.server.api.domain.enums.obu.LaneTypeEnum;
import io.netty.util.HashedWheelTimer;
import io.netty.util.Timeout;
import io.netty.util.TimerTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 车辆机动车道行驶告警
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleDrivingLaneService {
  /*
   * 车道行驶线服务
   */
  private final DrivingLaneRepository drivingLaneRepository;
  /*
   * 车辆规划状态缓存
   */
  private final VehiclePlanningStatusRepository vehiclePlanningStatusRepository;
  /*
   * 车辆实时状态缓存
   */
  private final VehicleRealtimeRepository vehicleRealtimeRepository;
  /*
   * 影子事件服务
   */
  private final TrackingEventCollectService trackingEventCollectService;
  /*
   * ducc动态配置
   */
  private final DuccConfigProperties duccConfigProperties;
  /*
   * 行驶场景服务
   */
  private final VehicleDrivingSecneManager vehicleDrivingSecneManager;
  /*
   * 时间轮, 槽位数32个，每个10S,5min 保障在1圈以内
  */
  private static final HashedWheelTimer wheelTimer = new HashedWheelTimer(10, TimeUnit.SECONDS, 20);
  /*
   * 持续监测5min时长
   */
  private static final Long DURATION_FIVE_MINUTES = TimeUnit.MINUTES.toSeconds(5);

  /*
   * 服务重新部署初始化车辆车道检测告警
   *
   * @param vehicleName 初始化车辆
   */
  public void initVehicleDrivingLane(String vehicleName) {
    drivingLaneRepository.remove(vehicleName);
  }

  /*
   * 消费车辆kafka告警处理消息
   *
   * @param vehicleAlarmDto 车辆实时告警
   */
  public void handleDrivingLaneMsg(VehicleDriverDataDTO vehicleDriverData) {

    Double latitude = vehicleDriverData.getLatitude();
    Double longitude = vehicleDriverData.getLongitude();
    String vehicleName = vehicleDriverData.getVehicleId();
    if (Objects.isNull(latitude) || Objects.isNull(longitude)) {
      return;
    }
    log.info("车辆行驶机动车道监测{}", vehicleName);
    if (isNotMonitorDrivingRegion(latitude, longitude)) {
        // 不在机动车道监控行驶区域，返回
        return;
    }
    Date reportTime = new Date(vehicleDriverData.getTimestampGnss() / NumberConstant.MILLION);
    if (StringUtils.equals(LaneTypeEnum.DRIVING.getValue(), vehicleDriverData.getLaneType())) {
      DrivingLaneAlarmDO drivingLaneAlarmDo = drivingLaneRepository.get(vehicleName);
      if (Objects.isNull(drivingLaneAlarmDo)) {
        VehiclePlanningStatusDO vehicleStatusDo = vehiclePlanningStatusRepository.get(vehicleName);
        if (Objects.nonNull(vehicleStatusDo) && Objects.equals(Boolean.TRUE, vehicleStatusDo.getIsInIntersection())) {
          // 路口范围内检测会上报机动车道行驶，过滤
          log.info("车辆{}机动车道压线但是在路口范围内过滤", vehicleName);
          return;
        }
        GetLaneAndRoadTypeDTO roadSceneDto = vehicleDrivingSecneManager.getVehicleDrivingRoadScene(vehicleName, latitude, longitude);
        if (Objects.isNull(roadSceneDto.getRightToLeftOrder()) || roadSceneDto.getRightToLeftOrder() < 1) {
          // 机非混行道路，过滤
          return;
        }
        if(StringUtils.equals(roadSceneDto.getRoadType(), MapRoadTypeEnum.PARK.getCode())) {
          // 园区内部范围不上报，过滤
          log.info("车辆{}机动车道压线或者机非混行但是在园区范围内过滤", vehicleName);
          return;
        }
        // 存储记录
        drivingLaneAlarmDo = new DrivingLaneAlarmDO();
        drivingLaneAlarmDo.setVehicleName(vehicleName);
        drivingLaneAlarmDo.setRecordTime(reportTime);
        drivingLaneRepository.set(vehicleName, drivingLaneAlarmDo);
        // 首次机动车道(发送咚咚告警+ 5min定时器)
        sendNotice(vehicleName, reportTime);
        wheelTimer.newTimeout(new DrivingLaneMonitorTimerTask(vehicleName), DURATION_FIVE_MINUTES, TimeUnit.SECONDS);
      }
    } else if (StringUtils.equals(LaneTypeEnum.BYCICLE.getValue(), vehicleDriverData.getLaneType())){
      // 行驶到非机动车道, 防止频繁的变道会高频告警
      if (drivingLaneRepository.exist(vehicleName)) {
        drivingLaneRepository.remove(vehicleName);
      }
    }
  }

  /**
   * 判断不在行驶机动车道监控区域
   * @param latitude 纬度。
   * @param longitude 经度。
   */
  private boolean isNotMonitorDrivingRegion(Double latitude, Double longitude) {
    String drivingRegionDucc = duccConfigProperties.getMonitorDrivingLaneRegion();
    if (StringUtils.isBlank(drivingRegionDucc)) {
      return false;
    }
    List<DuccDrivingLaneRegionProperties> drivingRegion = JsonUtils.readValue(drivingRegionDucc, new TypeReference<List<DuccDrivingLaneRegionProperties>>(){});
    if (CollectionUtils.isEmpty(drivingRegion)) {
      return false;
    }
    Optional optional = drivingRegion.stream().filter(region ->
       longitude > region.getMinLongitude() && longitude < region.getMaxLongitude() && latitude > region.getMinLatitude() && latitude < region.getMaxLatitude()).findAny();
    if (!optional.isPresent()) {
      // 车辆当前位置不在机动车道监控区域，返回
      return true;
    }
    String drivingRegionFilterDucc = duccConfigProperties.getMonitorDrivingLaneFilterRegion();
    if (StringUtils.isBlank(drivingRegionFilterDucc)) {
      return false;
    }
    List<DuccDrivingLaneRegionProperties> filterDrivingRegion = JsonUtils.readValue(drivingRegionFilterDucc, new TypeReference<List<DuccDrivingLaneRegionProperties>>(){});
    if (CollectionUtils.isEmpty(filterDrivingRegion)) {
      return false;
    }
    // 车辆在机动车道整体监控区域，判断在黑名单路段区域
    optional = filterDrivingRegion.stream().filter(region ->
            longitude > region.getMinLongitude() && longitude < region.getMaxLongitude() && latitude > region.getMinLatitude() && latitude < region.getMaxLatitude()).findAny();
    return optional.isPresent();
  }

  /**
   * 发送机动车道行驶通知
   * @param vehicleName 车辆名称。
   * @param reportTime 报警时间。
   */
  private void sendNotice(String vehicleName, Date reportTime) {
    // 发送影子记录
    trackingEventCollectService.pushDrivingLaneEvent(vehicleName, reportTime, BugConstant.userName);
    // 发送咚咚通知
    sendDdMsg(vehicleName, reportTime);
  }

  /**
   * 发送车辆机动车道行驶咚咚报警信息。
   * @param vehicleName 车辆名称。
   * @param reportTime 报警时间。
   */
  private void sendDdMsg(String vehicleName, Date reportTime) {
    List<AlarmInfoDTO> addAlarm = new ArrayList<>();
    AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
    alarmInfo.setStartTime(reportTime);
    alarmInfo.setAlarmType(AlarmNoticeProcessEnum.VEHICLE_DRIVING_LANE.getAlarm());
    alarmInfo.setErrorCode(AlarmNoticeProcessEnum.VEHICLE_DRIVING_LANE.getAlarmCode());
    addAlarm.add(alarmInfo);
    log.info("发送车辆{}机动车道行驶{}", vehicleName, JsonUtils.writeValueAsString(addAlarm));
    AlarmNoticeProcessInstance.INSTANCE.handleVehicleAlarm(vehicleName, addAlarm);
  }

  /**
   * 机动车道行驶定时5min监测任务
   */
   class DrivingLaneMonitorTimerTask implements TimerTask {

    private String vehicleName;

    /**
     * 构造一个DrivingLaneMonitorTimerTask对象，用于监控指定车辆的行驶车道。
     * @param vehicleName 车辆的名称。
     */
    DrivingLaneMonitorTimerTask(String vehicleName) {
      this.vehicleName = vehicleName;
    }

    /**
     * 超时时间后执行任务
     * @param timeout 超时时间限制。
     */
    @Override
    public void run(Timeout timeout) throws Exception {
      log.info("持续监测车辆{}机动车道行驶", vehicleName);
      DrivingLaneAlarmDO drivingLaneAlarmDo = drivingLaneRepository.get(vehicleName);;
      if (Objects.isNull(drivingLaneAlarmDo)) {
        log.info("车辆{} 在5分钟内行驶出机动车道", vehicleName);
        return;
      }
        String redisKey = RedisKeyEnum.TRAFFIC_LIGHT_SET_VEHICLE.getValue() + vehicleName;
        SingleVehiclePncTaskAndTrafficLightDTO trafficLightRedisDTO = RedissonUtils.getObject(redisKey);
        if (Objects.isNull(trafficLightRedisDTO) || !StringUtils.equals("ACTIVE", trafficLightRedisDTO.getPncTaskType())) {
          // PNC无任务，自检是否在机动车道
          log.info("车辆{}车道线检测变为空，且当前无planing任务", vehicleName);
          drivingLaneRepository.remove(vehicleName);
          return;
      }
      VehicleRealtimeInfoDTO vehicleRealtimeInfo = vehicleRealtimeRepository.get(vehicleName);
      if (Objects.isNull(vehicleRealtimeInfo) || StringUtils.equalsAny(vehicleRealtimeInfo.getSystemState(), SystemStateEnum.OFFLINE.getSystemState()) ||
              Objects.isNull(vehicleRealtimeInfo.getLat()) || Objects.isNull(vehicleRealtimeInfo.getLon())) {
        // 车辆离线不符合通知条件，返回
        drivingLaneRepository.remove(vehicleName);
        return;
      }
      VehiclePlanningStatusDO vehicleStatusDo = vehiclePlanningStatusRepository.get(vehicleName);
      if (Objects.nonNull(vehicleStatusDo) && Objects.equals(Boolean.TRUE, vehicleStatusDo.getIsInIntersection())) {
        log.info("持续监测车辆{}机动车道压线但是在路口范围内过滤", vehicleName);
        drivingLaneRepository.remove(vehicleName);
        return;
      }
      GetLaneAndRoadTypeDTO roadSceneDto = vehicleDrivingSecneManager.getVehicleDrivingRoadScene(vehicleName, vehicleRealtimeInfo.getLat(), vehicleRealtimeInfo.getLon());
      if (Objects.isNull(roadSceneDto.getRightToLeftOrder()) || roadSceneDto.getRightToLeftOrder() < 1) {
        // 机非混行道路，返回
        return;
      }
      if (StringUtils.equals(roadSceneDto.getRoadType(), MapRoadTypeEnum.PARK.getCode())) {
        // 车辆行驶到园区里，返回
        drivingLaneRepository.remove(vehicleName);
        return;
      }
      long durationTime = DateUtil.between(drivingLaneAlarmDo.getRecordTime(), new Date(), DateUnit.SECOND);
      log.info("持续监测车辆{}机动车道行驶差值时间{}", vehicleName, durationTime);
      if(durationTime >= DURATION_FIVE_MINUTES) {
        log.info("车辆{}持续占用机动车道行驶", JsonUtils.writeValueAsString(drivingLaneAlarmDo));
        sendNotice(vehicleName, new Date());
        wheelTimer.newTimeout(new DrivingLaneMonitorTimerTask(vehicleName), DURATION_FIVE_MINUTES, TimeUnit.SECONDS);
      }
    }
  }


}
