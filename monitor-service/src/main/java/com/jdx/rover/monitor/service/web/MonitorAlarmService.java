/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.api.domain.enums.CockpitTypeEnum;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.VehicleAlarmDTO;
import com.jdx.rover.monitor.api.domain.enums.AlarmSourceEnum;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.ManualAlarmRecordDTO;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.manager.abnormal.ManualAlarmRecordManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.po.ManualAlarmRecord;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import com.jdx.rover.permission.domain.dto.basic.UserMaskPhoneDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 告警服务对外接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class MonitorAlarmService {

  private final VehicleAlarmRepository vehicleAlarmRepository;

  private final ManualAlarmRecordManager manualAlarmRecordManager;

  private final MetadataUserApiManager userBasicApiManager;

  private final UserStatusRepository userStatusRepository;

  private final CockpitStatusRepository cockpitStatusRepository;

  private final TrackingEventCollectService trackingEventCollectService;

  private final JmqProducerService jmqProducerService;

  //{用户}于{小程序/巡查端}提报，提报内容：{备注}
  private static final String MANUAL_ALARM_MSG = "%s于%s %s端提报，提报内容：[%s]";

  /**
   * <p>
   *  上报巡查人工告警
   * </p>
   *
   */
  public HttpResult reportManualAlarm(MonitorManualAlarmReportVO manualAlarmReportVo, ManualAlarmSourceEnum sourceEnum) {
    ParameterCheckUtility.checkNotNull(manualAlarmReportVo, "manualAlarmReportVo");
    ParameterCheckUtility.checkNotNullNorEmpty(manualAlarmReportVo.getVehicleName(), "MonitorManualAlarmReportVO#vehicleName");
    String userName = UserUtils.getAndCheckLoginUser();
    String vehicleName = manualAlarmReportVo.getVehicleName();
    List<VehicleAlarmDO.VehicleAlarmEventDO> oldVehicleAlarmEventList = vehicleAlarmRepository.get(vehicleName);
    Map<String, VehicleAlarmDO.VehicleAlarmEventDO> alarmEventDTOMap =
            oldVehicleAlarmEventList.stream().collect(Collectors.toMap(VehicleAlarmDO.VehicleAlarmEventDO :: getType, Function.identity()));
    UserStatusDO userStatus = userStatusRepository.get(userName);
    if (sourceEnum == ManualAlarmSourceEnum.PATROL_MONITOR && !Objects.isNull(userStatus) && StringUtils.isNotBlank(userStatus.getCockpitNumber())) {
      CockpitStatusDO cockpitStatus = cockpitStatusRepository.get(userStatus.getCockpitNumber());
      if (StringUtils.equals(cockpitStatus.getCockpitType(), CockpitTypeEnum.MONITOR_SEAT.getValue())) {
        sourceEnum = ManualAlarmSourceEnum.PATROL_MONITOR;
      } else if (StringUtils.equals(cockpitStatus.getCockpitType(), CockpitTypeEnum.COCKPIT_SEAT.getValue())){
        sourceEnum = ManualAlarmSourceEnum.PATROL_REMOTE_CONTROL;
      }
    }
    VehicleAlarmDTO manualReportAlarm = buildManualAddAlarm(userName, alarmEventDTOMap, manualAlarmReportVo, sourceEnum);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MANUAL_VEHICLE_ALARM.getTopic(), vehicleName, manualReportAlarm);
    trackingEventCollectService.pushManualAlarmEvent(sourceEnum, manualAlarmReportVo, userName);
    saveManualAlarm(userName, sourceEnum.getSource(), userStatus, manualAlarmReportVo);
    return HttpResult.success();
  }

  /**
   * 呼叫远驾（京小鸽 + 小哥工作台）
   */
  public void callCockpit(MonitorManualAlarmReportVO manualAlarmReportVo, ManualAlarmSourceEnum sourceEnum) {
    // 获取当前用户
    String userName = JsfLoginUtil.getUsername();

    // 获取当前车辆告警
    String vehicleName = manualAlarmReportVo.getVehicleName();
    List<VehicleAlarmDO.VehicleAlarmEventDO> oldVehicleAlarmEventList = vehicleAlarmRepository.get(vehicleName);
    Map<String, VehicleAlarmDO.VehicleAlarmEventDO> alarmEventDTOMap = oldVehicleAlarmEventList.stream().collect(Collectors.toMap(VehicleAlarmDO.VehicleAlarmEventDO::getType, Function.identity()));

    // 构造人工告警
    VehicleAlarmDTO manualReportAlarm = buildManualAddAlarm(userName, alarmEventDTOMap, manualAlarmReportVo, sourceEnum);

    // jmq推送车辆人工告警，monitor-worker处理
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MANUAL_VEHICLE_ALARM.getTopic(), vehicleName, manualReportAlarm);

    // jmq推送车辆人工告警，shadow-worker处理
    trackingEventCollectService.pushManualAlarmEvent(sourceEnum, manualAlarmReportVo, userName);

    // 保存数据库
    saveManualAlarm(userName, sourceEnum.getSource(), null, manualAlarmReportVo);
  }

  /**
   * <p>
   *  结束巡查人工告警
   * </p>
   *
   */
  public void endManualAlarm(String vehicleName, Date alarmEndTime) {
    List<VehicleAlarmDO.VehicleAlarmEventDO> oldVehicleAlarmEventList = vehicleAlarmRepository.get(vehicleName);
    List<VehicleAlarmDO.VehicleAlarmEventDO> manualAlarmList = oldVehicleAlarmEventList.stream().filter(alarm ->
            StringUtils.equals(AlarmSourceEnum.MANUAL_ALARM.getSource(), alarm.getSource())).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(manualAlarmList)) {
      return;
    }
    VehicleAlarmDTO reportAlarm = new VehicleAlarmDTO();
    reportAlarm.setVehicleName(vehicleName);
    reportAlarm.setRecordTime(new Date());
    reportAlarm.setAlarmSource(AlarmSourceEnum.MANUAL_ALARM.getSource());
    reportAlarm.setAlarmEventList(manualAlarmList.stream().map(alarm -> buildDisappearAlarmInfo(alarm, alarmEndTime)).collect(Collectors.toList()));
    String jsonStr = JsonUtils.writeValueAsString(reportAlarm);
    log.info("结束人工告警，发送内容{}", jsonStr);
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MANUAL_VEHICLE_ALARM.getTopic(), vehicleName, reportAlarm);
  }

  /**
   * 查询历史记录
   */
  public List<ManualAlarmRecordDTO> listTodayManualAlarm(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmpty(vehicleName, "车号不能为空");
    List<ManualAlarmRecord> manualAlarmRecordList = manualAlarmRecordManager.listTodayManualAlarmRecord(vehicleName);
    if (CollectionUtils.isEmpty(manualAlarmRecordList)) {
      return new ArrayList<>();
    }
    List<String> userNameList = manualAlarmRecordList.stream().map(ManualAlarmRecord ::getReportUser).collect(Collectors.toList());
    List<UserMaskPhoneDTO> userMaskPhoneDtoList = userBasicApiManager.getUserPhoneInfoByName(userNameList);
    Map<String, String> userPhoneMap =
            userMaskPhoneDtoList.stream().collect(Collectors.toMap(UserMaskPhoneDTO ::getUserName, UserMaskPhoneDTO :: getMaskPhone));
    return manualAlarmRecordList.stream().map(record -> {
      ManualAlarmRecordDTO recordDto = BeanUtil.toBean(record, ManualAlarmRecordDTO.class);
      recordDto.setPhone(userPhoneMap.get(record.getReportUser()));
      return recordDto;
    }).collect(Collectors.toList());
  }

  /**
   * 构造人工消失告警
   */
  private AlarmInfoDTO buildDisappearAlarmInfo(VehicleAlarmDO.VehicleAlarmEventDO alarmEvent, Date alarmEndTime) {
    AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
    alarmInfo.setAlarmSource(alarmEvent.getSource());
    alarmInfo.setAlarmType(VehicleAlarmEnum.MANUAL_REPORT.getValue());
    alarmInfo.setStartTime(alarmEvent.getReportTime());
    alarmInfo.setEndTime(alarmEndTime);
    alarmInfo.setAlarmSource(AlarmSourceEnum.MANUAL_ALARM.getSource());
    alarmInfo.setErrorMessage(alarmEvent.getErrorMessage());
    return alarmInfo;
  }

  /**
   * 构造人工告警
   */
  private VehicleAlarmDTO buildManualAddAlarm(String userName, Map<String, VehicleAlarmDO.VehicleAlarmEventDO> alarmMap, MonitorManualAlarmReportVO alarmReportVo, ManualAlarmSourceEnum alarmSourceEnum) {
    VehicleAlarmDTO reportAlarm = new VehicleAlarmDTO();
    Date currentDate = new Date();
    VehicleAlarmDO.VehicleAlarmEventDO manualReportAlarm = alarmMap.get(VehicleAlarmEnum.MANUAL_REPORT.getValue());
    reportAlarm.setVehicleName(alarmReportVo.getVehicleName());
    reportAlarm.setRecordTime(currentDate);
    reportAlarm.setAlarmSource(AlarmSourceEnum.MANUAL_ALARM.getSource());
    String errorMsg = String.format(MANUAL_ALARM_MSG, userName, DateUtil.formatDateTime(new Date()), alarmSourceEnum.getSourceName(), StringUtils.isBlank(alarmReportVo.getRemark())? "" : alarmReportVo.getRemark());
    AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
    if (!Objects.isNull(manualReportAlarm)) {
      alarmInfo.setStartTime(manualReportAlarm.getReportTime());
      alarmInfo.setErrorMessage(manualReportAlarm.getErrorMessage() + ";" + errorMsg);
    } else {
      alarmInfo.setStartTime(currentDate);
      alarmInfo.setErrorMessage(errorMsg);
    }
    alarmInfo.setAlarmSource(alarmSourceEnum.getSource());
    alarmInfo.setAlarmType(VehicleAlarmEnum.MANUAL_REPORT.getValue());
    alarmInfo.setStartTime(currentDate);
    reportAlarm.setAlarmEventList(Lists.newArrayList(alarmInfo));
    return reportAlarm;
  }

  /**
   * 数据库存储人工告警
   * @param userName
   * @param source
   * @param userStatus
   * @param manualAlarmReportVo
   */
  private void saveManualAlarm(String userName, String source, UserStatusDO userStatus, MonitorManualAlarmReportVO manualAlarmReportVo) {
    ManualAlarmRecord alarmRecord = new ManualAlarmRecord();
    alarmRecord.setVehicleName(manualAlarmReportVo.getVehicleName());
    alarmRecord.setSource(source);
    alarmRecord.setDescription(manualAlarmReportVo.getRemark());
    if (!StringUtils.equals(source, ManualAlarmSourceEnum.MINI_MONITOR.getSource()) &&
            !Objects.isNull(userStatus) && StringUtils.isNotBlank(userStatus.getCockpitNumber())) {
      alarmRecord.setCockpitNumber(userStatus.getCockpitNumber());
    }
    alarmRecord.setReportTime(new Date());
    alarmRecord.setReportUser(userName);
    manualAlarmRecordManager.save(alarmRecord);
  }
  
}
