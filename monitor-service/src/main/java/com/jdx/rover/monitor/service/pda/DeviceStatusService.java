/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.pda;

import cn.hutool.core.bean.BeanUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.PdaRealtimeBasicInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.PdaRealtimeStatusInfoDTO;
import com.jdx.rover.monitor.enums.device.DeviceNetworkTypeEnum;
import com.jdx.rover.monitor.enums.device.DevicePropertyCodeEnum;
import com.jdx.rover.monitor.enums.device.DeviceRealtimeStateEnum;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.pda.PdaRealtimeInfoManager;
import com.jdx.rover.monitor.po.pda.PdaRealtimeInfo;
import com.jdx.rover.monitor.repository.redis.pda.DeviceBatchUpdateStatusRepository;
import com.jdx.rover.monitor.repository.redis.pda.PdaOnlineStatusStaticRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机器人设备状态服务
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeviceStatusService {

    /**
     * 设备实时服务接口
     */
    public final TransportDeviceApiManager realtimeDeviceApiManager;

    /**
     * 批量更新pad实时状态对象
     */
    public final DeviceBatchUpdateStatusRepository deviceBatchUpdateRepository;

    /**
     * 用于访问操作实体的Mapper接口
     */
    public final PdaRealtimeInfoManager pdaRealtimeInfoManager;

    /**
     * 在线状态统计接口
     */
    public final PdaOnlineStatusStaticRepository pdaOnlineStatusStaticRepository;

    /**
     * 获取Pda设备状态。
     *
     * @param deviceName 设备编号。
     */
    public PdaRealtimeStatusInfoDTO getRealtimeStatus(String deviceName) {
        Map<String, Object> result = realtimeDeviceApiManager.getDeviceStatusDetail(deviceName, new ArrayList<>());
        PdaRealtimeStatusInfoDTO realtimeInfoDto = new PdaRealtimeStatusInfoDTO();
        if (Objects.isNull(result)) {
            log.error("查询设备{}实时信息不存在", deviceName);
            return realtimeInfoDto;
        }
        try {
            BeanUtil.fillBeanWithMapIgnoreCase(result, realtimeInfoDto, true);
            Object onlineStatus = result.get("onlineStatus");
            if (!Objects.isNull(onlineStatus)) {
                realtimeInfoDto.setRealtimeStatusName(DeviceRealtimeStateEnum.getTitleByProperty((Integer) onlineStatus));
            } else {
                realtimeInfoDto.setRealtimeStatusName(DeviceRealtimeStateEnum.OFFLINE.getTitle());
            }
            Object meid = result.get("meid");
            if (!Objects.isNull(meid)) {
                String meidStr = (String) meid;
                String[] array = meidStr.split(",");
                String meidValue =
                        Arrays.stream(array).toList().stream().filter(str ->
                                StringUtils.isNotBlank(str) && !StringUtils.equals(str, "null")).collect(Collectors.joining(","));
                realtimeInfoDto.setMeid(meidValue);
            }
        } catch (Exception e) {
            log.error("设备{}实时信息转换异常", deviceName, e);
        }
        return realtimeInfoDto;
    }

    /**
     * 获取Pda设备状态。
     *
     * @param deviceNameList 设备编号列表。
     */
    public Map<String, PdaRealtimeBasicInfoDTO> getBatchBasicRealtimeStatus(DevicePropertyCodeEnum propertyCodeEnum, List<String> deviceNameList) {
        if (CollectionUtils.isEmpty(deviceNameList)) {
            return new HashMap<>();
        }
        List<Map<String, Object>> result = realtimeDeviceApiManager.getDeviceStatusList(deviceNameList, propertyCodeEnum.getPropertyList());

        if (Objects.isNull(result)) {
            log.error("查询设备列表{}实时信息不存在", deviceNameList.toArray());
            return new HashMap<>();
        }
        return result.stream().map(mapData -> {
            PdaRealtimeBasicInfoDTO realtimeInfoDto = new PdaRealtimeBasicInfoDTO();
            try {
                realtimeInfoDto = BeanUtil.mapToBean(mapData, PdaRealtimeBasicInfoDTO.class, true);
                Object onlineStatus = mapData.get("onlineStatus");
                if (!Objects.isNull(onlineStatus)) {
                    Integer onlineValue = (Integer) onlineStatus;
                    realtimeInfoDto.setRealtimeStatus(onlineValue == 1 ? DeviceRealtimeStateEnum.ONLINE.getValue() : DeviceRealtimeStateEnum.OFFLINE.getValue());
                    realtimeInfoDto.setRealtimeStatusName(DeviceRealtimeStateEnum.getTitleByProperty(onlineValue));
                } else {
                    realtimeInfoDto.setRealtimeStatus(DeviceRealtimeStateEnum.OFFLINE.getValue());
                    realtimeInfoDto.setRealtimeStatusName(DeviceRealtimeStateEnum.OFFLINE.getTitle());
                }
                return realtimeInfoDto;
            } catch (Exception e) {
                log.error("设备{}实时信息转换异常", JsonUtils.writeValueAsString(mapData), e);
            }
            return realtimeInfoDto;
        }).collect(Collectors.toMap(PdaRealtimeBasicInfoDTO::getDeviceName, Function.identity()));
    }

    /**
     * 构建PDA基本状态信息列表。
     *
     * @param records PDA实时信息列表
     * @return PDA基本状态信息DTO列表
     */
    public List<PdaRealtimeBasicInfoDTO> buildPdaBasicStatusInfo(List<PdaRealtimeInfo> records) {
        List<String> deviceList =
                records.stream().map(device -> device.getDeviceName()).collect(Collectors.toList());
        Map<String, PdaRealtimeBasicInfoDTO> mapStatusResult = getBatchBasicRealtimeStatus(DevicePropertyCodeEnum.MONITOR_PDA_STATUS, deviceList);
        return records.stream().map(device -> {
            PdaRealtimeBasicInfoDTO basicInfo = new PdaRealtimeBasicInfoDTO();
            basicInfo.setDeviceName(device.getDeviceName());
            basicInfo.setRealtimeStatus(device.getRealtimeStatus());
            basicInfo.setBurialStatus(device.getBurialStatus());
            basicInfo.setProductModelNo(device.getProductModelNo());
            basicInfo.setProductModelName(device.getProductModelName());
            basicInfo.setPositionStatus(device.getPositionStatus());
            basicInfo.setGroupName(device.getGroupName());
            basicInfo.setNetworkType(device.getNetworkType());
            if (StringUtils.isNotBlank(device.getRealtimeStatus())) {
                basicInfo.setRealtimeStatusName(DeviceRealtimeStateEnum.getNameByValue(device.getRealtimeStatus()));
            }
            if (!Objects.isNull(device.getNetworkType())) {
                basicInfo.setNetworkTypeName(DeviceNetworkTypeEnum.getNameByValue(device.getNetworkType()));
            }
            PdaRealtimeBasicInfoDTO realtimeInfo = mapStatusResult.get(device.getDeviceName());
            if (!Objects.isNull(realtimeInfo)) {
                basicInfo.setDeviceRestElectric(realtimeInfo.getDeviceRestElectric());
            }
            return basicInfo;
        }).collect(Collectors.toList());
    }

}