package com.jdx.rover.monitor.worker.service.kafka.require;

import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import com.jdx.rover.monitor.worker.service.jmq.metadata.MetadataRequireInfoJmqConsumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest(classes = MonitorWorkerApplication.class)
public class RequireMessageListenerTest {

    private final MetadataRequireInfoJmqConsumer requireInfoChangeListener;
    @Test
    void onMessage() throws Exception{
        String msg = "{\n" +
                "    \"requireNumber\": \"wx20240326142018\",\n" +
                "    \"vehicleName\": null,\n" +
                "    \"stationName\": null,\n" +
                "    \"cityName\": null,\n" +
                "    \"provinceName\": null,\n" +
                "    \"stationUseCase\": null,\n" +
                "    \"stationUseCaseName\": null,\n" +
                "    \"stationEnable\": null,\n" +
                "    \"stationEnableName\": null,\n" +
                "    \"vehicleOwnerUseCase\": null,\n" +
                "    \"vehicleOwnerUseCaseName\": null,\n" +
                "    \"reportTime\": null,\n" +
                "    \"reportErp\": null,\n" +
                "    \"requireHardwareTypeNames\": null,\n" +
                "    \"title\": null,\n" +
                "    \"requireStatus\": 3,\n" +
                "    \"requireStatusName\": \"不受理\",\n" +
                "    \"isInfluenceOperation\": 0,\n" +
                "    \"isInfluenceOperationName\": \"否\",\n" +
                "    \"requireBeginTime\": null,\n" +
                "    \"requireCompleteTime\": null,\n" +
                "    \"affirmTime\": null\n" +
                "}";
        ConsumerRecord<String, String> record = new ConsumerRecord<>("topic", 1, 1, "1", msg);
        requireInfoChangeListener.onMessage(Lists.newArrayList(new Message(record.topic(),msg)));
    }
}
