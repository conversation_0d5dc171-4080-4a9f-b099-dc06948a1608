/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest(classes = MonitorWorkerApplication.class)
class ServerReportAbnormalJmqConsumerTest {

    private final ServerReportAbnormalJmqConsumer serverReportAbnormalJmqConsumer;

    @Test
    void onMessage() throws Exception {
        String msg = "[{\"vehicleName\":\"JDE6002\",\"runtimeUuid\":1747352800401763658,\"moduleName\":\"ARTIFICIAL_OBSTACLE\",\"errorCode\":\"-13000\",\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_NOT_INITIALIZED, Message: Component not initialized.\",\"startTime\":1747352991491586098,\"endTime\":1747352993639955808,\"reportTime\":1747352993640213602},{\"vehicleName\":\"JDE6002\",\"runtimeUuid\":1747352800401763658,\"moduleName\":\"ARTIFICIAL_OBSTACLE\",\"errorCode\":\"-13000\",\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_NOT_INITIALIZED, Message: Component not initialized.\",\"startTime\":1747352991491586098,\"endTime\":0,\"reportTime\":1747352993640213602},{\"vehicleName\":\"JDE6002\",\"runtimeUuid\":1747352800401763658,\"moduleName\":\"10.10.1.102@ComponentExistance\",\"errorCode\":\"-13500\",\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_NOT_EXIST, Detail: [ Not found required following component: ARTIFICIAL_OBSTACLE DRIVERS_GNSS PNC_STATUS SENSING_LIDAR_FRONT SENSING_LIDAR_MASTER_LEFT PERC2_TRAFFIC_LIGHT_MAP_PROCESS MAP_INITIALIZATION SENSING_LIDAR_RIGHT DRIVERS_CHASSIS_TX SENSING_LIDAR_MASTER_RIGHT SENSING_LIDAR_MASTER LOCALIZATION_IMU LOCALIZATION_LIDAR LOCALIZATION_EKF LOCALIZATION_INIT SENSING_LIDAR_REAR CONTROL LOCALIZATION_TRANSFORM PERC2_SOD_6FE SENSING_LIDAR_LEFT PERC2_FUSION GPS_SIGNAL PLANNING PERC2_GEO_6FE UNIFORM PERC2_CLOUD_6FE ROUTING PREDICTION ].\",\"startTime\":1747352808601229524,\"endTime\":1747352993639959520,\"reportTime\":1747352993640213602}]";
        Message message = new Message();
        message.setText(msg);
        serverReportAbnormalJmqConsumer.onMessage(Lists.newArrayList(message));
    }
}