package com.jdx.rover.monitor.service.video;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.monitor.service.web.MonitorVideoService;
import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@RequiredArgsConstructor(onConstructor_ = @Autowired)

@SpringBootTest(classes = MonitorWorkerApplication.class)
@Slf4j
public class MonitorVideoServiceTest {

    private final MonitorVideoService monitorVideoService;

    @Test
    public void getVehicleSnapshot(){
        monitorVideoService.getVehicleSnapshot("JDZ0002", DateUtil.parse("2024-10-15 13:00:10", "yyyy-MM-dd HH:mm:ss"), "ALL", "liuwenwen52", "123");
    }
}
