package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 */

@SpringBootTest(classes = MonitorWorkerApplication.class)
@Slf4j
class AllStationSortRepositoryTest {
  @Autowired
  private AllStationSortRepository allStationSortRepository;

  @Test
  void get() throws Exception {
    List<String> stationSortList = Lists.newArrayList("A站", "B站", "C站");
    allStationSortRepository.set(stationSortList);
    Thread.sleep(1000);
    allStationSortRepository.get();
    Thread.sleep(1000);
    allStationSortRepository.get();
    Thread.sleep(1000);
    allStationSortRepository.remove("A站");
    Thread.sleep(1000);
    allStationSortRepository.get();
    Thread.sleep(1000);
    allStationSortRepository.get();
    Thread.sleep(1000);
    allStationSortRepository.get();
    Thread.sleep(1000);
  }
}