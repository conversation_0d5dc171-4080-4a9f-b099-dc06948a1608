package com.jdx.rover.monitor.worker;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
class NoSpringTest {

  @Test
  void demoTest() {
    String msg = "{\"vehicleName\":\"JD40005\",\"recordTime\":1645683539915,\"alarmEventList\":[{\"type\":\"VEHICLE_STOP_FATAL\",\"reportTime\":1645683539842,\"errorCode\":\"-1350\",\"errorLevel\":\"FATAL\",\"errorMessage\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_NOT_EXIST, Detail: [ Not found required following component: CONTROL PNC_STATUS PLANNING DRIVERS_JOYSTICK ARTIFICIAL_OBSTACLE PREDICTION ].\"}]}";
    VehicleAlarmDTO vehicleAlarmDTO = JsonUtils.readValue(msg, VehicleAlarmDTO.class);
    assertThat(vehicleAlarmDTO).isNotNull();
    log.info("msg = {}", vehicleAlarmDTO);
  }
}