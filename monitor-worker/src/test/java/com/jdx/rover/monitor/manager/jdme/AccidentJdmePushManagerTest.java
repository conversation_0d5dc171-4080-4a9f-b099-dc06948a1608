package com.jdx.rover.monitor.manager.jdme;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementColumnItem;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementColumnItemElement;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementColumnItemImgElement;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AccidentJdmePushManagerTest {

    @Test
    public void test() {


        List<String> snapshotUrlList = new ArrayList<>();
        snapshotUrlList.add("https://s3.cn-north-1.jdcloud-oss.com/video-stream/export/Logs/snapshot/2024-10-17/JDK8154/xuehongwei3_snapshot_fa57e1df216942e6ab9d9143b516e0f2_0_JDK8154_2024-10-17-11-02-23.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241017T030229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20241017%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=1fc2be3aa79d4bd9981be9785189e82e8426938e36fbfb07708bb78953bccdb9");
        snapshotUrlList.add("https://s3.cn-north-1.jdcloud-oss.com/video-stream/export/Logs/snapshot/2024-10-17/JDK8154/xuehongwei3_snapshot_fa57e1df216942e6ab9d9143b516e0f2_0_JDK8154_2024-10-17-11-02-23.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241017T030229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20241017%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=1fc2be3aa79d4bd9981be9785189e82e8426938e36fbfb07708bb78953bccdb9");
        snapshotUrlList.add("https://s3.cn-north-1.jdcloud-oss.com/video-stream/export/Logs/snapshot/2024-10-17/JDK8154/xuehongwei3_snapshot_fa57e1df216942e6ab9d9143b516e0f2_0_JDK8154_2024-10-17-11-02-23.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241017T030229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20241017%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=1fc2be3aa79d4bd9981be9785189e82e8426938e36fbfb07708bb78953bccdb9");
        snapshotUrlList.add("https://s3.cn-north-1.jdcloud-oss.com/video-stream/export/Logs/snapshot/2024-10-17/JDK8154/xuehongwei3_snapshot_fa57e1df216942e6ab9d9143b516e0f2_0_JDK8154_2024-10-17-11-02-23.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241017T030229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20241017%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=1fc2be3aa79d4bd9981be9785189e82e8426938e36fbfb07708bb78953bccdb9");
        List<Map<String, Object>> elements = new ArrayList<>();
        Map<String, Object> snapshotTitleMap = new HashMap<>();
        snapshotTitleMap.put("tag", "me_md");
        snapshotTitleMap.put("content", "**事故快照:**");
        elements.add(snapshotTitleMap);
        List<JueCardDataElementColumnItem> columns = new ArrayList<>();
        JueCardDataElementColumnItem<JueCardDataElementColumnItemElement> jueCardDataElementColumnItem = new JueCardDataElementColumnItem<>();
        for (String snapshotUrl : snapshotUrlList) {
            JueCardDataElementColumnItem<JueCardDataElementColumnItemImgElement> item = new JueCardDataElementColumnItem<>();
            item.setPadding("4px 4px 4px 4px");
            List<JueCardDataElementColumnItemImgElement> jueCardDataElementColumnItemImgElements = new ArrayList<>();
            JueCardDataElementColumnItemImgElement jueCardDataElementColumnItemImgElement = new JueCardDataElementColumnItemImgElement();
            jueCardDataElementColumnItemImgElement.setPreview(true);
            jueCardDataElementColumnItemImgElement.setImg_url(snapshotUrl);
            jueCardDataElementColumnItemImgElement.setScale(1.5);
            jueCardDataElementColumnItemImgElement.setTag("img");
            jueCardDataElementColumnItemImgElements.add(jueCardDataElementColumnItemImgElement);
            item.setElements(jueCardDataElementColumnItemImgElements);
            item.setWeight(1);
            item.setTag("column");
            jueCardDataElementColumnItem.setTag("column");
            columns.add(item);
        }
        Map<String, Object> snapshotMap = new HashMap<>();
        snapshotMap.put("columns", columns);

        elements.add(snapshotMap);

        Map<String, Object> body = new HashMap<>();

        System.out.println(JsonUtils.writeValueAsString(columns));
        System.out.println(JsonUtils.writeValueAsString(snapshotMap));
        System.out.println(JsonUtils.writeValueAsString(elements));
    }
}
