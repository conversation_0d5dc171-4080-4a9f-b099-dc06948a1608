package com.jdx.rover.monitor.worker.service.kafka.cockpit;

import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import com.jdx.rover.monitor.worker.service.jmq.metadata.MetadataCockpitTeamJmqConsumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest(classes = MonitorWorkerApplication.class)
class MetadataCockpitTeamMessageListenerTest {

    private final MetadataCockpitTeamJmqConsumer metadataCockpitTeamMessageListener;

    @Test
    void onMessage() {
        String msg = "{\"subject\":\"COCKPIT_TEAM_INFO\", \"operation\":\"EDIT\", \"id\":7, \"key\":\"JSTD202406170001\", \"timestamp\":1718629991677, \"extendedInfo\":{}}";
        Message message = new Message();
        message.setText(msg);
        metadataCockpitTeamMessageListener.onMessage(Lists.newArrayList(message));
    }
}