/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.area;

import com.bedatadriven.jackson.datatype.jts.JtsModule;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.map.api.domain.dto.MapVariableApiJsfDTO;
import com.jdx.rover.map.api.domain.enums.VariableMapTypeEnum;
import com.jdx.rover.monitor.common.utils.jts.WktUtils;
import com.jdx.rover.monitor.service.vehicle.VehicleLocationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Polygon;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/5
 */

@Slf4j
class EarlyWarningAreaServiceTest {

    @Test
    void getVehicleInAreaMap() {
        Set<EarlyWarningAreaService.AreaLocation> areaSet = new HashSet<>();
        {
            String wkt1 = "POLYGON((116.554484128608 39.784087177984,116.554349534614 39.784000560896,116.554434767843 39.7838854460916,116.554565372052 39.7839784901156,116.554484128608 39.784087177984))";
            Geometry geometry1 = WktUtils.readWkt(wkt1);
            EarlyWarningAreaService.AreaLocation areaLocation1 = new EarlyWarningAreaService.AreaLocation(4483, VariableMapTypeEnum.ATTENTION_REGION_WARN.getValue(), null, (Polygon) geometry1, null, null, null, null);
            areaSet.add(areaLocation1);
        }
        {
            String wkt1 = "POLYGON ((116.55697531258555 39.78706011594162, 116.5569191538343 39.78665805700124, 116.55742372345047 39.78691087615565, 116.55697531258555 39.78706011594162))";
            Geometry geometry1 = WktUtils.readWkt(wkt1);
            EarlyWarningAreaService.AreaLocation areaLocation1 = new EarlyWarningAreaService.AreaLocation(4486, VariableMapTypeEnum.ATTENTION_REGION_WARN.getValue(), null, (Polygon) geometry1, null, null, null, null);
            areaSet.add(areaLocation1);
        }
        {
            String wkt1 = "POLYGON ((116.52184786234288 39.78419097579932, 116.52169382221894 39.78405035450199, 116.52185542949368 39.78405035450199, 116.52184786234288 39.78419097579932))";
            Geometry geometry1 = WktUtils.readWkt(wkt1);
            EarlyWarningAreaService.AreaLocation areaLocation1 = new EarlyWarningAreaService.AreaLocation(4490, VariableMapTypeEnum.ATTENTION_REGION_WARN.getValue(), null, (Polygon) geometry1, null, null, null, null);
            areaSet.add(areaLocation1);
        }
//        EarlyWarningAreaService.areaCacheMap.put("116.5_39.7", areaSet);

        String msg = "{\"116.5_39.7\":[{\"id\":4483,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55301151832279,39.783193333172164],[116.5539161717867,39.78220301177156],[116.5550881093177,39.78284723476016],[116.55301151832279,39.783193333172164]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null},{\"id\":4521,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55401232716353,39.783442661542345],[116.55433932178192,39.78317357223314],[116.55387948557951,39.78322807153111],[116.55401232716353,39.783442661542345]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\",\"JDZ0022\"],\"weekLimit\":null,\"timeLimit\":[{\"startTime\":[3,0],\"endTime\":[23,0]}]},{\"id\":4490,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.52184786234288,39.78419097579932],[116.52169382221894,39.78405035450199],[116.52185542949368,39.78405035450199],[116.52184786234288,39.78419097579932]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null},{\"id\":4486,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55697531258555,39.78706011594162],[116.5569191538343,39.78665805700124],[116.55742372345047,39.78691087615565],[116.55697531258555,39.78706011594162]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"ceshi0001\",\"JDZ0027\",\"WWWTest\",\"JDZ0029\"],\"weekLimit\":null,\"timeLimit\":null},{\"id\":4498,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55515827245108,39.78392761509751],[116.55539672730247,39.78392367364611],[116.55534351835794,39.78385075773321],[116.55515827245108,39.78392761509751]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null},{\"id\":4512,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55441055392036,39.78384197388206],[116.55456840499389,39.7839744082025],[116.55460586113267,39.78394497833719],[116.55456706730614,39.78379247816162],[116.55441055392036,39.78384197388206]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\"],\"weekLimit\":null,\"timeLimit\":null},{\"id\":4493,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55448412860815,39.784087177983956],[116.55434953461412,39.78400056089598],[116.55443476784345,39.78388544609161],[116.5545653720516,39.783978490115565],[116.55448412860815,39.784087177983956]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\",\"JDZ0026\"],\"weekLimit\":null,\"timeLimit\":null},{\"id\":4492,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55503829478036,39.78414903938805],[116.55518705915526,39.7842810127809],[116.55537239875045,39.784074245963474],[116.55503829478036,39.78414903938805]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDZ0024\"],\"weekLimit\":null,\"timeLimit\":null},{\"id\":4517,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55466333742818,39.7841671333776],[116.55462300688052,39.784124073702685],[116.55469093197934,39.78406494246215],[116.55475430856748,39.784090717618284],[116.55466333742818,39.7841671333776]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\",\"JDZ0022\"],\"weekLimit\":null,\"timeLimit\":[{\"startTime\":[1,0],\"endTime\":[22,0]}]},{\"id\":4514,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55993908486194,39.78842260477954],[116.55980375903523,39.78785288682097],[116.56021078425324,39.78803963562363],[116.55993908486194,39.78842260477954]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null},{\"id\":4513,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55827957280776,39.78718457672634],[116.55819332302413,39.78648577745893],[116.55864192248187,39.78746807364636],[116.55827957280776,39.78718457672634]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null}]}";
//        String msg2 = "{\"116.5_39.7\":null}";
        JsonUtils.getObjectMapper().registerModule(new JtsModule());
        HashMap<String, HashSet<EarlyWarningAreaService.AreaLocation>> cacheMap = JsonUtils.readValue(msg, new TypeReference<>() {
        });
        EarlyWarningAreaService.areaCacheMap.putAll(cacheMap);

        VehicleLocationService.VehicleLocation vehicleLocation = new VehicleLocationService.VehicleLocation(39.783939361572266, 116.55458068847656, new Date());
        VehicleLocationService.CACHE.put("JDK8124", vehicleLocation);
        Map<String, Set<EarlyWarningAreaService.AreaLocation>> result = EarlyWarningAreaService.getVehicleInAreaMap();
        System.out.println(result);
    }

    @Test
    @Disabled
    void bindKeyToMapVariable() {
        Set<EarlyWarningAreaService.AreaLocation> areaSet = new HashSet<>();
        String msg = "{\"116.5_39.7\":[{\"id\":4483,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55301151832279,39.783193333172164],[116.5539161717867,39.78220301177156],[116.5550881093177,39.78284723476016],[116.55301151832279,39.783193333172164]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null},{\"id\":4521,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55401232716353,39.783442661542345],[116.55433932178192,39.78317357223314],[116.55387948557951,39.78322807153111],[116.55401232716353,39.783442661542345]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\",\"JDZ0022\"],\"weekLimit\":null,\"timeLimit\":[{\"startTime\":[3,0],\"endTime\":[23,0]}]},{\"id\":4490,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.52184786234288,39.78419097579932],[116.52169382221894,39.78405035450199],[116.52185542949368,39.78405035450199],[116.52184786234288,39.78419097579932]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null},{\"id\":4486,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55697531258555,39.78706011594162],[116.5569191538343,39.78665805700124],[116.55742372345047,39.78691087615565],[116.55697531258555,39.78706011594162]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"ceshi0001\",\"JDZ0027\",\"WWWTest\",\"JDZ0029\"],\"weekLimit\":null,\"timeLimit\":null},{\"id\":4498,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55515827245108,39.78392761509751],[116.55539672730247,39.78392367364611],[116.55534351835794,39.78385075773321],[116.55515827245108,39.78392761509751]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null},{\"id\":4512,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55441055392036,39.78384197388206],[116.55456840499389,39.7839744082025],[116.55460586113267,39.78394497833719],[116.55456706730614,39.78379247816162],[116.55441055392036,39.78384197388206]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\",\"JDZ0022\"],\"weekLimit\":null,\"timeLimit\":[{\"startTime\":[2,0],\"endTime\":[23,0]}]},{\"id\":4492,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55503829478036,39.78414903938805],[116.55518705915526,39.7842810127809],[116.55537239875045,39.784074245963474],[116.55503829478036,39.78414903938805]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDZ0024\"],\"weekLimit\":null,\"timeLimit\":null},{\"id\":4517,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55466333742818,39.7841671333776],[116.55462300688052,39.784124073702685],[116.55469093197934,39.78406494246215],[116.55475430856748,39.784090717618284],[116.55466333742818,39.7841671333776]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\",\"JDZ0022\"],\"weekLimit\":null,\"timeLimit\":[{\"startTime\":[1,0],\"endTime\":[22,0]}]},{\"id\":4493,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55448412860815,39.784087177983956],[116.55434953461412,39.78400056089598],[116.55443476784345,39.78388544609161],[116.5545653720516,39.783978490115565],[116.55448412860815,39.784087177983956]]]},\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\",\"JDZ0026\",\"JDZ0002\",\"JDZ0022\"],\"weekLimit\":null,\"timeLimit\":null},{\"id\":4514,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55993908486194,39.78842260477954],[116.55980375903523,39.78785288682097],[116.56021078425324,39.78803963562363],[116.55993908486194,39.78842260477954]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null},{\"id\":4513,\"type\":\"ATTENTION_REGION_WARN\",\"remark\":null,\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55827957280776,39.78718457672634],[116.55819332302413,39.78648577745893],[116.55864192248187,39.78746807364636],[116.55827957280776,39.78718457672634]]]},\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null,\"weekLimit\":null,\"timeLimit\":null}]}";
        JsonUtils.getObjectMapper().registerModule(new JtsModule());
        HashMap<String, HashSet<EarlyWarningAreaService.AreaLocation>> cacheMap = JsonUtils.readValue(msg, new TypeReference<>() {
        });
        EarlyWarningAreaService.areaCacheMap.putAll(cacheMap);

        String variableMsg = "{\"id\":4512,\"mapId\":149,\"mapVersion\":240215,\"variableVersion\":20240183,\"type\":\"ATTENTION_REGION_WARN\",\"geometryWgs84\":{\"type\":\"Polygon\",\"coordinates\":[[[116.55441055392036,39.78384197388206],[116.55456840499389,39.7839744082025],[116.55460586113267,39.78394497833719],[116.55456706730614,39.78379247816162],[116.55441055392036,39.78384197388206]]]},\"status\":\"RS_INUSE\",\"effectDateType\":\"CYCLICAL_TERM\",\"expireTime\":2036727598221,\"remark\":null,\"weekList\":null,\"timeLimitList\":[{\"startTime\":[2,0],\"endTime\":[23,0]}],\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8124\",\"JDZ0022\"]}";
        MapVariableApiJsfDTO mapVariable = JsonUtils.readValue(variableMsg, MapVariableApiJsfDTO.class);
        Set<String> keySet = EarlyWarningAreaService.bindKeyToMapVariable(mapVariable);
        System.out.println(keySet);
    }
}