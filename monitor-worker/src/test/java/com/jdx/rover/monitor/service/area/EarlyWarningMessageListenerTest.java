/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.area;

import com.jdx.rover.monitor.service.listener.redis.EarlyWarningMessageListener;
import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/19
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest(classes = MonitorWorkerApplication.class)
class EarlyWarningMessageListenerTest {
    private final EarlyWarningMessageListener earlyWarningMessageListener;

    @Test
    @Disabled
    void onMessage() {
        String msg = "";
        earlyWarningMessageListener.onMessage("", msg);
    }
}