package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.monitor.service.VehiclePncService;
import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncTrafficLightInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.IntersectionStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = MonitorWorkerApplication.class)
@Slf4j
class VehicleSchedulePncInfoManagerTest {
  @Autowired
  private VehiclePncService vehiclePncService;

  @Test
  void testHandleVehicleSchedulePncInfo() throws Exception{
    VehiclePncInfoDTO pncInfoDTO = new VehiclePncInfoDTO();
    pncInfoDTO.setVehicleName("JD0001");
    pncInfoDTO.setIntersectionStatus(IntersectionStatusEnum.IN_INTERSECTION.getValue());
    pncInfoDTO.setGlobalMileage(1.1);
    pncInfoDTO.setNaviRefreshId(1);
    VehiclePncTrafficLightInfoDTO vehiclePncTrafficLightInfoDTO = new VehiclePncTrafficLightInfoDTO();
    vehiclePncTrafficLightInfoDTO.setId("11");
    pncInfoDTO.setTrafficLightInfo(Lists.newArrayList(vehiclePncTrafficLightInfoDTO));
    pncInfoDTO.setIntersectionStatus(IntersectionStatusEnum.IN_INTERSECTION.getValue());
    vehiclePncService.handleVehicleSchedulePncInfo(pncInfoDTO);
    log.info("执行完成!");
  }
}