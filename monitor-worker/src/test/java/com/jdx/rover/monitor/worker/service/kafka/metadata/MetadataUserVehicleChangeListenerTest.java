/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.service.kafka.metadata;

import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import com.jdx.rover.monitor.worker.service.jmq.metadata.MetadataUserVehicleJmqConsumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/4
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest(classes = MonitorWorkerApplication.class)
class MetadataUserVehicleChangeListenerTest {
    private final MetadataUserVehicleJmqConsumer metadataUserVehicleChangeListener;
    @Test
    void onMessage() throws Exception{
        String msg = "{\"increaseUserNameList\":[],\"decreaseUserNameList\":[\"liuwenwen52\"],\"vehicleNameList\":[\"JDXT001\",\"JDXT002\",\"sxc001\",\"JIXIANG\",\"zh0010\",\"lyh01\",\"sunyu001\",\"JDE0017\",\"lhqTest02\",\"chashuyunying\",\"lyh009\",\"jixiang22\",\"lusiyao6917\",\"ceshi0001\",\"LYH001\",\"yh001test3\",\"JDXT0003\",\"T-Vehicle-ffe835\",\"T-Vehicle-fec8a9\",\"T-Vehicle-2ac787\",\"T-Vehicle-fec643\",\"T-Vehicle-ff6261\",\"T-Vehicle-ffd35c\",\"T-Vehicle-ff72f5\",\"T-Vehicle-ff3ee2\",\"zh001test\",\"lixiaoTest\",\"bianmeng\",\"gx007\",\"zmy001\",\"wgt04203\",\"wgt04201452\",\"lhqsmctest3\",\"sxc002\",\"zmy009\",\"lhqsmctest5\",\"lhqsmc101\",\"lhqsmc102\",\"lhqsmc103\",\"sxc003\",\"JD0018\",\"JDES0001\",\"ykcTest\",\"lyh0526\",\"lyh9999\",\"lyh787\",\"lyhTEST\",\"JD0017\",\"JD0015\",\"jznTest\",\"pscLYH\",\"JDZ0027\",\"lyhZZZ\",\"JD100\",\"smc001\",\"smc0822\",\"dadatest001\",\"Test1\",\"wenti\",\"JDX3001\",\"JDZ0029\",\"JDZ0024\",\"smcliyahui\",\"psclyf001\",\"psclyf002\",\"JDZ0026\",\"lyhTest1\",\"lhqTest54\",\"autotest001\",\"lhqTest10\",\"liyahui0515\",\"6666666666\",\"TQ-lyh\",\"WWWTest\",\"88886QQQ\",\"WHHHHHG\",\"whh004\",\"bzliaohq2\",\"NlhqTest2\",\"zys727\",\"lyh09250925\",\"JTHN\",\"0101\"]}";
        ConsumerRecord<String, String> record = new ConsumerRecord<>("topic", 1, 1, "1", msg);
        Message message = new Message();
        message.setText(msg);
        metadataUserVehicleChangeListener.onMessage(Lists.newArrayList(message));
    }
}