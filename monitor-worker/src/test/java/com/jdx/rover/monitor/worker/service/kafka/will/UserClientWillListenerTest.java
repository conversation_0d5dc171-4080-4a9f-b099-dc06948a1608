/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.service.kafka.will;

import com.jdx.rover.monitor.worker.MonitorWorkerApplication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest(classes = MonitorWorkerApplication.class)
class UserClientWillListenerTest {
    private final UserClientWillListener userClientWillListener;

    @Test
    void onMessage() {

        String msg = "{\"header\":{\"requestId\":1658238662844000002, \"clientName\":\"JSC0001\", \"requestTime\":1658238662844, \"needResponse\":false}, \"data\":{\"online\":false, \"steeringStatus\":\"OFFLINE\", \"brakeStatus\":\"OFFLINE\"}}";
        ConsumerRecord<String, String> record = new ConsumerRecord<>("topic", 1, 1, "1", msg);
        userClientWillListener.onMessage(record);
    }
}