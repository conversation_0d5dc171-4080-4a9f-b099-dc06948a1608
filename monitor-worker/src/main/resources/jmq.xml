<?xml version="1.0" encoding="UTF-8"?>
<beans
        xmlns="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:jmq="http://code.jd.com/schema/jmq" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://code.jd.com/schema/jmq http://code.jd.com/schema/jmq/jmq-1.1.xsd">
    <!--配置transport，每个transport实例对应一个APP，同一APP如果生产和消费多个主题，只需要配置一个transport实例即可-->
    <jmq:transport address="${jmq.address}" password="${jmq.password}" app="${jmq.app}" user="${jmq.app}"/>
    <!--配置producer-->
    <jmq:producer id="producer" retryTimes="2" transport="jmq.transport"/>
    <!--配置Consumer，messageListener bean需要实现com.jd.jmq.client.consumer.MessageListener接口，在onMessage方法中接收消息。-->
    <jmq:consumer id="consumer" transport="jmq.transport">
        <jmq:listener topic="${monitor.jmq.topic.intelligent-device-message}" listener="metadataDeviceChangeListener"/>
        <jmq:listener topic="${monitor.jmq.topic.transport-property-change}" listener="transportDeviceStatusChangeListener"/>
        <jmq:listener topic="${monitor.jmq.topic.transport-events-change}" listener="transportDeviceEventsJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.transport-services-reply}" listener="transportDeviceServicesReplyListener"/>
        <jmq:listener topic="${monitor.jmq.topic.transport-device-property}" listener="transportDevicePropertyMessageListener"/>
        <jmq:listener topic="${monitor.jmq.topic.schedule-schedule-change}" listener="monitorScheduleJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.schedule-order-change}" listener="monitorScheduleOrderJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.accident-flow-event}" listener="monitorAccidentFlowEventJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.vehicle-map-alarm}" listener="monitorVehicleMapAlarmJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.vehicle-manual-alarm}" listener="monitorVehicleManualAlarmJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.vehicle-event-change}" listener="monitorVehicleAlarmEventJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.map-variable-upgrade}" listener="mapVaiableUpgradeJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.monitor_remote_control_command}" listener="monitorControlCommandJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.monitor_remote_command_log}" listener="monitorRemoteCommandLogJmqConsumer"/>

        <jmq:listener topic="${monitor.jmq.topic.metadata-cockpit-team-change}" listener="metadataCockpitTeamJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-cockpit-vehicle-change}" listener="metadataCockpitVehicleJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-error-code-change}" listener="metadataErrorCodeTranslateJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-require-info-change}" listener="metadataRequireInfoJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-user-station-change}" listener="metadataUserStationJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-user-stop-change}" listener="metadataUserStopJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-user-vehicle-change}" listener="metadataUserVehicleJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-vehicle-basic-change}" listener="metadataVehicleBasicInfoJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-vehicle-exception-change}" listener="metadataVehicleExceptionJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-integrate-station-change}" listener="metadataGroupInfoJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-device-state-change}" listener="metadataDeviceStateJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.metadata-wh-work-mode-message}" listener="metadataDeviceWorkModeJmqConsumer"/>

        <jmq:listener topic="${monitor.jmq.topic.ticket-issue-change}" listener="ticketIssueChangeJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.ticket-issue-cockpit-change}" listener="ticketIssueCockpitListJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.ticket-issue-today-statistic}" listener="ticketIssueTodayStatisticJmqConsumer"/>

        <jmq:listener topic="${monitor.jmq.topic.server-guardian-alarm}" listener="serverGuardianAlarmJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-guardian-connect}" listener="serverGuardianConnectJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-guardian-pnc-info}" listener="serverGuardianPncInfoJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-guardian-power}" listener="serverGuardianPowerJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-local-view}" listener="serverLocalViewJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-mqtt-topic}" listener="serverMqttTopicJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-pdu-will}" listener="serverPduWillJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-power-manager}" listener="serverPowerManagerJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-report-abnormal}" listener="serverReportAbnormalJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-report-boot}" listener="serverReportBootJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.vehicle-status-change}" listener="serverVehicleChangeStatusJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-drive-will}" listener="serverDriveWillJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.server-hardware-event}" listener="serverHardwareEventJmqConsumer"/>

        <jmq:listener topic="${monitor.jmq.topic.shadow-video-task-result}" listener="shadowVideoEventJmqConsumer"/>

        <jmq:listener topic="${monitor.jmq.topic.reply_r_drive_control_connect_server}" listener="cockpitConnectVehicleJmqListener"/>
        <jmq:listener topic="${monitor.jmq.topic.monitor_map_collection_takeover}" listener="monitorMapCollectionTakeoverJmqListener"/>

        <jmq:listener topic="${monitor.jmq.topic.schedule-pnc-route}" listener="monitorSchedulePncJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.monitor_robot_change_event}" listener="monitorRobotAlarmEventJmqConsumer"/>

        <jmq:listener topic="${monitor.jmq.topic.integrate_task_info_message}" listener="integrateDeviceTaskJmqConsumer"/>
        <jmq:listener topic="${monitor.jmq.topic.amr_congestion_info_message}" listener="integrateCongestionInfoJmqConsumer"/>

    </jmq:consumer>
</beans>