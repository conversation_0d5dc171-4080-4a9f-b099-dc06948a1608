<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:laf-config="http://ducc.jd.com/schema/laf-config"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
	    http://ducc.jd.com/schema/laf-config http://ducc.jd.com/schema/laf-config/laf-config.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd">

    <laf-config:manager id="configuratorManager" application="jdos_rover-auto-monitor-worker">
        <!--
        可以有多个 resource； 每个 resource 通常对应一个 ducc profile；
        name 表示资源名称，ducc 要求 这个 name 进程内唯一；
        uri 是配置定位，可以在 ducc 管理端-配置页面上，点击按钮生成这个 uri 的模板，然后把 appName 和 token 修改成实际值即可；
        -->
        <laf-config:parameter key="lafValueDefaultValueEnable" value="true"/>
        <!-- 开启全局监听器  注解方式需要开启这个-->
        <!-- <laf-config:parameter key="autoListener" value="true"/>-->
        <laf-config:resource name="monitor.drive.lane.region" uri="ucc://${ducc.config.name}:${ducc.config.token}@${ducc.config.url}/v1/namespace/rover_auto_monitor_worker/config/monitor_dongdong_notice/profiles/${ducc.config.profile}?longPolling=60000&amp;necessary=true"/>

    </laf-config:manager>


</beans>