server:
  port: 8082
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: **************************************************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  data:
    redis:
      host: redis-yh7thvwgyt4s-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
      database: 8
  kafka:
    bootstrap-servers: broker-kafka-pqxxtsd6av-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-2.jvessel-open-hb.jdcloud.com:9092
jmq:
  address: nameserver.jmq.jd.local:80
monitor-notify:
  baseUser: gulin21,zhaoyusha1,liuxiurong3,shaojinyu,liuyafei11
  monitorUrl: https://jdxmonitor-test3.jdl.cn/app/single?license=
jdd:
  easyjob:
    enable: true
    host: http://schedule.jdfin.local
    appId: monitor-worker-test
    secret: eb2c6464346de2a3507992017f7f95d2