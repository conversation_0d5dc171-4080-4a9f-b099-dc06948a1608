server:
  port: 8082
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: **************************************************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  data:
    redis:
      host: redis-hb3hpfz16cbi-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
      database: 8
  kafka:
    bootstrap-servers: broker-kafka-poiybufu79-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-2.jvessel-open-hb.jdcloud.com:9092
jmq:
  address: nameserver.jmq.jd.local:80
monitor-notify:
  baseUser: gulin21
  monitorUrl: https://jdxmonitor-beta.jdl.cn/app/single?license=
jdd:
  easyjob:
    enable: true
    host: http://schedule.jdfin.local
    secret: 316dea8940776107c377a645a05c29c1
rover-video:
  snapshotUrl: http://rover-video-process-staging.jd.local/video/process/snapshot/realtime
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
accident:
  url: https://jdxmonitor-beta.jdl.cn/app/accidentmanage
