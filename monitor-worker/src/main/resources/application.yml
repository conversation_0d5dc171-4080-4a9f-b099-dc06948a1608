spring:
  application:
    name: rover-monitor-worker
  profiles:
    active: "@activatedProperties@"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  datasource:
    dynamic:
      primary: mysql
      strict: false
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          druid:
            initial-size: 1
            max-active: 15
            max-wait: 60000 #缺省使用公平锁
            min-idle: 1 #最小连接池数量
            max-evictable-idle-time-millis: 600000 #连接最大生存时间, ms
            min-evictable-idle-time-millis: 300000 #连接最小生存时间, ms，默认300s
            test-on-borrow: false
            test-on-return: false #默认值,不配置
            test-while-idle: true
            validation-query: SELECT 1 FROM DUAL
            validation-query-timeout: 10000
  data:
    redis:
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
  kafka:
    producer:
      retries: 5
    consumer:
      enable-auto-commit: true
      auto-commit-interval: 1000
      group-id: ${spring.application.name}
  cache:
    type: caffeine
    caffeine:
      spec: "maximumSize=500,expireAfterWrite=600s"
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回
jmq:
  password: 06fe248cde7b40a2b1feb4a166e4d04b
  app: monitorworkertest
  address: test-nameserver.jmq.jd.local:50088

monitor:
  jmq:
    topic:
      transport-property-change: transport_property_change_${spring.profiles.active}
      transport-events-change: transport_device_events_${spring.profiles.active}
      intelligent-device-message: i_device_device_message_${spring.profiles.active}
      transport-services-reply: transport_device_services_reply_${spring.profiles.active}
      transport-device-property: transport_device_property_${spring.profiles.active}
      metadata-integrate-station-change: metadata_integrate_station_message_${spring.profiles.active}
      schedule-schedule-change: server_schedule_task_${spring.profiles.active}
      schedule-order-change: server_schedule_monitor_order_task_${spring.profiles.active}
      accident-flow-event: monitor_accident_flow_event_${spring.profiles.active}
      vehicle-event-change: monitor_vehicle_change_event_${spring.profiles.active}
      vehicle-map-alarm: map_vehicle_alarm_${spring.profiles.active}
      vehicle-manual-alarm: monitor_manual_vehicle_alarm_${spring.profiles.active}
      metadata-cockpit-team-change: metadata_cockpit_team_message_${spring.profiles.active}
      metadata-cockpit-vehicle-change: metadata_cockpit_vehicle_message_${spring.profiles.active}
      metadata-error-code-change: metadata_error_code_translate_change_message_${spring.profiles.active}
      metadata-require-info-change: metadata_vehicle_require_info_${spring.profiles.active}
      metadata-user-station-change: metadata_user_station_message_${spring.profiles.active}
      metadata-user-stop-change: metadata_stop_message_${spring.profiles.active}
      metadata-user-vehicle-change: operation_user_vehicle_change_${spring.profiles.active}
      metadata-vehicle-basic-change: metadata_vehicle_message_${spring.profiles.active}
      metadata-vehicle-exception-change: metadata_vehicle_exception_message_${spring.profiles.active}
      metadata-device-state-change: metadata_device_status_message_${spring.profiles.active}
      metadata-wh-work-mode-message: metadata_wh_work_mode_message_${spring.profiles.active}
      ticket-issue-change: issue_full_info_sync_${spring.profiles.active}
      ticket-issue-cockpit-change: issue_cockpit_list_${spring.profiles.active}
      ticket-issue-today-statistic: issue_today_statistic_${spring.profiles.active}
      server-guardian-alarm: server_guardian_alarm_${spring.profiles.active}
      server-guardian-connect: server_guardian_connect_${spring.profiles.active}
      server-guardian-pnc-info: server_guardian_pnc_${spring.profiles.active}
      server-guardian-power: server_guardian_power_${spring.profiles.active}
      server-local-view: server_decrease_local_view_${spring.profiles.active}
      server-mqtt-topic: mqtt_system_disconnected_${spring.profiles.active}
      server-pdu-will: r_pdu_will_${spring.profiles.active}
      server-drive-will: r_drive_will_${spring.profiles.active}
      server-power-manager: server_power_manager_${spring.profiles.active}
      server-report-abnormal: server_report_abnormal_${spring.profiles.active}
      server-report-boot: server_report_boot_${spring.profiles.active}
      vehicle-status-change: server_vehicle_change_status_${spring.profiles.active}
      server-hardware-event: server_report_hardware_event_${spring.profiles.active}
      shadow-video-task-result: rover_shadow_event_task_result_${spring.profiles.active}
      map-variable-upgrade: map_variable_version_upgrade_${spring.profiles.active}
      monitor_vehicle_remote_command_operation: vehicle_remote_command_operation_${spring.profiles.active}
      monitor_remote_control_command: monitor_remote_control_command_${spring.profiles.active}
      monitor_remote_command_log: monitor_remote_command_log_${spring.profiles.active}
      reply_r_drive_control_connect_server: reply_r_drive_control_connect_server_${spring.profiles.active}
      monitor_map_collection_takeover: monitor_map_collection_takeover_${spring.profiles.active}
      schedule-pnc-route: server_schedule_pnc_route_${spring.profiles.active}
      monitor_robot_change_event: monitor_robot_change_event_${spring.profiles.active}
      integrate_task_info_message: integrate_task_info_message_${spring.profiles.active}
      amr_congestion_info_message: amr_congestion_info_message_${spring.profiles.active}
    provider:
      topic:
        monitor_cockpit_realtime_status_change: cockpit_realtime_status_change_${spring.profiles.active}
        monitor_remote_command_log: monitor_remote_command_log_${spring.profiles.active}
        shadow_tracking_event: rover_shadow_tracking_event_${spring.profiles.active}
        shadow_jira_event: rover_shadow_jira_event_${spring.profiles.active}
        monitor_pnc_route: monitor_pnc_route_${spring.profiles.active}
        server_web_terminal_command_down: server_web_terminal_command_down_${spring.profiles.active}
        monitor_vehicle_remote_command_operation: vehicle_remote_command_operation_${spring.profiles.active}
        monitor_remote_control_command: monitor_remote_control_command_${spring.profiles.active}
        monitor_manual_vehicle_alarm: monitor_manual_vehicle_alarm_${spring.profiles.active}
        monitor_view_order_detail: monitor_view_order_detail_${spring.profiles.active}
        monitor_vehicle_change_event: monitor_vehicle_change_event_${spring.profiles.active}
        monitor_single_vehicle_realtime: monitor_single_vehicle_realtime_${spring.profiles.active}
        monitor_single_vehicle_schedule: monitor_single_vehicle_schedule_${spring.profiles.active}
        monitor_accident_flow_event: monitor_accident_flow_event_${spring.profiles.active}
        map_vehicle_alarm: map_vehicle_alarm_${spring.profiles.active}
        monitor_robot_change_event: monitor_robot_change_event_${spring.profiles.active}

#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:sqlmap/*.xml
  type-handlers-package: com.jdx.rover.monitor.domain.handler
  configuration:
    auto-mapping-behavior: FULL
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
s3:
  access-key: 85C76AB2AB89077E8500CB6021A81EC7
  secret-key: EEDD3436DF9B9ED075A39DEE468F66A2
  endpoint: https://s3-internal.cn-north-1.jdcloud-oss.com
  out-endpoint: https://s3.cn-north-1.jdcloud-oss.com
  region: cn-north-1
  signer-override: AWSS3V4SignerType
logging:
  config: classpath:log4j2.xml
crash-notify-user:
  baseUser: gulin21
jira-service:
  userName: org.jdx.data1
  passWord: Zhenxinaini%521
project:
  quartz:
    cron:
      singleVehicle: 0/2 * * * * ?
  push:
    interval:
      singleVehicle: 5000
    batch:
      singleVehicle: 5
  local:
    cache:
      localCacheEvictTopic: local:cache:evict
liteflow:
  ruleSource: config/flow.xml

jdd:
  easyjob:
    enable: false
    appId: rover-auto-monitor-worker
#京ME消息推送
jdme:
  api:
    app-access-token: /open-api/auth/v1/app_access_token
    team-access-token: /open-api/auth/v1/team_access_token
    create-group: /open-api/suite/v1/timline/createGroup
    add-group-member: /open-api/suite/v1/timline/addGroupMember
    send-robot-msg: /open-api/suite/v1/timline/sendRobotMsg
    send-jue-msg: /open-api/suite/v1/timline/sendJUEMsg
  robot:
    id: 00_ee824b500481492c
    pin: app.w8jfe2tb
    env: PROD
    host: http://openme.jd.local
    appKey: zhinengche
    appSecret: 124A2644A08669F8
    openTeamId: f65e9fb91c8cca695105735ca51ce2e9
    cardTemplateId: templateMsgCard
ducc:
  config:
    name: jdos_rover-auto-monitor-worker
    token: 8edb245b31664768a79f2aadb59416b5
    url: ducc.jd.local
    profile: ${spring.profiles.active}