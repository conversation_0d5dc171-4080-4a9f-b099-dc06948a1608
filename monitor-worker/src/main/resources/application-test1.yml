server:
  port: 8082
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: **************************************************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  data:
    redis:
      host: redis-hjhb8wlo35rc-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: Zndh2018
      database: 8
  kafka:
    bootstrap-servers: broker-kafka-klv9p39t1j-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-klv9p39t1j-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-klv9p39t1j-az1-2.jvessel-open-hb.jdcloud.com:9092
jmq:
  address: nameserver.jmq.jd.local:80
monitor-notify:
  baseUser: gulin21,shaojinyu
  monitorUrl: https://jdxmonitor-test1.jdl.cn/app/single?license=
jdd:
  easyjob:
    enable: true
    host: http://schedule.jdfin.local
    appId: monitor-worker-test
    secret: eb2c6464346de2a3507992017f7f95d2
rover-video:
  snapshotUrl: http://rover-video-process-dev.jd.local/video/process/snapshot/realtime
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
accident:
  url: https://jdxmonitor-test1.jdl.cn/app/accidentmanage