/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker;

import com.bedatadriven.jackson.datatype.jts.JtsModule;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.service.jira.MonitorJiraService;
import com.jdx.rover.monitor.service.web.MonitorBasicInfoService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动类
 *
 * <AUTHOR> shiling
 */
@EnableCaching
@EnableDiscoveryClient
@SpringBootApplication
@EnableAsync
@EnableAspectJAutoProxy(proxyTargetClass = true)
@ComponentScan(basePackages = {"com.jdx.rover.monitor.domain"
    , "com.jdx.rover.monitor.repository", "com.jdx.rover.monitor.manager"
    , "com.jdx.rover.monitor.service", "com.jdx.rover.monitor.worker"}
    , excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE
    , value = {MonitorJiraService.class, MonitorBasicInfoService.class})})
@EnableFeignClients(basePackages = {"com.jdx.rover.monitor.repository.feign"})
@ImportResource(value = {"classpath:jmq.xml", "classpath:ducc.xml"})
@MapperScan("com.jdx.rover.monitor.repository")
@Slf4j
@EnableScheduling
public class MonitorWorkerApplication {

  public static void main(String[] args) {
    JsonUtils.getObjectMapper().registerModule(new JtsModule());
    SpringApplication.run(MonitorWorkerApplication.class, args);
  }
}
