/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.config;

import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.area.EarlyWarningAreaService;
import com.jdx.rover.monitor.service.listener.redis.EarlyWarningMessageListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/22
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class EarlyWarningAreaConfig {
    private final EarlyWarningAreaService earlyWarningAreaService;

    @PostConstruct
    public void initMapArea() {
        earlyWarningAreaService.initMapArea();
        String topicName = RedisTopicEnum.MAP_VARIABLE_VERSION_UPGRADE.getValue();
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        EarlyWarningMessageListener messageListener = new EarlyWarningMessageListener();
        int listenerId = rTopic.addListener(String.class, messageListener);
        log.info("EarlyWarningAreaConfig add topic={},listener count={},subscribers={},listenerId={}", topicName
                , rTopic.countListeners(), rTopic.countSubscribers(), listenerId);
    }
}
