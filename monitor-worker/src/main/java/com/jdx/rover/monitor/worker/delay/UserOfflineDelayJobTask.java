/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.delay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.monitor.entity.user.UserClientOnlineDO;
import com.jdx.rover.monitor.manager.delay.DelayJob;
import com.jdx.rover.monitor.manager.delay.DelayJobTask;
import com.jdx.rover.monitor.repository.redis.user.UserClientOnlineRepository;
import com.jdx.rover.monitor.service.cockpit.CockpitChangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 用户离线延迟任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class UserOfflineDelayJobTask implements DelayJobTask {

    private final UserClientOnlineRepository userClientOnlineRepository;

    private final CockpitChangeService cockpitChangeService;

    @Override
    public void execute(DelayJob delayJob) {
        log.info("执行用户延迟离线任务开始={}", delayJob);
        UserClientOnlineDO jobParam = BeanUtil.toBean(delayJob.getJobParam(), UserClientOnlineDO.class);
        String userName = jobParam.getUserName();
        String clientName = jobParam.getClientName();
        Map<String, UserClientOnlineDO> map = userClientOnlineRepository.get(userName);
        log.info("执行用户延迟离线任务用户客户端明细={}", map);
        if (MapUtils.isEmpty(map)) {
            return;
        }

        UserClientOnlineDO userClientOnlineDO = map.get(clientName);
        if (Objects.isNull(userClientOnlineDO)) {
            return;
        }

        Date startTime = DateUtil.offsetMinute(new Date(), -1);
        // 存在1分钟以内的登录,直接返回,不把用户设置为离线
        if (userClientOnlineDO.getStartTime().after(startTime)) {
            return;
        }

        userClientOnlineRepository.fastRemoveByMapKey(userName, clientName);
        if (map.size() > 1) {
            log.info("存在多于1个客户端,不执行离线任务={}", map);
            return;
        }

        for (UserClientOnlineDO value : map.values()) {
            // 存在1分钟以内的登录,直接返回,不把用户设置为离线
            if (value.getStartTime().after(startTime)) {
                log.info("存在1个1分钟内登录客户端,不执行离线任务={}", map);
                return;
            }
        }
        cockpitChangeService.forceQuitCockpit(userName);

        log.info("执行用户延迟离线任务完成,在线用户={},客户端明细={}", userName, userClientOnlineRepository.get(userName));
    }
}
