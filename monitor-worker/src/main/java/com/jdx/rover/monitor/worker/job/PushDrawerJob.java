/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.worker.job;

import com.jdx.rover.monitor.service.drawer.DrawerService;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 推送抽屉任务JOB,5s一次
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PushDrawerJob implements ScheduleFlowTask {

    private final DrawerService drawerService;

    @Override
    public TaskResult doTask(ScheduleContext scheduleContext) {
        drawerService.pushAllDrawer();
        return TaskResult.success();
    }
}