/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.service.report.ReportAbnormalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 接收report车辆异常信息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerReportAbnormalJmqConsumer implements MessageListener {
    /**
     * 报告异常服务的实例，用于处理接收到的车辆异常信息。
     */
    private final ReportAbnormalService reportAbnormalService;

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        List<String> messageList = new ArrayList<>();
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            messageList.add(message.getText());
        }
        try {
            reportAbnormalService.handleMessage(messageList);
        } catch (Exception e) {
            log.error("处理车辆异常消息失败!{}", messageList.size(), e);
        }

    }

}


