package com.jdx.rover.monitor.worker.service.vehicle;

import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.service.cockpit.CockpitScoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleSortService {
  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;
  @Autowired
  private MetadataVehicleApiManager metadataVehicleApiManager;
  @Autowired
  private InitScoreService initScoreService;
  @Autowired
  private BasicInfoScoreService basicInfoScoreService;
  @Autowired
  private RealtimeScoreService realtimeScoreService;
  @Autowired
  private CockpitScoreService cockpitScoreService;

  /**
   * 初始化用户车辆名称数据
   *
   * @param username
   */
  public void initUserSortData(String username) {
    List<String> vehicleNameList = metadataVehicleApiManager.getVehicleInUser(username);
    userVehicleNameRepository.initSet(username, vehicleNameList);
  }

  /**
   * 初始化所有车辆基础数据
   */
  public void initAllVehicleBasicInfo() {
    initScoreService.initAllVehicleBasicInfo();
    cockpitScoreService.initScore();
  }

  /**
   * 初始化所有车辆基础数据
   */
  public void updateVehicleBasicInfo(String vehicleName) {
    basicInfoScoreService.updateVehicleBasicInfo(vehicleName);

    cockpitScoreService.updateScore(vehicleName);
  }

  /**
   * 初始化辆实时数据
   */
  public void updateVehicleRealtimeInfo(String vehicleName) {
    realtimeScoreService.updateVehicleRealtime(vehicleName);
  }
}
