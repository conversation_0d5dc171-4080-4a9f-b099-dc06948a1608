/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.user.UserClientOnlineDO;
import com.jdx.rover.monitor.manager.delay.DelayJob;
import com.jdx.rover.monitor.manager.delay.DelayJobProducer;
import com.jdx.rover.monitor.repository.redis.user.UserClientOnlineRepository;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttOnline;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * PDU遗嘱监听
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerDriveWillJmqConsumer implements MessageListener {

    /**
     * 用户离线延迟任务的任务常量
     */
    private static final String USER_OFFLINE_DELAY_JOB_TASK = "userOfflineDelayJobTask";
    /**
     * 用户在线状态缓存
     */
    private final UserClientOnlineRepository userClientOnlineRepository;

    /**
     * 处理接收到的 MQTT 消息。
     * @param messages 接收到的消息列表。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理MQTT变化消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息。
     * @param message 要处理的消息。
     */
    private void handleOneMessage(String message) {
        MqttRequestDTO<MqttOnline> mqttRequestDTO = JsonUtils.readValue(message, new TypeReference<>() {
        });
        if (mqttRequestDTO == null) {
            return;
        }
        String clientName = mqttRequestDTO.getHeader().getClientName();
        String userName = StringUtils.substringBefore(clientName, "_monitor_");
        if (StringUtils.isBlank(userName)) {
            return;
        }
        if (mqttRequestDTO.getData().getOnline()) {
            UserClientOnlineDO userClientOnlineDO = new UserClientOnlineDO();
            userClientOnlineDO.setUserName(userName);
            userClientOnlineDO.setClientName(clientName);
            if (!Objects.isNull(mqttRequestDTO.getHeader().getRequestTime())) {
                userClientOnlineDO.setStartTime(new Date(mqttRequestDTO.getHeader().getRequestTime()));
            }
            userClientOnlineDO.setRecordTime(new Date());
            userClientOnlineRepository.putByMapKey(userName, clientName, userClientOnlineDO);

            // 取消离线任务
            DelayJob delayJob = getDelayJob(userName, clientName);
            DelayJobProducer.cancelJob(delayJob);

            log.info("当前在线用户客户端明细={}", userClientOnlineRepository.get(userName));
        } else {
            disconnectedClient(userName, clientName);
        }
    }

    public synchronized void disconnectedClient(String userName, String clientName) {
        Map<String, UserClientOnlineDO> userMap = userClientOnlineRepository.get(userName);
        if (!MapUtils.isEmpty(userMap) && userMap.size() > 1) {
            userClientOnlineRepository.fastRemoveByMapKey(userName, clientName);
            log.info("存在多于1个客户端,不加入离线任务={}", userMap);
        } else {
            DelayJob delayJob = getDelayJob(userName, clientName);
            DelayJobProducer.submitJob(delayJob, 1L, TimeUnit.MINUTES);
            log.info("加入用户离线任务={}", userMap);
        }
    }


    private static DelayJob getDelayJob(String userName, String clientName) {
        Map<String, Object> paramMap = new ParamMap<UserClientOnlineDO>()
                .addProperty(UserClientOnlineDO::getUserName, userName)
                .addProperty(UserClientOnlineDO::getClientName, clientName)
                .toMap();
        return new DelayJob(paramMap, USER_OFFLINE_DELAY_JOB_TASK);
    }
}
