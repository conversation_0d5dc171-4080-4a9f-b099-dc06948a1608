package com.jdx.rover.monitor.worker.service.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleOrderForMonitor;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 接收订单信息变更信息
 *
 * <AUTHOR>
 * @date 2024/12/5
 */
@Service
@Slf4j
public class MonitorScheduleOrderJmqConsumer implements MessageListener {

  @Resource
  private FlowExecutor flowExecutor;

  /**
   * 消费订单信息变更
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      ScheduleOrderForMonitor scheduleTaskOrder = JsonUtils.readValue(message.getText(), ScheduleOrderForMonitor.class);
      if (scheduleTaskOrder == null) {
        return;
      }
      String key = RedisKeyEnum.SCHEDULE_UPDATE_LOCK_PREFIX.getValue() + scheduleTaskOrder.getVehicleName();
      try {
        if (RedissonUtils.tryLock(key, 3, TimeUnit.SECONDS)) {
          LiteflowResponse response = flowExecutor.execute2Resp(LiteFlowTaskEnum.UPDATEORDER.getType(), scheduleTaskOrder, ScheduleOrderForMonitor.class);
          if (!response.isSuccess()) {
            log.info("更新调度订单执行失败，{}", response.getCause());
            return;
          }
        } else {
          log.error("Lock schedule order change fail, current count{} ", RedissonUtils.getLockCount(key));
        }
      } catch (Exception e) {
        log.error("Handle schedule order exception ", e);
      } finally {
        RedissonUtils.unLock(key);
        log.info("Unlock schedule order change lock count{} ", RedissonUtils.getLockCount(key));
      }
    }
  }
}
