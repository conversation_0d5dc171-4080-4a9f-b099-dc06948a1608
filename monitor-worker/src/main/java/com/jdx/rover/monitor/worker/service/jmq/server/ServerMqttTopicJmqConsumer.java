/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.worker.service.kafka.will.UserClientWillListener;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * mqtt遗嘱和上线消息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerMqttTopicJmqConsumer implements MessageListener {

    /**
     * 用户客户端遗嘱和上线消息监听器
     */
    private final UserClientWillListener userClientWillListener;

    /**
     * 处理 MQTT 消息。
     * @param messages 消息列表。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理MQTT变化消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个 MQTT 断开消息
     * @param message MQTT 断开消息的 JSON 字符串
     */
    private void handleOneMessage(String message) {
        MqttDisconnected dto = JsonUtils.readValue(message, MqttDisconnected.class);
        if (Objects.isNull(dto)) {
            return;
        }
        if (Objects.isNull(dto.getDisconnected_at())) {
            return;
        }
        if (StringUtils.indexOf(dto.getClientid(), "_monitor_") == -1) {
            return;
        }
        userClientWillListener.disconnectedClient(dto.getUsername(), dto.getClientid());
    }

    @Data
    public static class MqttDisconnected {
        /**
         * 断开连接时间
         */
        private Long disconnected_at;
        /**
         * 客户端名称
         */
        private String clientid;
        /**
         * 用户名称
         */
        private String username;
    }
}

