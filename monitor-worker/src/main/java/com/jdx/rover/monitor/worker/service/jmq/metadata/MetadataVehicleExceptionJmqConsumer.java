/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleBootUpExceptionInfoDTO;
import com.jdx.rover.metadata.jsf.service.vehicle.MetadataVehicleExceptionService;
import com.jdx.rover.monitor.repository.redis.metadata.VehicleBootModuleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 监听异常配置信息变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataVehicleExceptionJmqConsumer implements MessageListener {

    private final MetadataVehicleExceptionService metadataVehicleExceptionService;
    private final VehicleBootModuleRepository vehicleBootModuleRepository;

    /**
     * 批量处理接收到的消息。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息并更新车辆异常信息。
     * @param message 消息内容。
     */
    private void handleOneMessage(String message) {
        HttpResult<List<VehicleBootUpExceptionInfoDTO>> httpResult = metadataVehicleExceptionService.getAllVehicleException();
        if (!HttpResult.isSuccess(httpResult) || CollectionUtils.isEmpty(httpResult.getData())) {
            log.error("返回错误请求!{}", httpResult);
        }
        List<com.jdx.rover.metadata.api.domain.dto.vehicle.VehicleBootUpExceptionInfoDTO> exceptionInfoDtoList = httpResult.getData().stream().map(exceptionInfo -> {
            com.jdx.rover.metadata.api.domain.dto.vehicle.VehicleBootUpExceptionInfoDTO exceptionInfoDto = new com.jdx.rover.metadata.api.domain.dto.vehicle.VehicleBootUpExceptionInfoDTO();
            exceptionInfoDto.setDevice(exceptionInfo.getDevice());
            exceptionInfoDto.setModule(exceptionInfo.getModule());
            exceptionInfoDto.setModuleField(exceptionInfo.getModuleField());
            exceptionInfoDto.setNumber(exceptionInfo.getNumber());
            exceptionInfoDto.setNormalTime(exceptionInfo.getNormalTime());
            exceptionInfoDto.setModuleFieldName(exceptionInfo.getModuleFieldName());
            exceptionInfoDto.setOvertimeThreshold(exceptionInfo.getOvertimeThreshold());
            exceptionInfoDto.setPrincipalErps(exceptionInfo.getPrincipalErps());
            return exceptionInfoDto;
        }).collect(Collectors.toList());
        vehicleBootModuleRepository.set(exceptionInfoDtoList);
    }
}

