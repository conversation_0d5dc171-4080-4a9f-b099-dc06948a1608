/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.worker.delay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jdx.rover.monitor.constant.MapCollectConstant;
import com.jdx.rover.monitor.entity.MapTaskDO;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.mapcollection.CollectionStatusEnum;
import com.jdx.rover.monitor.manager.delay.DelayJob;
import com.jdx.rover.monitor.manager.delay.DelayJobProducer;
import com.jdx.rover.monitor.manager.delay.DelayJobTask;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskTimeRecordManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleMapDistanceRepository;
import com.jdx.rover.monitor.repository.redis.VehicleMapRealRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.service.cockpit.MapCollectionCockpitService;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import java.util.Date;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:18
 * @description 地图采集定时上传子任务
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MapTaskAutoUploadJobTask implements DelayJobTask {

    /**
     * DuccMonitorProperties
     */
    private final DuccConfigProperties duccConfigProperties;

    /**
     * MapCollectionCockpitService
     */
    private final MapCollectionCockpitService mapCollectionCockpitService;

    /**
     * VehicleMapRealRouteRepository
     */
    private final VehicleMapRealRouteRepository vehicleMapRealRouteRepository;

    /**
     * MapCollectionTaskTimeRecordManager
     */
    private final MapCollectionTaskTimeRecordManager mapCollectionTaskTimeRecordManager;

    /**
     * VehicleStatusRepository
     */
    private final VehicleStatusRepository vehicleStatusRepository;

    /**
     * VehicleMapDistanceRepository
     */
    private final VehicleMapDistanceRepository vehicleMapDistanceRepository;

    /**
     * Executor
     */
    private final Executor asyncThreadPool;

    @Override
    public void execute(DelayJob delayJob) {
        MapTaskDO mapTaskDO = BeanUtil.toBean(delayJob.getJobParam(), MapTaskDO.class);
        String vehicleName = mapTaskDO.getVehicleName();
        Integer taskId = mapTaskDO.getTaskId();
        String cockpitNumber = mapTaskDO.getCockpitNumber();
        log.info("地图采集处理延时任务：[{}]", mapTaskDO);

        // 采集任务加锁
        RedissonUtils.tryLock(MapCollectConstant.getTaskLock(taskId), 10, TimeUnit.SECONDS);
        try {
            // 车辆采集状态如果非进行中，则不处理任务
            VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
            log.info("获取车辆状态信息返回结果, vehicleName: [{}] vehicleStatusDO: [{}].", vehicleName, vehicleStatusDO);
            if (ObjectUtil.isNull(vehicleStatusDO) || !CollectionStatusEnum.PROCESSING.getCollectionStatus().equals(vehicleStatusDO.getCollectionStatus())) {
                return;
            }

            // 更新任务记录，推送任务
            Date endTime = mapCollectionCockpitService.persistentRecord(vehicleName, "system", taskId, duccConfigProperties.getOpenXataDataCopy(), false);

            // 清除车辆实时轨迹
            vehicleMapRealRouteRepository.remove(vehicleName);

            // 清除采图车辆行驶距离
//            vehicleMapDistanceRepository.remove(vehicleName);

            // 新增任务记录
            mapCollectionTaskTimeRecordManager.createRecord(taskId, vehicleName, cockpitNumber, "system", endTime);
            DelayJob nextDelayJob = mapCollectionCockpitService.getDelayJob(taskId, vehicleName, cockpitNumber);
            DelayJobProducer.submitJob(nextDelayJob, Long.valueOf(duccConfigProperties.getMapTaskAutoUploadTimeInterval()), TimeUnit.MINUTES);
            log.info("地图采集新增延时任务：[interval:{}, job:{}]", duccConfigProperties.getMapTaskAutoUploadTimeInterval(), nextDelayJob);
        } catch (Exception e) {
            log.error("地图采集延时任务：{}", e.getMessage(), e);
        } finally {
            RedissonUtils.unLock(MapCollectConstant.getTaskLock(taskId));
        }
    }
}
