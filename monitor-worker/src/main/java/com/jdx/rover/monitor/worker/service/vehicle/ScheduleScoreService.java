package com.jdx.rover.monitor.worker.service.vehicle;

import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleSortTypeEnum;
import com.jdx.rover.monitor.repository.redis.*;
import com.jdx.rover.monitor.service.score.UpdateScoreTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 调度分数
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScheduleScoreService {
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;
  @Autowired
  private AllVehicleNameSortRepository allVehicleNameSortRepository;
  @Autowired
  private VehicleBasicRepository vehicleBasicRepository;
  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  /**
   * 初始化车辆基础数据
   */
  public void updateScore(String vehicleName) {
    log.info("初始化单个调度分数开始!={}", vehicleName);
    String alarmEvent = vehicleAlarmRepository.getHighPriorityAlarm(vehicleName);
    String systemState = vehicleRealtimeRepository.getSystemState(vehicleName);

    VehicleBasicDTO dto = vehicleBasicRepository.get(vehicleName);
    String businessType = dto.getBusinessType();

    String scheduleState = vehicleScheduleRepository.getScheduleState(vehicleName);
    List<String> vehicleNameSortList = allVehicleNameSortRepository.get();

    VehicleScoreBO bo = new VehicleScoreBO();
    bo.setVehicleName(vehicleName);
    bo.setSystemState(systemState);
    bo.setAlarmEvent(alarmEvent);
    bo.setBusinessType(businessType);
    bo.setVehicleNameSortList(vehicleNameSortList);

    // 通过业务类型排序(售卖/配送)
    bo.setScoreType(VehicleSortTypeEnum.BUSINESS_TYPE.getValue());
    bo.setScheduleState(scheduleState);
    UpdateScoreTypeEnum.BUSINESS_TYPE.getConsumer().accept(bo);
    log.info("初始化单个调度分数结束!={}", vehicleName);
  }
}

