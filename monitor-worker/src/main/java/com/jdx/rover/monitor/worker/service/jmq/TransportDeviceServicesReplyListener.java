/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.service.robot.IRobotDeviceService;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceHeader;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 接收设备指令响应信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/12
 */
@Service
@Slf4j
public class TransportDeviceServicesReplyListener implements MessageListener {

    public void onMessage(List<Message> messages) {
        for (Message message : messages) {
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            log.info("收到指令回复通知{}", message.getText());
            TransportDeviceMessage transportDeviceMessage = JsonUtils.readValue(message.getText(), new TypeReference<>() {
            });
            if (Objects.isNull(transportDeviceMessage) || Objects.isNull(transportDeviceMessage.getHeader()) || Objects.isNull(transportDeviceMessage.getData())) {
                continue;
            }
            try {
               handleOneMessage(transportDeviceMessage);
            } catch (Exception e) {
                log.info("同步设备基础信息异常", e);
            }
        }
    }

    /**
     * 处理单个设备消息，解析视频模式并通知视频服务更新。
     * @param transportDeviceMessage 设备消息对象，包含视频模式信息。
     */
    private void handleOneMessage(TransportDeviceMessage transportDeviceMessage) {
        TransportDeviceHeader headerMessage = transportDeviceMessage.getHeader();
        ProductTypeEnum productTypeEnum = ProductTypeEnum.getByValue(headerMessage.getProductKey());
        if (Objects.isNull(productTypeEnum) || StringUtils.isBlank(headerMessage.getDeviceName())) {
            return;
        }
        IRobotDeviceService robotService = (IRobotDeviceService) SpringUtil.getBean(productTypeEnum.getClazz());
        robotService.eventsReply(transportDeviceMessage);

    }
}


