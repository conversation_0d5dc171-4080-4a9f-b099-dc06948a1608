/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.worker.job;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.vehicle.VehicleSpeedLimitService;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/8 18:05
 * @description 车辆限速定时任务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleSpeedLimitJob {

    /**
     * VehicleSpeedLimitService
     */
    private final VehicleSpeedLimitService vehicleSpeedLimitService;

    /**
     * 每天23:00，自动清除车辆限速配置
     */
    @Scheduled(cron = "0 0 23 * * ?")
    public void handle() {
        boolean locked = RedissonUtils.tryLock(this.getClass().getName(), 30, 10, TimeUnit.SECONDS);
        if (!locked) {
            log.info("【车辆限速】自动清除车辆限速配置任务加锁失败");
            return;
        }

        try {
            if (isFinish()) {
                log.info("【车辆限速】车自动清除车辆限速配置已执行完成");
                return;
            }

            // 清除所有车辆限速配置
            vehicleSpeedLimitService.clearAllSpeedLimitConfig();

            success();
            log.info("【车辆限速】自动清除车辆限速配置完成");
        } catch (Exception e) {
            log.error("【车辆限速】自动清除车辆限速配置异常：{}", e.getMessage(), e);
        } finally {
            RedissonUtils.unLock(this.getClass().getName());
        }
    }

    /**
     * 判断任务是否已经执行完成
     *
     * @return 是否已完成
     */
    private boolean isFinish() {
        RBucket<Integer> rBucket = RedissonUtils.getRBucket(this.getClass().getName() + DateUtil.today());
        return rBucket.isExists() && rBucket.get() == 1;
    }

    /**
     * 任务执行成功
     */
    private void success() {
        RBucket<Integer> rBucket = RedissonUtils.getRBucket(this.getClass().getName() + DateUtil.today());
        rBucket.set(1);
        rBucket.expire(1, TimeUnit.HOURS);
    }
}
