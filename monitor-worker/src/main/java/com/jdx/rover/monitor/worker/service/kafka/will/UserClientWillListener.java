/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.service.kafka.will;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.user.UserClientOnlineDO;
import com.jdx.rover.monitor.manager.delay.DelayJob;
import com.jdx.rover.monitor.manager.delay.DelayJobProducer;
import com.jdx.rover.monitor.repository.redis.user.UserClientOnlineRepository;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttOnline;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class UserClientWillListener {
    public static final String USER_OFFLINE_DELAY_JOB_TASK = "userOfflineDelayJobTask";

    private final UserClientOnlineRepository userClientOnlineRepository;

    // @KafkaListener(topics = {"#{'${project.kafka.topic.will}'.split(',')}"})
    @KafkaListener(topics = {"#{'r_${spring.profiles.active}_drive_monitor_will'}", "#{'r_${spring.profiles.active}_drive_screen_will'}"})
    public void onMessage(ConsumerRecord<String, String> record) {
        String message = record.value();
        log.info("Received topic={}, message={}", record.topic(), record.value());
        if (StringUtils.isBlank(record.value())) {
            return;
        }
        try {
            handleOneMessage(message);
        } catch (Exception e) {
            log.error("处理消息失败!{}", message, e);
        }
    }

    private void handleOneMessage(String message) {
        MqttRequestDTO<MqttOnline> mqttRequestDTO = JsonUtils.readValue(message, new TypeReference<>() {
        });
        if (mqttRequestDTO == null) {
            return;
        }
        String clientName = mqttRequestDTO.getHeader().getClientName();
        String userName = StringUtils.substringBefore(clientName, "_monitor_");
        if (StringUtils.isBlank(userName)) {
            return;
        }
        if (mqttRequestDTO.getData().getOnline()) {
            UserClientOnlineDO userClientOnlineDO = new UserClientOnlineDO();
            userClientOnlineDO.setUserName(userName);
            userClientOnlineDO.setClientName(clientName);
            if (!Objects.isNull(mqttRequestDTO.getHeader().getRequestTime())) {
                userClientOnlineDO.setStartTime(new Date(mqttRequestDTO.getHeader().getRequestTime()));
            }
            userClientOnlineDO.setRecordTime(new Date());
            userClientOnlineRepository.putByMapKey(userName, clientName, userClientOnlineDO);

            // 取消离线任务
            DelayJob delayJob = getDelayJob(userName, clientName);
            DelayJobProducer.cancelJob(delayJob);

            log.info("当前在线用户客户端明细={}", userClientOnlineRepository.get(userName));
        } else {
            disconnectedClient(userName, clientName);
        }
    }


    public synchronized void disconnectedClient(String userName, String clientName) {
        Map<String, UserClientOnlineDO> userMap = userClientOnlineRepository.get(userName);
        if (!MapUtils.isEmpty(userMap) && userMap.size() > 1) {
            userClientOnlineRepository.fastRemoveByMapKey(userName, clientName);
            log.info("存在多于1个客户端,不加入离线任务={}", userMap);
        } else {
            DelayJob delayJob = getDelayJob(userName, clientName);
            DelayJobProducer.submitJob(delayJob, 1L, TimeUnit.MINUTES);
            log.info("加入用户离线任务={}", userMap);
        }
    }


    private static DelayJob getDelayJob(String userName, String clientName) {
        Map<String, Object> paramMap = new ParamMap<UserClientOnlineDO>()
                .addProperty(UserClientOnlineDO::getUserName, userName)
                .addProperty(UserClientOnlineDO::getClientName, clientName)
                .toMap();
        return new DelayJob(paramMap, USER_OFFLINE_DELAY_JOB_TASK);
    }
}

