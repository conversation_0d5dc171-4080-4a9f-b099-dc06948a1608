/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import cn.hutool.core.util.StrUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.mapcollection.CollectionModeEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.vehicle.VehicleGpsManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接收车辆状态变更信息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerVehicleChangeStatusJmqConsumer implements MessageListener {

    private final VehicleGpsManager vehicleGpsManager;
    private final VehicleStatusRepository vehicleStatusRepository;
    private final TrackingEventCollectService trackingEventCollectService;

    /**
     * 处理接收到的消息列表。
     *
     * @param messages 消息列表，包含多个Message对象。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理车辆状态变化消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息并更新相应的车辆状态。
     *
     * @param message JSON格式的消息，包含车辆名称、更改类型和新值。
     */
    public void handleOneMessage(String message) {
        ChangeStatusDTO dto = JsonUtils.readValue(message, ChangeStatusDTO.class);
        ChangeStatusDTO.ChangeTypeEnum changeTypeEnum = ChangeStatusDTO.ChangeTypeEnum.valueOf(dto.getChangeType());
        switch (changeTypeEnum) {
            case GPS_SIGNAL: {
                vehicleStatusRepository.putMapValue(dto.getVehicleName(), VehicleStatusDO::getGpsSignal, dto.getNewValue());
                vehicleGpsManager.pushSingleVehicle(dto.getVehicleName());
                break;
            }
            case SCENE_SIGNAL: {
                vehicleStatusRepository.putMapValue(dto.getVehicleName(), VehicleStatusDO::getSceneSignal, dto.getNewValue());
                trackingEventCollectService.pushSceneSignalStatus(dto.getVehicleName(), dto.getOldValue(), dto.getNewValue());
                break;
            }
            case RUN_MAP_STATE:
                if (GuardianDataDto.RoverStatus.RunMapState.RUN_CALIBRATION.name().equals(dto.getNewValue())) {
                    return;
                } else if (GuardianDataDto.RoverStatus.RunMapState.RUN_MAP_COLLECTION.name().equals(dto.getNewValue())) {
                    vehicleStatusRepository.putMapValue(dto.getVehicleName(), VehicleStatusDO::getCollectionMode, CollectionModeEnum.COLLECTION.getCollectionMode());
                    vehicleStatusRepository.putMapValue(dto.getVehicleName(), VehicleStatusDO::getRunMapState, GuardianDataDto.RoverStatus.RunMapState.RUN_NO_MAP.name());
                } else {
                    vehicleStatusRepository.putMapValue(dto.getVehicleName(), VehicleStatusDO::getCollectionMode, CollectionModeEnum.NO_COLLECTION.getCollectionMode());
                    vehicleStatusRepository.putMapValue(dto.getVehicleName(), VehicleStatusDO::getRunMapState, dto.getNewValue());
                }
                // 删除切换缓存
                String statusKey = String.format(RedisKeyEnum.COLLECTION_VEHICLE_STATUS.getValue(), dto.getVehicleName());
                if (RedissonUtils.hasKey(statusKey)) {
                    log.info("触发删除切换缓存操作, vehicleName: [{}].", dto.getVehicleName());
                    RedissonUtils.deleteObject(statusKey);
                }
                break;
            case STEER_ZERO:
                vehicleStatusRepository.putMapValue(dto.getVehicleName(), VehicleStatusDO::getSteerZero, dto.getNewValue());
                trackingEventCollectService.pushSteerZeroStatus(dto.getVehicleName(), dto.getNewValue());
                break;
            case MAX_VELOCITY:
                if (StrUtil.isNotBlank(dto.getNewValue())) {
                    log.info("【车辆限速】车辆上报底盘限速值：[vehicleName: {}, maxVelocity: {}]", dto.getVehicleName(), dto.getNewValue());
                    vehicleStatusRepository.putMapValue(dto.getVehicleName(), VehicleStatusDO::getReportMaxVelocity, dto.getNewValue());
                }
                break;
            default:
                log.info("未匹配的消息{}", dto);
        }
    }
}