/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.xata.DataCopyTaskDTO;
import com.jdx.rover.monitor.dto.xata.DataCopyTaskItemDTO;
import com.jdx.rover.monitor.entity.datacollection.DataCollectionVehicleSceneDO;
import com.jdx.rover.monitor.enums.MonitorKafkaTopicConstant;
import com.jdx.rover.monitor.repository.redis.datacollection.DataCollectionVehicleSceneRepository;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import com.jdx.rover.monitor.service.datacollection.DataCollectionSceneService;
import com.jdx.rover.server.api.domain.dto.hardware.ReportHardwareEventDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 接收车辆硬件事件变更
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerHardwareEventJmqConsumer implements MessageListener {

    /**
     * DataCollectionSceneService
     */
    private final DataCollectionSceneService dataCollectionSceneService;

    /**
     * 管理数据采集车与场景关联关系的数据访问操作
     */
    private final DataCollectionVehicleSceneRepository vehicleSceneRepository;

    /**
     * KafkaTemplate
     */
    private final KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 存储DUCC(数据采集配置中心)的相关配置属性
     */
    private final DuccConfigProperties duccConfigProperties;

    /**
     * 处理接收到的消息列表。
     *
     * @param messages 消息列表，包含多个Message对象。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理车辆状态变化消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息。
     *
     * @param message JSON格式的消息
     */
    public void handleOneMessage(String message) {
        ReportHardwareEventDTO dto = JsonUtils.readValue(message, ReportHardwareEventDTO.class);
        ReportHardwareEventDTO.EventTypeEnum eventTypeEnum = ReportHardwareEventDTO.EventTypeEnum.of(dto.getEventType());
        if (Objects.isNull(eventTypeEnum)) {
            return;
        }
        switch (eventTypeEnum) {
            case DC_STALK_EVENT: {
                processDataCollectionStackEvent(dto.getVehicleName(), dto.getRecordTime());
                break;
            }
            default:
                log.info("未匹配的消息{}", dto);
        }
    }

    /**
     * 处理数据采集事件
     * @param vehicleName 车辆名称，为空时方法直接返回
     * @param recordTime 记录时间，为null时方法直接返回
     */
    public void processDataCollectionStackEvent(String vehicleName, Date recordTime) {
        if (StrUtil.isBlank(vehicleName) || null == recordTime) {
            return;
        }

        // 1. 获取旧的场景缓存数据
        DataCollectionVehicleSceneDO oldVehicleSceneDO = vehicleSceneRepository.get(vehicleName);

        // 2. 过滤区间内指令（当前拨杆时间在上一次拨杆+-配置时间区间内）
        if (null != oldVehicleSceneDO && null != oldVehicleSceneDO.getEndTime() && recordTime.before(oldVehicleSceneDO.getEndTime())) {
            log.info("【数据采集】过滤拨杆指令：[lastRecordTime:{}, recordTime:{}]", oldVehicleSceneDO.getReportTime(), recordTime);
            return;
        }

        // 3. 场景数据落库
        DataCollectionVehicleSceneDO newVehicleSceneDO = dataCollectionSceneService.addDataCollectionScene(vehicleName, recordTime);
        log.info("【数据采集】新增拨杆场景：[{}]", newVehicleSceneDO);
        if (duccConfigProperties.getOpenDataCollectionXataCopy()) {
            // 4. 判断旧场景结束时间和新场景开始时间是否有重叠
            Date startTime = newVehicleSceneDO.getStartTime(), endTime = newVehicleSceneDO.getEndTime();
            log.info("【数据采集】固化任务区间：[vehicleName:{}, startTime:{}, endTime:{}]", vehicleName, startTime, endTime);
            if (null != oldVehicleSceneDO && null != oldVehicleSceneDO.getEndTime()) {
                if (oldVehicleSceneDO.getEndTime().after(newVehicleSceneDO.getStartTime()) && oldVehicleSceneDO.getEndTime().before(newVehicleSceneDO.getEndTime())) {
                    // 有重叠，更新新场景开始时间
                    startTime = oldVehicleSceneDO.getEndTime();
                    log.info("【数据采集】更新固化任务区间：[vehicleName:{}, startTime:{}, endTime:{}]", vehicleName, startTime, endTime);
                }
            }
            // 5. 推送Xata数据拷贝任务
            DataCopyTaskDTO dataCopyTaskDTO = buildXataCopyTask(startTime, endTime, vehicleName);
            kafkaTemplate.send(MonitorKafkaTopicConstant.XATA_DATA_COLLECTION, JsonUtils.writeValueAsString(dataCopyTaskDTO));
            log.info("【数据采集】推送Xata数据固化任务：[{}]", dataCopyTaskDTO);
        }
    }

    /**
     * 创建数据固化任务
     *
     * @param startTime startTime
     * @param endTime endTime
     * @param vehicleName vehicleName
     * @return DataCopyTaskDTO
     */
    private DataCopyTaskDTO buildXataCopyTask(Date startTime, Date endTime, String vehicleName) {
        List<DataCopyTaskItemDTO> dataItems = new ArrayList<>();
        DataCopyTaskItemDTO dataItem = DataCopyTaskItemDTO.builder()
            .startTime(DateUtil.formatDateTime(startTime))
            .endTime(DateUtil.formatDateTime(endTime))
            .topics(Lists.newArrayList())
            .build();
        dataItems.add(dataItem);
        return DataCopyTaskDTO.builder()
            .type("dataplateform")
            .carNum(vehicleName)
            .priority("normal")
            .scene("collect_data")
            .env("instant_task_solid")
            .description("有人数据采集车采集")
            .creator("system")
            .dataItems(dataItems)
            .build();
    }
}