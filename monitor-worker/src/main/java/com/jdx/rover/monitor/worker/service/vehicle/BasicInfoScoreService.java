package com.jdx.rover.monitor.worker.service.vehicle;

import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.repository.redis.AllStationSortRepository;
import com.jdx.rover.monitor.repository.redis.AllVehicleNameSortRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSelectTypeRepository;
import com.jdx.rover.monitor.repository.redis.sort.BusinessTypeScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.redis.sort.PowerScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.redis.sort.VehicleNameScoreSortedSetRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 更新车辆基础信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BasicInfoScoreService {
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private VehicleBasicRepository vehicleBasicRepository;
  @Autowired
  private MetadataVehicleApiManager metadataVehicleApiManager;
  @Autowired
  private VehicleSelectTypeRepository vehicleSelectTypeRepository;
  @Autowired
  private AllVehicleNameSortRepository allVehicleNameSortRepository;
  @Autowired
  private AllStationSortRepository allStationSortRepository;
  @Autowired
  private BusinessTypeScoreSortedSetRepository businessTypeScoreSortedSetRepository;
  @Autowired
  private PowerScoreSortedSetRepository powerScoreSortedSetRepository;
  @Autowired
  private VehicleNameScoreSortedSetRepository vehicleNameScoreSortedSetRepository;
  @Autowired
  private RealtimeScoreService realtimeScoreService;
  @Autowired
  private InitScoreService initScoreService;

  /**
   * 初始化所有车辆基础数据
   */
  public void updateVehicleBasicInfo(String vehicleName) {
    log.info("初始化单个基础信息开始!={}", vehicleName);
    VehicleBasicDTO dto = metadataVehicleApiManager.getByName(vehicleName);
    if (dto == null) {
      // 删除逻辑
      vehicleBasicRepository.remove(vehicleName);
      vehicleSelectTypeRepository.remove(vehicleName);
      vehicleRealtimeRepository.remove(vehicleName);
      allVehicleNameSortRepository.remove(vehicleName);
      powerScoreSortedSetRepository.remove(vehicleName);
      businessTypeScoreSortedSetRepository.remove(vehicleName);
      vehicleNameScoreSortedSetRepository.remove(vehicleName);
      log.info("车辆被删除!={}", vehicleName);
    } else {
      List<String> stationSortList = allStationSortRepository.get();
      String stationName = Optional.ofNullable(dto.getStationName()).orElse("");
      if (stationSortList.indexOf(stationName) == -1) {
        // 不知道删除站点分数,所以新建站或者更改站名直接全部初始化
        initScoreService.initAllVehicleBasicInfo();
        log.info("车辆站点新增,初始化所有车辆基础数据和分数!vehicleName={},stationName={}", vehicleName, stationName);
        return;
      }
      // 更新逻辑
      vehicleBasicRepository.set(dto);
      // 筛选条件业务类型(配送/售卖) 车辆类型(运营/测试)
      vehicleSelectTypeRepository.add(dto.getBusinessType(), dto.getOwnerUseCase(), vehicleName);

      List<String> vehicleNameSortList = allVehicleNameSortRepository.get();
      if (vehicleNameSortList.indexOf(vehicleName) == -1) {
        vehicleNameSortList.add(vehicleName);
        vehicleNameSortList = vehicleNameSortList.stream().sorted().collect(Collectors.toList());
        // 车辆排序列表新增
        allVehicleNameSortRepository.set(vehicleNameSortList);

        List<VehicleBasicDTO> vehicleList = metadataVehicleApiManager.listAll();
        // 通过车辆名称变更分数,所有排序都变化
        initScoreService.updateScore(vehicleList, vehicleNameSortList, stationSortList);
        log.info("车辆名称新增,初始化所有车辆分数!vehicleName={},stationName={}", vehicleName, stationName);
      } else {
        realtimeScoreService.updateRealtimeScore(dto, vehicleNameSortList, stationSortList);
      }
    }
    log.info("初始化单个基础信息结束!={}", vehicleName);
  }

}
