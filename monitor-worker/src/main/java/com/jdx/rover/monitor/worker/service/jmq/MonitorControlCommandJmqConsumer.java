/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.enums.mobile.BrodcastMsgEnum;
import com.jdx.rover.monitor.service.mobile.CommandService;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteShoutVO;
import com.jdx.rover.monitor.worker.service.kafka.shadow.AbstractEventListener;
import com.jdx.rover.server.api.domain.vo.RemoteControlCommandVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jodah.expiringmap.ExpirationPolicy;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 车辆遥控指令监听
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MonitorControlCommandJmqConsumer extends AbstractEventListener implements MessageListener {

  private final CommandService commandService;

  private final ExpiringMap<String, List<RemoteControlCommandVO>> commandMap = ExpiringMap.builder().variableExpiration().expiration(10, TimeUnit.SECONDS)
          .expirationPolicy(ExpirationPolicy.CREATED).build();

  /**
   * 车辆遥控指令监听
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理下发指令消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个消息，解析并执行相应的远程控制命令。
   */
  private void handleOneMessage(String message) {
    RemoteControlCommandVO remoteControlCommandVo =
            JsonUtils.readValue(message, RemoteControlCommandVO.class);
    if (Objects.isNull(remoteControlCommandVo)) {
      return;
    }
    String vehicleName = remoteControlCommandVo.getVehicleName();
    if (remoteControlCommandVo.getTargetVelocity() >= 0) {
      return;
    }
    List<RemoteControlCommandVO> commandList = commandMap.getOrDefault(vehicleName, new ArrayList<>());
    commandList.add(remoteControlCommandVo);
    List<Date> dateList = commandList.stream().map(RemoteControlCommandVO :: getReceiveTimeStamp).sorted().collect(Collectors.toList());
    long duration = DateUtil.between(dateList.get(0), dateList.get(dateList.size() - 1), DateUnit.SECOND);
    log.info("监测{}车辆{}倒车列表{}， 持续时间{}", remoteControlCommandVo.getReceiveTimeStamp(), remoteControlCommandVo.getVehicleName(), commandList.size(), duration);
    // 3HZ下发
    if (duration >= 2) {
      commandMap.remove(vehicleName);
    } else if (commandList.size() > 1 && duration < 1) {
      MiniMonitorRemoteShoutVO remoteShout = new MiniMonitorRemoteShoutVO();
      remoteShout.setVoiceMsg(BrodcastMsgEnum.BACKWARD_ATTENTION.getName());
      remoteShout.setRate(1);
      commandService.remoteBroadCastWord(vehicleName, remoteShout);
      RemoteControlCommandVO magicVo = new RemoteControlCommandVO();
      magicVo.setReceiveTimeStamp(DateUtil.offsetSecond(remoteControlCommandVo.getReceiveTimeStamp(), 1).toJdkDate());
      commandList.add(magicVo);
      commandMap.put(vehicleName, commandList);
    } else {
      commandMap.put(vehicleName, commandList);
    }
  }
}
