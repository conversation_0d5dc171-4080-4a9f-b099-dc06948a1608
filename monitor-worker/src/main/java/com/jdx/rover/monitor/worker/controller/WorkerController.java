package com.jdx.rover.monitor.worker.controller;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.map.api.domain.dto.MapVariableApiJsfDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamInfoDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleStationInfoDTO;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.metadata.CockpitVehicleNameRepository;
import com.jdx.rover.monitor.service.alarm.notice.VehicleCommonAlarmNoticeService;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.pda.DeviceStatusService;
import com.jdx.rover.monitor.service.pda.PdaService;
import com.jdx.rover.monitor.service.robot.RoverVehicleService;
import com.jdx.rover.monitor.service.vehicle.MultiVehicleService;
import com.jdx.rover.monitor.service.web.MonitorGuardianInfoService;
import com.jdx.rover.monitor.worker.service.jmq.server.ServerHardwareEventJmqConsumer;
import com.jdx.rover.monitor.worker.service.vehicle.AttentionService;
import com.jdx.rover.monitor.worker.service.vehicle.VehicleAlarmService;
import com.jdx.rover.monitor.worker.service.vehicle.VehicleSortService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmDTO;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Polygon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/monitor/worker")
public class WorkerController {
  @Autowired
  private MultiVehicleService multiVehicleService;
  @Autowired
  private VehicleSortService vehicleSortService;
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private MonitorGuardianInfoService guardianInfoService;
  @Autowired
  private VehicleAlarmService vehicleAlarmService;
  @Autowired
  private VehicleCommonAlarmNoticeService vehicleAlarmNoticeService;
  @Autowired
  private TrackingEventCollectService trackingEventService;
  @Autowired
  private AttentionService attentionService;
  @Autowired
  private PdaService pdaService;
  @Autowired
  private DeviceStatusService deviceStatusService;
  @Autowired
  private CockpitTeamManager cockpitTeamManager;
  @Autowired
  private CockpitVehicleNameRepository vehicleNameRepository;
  @Autowired
  private ServerHardwareEventJmqConsumer serverHardwareEventJmqConsumer;
  @Autowired
  private RoverVehicleService roverVehicleService;

  /**
   * 初始化用户车辆名称数据
   *
   * @return
   */
  @GetMapping("/init/user/data")
  public String initUserData(String username) {
    vehicleSortService.initUserSortData(username);
    return "initUserData success";
  }

  /**
   * 测试初始化数据
   * 全量车按车号/业务/电量 排序
   *
   * @return
   */
  @GetMapping("/refresh/data")
  public String refreshData() {
    vehicleSortService.initAllVehicleBasicInfo();
    return "refreshData success";
  }

  /**
   * 测试初始化数据
   * 全量车按车号/业务/电量 排序
   *
   * @return
   */
  @GetMapping("/refresh/basic/{vehicleName}")
  public String refreshBasicVehicle(@PathVariable("vehicleName") String vehicleName) {
    vehicleSortService.updateVehicleBasicInfo(vehicleName);
    return "refreshData success";
  }

  /**
   * 测试初始化数据
   * 全量车按车号/业务/电量 排序
   *
   * @return
   */
  @GetMapping("/refresh/realtime/{vehicleName}")
  public String refreshRealtimeVehicle(@PathVariable("vehicleName") String vehicleName) {
    vehicleSortService.updateVehicleRealtimeInfo(vehicleName);
    return "refreshData success";
  }

  /**
   * 初始化座席数据
   * 座席下车辆
   *
   * @return
   */
  @GetMapping("/refresh/cockpit")
  public String refreshCockpitInfo() {
    List<CockpitTeamInfoDTO> cockpitTeamInfoList = cockpitTeamManager.getCockpitTeamInfoList();

    cockpitTeamInfoList.stream().forEach(teamInfo -> {
      teamInfo.getCockpitInfoList().stream().forEach(cockpit -> {
        List<String> vehicleNameList =
                cockpit.getVehicleInfoList().stream().map(VehicleStationInfoDTO::getVehicleName).collect(Collectors.toList());
        vehicleNameRepository.initSet(cockpit.getCockpitNumber(), vehicleNameList);
      });
    });
    return "refreshData success";
  }

  /**
   * 构造告警
   *
   * @return
   */
  @PostMapping("/alarm/add")
  public String postAlarmInfo(@RequestBody VehicleAlarmDTO vehicleAlarmDTO) {
    log.info("构造车辆告警", JsonUtils.writeValueAsString(vehicleAlarmDTO));
    vehicleAlarmService.handleVehicleAlarm(vehicleAlarmDTO);
    return "addAlarm success";
  }

  /**
   * 初始化座席数据
   * 座席下车辆
   *
   * @return
   */
  @GetMapping("/refresh/attention")
  public String refreshAttentionInfo(@RequestParam("stationIdList") List<Integer> stationIdList) {
    attentionService.copyUserAttention(stationIdList);
    return "refreshData success";
  }

  /**
   * 刷新Pda状态统计
   *
   * @return
   */
  @GetMapping("/refresh/pdaStatus")
  public String refreshPdaStatus() {
    pdaService.refreshPdaStatus();
    return "refreshData success";
  }

  /**
   * pdu上报开始结束
   *
   * @return
   */
  @GetMapping("/dataCollection/pdu/{action}/{vehicleName}")
  public String dataCollectionPdu(@PathVariable("action") String action, @PathVariable("vehicleName") String vehicleName) {
    if (StringUtils.equals(action, "start")) {
      roverVehicleService.processReportPduPowerOn(new TransportDeviceMessage(), vehicleName);
    } else if (StringUtils.equals(action, "end")) {
      roverVehicleService.processReportPduPowerOff(new TransportDeviceMessage(), vehicleName);
    }
    return "success";
  }

  /**
   * 遥控拨杆事件
   *
   * @return
   */
  @GetMapping("/dataCollection/stalk/{vehicleName}")
  public String dataCollectionStalk(@PathVariable("vehicleName") String vehicleName) {
    serverHardwareEventJmqConsumer.processDataCollectionStackEvent(vehicleName, new Date());
    return "dataCollectionStalk success";
  }

  /**
   * 测试polygon反序列化
   *
   * @return
   */
  @GetMapping("/test/polygon")
  public String testPolygon() {
    log.info("JsonUtils.getObjectMapper().getRegisteredModuleIds()={}",JsonUtils.getObjectMapper().getRegisteredModuleIds());
    String message = "{\"id\":4898,\"mapId\":149,\"mapVersion\":201532,\"variableVersion\":20240914,\"type\":\"ATTENTION_REGION_WARN\",\"geometryWgs84\":\"{\\\"type\\\":\\\"Polygon\\\",\\\"coordinates\\\":[[[116.5542586797449,39.784359902346175],[116.55408920123452,39.784245433912716],[116.55435353724869,39.78396604852363],[116.55449731080458,39.78407389481548],[116.5542586797449,39.784359902346175]]]}\",\"status\":\"RS_INUSE\",\"effectDateType\":\"LONG_TERM\",\"expireTime\":2054959235018,\"remark\":null,\"weekList\":null,\"timeLimitList\":null,\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null},{\"id\":4616,\"mapId\":130,\"mapVersion\":201097,\"variableVersion\":20240045,\"type\":\"ATTENTION_REGION_WARN\",\"geometryWgs84\":\"{\\\"type\\\":\\\"Polygon\\\",\\\"coordinates\\\":[[[120.77065392015326,31.594886339618764],[120.77071435779796,31.594661511580547],[120.77393205800082,31.59535775324728],[120.77388370788508,31.595640601424396],[120.77065392015326,31.594886339618764]]]}\",\"status\":\"RS_INUSE\",\"effectDateType\":\"CYCLICAL_TERM\",\"expireTime\":2038552755261,\"remark\":null,\"weekList\":[\"WEDNESDAY\",\"SUNDAY\"],\"timeLimitList\":[{\"startTime\":[16,0],\"endTime\":[20,0]}],\"effectVehicleType\":\"PART_EFFECT\",\"vehicleLimit\":[\"JDK8188\",\"JDE0144\"]},{\"id\":4617,\"mapId\":130,\"mapVersion\":201097,\"variableVersion\":20240045,\"type\":\"ATTENTION_REGION_WARN\",\"geometryWgs84\":\"{\\\"type\\\":\\\"Polygon\\\",\\\"coordinates\\\":[[[120.7743750370473,31.59559279726008],[120.77466384347909,31.595148479672712],[120.77559135644272,31.59546505595371],[120.77534698176967,31.59573164650613],[120.7743750370473,31.59559279726008]]]}\",\"status\":\"RS_INUSE\",\"effectDateType\":\"CYCLICAL_TERM\",\"expireTime\":2038552873216,\"remark\":null,\"weekList\":[\"THURSDAY\",\"FRIDAY\",\"SATURDAY\",\"SUNDAY\"],\"timeLimitList\":null,\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null},{\"id\":4618,\"mapId\":130,\"mapVersion\":201097,\"variableVersion\":20240045,\"type\":\"ATTENTION_REGION_WARN\",\"geometryWgs84\":\"{\\\"type\\\":\\\"Polygon\\\",\\\"coordinates\\\":[[[120.77524701031251,31.595165141582235],[120.77547472307603,31.59530954479813],[120.77647999161745,31.59413765716145],[120.77621340106504,31.593954376156663],[120.77524701031251,31.595165141582235]]]}\",\"status\":\"RS_INUSE\",\"effectDateType\":\"CYCLICAL_TERM\",\"expireTime\":2038552965405,\"remark\":null,\"weekList\":[\"WEDNESDAY\"],\"timeLimitList\":[{\"startTime\":[16,0],\"endTime\":[18,0]},{\"startTime\":[18,5],\"endTime\":[20,0]}],\"effectVehicleType\":\"PART_EFFECTLESS\",\"vehicleLimit\":[\"JDK8188\",\"JDE0144\"]},{\"id\":4619,\"mapId\":130,\"mapVersion\":201097,\"variableVersion\":20240045,\"type\":\"ATTENTION_REGION_WARN\",\"geometryWgs84\":\"{\\\"type\\\":\\\"Polygon\\\",\\\"coordinates\\\":[[[120.77489155624261,31.593376763293083],[120.77514148488551,31.593004647313663],[120.77635225031108,31.593532274448663],[120.7760245660904,31.59387106660903],[120.77489155624261,31.593376763293083]]]}\",\"status\":\"RS_DISCARDED\",\"effectDateType\":\"LONG_TERM\",\"expireTime\":2038553020978,\"remark\":null,\"weekList\":null,\"timeLimitList\":null,\"effectVehicleType\":\"ALL_EFFECT\",\"vehicleLimit\":null}";
    MapVariableApiJsfDTO mapVariable = JsonUtils.readValue(message, MapVariableApiJsfDTO.class);
    log.info("result={}",mapVariable);
    Polygon polygon = JsonUtils.readValue(mapVariable.getGeometryWgs84(), Polygon.class);
    log.info("polygon={}",polygon);
    log.info("JsonUtils.getObjectMapper().getRegisteredModuleIds()={}",JsonUtils.getObjectMapper().getRegisteredModuleIds());
    return "testPolygon success";
  }

}