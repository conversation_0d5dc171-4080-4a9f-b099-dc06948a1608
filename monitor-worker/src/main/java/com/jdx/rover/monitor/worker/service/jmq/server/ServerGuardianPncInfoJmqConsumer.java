/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.service.VehiclePncService;
import com.jdx.rover.monitor.service.vehicle.VehicleStatusService;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接收guardian电量信息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerGuardianPncInfoJmqConsumer implements MessageListener {

  /**
   * 提供与车辆PNC信息相关的服务。
   */
  private final VehiclePncService vehiclePncService;

  /**
   * 车辆状态服务。
   */
  private final VehicleStatusService vehicleStatusService;

  /**
   * 处理从消息队列中接收到消息。
   * @param messages 接收到的消息列表。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理工单统计消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个车辆PNC信息消息。
   * @param message 车辆PNC信息的JSON字符串。
   */
  private void handleOneMessage(String message) {
    VehiclePncInfoDTO vehiclePncInfoDto = JsonUtils.readValue(message, VehiclePncInfoDTO.class);
    if (vehiclePncInfoDto == null || StringUtils.isBlank(vehiclePncInfoDto.getVehicleName())) {
      return;
    }
    vehiclePncService.handleVehicleSchedulePncInfo(vehiclePncInfoDto);

    vehicleStatusService.handByPncInfo(vehiclePncInfoDto);
  }
}
