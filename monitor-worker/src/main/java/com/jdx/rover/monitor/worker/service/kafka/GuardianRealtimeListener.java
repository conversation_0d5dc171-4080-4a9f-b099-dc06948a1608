package com.jdx.rover.monitor.worker.service.kafka;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRealRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.service.event.SingleVehicleHistoryService;
import com.jdx.rover.monitor.service.mapcollection.MapCollectionService;
import com.jdx.rover.monitor.service.vehicle.SingleVehicleService;
import com.jdx.rover.monitor.service.vehicle.VehicleLocationService;
import com.jdx.rover.monitor.service.vehicle.VehicleStatusService;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 接收guardian实时信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class GuardianRealtimeListener {
  private final VehicleRealtimeRepository vehicleRealtimeRepository;
  private final VehicleScheduleRepository vehicleScheduleRepository;
  private final VehicleScheduleRealRouteRepository vehicleScheduleRealRouteRepository;
  private final SingleVehicleHistoryService singleVehicleHistoryService;
  private final MapCollectionService mapCollectionService;
  private final SingleVehicleService singleVehicleService;


  /**
   * 车辆状态服务
   */
  private final VehicleStatusService vehicleStatusService;

  private final VehicleLocationService vehicleLocationService;

  @KafkaListener(topics = {KafkaTopicConstant.SERVER_GUARDIAN_REALTIME}, containerFactory = "batchListenerContainerFactory")
  public void onMessage(List<String> messageList) {
    if (CollectionUtils.isEmpty(messageList)) {
      return;
    }
    log.info("Received topic={}, size={}, message={}", KafkaTopicConstant.SERVER_GUARDIAN_REALTIME, messageList.size(), messageList);
    List<VehicleRealtimeInfoDTO> dtoList = new ArrayList<>();
    List<String> vehicleList = new ArrayList<>();
    try {
      for (String message : messageList) {
        VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = JsonUtils.readValue(message, VehicleRealtimeInfoDTO.class);
        dtoList.add(vehicleRealtimeInfoDTO);
        vehicleList.add(vehicleRealtimeInfoDTO.getVehicleName());
      }
      vehicleRealtimeRepository.setList(dtoList);

      for (VehicleRealtimeInfoDTO dto : dtoList) {
        vehicleStatusService.handByRealtime(dto);
      }
      vehicleLocationService.cacheInMotionVehicle(dtoList);

      Map<String, MonitorScheduleEntity> scheduleEntityMap = vehicleScheduleRepository.listMap(vehicleList);
      handlePosition(dtoList, scheduleEntityMap);

      // 处理地图采集
      handleMapCollection(dtoList);
      // 处理6fe实时数据
      singleVehicleService.pushSingleVehicle(dtoList, Boolean.FALSE);
      singleVehicleHistoryService.pushSingleVehicleRealtime(dtoList, scheduleEntityMap);
    } catch (Exception e) {
      log.info("Guardian vehicle realtime info exception", e);
    }
  }

  /**
   * 处理地图采集路线+行驶距离
   */
  private void handleMapCollection(List<VehicleRealtimeInfoDTO> dtoList) {
    for (VehicleRealtimeInfoDTO dto : dtoList) {
      try {
        mapCollectionService.handleRouteAndDistance(dto);
      } catch (Exception e) {
        log.error("[告警]guardian实时数据处理地图采集异常.", e);
      }
    }
  }

  private void handlePosition(List<VehicleRealtimeInfoDTO> dtoList, Map<String, MonitorScheduleEntity> scheduleEntityMap){
    for (VehicleRealtimeInfoDTO dto : dtoList) {
      try {
        if (!isSavePosition(dto, scheduleEntityMap)) {
          continue;
        }
        MapPointBO mapPoint = TransformUtility.toGCJ02Point(dto.getLat(), dto.getLon());
        if (mapPoint == null) {
          continue;
        }
        MonitorRoutingPointEntity entity = new MonitorRoutingPointEntity();
        entity.setLon(mapPoint.getLongitude());
        entity.setLat(mapPoint.getLatitude());
        vehicleScheduleRealRouteRepository.push(dto.getVehicleName(), entity);
      } catch (Exception e) {
        log.error("Handler vehicle guardian realtime info exception", e);
      }
    }
  }

  /**
   * 是否保存车辆实时位置信息
   *
   * @param vehicleRealtimeInfoDTO
   * @return
   */
  private boolean isSavePosition(VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO, Map<String, MonitorScheduleEntity> scheduleEntityMap) {
    if (vehicleRealtimeInfoDTO.getLon() == null || vehicleRealtimeInfoDTO.getLat() == null) {
      return false;
    }
    // 速度小于0.1,不保存实时位置
    if (vehicleRealtimeInfoDTO.getSpeed() == null || Double.compare(vehicleRealtimeInfoDTO.getSpeed(), 0.3) < 0) {
      return false;
    }
    // 没有调度,不保存实时位置
    MonitorScheduleEntity monitorScheduleEntity = scheduleEntityMap.get(vehicleRealtimeInfoDTO.getVehicleName());
    if (monitorScheduleEntity == null || monitorScheduleEntity.getScheduleNo() == null) {
      return false;
    }
    return true;
  }

}

