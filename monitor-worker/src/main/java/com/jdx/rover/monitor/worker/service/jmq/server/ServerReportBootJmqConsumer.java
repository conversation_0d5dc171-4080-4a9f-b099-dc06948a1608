/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.service.report.ReportBootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 接收report 开机信息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerReportBootJmqConsumer implements MessageListener {

    /**
     * 提供处理开机报告消息的服务实例。
     */
    private final ReportBootService reportBootService;

    /**
     * 处理接收到的消息列表。
     *
     * @param messages 消息列表。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        List<String> messageList = new ArrayList<>();
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            messageList.add(message.getText());
        }
        try {
            handleMessage(messageList);
        } catch (Exception e) {
            log.error("处理车辆启动消息失败!{}", messageList.size(), e);
        }
    }

    /**
     * 处理消息列表
     * @param messageList 要处理的消息列表
     */
    private void handleMessage(List<String> messageList) {
        reportBootService.handleMessage(messageList);
    }
}
