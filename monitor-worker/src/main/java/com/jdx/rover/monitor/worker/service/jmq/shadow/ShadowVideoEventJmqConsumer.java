/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.shadow;

import cn.hutool.json.JSONUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.enums.vehicle.command.RemoteOperationStateEnum;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.worker.service.kafka.shadow.AbstractEventListener;
import com.jdx.rover.shadow.api.domain.dto.ShadowSubscribeEventTaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 影子系统视频生成事件监听，京ME消息推送逻辑处理
 * <AUTHOR>
 * @date 2024/12/5
 */
@Service
@Slf4j
public class ShadowVideoEventJmqConsumer extends AbstractEventListener implements MessageListener {
  @Autowired
  private AccidentJdmePushManager accidentJdmePushManager;

    /**
     * 处理接收到的消息列表。
     * @param messages 消息列表。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理视频合成结果消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息，更新事故推送信息的视频状态和播放地址。
     * @param message 消息内容，需要转换为 ShadowSubscribeEventTaskDTO 对象。
     */
    private void handleOneMessage(String message) {
        ShadowSubscribeEventTaskDTO sset = JSONUtil.toBean(message, ShadowSubscribeEventTaskDTO.class);
        if(!RemoteOperationStateEnum.SUCCESS.getValue().equals(sset.getStatus())
                && !RemoteOperationStateEnum.FAIL.getValue().equals(sset.getStatus())) {
            log.info("事故推送信息状态【{}】未在检测范围！事件编号【{}】", sset.getStatus(), sset.getEventId());
            return;
        }
        //更新视频状态
        AccidentJdmePush accidentJdmePush = this.accidentJdmePushManager.getByEventId(sset.getEventId());
        if(null == accidentJdmePush) {
            log.info("未发现事故推送信息，忽略！事件编号【{}】", sset.getEventId());
            return;
        }
        accidentJdmePush.setVideoUrl(sset.getVideoUrl());
        accidentJdmePush.setEventPlayUrl(sset.getEventPlayUrl());
        accidentJdmePush.setStatus(sset.getStatus());
        this.accidentJdmePushManager.updateById(accidentJdmePush);
    }
}
