/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.manager.vehicle.AlarmEventManager;
import com.jdx.rover.monitor.manager.vehicle.SystemStateManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleConnectManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.service.cockpit.CockpitScoreService;
import com.jdx.rover.monitor.service.vehicle.SingleVehicleService;
import com.jdx.rover.monitor.service.vehicle.VehicleStatusService;
import com.jdx.rover.monitor.worker.service.vehicle.VehicleAlarmService;
import com.jdx.rover.monitor.worker.service.vehicle.VehicleSortService;
import com.jdx.rover.server.api.domain.dto.guardian.GuardianConnectDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.GuardianConnectStatusEnum;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 接收guardian连接信息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerGuardianConnectJmqConsumer implements MessageListener {

  private final VehicleScheduleRepository vehicleScheduleRepository;
  private final VehicleRealtimeRepository vehicleRealtimeRepository;
  private final VehicleSortService vehicleSortService;
  private final AlarmEventManager alarmEventManager;
  private final VehicleAlarmService vehicleAlarmService;
  private final VehicleConnectManager vehicleConnectManager;
  private final VehicleStatusService vehicleStatusService;
  private final CockpitScoreService cockpitScoreService;
  private final SingleVehicleService singleVehicleService;

  /**
   * 消费消息列表
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个消息，更新车辆实时信息和状态。
   * @param message Guardian Connect消息内容
   */
  private void handleOneMessage(String message) {
    GuardianConnectDTO guardianConnectDTO = JsonUtils.readValue(message, GuardianConnectDTO.class);
    String vehicleName = guardianConnectDTO.getVehicleName();
    MonitorScheduleEntity monitorScheduleEntity = vehicleScheduleRepository.get(vehicleName);
    String systemState = SystemStateEnum.NORMAL.getSystemState();
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
    if (vehicleRealtimeInfoDTO == null) {
      return;
    }
    boolean isTodaySchedule = isTodaySchedule(monitorScheduleEntity);
    if (Objects.equals(GuardianConnectStatusEnum.DISCONNECT.getValue(), guardianConnectDTO.getGuardianConnectStatus())) {
      if (isTodaySchedule) {
        systemState = SystemStateEnum.CONNECTION_LOST.getSystemState();
      } else {
        systemState = SystemStateEnum.OFFLINE.getSystemState();
      }
      vehicleRealtimeInfoDTO.setSystemState(systemState);
      vehicleRealtimeRepository.set(vehicleRealtimeInfoDTO);
      // 推送6Fe实时信息
      singleVehicleService.pushSingleVehicle(Lists.newArrayList(vehicleRealtimeInfoDTO), Boolean.FALSE);
    } else if (SystemStateManager.isOfflineOrLost(vehicleRealtimeInfoDTO.getSystemState())) {
      log.info("收到在线消息,但是实时信息不为在线!{}", message);
      return;
    }

    vehicleSortService.updateVehicleRealtimeInfo(vehicleName);
    // 推送小铃铛告警,失联删除告警,每次上线重复推送一次
    if (vehicleRealtimeInfoDTO != null) {
      systemState = vehicleRealtimeInfoDTO.getSystemState();
    }

    alarmEventManager.pushChangeAlarmEvent(vehicleName, systemState);
    vehicleConnectManager.pushChangeConnect(vehicleName, systemState);
    vehicleAlarmService.handleGuardianConnectAlarm(guardianConnectDTO, isTodaySchedule);
    vehicleStatusService.handByGuardianConnect(guardianConnectDTO);

    cockpitScoreService.updateScore(vehicleName);
    log.info("车辆连接状态变化当前实时信息={}", vehicleRealtimeInfoDTO);
  }

  /**
   * 判断今天是否有调度
   *
   * @param monitorScheduleEntity
   * @return
   */
  private boolean isTodaySchedule(MonitorScheduleEntity monitorScheduleEntity) {
    if (monitorScheduleEntity == null) {
      return false;
    }
    if (monitorScheduleEntity.getStartDateTime() == null) {
      return false;
    }
    Date beginOfToday = DateUtil.beginOfDay(new Date());
    if (beginOfToday.after(monitorScheduleEntity.getStartDateTime())) {
      // 今天开始时间大于调度开始时间,表示今天没有调度
      return false;
    }
    return true;
  }
}
