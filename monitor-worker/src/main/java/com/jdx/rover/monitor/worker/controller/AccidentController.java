package com.jdx.rover.monitor.worker.controller;

import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.service.web.MonitorAccidentReportService;
import com.jdx.rover.monitor.worker.quartz.AccidentBugSynchronizationJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/monitor/worker/accident")
public class AccidentController {

    @Autowired
    private AccidentBugSynchronizationJob accidentBugSynchronizationJob;

    @Autowired
    private AccidentJdmePushManager accidentJdmePushManager;

    @Autowired
    private MonitorAccidentReportService monitorAccidentReportService;

    /**
     * 刷新事故bug状态
     * @return
     */
    @GetMapping("/refresh")
    public HttpResult changeVehicleStatus() {
        log.info("开始同步事故bug状态, username:{}", LoginUtils.getUsername());
        accidentBugSynchronizationJob.updateBugStatus();
        log.info("同步事故bug状态结束");
        return HttpResult.success();
    }

    /**
     * 手动发送事故日报
     */
    @GetMapping("/send_daily_report")
    public HttpResult sendDailyReport() throws Exception {
        monitorAccidentReportService.accidentDailyReportJob();
        return HttpResult.success();
    }

    /**
     * 手动发送事故周报
     */
    @GetMapping("/send_weekly_report")
    public HttpResult sendWeeklyReport() throws Exception {
        monitorAccidentReportService.accidentWeeklyReportJob();
        return HttpResult.success();
    }
}
