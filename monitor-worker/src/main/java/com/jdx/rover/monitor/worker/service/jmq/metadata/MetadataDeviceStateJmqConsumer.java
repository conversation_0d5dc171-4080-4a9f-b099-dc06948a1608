/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.dto.kafka.DeviceEnableMessageDTO;
import com.jdx.rover.monitor.service.robot.RobotRealtimeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 监听设备主数据状态变更信息(metadata_device_status_message)
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataDeviceStateJmqConsumer implements MessageListener {

    /**
     * 机器人设备停用列表
     */
    private final RobotRealtimeService robotRealtimeService;

    /**
     * 批量处理接收到的消息。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息并更新车辆异常信息。
     * @param message 消息内容。
     */
    private void handleOneMessage(String message) {
        CommonMessageDTO deviceStateChangeMessage = JsonUtils.readValue(message, new TypeReference<CommonMessageDTO>() {
        });
        if (Objects.isNull(deviceStateChangeMessage)) {
            return;
        }
        Map<String, Object> mapInfo = deviceStateChangeMessage.getExtendedInfo();
        if (MapUtils.isEmpty(mapInfo) || Objects.isNull(mapInfo.get("deviceInfo"))) {
            return;
        }
        DeviceEnableMessageDTO deviceBaseInfo = JsonUtils.readValue(
                JsonUtils.writeValueAsString(mapInfo.get("deviceInfo")), DeviceEnableMessageDTO.class);
        robotRealtimeService.processRobotEnableState(deviceBaseInfo.getSerialNo(), deviceBaseInfo.getDeviceId(), deviceBaseInfo.getEnable());
    }
}

