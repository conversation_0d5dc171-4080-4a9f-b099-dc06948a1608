package com.jdx.rover.monitor.worker.service.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.service.cockpit.CockpitScoreService;
import com.jdx.rover.monitor.service.event.SingleVehicleHistoryService;
import com.jdx.rover.monitor.service.vehicle.SingleVehicleService;
import com.jdx.rover.monitor.service.vehicle.VehicleStatusService;
import com.jdx.rover.monitor.worker.service.vehicle.ScheduleScoreService;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 接收调度信息变更信息
 *
 * <AUTHOR>
 * @date 2024/12/5
 */
@Service
@Slf4j
public class MonitorScheduleJmqConsumer implements MessageListener {

  @Resource
  private FlowExecutor flowExecutor;

  @Autowired
  private SingleVehicleService singleVehicleService;

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private ScheduleScoreService scheduleScoreService;

  @Autowired
  private SingleVehicleHistoryService singleVehicleHistoryService;

  /**
   * 车辆状态服务
   */
  @Autowired
  private VehicleStatusService vehicleStatusService;
  @Autowired
  private CockpitScoreService cockpitScoreService;

  /**
   * 消费调度信息变更
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      ScheduleTask scheduleTask = JsonUtils.readValue(message.getText(), ScheduleTask.class);
      if (scheduleTask == null) {
        return;
      }
      String key = RedisKeyEnum.SCHEDULE_UPDATE_LOCK_PREFIX.getValue() + scheduleTask.getVehicleName();
      try {
        // 增加重复获取调度锁更新调度
        if (RedissonUtils.tryLock(key, 3, TimeUnit.SECONDS)) {
          updateSchedule(scheduleTask);
        } else {
          Integer lockCount = RedissonUtils.getLockCount(key);
          log.error("Lock schedule change fail, current count{} ", lockCount);
          if (RedissonUtils.tryLock(key, 2, TimeUnit.SECONDS)) {
            updateSchedule(scheduleTask);
          } else {
            log.error("Lock schedule change repeat fail {}", key);
          }
        }
      } catch (Exception e) {
        log.error("Handle schedule exception ", e);
      } finally {
        RedissonUtils.unLock(key);
        log.info("Unlock schedule change lock count{} ", RedissonUtils.getLockCount(key));
      }
      vehicleStatusService.handBySchedule(scheduleTask);
      // 推送单车页调度变化ws消息
      singleVehicleService.pushSingleVehicleSchedule(scheduleTask.getVehicleName());

      scheduleScoreService.updateScore(scheduleTask.getVehicleName());
      cockpitScoreService.updateScore(scheduleTask.getVehicleName());
    }
  }

  /**
   * 发起更新调度链
   */
  private boolean updateSchedule(ScheduleTask scheduleTask) {
    LiteflowResponse response = flowExecutor.execute2Resp(LiteFlowTaskEnum.SCHEDULECHAIN.getType(), scheduleTask, ScheduleTask.class);
    if (!response.isSuccess()) {
      log.info("调度执行失败，{}", response.getCause());
      return false;
    }
    MonitorScheduleEntity scheduleEntity = response.getSlot().getResponseData();
    if (scheduleEntity != null) {
      vehicleScheduleRepository.set(scheduleEntity);
    }
    singleVehicleHistoryService.pushSingleVehicleSchedule(scheduleTask, scheduleEntity);
    return true;
  }
}
