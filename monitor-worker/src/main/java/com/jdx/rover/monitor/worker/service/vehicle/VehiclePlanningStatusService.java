package com.jdx.rover.monitor.worker.service.vehicle;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.vehicle.VehiclePlanningStatusDO;
import com.jdx.rover.monitor.repository.redis.VehiclePlanningStatusRepository;
import com.jdx.rover.server.api.domain.dto.localview.LocalViewPlanningDTO;
import com.jdx.rover.server.api.domain.dto.localview.PlanningDecisionDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 车辆规划决策服务
 * <AUTHOR>
 * @date 2024/11/25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehiclePlanningStatusService {
  /**
   * 车辆状态
   */
  private final VehiclePlanningStatusRepository vehicleStatusRepository;

  /**
   * 缓存车辆规划决策数据
   *
   * @param planningDto 规划数据
   */
  public void handleVehiclePlaningInfo(String vehicleName, LocalViewPlanningDTO planningDto) {
    PlanningDecisionDTO decision = planningDto.getDecision();
    if (Objects.nonNull(decision)) {
      VehiclePlanningStatusDO vehicleStatus = vehicleStatusRepository.get(vehicleName);
      if (Objects.equals(decision.getIsInPark(), vehicleStatus.getIsInPark())
              && Objects.equals(decision.getIsInFrontOfGate(), vehicleStatus.getIsInFrontOfGate())
              && Objects.equals(decision.getIsInIntersection(), vehicleStatus.getIsInIntersection())) {
        return;
      }
      log.info("更新车辆{}规划状态数据{}", vehicleName, JsonUtils.writeValueAsString(decision));
      ParamMap<VehiclePlanningStatusDO> paramMap = new ParamMap<VehiclePlanningStatusDO>()
              .addNonNullProperty(VehiclePlanningStatusDO::getIsInPark, decision.getIsInPark())
              .addNonNullProperty(VehiclePlanningStatusDO::getIsInFrontOfGate, decision.getIsInFrontOfGate())
              .addNonNullProperty(VehiclePlanningStatusDO::getIsInIntersection, decision.getIsInIntersection());
      vehicleStatusRepository.putAllMapObject(vehicleName, paramMap.toMap());
    }
  }

}
