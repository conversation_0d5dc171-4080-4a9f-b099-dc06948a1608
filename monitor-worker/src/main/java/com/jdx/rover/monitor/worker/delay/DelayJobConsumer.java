/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.delay;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.monitor.manager.delay.DelayJob;
import com.jdx.rover.monitor.manager.delay.DelayJobProducer;
import com.jdx.rover.monitor.manager.delay.DelayJobTask;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 延迟任务消费
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DelayJobConsumer {

    @PostConstruct
    public void startJob() {
        RBlockingQueue<DelayJob> blockingQueue = RedissonUtils.getRedissonClient().getBlockingQueue(DelayJobProducer.DELAY_QUEUE_NAME);
        new Thread(() -> {
            while (true) {
                try {
                    DelayJob delayJob = blockingQueue.poll(5, TimeUnit.MINUTES);
                    if (delayJob == null) {
                        log.info("5分钟内暂无延迟任务,继续获取执行!");
                        continue;
                    }

                    log.info("开始处理延迟任务={}", delayJob);
                    SpringUtil.getBean(delayJob.getBeanName(), DelayJobTask.class).execute(delayJob);
                    log.info("完成处理延迟任务={}", delayJob);

                } catch (Exception e) {
                    log.error("执行任务失败!", e);
                    try {
                        TimeUnit.SECONDS.sleep(60);
                    } catch (Exception ignored) {
                    }
                }
            }
        }).start();
    }
}
