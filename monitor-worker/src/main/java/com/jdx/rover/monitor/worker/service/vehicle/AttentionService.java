package com.jdx.rover.monitor.worker.service.vehicle;

import com.jdx.rover.metadata.api.domain.enums.StationTypeEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.entity.UserAttentionEntity;
import com.jdx.rover.monitor.enums.UserAttentionTypeEnum;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.repository.redis.UserAttentionEventRepository;
import com.jdx.rover.monitor.repository.redis.UserAttentionStationRepository;
import com.jdx.rover.permission.domain.dto.basic.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 用户关注信息同步
 * <AUTHOR>
 */
@Service
@Slf4j
public class AttentionService {
  @Autowired
  private UserAttentionStationRepository userAttentionStationRepository;
  @Autowired
  private UserAttentionEventRepository userAttentionEventRepository;
  @Autowired
  private MetadataStationApiManager metadataStationApiManager;
  @Autowired
  private MetadataUserApiManager userBasicApiManager;

  /**
   * 同步用户关注站点信息
   *
   * @param stationIdList
   */
  public void copyUserAttention(List<Integer> stationIdList) {
    stationIdList.stream().forEach(station -> {
      List<UserAttentionEntity> userAttentionEntityList = userAttentionStationRepository.get(station, UserAttentionTypeEnum.BUMP.getType());
      if (CollectionUtils.isNotEmpty(userAttentionEntityList)) {
        userAttentionEntityList.stream().forEach(userAttentionEntity -> {
          userAttentionEventRepository.set(UserAttentionTypeEnum.BUMP.getType(), userAttentionEntity.getUserName(), String.valueOf(station));
        });
      }
    });

    List<StationBasicDTO> stationBasicList = metadataStationApiManager.getStationInfoListByType(StationTypeEnum.SELF.getValue());
    if (CollectionUtils.isEmpty(stationBasicList)) {
      return;
    }

    stationBasicList.stream().forEach(stationBasic -> {
      UserInfoDTO stationUserInfo = userBasicApiManager.getUserInfoByPhone(stationBasic.getContact());
      if (!Objects.isNull(stationUserInfo)) {
        List<StationBasicDTO> userStationList = metadataStationApiManager.getStationListByUser(stationUserInfo.getUserName());
        if (CollectionUtils.isEmpty(userStationList) || !userStationList.stream().filter(userStation -> Objects.equals(userStation.getStationId(), stationUserInfo.getId())).findAny().isPresent()) {
          return;
        }
        userAttentionEventRepository.set(UserAttentionTypeEnum.BUMP.getType(), stationUserInfo.getUserName(), String.valueOf(stationBasic.getStationId()));
      }
    });
  }


}
