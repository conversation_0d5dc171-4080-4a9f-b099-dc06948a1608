/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.message.UserVehicleChangeMessage;
import com.jdx.rover.monitor.repository.redis.UserAttentionRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSearchRecordRepository;
import com.jdx.rover.monitor.worker.service.vehicle.VehicleSortService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 监听用户车辆列表变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataUserVehicleJmqConsumer implements MessageListener {

  private final VehicleSortService vehicleSortService;

  private final UserAttentionRepository userAttentionRepository;

  private final VehicleSearchRecordRepository vehicleSearchRecordRepository;

  /**
   * 处理传入的消息列表。
   * @param messages 消息列表。
   * @throws Exception 如果处理消息时发生异常。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理用户车辆变更消息
   * @param message 用户车辆变更消息的 JSON 字符串
   */
  private void handleOneMessage(String message) {
    UserVehicleChangeMessage userVehicleChangeMessage = JsonUtils.readValue(message, UserVehicleChangeMessage.class);
    if (userVehicleChangeMessage == null) {
      return;
    }
    if (CollectionUtils.isNotEmpty(userVehicleChangeMessage.getIncreaseUserNameList())) {
      for (String username : userVehicleChangeMessage.getIncreaseUserNameList()) {
        vehicleSortService.initUserSortData(username);
      }
    }
    if (CollectionUtils.isNotEmpty(userVehicleChangeMessage.getDecreaseUserNameList())) {
      for (String username : userVehicleChangeMessage.getDecreaseUserNameList()) {
        vehicleSortService.initUserSortData(username);
        //更新车辆关注和历史记录
        if (CollectionUtils.isNotEmpty(userVehicleChangeMessage.getVehicleNameList())) {
          //更新用户车辆关注列表
          Set<String> vehicleNameSet = userAttentionRepository.get(username);
          userVehicleChangeMessage.getVehicleNameList().forEach(vehicleNameSet::remove);
          List<String> vehicleNameList = vehicleNameSet.stream().sorted().collect(Collectors.toList());
          userAttentionRepository.initSet(username, vehicleNameList);
          //更新历史记录
          vehicleSearchRecordRepository.remove(username, userVehicleChangeMessage.getVehicleNameList());
        }
      }
    }
  }
}

