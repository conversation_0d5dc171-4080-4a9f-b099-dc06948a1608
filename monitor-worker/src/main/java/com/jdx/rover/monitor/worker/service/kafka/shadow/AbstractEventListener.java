package com.jdx.rover.monitor.worker.service.kafka.shadow;

import com.jdx.rover.monitor.enums.mobile.AccidentFlowEnum;
import com.jdx.rover.monitor.service.jdme.*;
import java.util.concurrent.*;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AbstractEventListener {
  //经测试，异步连接时线程池线程核心数量至少要4个
  private static final int coreSize = Runtime.getRuntime().availableProcessors() < 4 ? 4 : Runtime.getRuntime().availableProcessors();
  private static final ThreadPoolExecutor poolExecutor;

    /**
     * 执行任务
     * @param accidentNo
     * @param accidentFlowType
     * @param operator
     */
  public void executeTask(String accidentNo, String accidentFlowType, String operator) {
      poolExecutor.submit(() -> {
           try {
               AccidentFlowEnum flowEnum = AccidentFlowEnum.getByValue(accidentFlowType);
               IAccidentFlowEventService handler = JdmeMessageServiceFactory.create(flowEnum);
               if (handler == null) {
                   log.warn("不支持的事故处理流程类型：【{}】", accidentFlowType);
                   return;
               }
               handler.handleMessage(accidentNo, accidentFlowType, operator, false);
           } catch (Exception e) {
               log.error("京ME消息推送逻辑处理失败：", e);
           }
       });
  }

  static {
        poolExecutor = new ThreadPoolExecutor(coreSize, 10 * coreSize, 5L,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());
    }
}
