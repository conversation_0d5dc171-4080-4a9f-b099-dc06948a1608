package com.jdx.rover.monitor.worker.service.vehicle;

import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleSortTypeEnum;
import com.jdx.rover.monitor.repository.redis.AllVehicleNameSortRepository;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.service.score.UpdateScoreTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 电量分数服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PowerScoreService {
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;
  @Autowired
  private AllVehicleNameSortRepository allVehicleNameSortRepository;

  /**
   * 初始化车辆基础数据
   */
  public void updateScore(String vehicleName, Double power) {
    log.info("初始化单个电量分数开始!={}", vehicleName);
    String systemState = vehicleRealtimeRepository.getSystemState(vehicleName);
    String alarmEvent = vehicleAlarmRepository.getHighPriorityAlarm(vehicleName);
    List<String> vehicleNameSortList = allVehicleNameSortRepository.get();
    // 通过电量排序
    VehicleScoreBO bo = new VehicleScoreBO();
    bo.setScoreType(VehicleSortTypeEnum.POWER.getValue());
    bo.setVehicleName(vehicleName);
    bo.setSystemState(systemState);
    bo.setAlarmEvent(alarmEvent);
    bo.setVehicleNameSortList(vehicleNameSortList);
    bo.setPower(power);
    UpdateScoreTypeEnum.POWER.getConsumer().accept(bo);
    log.info("初始化单个电量分数结束!={}", vehicleName);
  }
}

