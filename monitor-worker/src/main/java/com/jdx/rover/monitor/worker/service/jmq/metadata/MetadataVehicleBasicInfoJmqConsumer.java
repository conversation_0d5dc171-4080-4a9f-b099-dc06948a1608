/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.dto.kafka.VehicleMessageDTO;
import com.jdx.rover.metadata.api.domain.enums.kafka.OperationTypeEnum;
import com.jdx.rover.monitor.worker.service.vehicle.VehicleSortService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 监听车辆基础信息变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataVehicleBasicInfoJmqConsumer implements MessageListener {
  private final VehicleSortService vehicleSortService;

  private static final String key = "vehicleInfo";

  /**
   * 处理从消息队列接收到的消息。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个消息，更新车辆基本信息。
   * @param message JSON 格式的消息字符串，表示车辆基本信息的变化。
   */
  private void handleOneMessage(String message) {
    CommonMessageDTO vehicleBasicChangeMessage = JsonUtils.readValue(message, new TypeReference<CommonMessageDTO>() {
    });
    if (Objects.isNull(vehicleBasicChangeMessage)) {
      return;
    }
    Map<String, Object> vehicleMapInfo = vehicleBasicChangeMessage.getExtendedInfo();
    if (MapUtils.isEmpty(vehicleMapInfo) || Objects.isNull(vehicleMapInfo.get(key))) {
      return;
    }
    VehicleMessageDTO vehicleMessageDTO = JsonUtils.readValue(JsonUtils.writeValueAsString(vehicleMapInfo.get(key)), VehicleMessageDTO.class);
    // TODO: 此处能够判断车辆是删除还是更新，可直接操作监控逻辑
    if (StringUtils.equalsAny(vehicleBasicChangeMessage.getOperation(),
            OperationTypeEnum.ADD.getValue(), OperationTypeEnum.EDIT.getValue())) {
      vehicleSortService.updateVehicleBasicInfo(vehicleMessageDTO.getVehicleName());
    } else if (StringUtils.equalsAny(vehicleBasicChangeMessage.getOperation(), OperationTypeEnum.DELETE.getValue())) {
      vehicleSortService.updateVehicleBasicInfo(vehicleMessageDTO.getVehicleName());
    }
  }
}

