package com.jdx.rover.monitor.worker.service.vehicle;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.SetMultimap;
import com.jdx.rover.local.cache.autoconfigure.service.LocalCacheEvictService;
import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleSortTypeEnum;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.repository.redis.*;
import com.jdx.rover.monitor.service.score.SortScoreUtils;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 初始化分数
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class InitScoreService {
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;
  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;
  @Autowired
  private MetadataVehicleApiManager metadataVehicleApiManager;
  @Autowired
  private AllVehicleNameSortRepository allVehicleNameSortRepository;
  @Autowired
  private AllStationSortRepository allStationSortRepository;
  @Autowired
  private LocalCacheEvictService localCacheEvictService;

  /**
   * 初始化所有车辆基础数据
   */
  public void initAllVehicleBasicInfo() {
    log.info("初始化所有基础信息开始!");
    StopWatch sw = new StopWatch();
    sw.start("Feign获取基础信息");
    List<VehicleBasicDTO> vehicleList = metadataVehicleApiManager.listAll();
    sw.stop();
    sw.start("内存构建筛选对象map");

    // 保存原始基础数据
    SetMultimap<String, String> setMultimap = HashMultimap.create();
    Map<String, VehicleBasicDTO> basicDtoMap = new HashMap<>(vehicleList.size());
    for (VehicleBasicDTO dto : vehicleList) {
      basicDtoMap.put(RedisKeyEnum.BASIC_VEHICLE_PREFIX.getValue() + dto.getName(), dto);

      // 筛选条件业务类型(配送/售卖) 车辆类型(运营/测试)
      String sortTypeKey = RedisKeyEnum.VEHICLE_NAME_SET_SELECT_TYPE_PREFIX.getValue() + StringUtils.trimToEmpty(dto.getBusinessType()) + ":" + StringUtils.trimToEmpty(dto.getOwnerUseCase());
      setMultimap.put(sortTypeKey, dto.getName());
      String sortTypeKey2 = RedisKeyEnum.VEHICLE_NAME_SET_SELECT_TYPE_PREFIX.getValue() + ":" + StringUtils.trimToEmpty(dto.getOwnerUseCase());
      setMultimap.put(sortTypeKey2, dto.getName());
      String sortTypeKey3 = RedisKeyEnum.VEHICLE_NAME_SET_SELECT_TYPE_PREFIX.getValue() + StringUtils.trimToEmpty(dto.getBusinessType()) + ":";
      setMultimap.put(sortTypeKey3, dto.getName());
      String sortTypeKey4 = RedisKeyEnum.VEHICLE_NAME_SET_SELECT_TYPE_PREFIX.getValue() + ":";
      setMultimap.put(sortTypeKey4, dto.getName());
    }
    sw.stop();
    sw.start("Redis保存基础对象");

    RedissonClient redisson = RedissonUtils.getRedissonClient();
    redisson.getKeys().deleteByPattern(RedisKeyEnum.BASIC_VEHICLE_PREFIX.getValue() + "*");
    redisson.getBuckets().set(basicDtoMap);
    localCacheEvictService.publishByName(new String[]{LocalCacheConstant.BASIC_VEHICLE});
    sw.stop();
    sw.start("Redis保存筛选对象map");

    for (String key : setMultimap.keySet()) {
      redisson.getSet(key).delete();
      redisson.getSet(key).addAll(setMultimap.get(key));
    }
    localCacheEvictService.publishByName(new String[]{LocalCacheConstant.VEHICLE_NAME_SET_SELECT_TYPE});
    sw.stop();
    sw.start("Redis保存所有车辆名称");

    // 设置车辆名称排序列表信息
    List<String> vehicleNameSortList = vehicleList.stream().map(VehicleBasicDTO::getName).sorted().collect(Collectors.toList());
    allVehicleNameSortRepository.set(vehicleNameSortList);
    sw.stop();
    sw.start("Redis保存所有站点名称");

    // 设置站点名称排序列表信息
    Set<String> stationTreeSet = new TreeSet<>(Collator.getInstance(java.util.Locale.CHINA));
    for (VehicleBasicDTO dto : vehicleList) {
      stationTreeSet.add(dto.getStationName());
    }
    List<String> stationSortList = new ArrayList<>(stationTreeSet);
    allStationSortRepository.set(stationSortList);
    sw.stop();
    sw.start("初始化所有分数");

    updateScore(vehicleList, vehicleNameSortList, stationSortList);
    sw.stop();
    log.info("初始化所有基础信息和分数耗时={}",sw.prettyPrint());

    log.info("初始化所有基础信息和分数结束!");
  }

  /**
   * 初始化所有车辆基础数据
   */
  public void updateScore(List<VehicleBasicDTO> vehicleList, List<String> vehicleNameSortList, List<String> stationSortList) {
    log.info("初始化所有分数开始!");

    StopWatch sw = new StopWatch();
    sw.start("获取实时信息");
    Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameSortList);
    sw.stop();
    sw.start("获取告警信息");
    Map<String, VehicleAlarmDO> alarmEventMap = vehicleAlarmRepository.listMap(vehicleNameSortList);
    sw.stop();
    sw.start("获取调度信息");
    Map<String, MonitorScheduleEntity> scheduleMap = vehicleScheduleRepository.listMap(vehicleNameSortList);

    sw.stop();
    sw.start("构建BO对象");
    List<VehicleScoreBO> vehicleScoreBOList = new ArrayList<>(vehicleList.size());
    for (VehicleBasicDTO dto : vehicleList) {
      String systemState = getSystemState(dto.getName(), realtimeMap);
      String alarmEvent = getAlarmEvent(dto.getName(), alarmEventMap);
      VehicleScoreBO bo = new VehicleScoreBO();
      bo.setVehicleName(dto.getName());
      bo.setSystemState(systemState);
      bo.setAlarmEvent(alarmEvent);
      bo.setBusinessType(dto.getBusinessType());
      bo.setVehicleNameSortList(vehicleNameSortList);
      bo.setStationName(Optional.ofNullable(dto.getStationName()).orElse(""));
      double power = getPower(bo.getVehicleName(), realtimeMap);
      bo.setPower(power);
      String scheduleState = getScheduleState(bo.getVehicleName(), scheduleMap);
      bo.setScheduleState(scheduleState);
      bo.setStationSortList(stationSortList);
      vehicleScoreBOList.add(bo);
    }

    sw.stop();
    sw.start("通过车号排序");
    // 通过车号排序
    updateScoreByType(VehicleSortTypeEnum.VEHICLE_NAME.getValue(), LocalCacheConstant.SORTED_SET_VEHICLE_NAME, vehicleScoreBOList);

    sw.stop();
    sw.start("通过电量排序");
    // 通过电量排序
    updateScoreByType(VehicleSortTypeEnum.POWER.getValue(), LocalCacheConstant.SORTED_SET_POWER, vehicleScoreBOList);

    sw.stop();
    sw.start("通过业务类型排序");
    // 通过业务类型排序(售卖/配送)
    updateScoreByType(VehicleSortTypeEnum.BUSINESS_TYPE.getValue(), LocalCacheConstant.SORTED_SET_BUSINESS_TYPE, vehicleScoreBOList);

    sw.stop();
    sw.start("通过站点排序");
    // 通过站点排序
    updateScoreByType(VehicleSortTypeEnum.STATION.getValue(), LocalCacheConstant.SORTED_SET_STATION, vehicleScoreBOList);
    sw.stop();
    log.info("初始化所有分数耗时={}",sw.prettyPrint());

    log.info("初始化所有分数结束!");
  }

  /**
   * 通过类型更新分数
   *
   * @param scoreType
   * @param cacheName
   * @param vehicleScoreBOList
   */
  private void updateScoreByType(String scoreType, String cacheName, List<VehicleScoreBO> vehicleScoreBOList) {
    Map<String, Double> scoreMap = new HashMap<>(vehicleScoreBOList.size());
    RScoredSortedSet<String> sortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(cacheName);
    sortedSet.clear();
    // 通过字母对站点名称排序
    for (VehicleScoreBO bo : vehicleScoreBOList) {
      bo.setScoreType(scoreType);
      double score = SortScoreUtils.getScore(bo);
      scoreMap.put(bo.getVehicleName(), score);
    }
    sortedSet.addAll(scoreMap);
    localCacheEvictService.publishByName(new String[]{cacheName});
  }

  /**
   * 获取系统状态
   *
   * @param vehicleName
   * @param realtimeMap
   * @return
   */
  private String getSystemState(String vehicleName, Map<String, VehicleRealtimeInfoDTO> realtimeMap) {
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = realtimeMap.get(vehicleName);
    if (vehicleRealtimeInfoDTO == null || vehicleRealtimeInfoDTO.getSystemState() == null) {
      return SystemStateEnum.OFFLINE.getSystemState();
    }
    return vehicleRealtimeInfoDTO.getSystemState();
  }

  /**
   * 获取调度状态
   *
   * @param vehicleName
   * @param scheduleMap
   * @return
   */
  private String getScheduleState(String vehicleName, Map<String, MonitorScheduleEntity> scheduleMap) {
    MonitorScheduleEntity schedule = scheduleMap.get(vehicleName);
    if (schedule == null || schedule.getScheduleState() == null) {
      return VehicleScheduleState.WAITING.getVehicleScheduleState();
    }
    return schedule.getScheduleState();
  }

  /**
   * 获取最高优告警
   *
   * @param vehicleName
   * @param alarmEventMap
   * @return
   */
  private String getAlarmEvent(String vehicleName, Map<String, VehicleAlarmDO> alarmEventMap) {
    VehicleAlarmDO vehicleAlarmDTO = alarmEventMap.get(vehicleName);
    if (vehicleAlarmDTO == null || CollectionUtils.isEmpty(vehicleAlarmDTO.getAlarmEventList())) {
      return null;
    }
    String result = vehicleAlarmDTO.getAlarmEventList().get(0).getType();
    return result;
  }

  /**
   * 获取电量
   *
   * @param vehicleName
   * @param realtimeMap
   * @return
   */
  private double getPower(String vehicleName, Map<String, VehicleRealtimeInfoDTO> realtimeMap) {
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = realtimeMap.get(vehicleName);
    if (vehicleRealtimeInfoDTO == null || vehicleRealtimeInfoDTO.getPower() == null) {
      return 0.0;
    }
    return vehicleRealtimeInfoDTO.getPower();
  }
}
