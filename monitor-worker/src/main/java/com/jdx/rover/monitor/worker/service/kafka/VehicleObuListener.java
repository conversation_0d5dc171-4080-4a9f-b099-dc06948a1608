package com.jdx.rover.monitor.worker.service.kafka;

import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.entity.Tuple2;
import com.jdx.rover.monitor.service.obu.VehicleDrivingLaneService;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.obu.VehicleDriverDataDTO;
import com.jdx.rover.server.api.domain.enums.obu.LaneTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/**
 * 接收OBU实时信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleObuListener {

  /**
   * 车辆车道线检测服务
   */
  private final VehicleDrivingLaneService vehicleDrivingLaneService;

  /**
   * 一分钟的纳秒数
   */
  private static final long ONE_HOUR_NANOSECOND = TimeUnit.HOURS.toNanos(1);

  private static final long TWO_SECOND_NANOSECOND = TimeUnit.SECONDS.toNanos(2);

  /*
   * 本地过滤Map
   */
  private static ConcurrentMap<String, Tuple2<Long, String>> vehicleHandleTimeMap = new ConcurrentHashMap<>(100);

  @KafkaListener(topics = {KafkaTopicConstant.SERVER_BUS_OBU}, containerFactory = "batchListenerContainerFactory")
  public void onMessage(List<String> messageList) {
    if (CollectionUtils.isEmpty(messageList)) {
      return;
    }
    try {
      for (String message : messageList) {
        if (StringUtils.isBlank(message)) {
          continue;
        }
        VehicleDriverDataDTO vehicleReportData = JsonUtils.readValue(message, VehicleDriverDataDTO.class);
        Tuple2<Long, String> tuple2 = vehicleHandleTimeMap.get(vehicleReportData.getVehicleId());
        if (Objects.isNull(tuple2)) {
          // 车辆重新部署初始化数据
          vehicleDrivingLaneService.initVehicleDrivingLane(vehicleReportData.getVehicleId());
        }
        if (isLaneTypeNoChange(vehicleReportData)) {
          continue;
        }
        vehicleDrivingLaneService.handleDrivingLaneMsg(vehicleReportData);
      }
    } catch (Exception e) {
      log.info("Process vehicle obuInfo exception", e);
    }
  }

  /**
   * 不满足最小时间间隔
   *
   * @param vehicleReportData OBU数据
   * @return
   */
  private boolean isLaneTypeNoChange(VehicleDriverDataDTO vehicleReportData) {
    if (Objects.isNull(vehicleReportData) || StringUtils.isBlank(vehicleReportData.getVehicleId())
            || Objects.isNull(vehicleReportData.getTimestampGnss())) {
      return true;
    }
    String vehicleName = vehicleReportData.getVehicleId();
    Long timeStamp = vehicleReportData.getTimestampGnss();
    String laneType = vehicleReportData.getLaneType();
    Tuple2<Long, String> lastValue = vehicleHandleTimeMap.getOrDefault(vehicleName, Tuple2.tuple(0L, ""));
    // 两条数据间隔小于2s,不满足最小间隔
    if (timeStamp - (Long)lastValue.getField(0) < TWO_SECOND_NANOSECOND) {
      return true;
    } else if (timeStamp - System.currentTimeMillis() * NumberConstant.MILLION > ONE_HOUR_NANOSECOND) {
      // 数据时间大于服务器时间超过1分钟,非法数据,不处理
      return true;
    } else if (Objects.nonNull(lastValue.getField(1)) && StringUtils.equals(laneType, (String)lastValue.getField(1))
          && StringUtils.equals(laneType, LaneTypeEnum.BYCICLE.getValue())) {
      // 上报非机动车道，过滤
      return true;
    }
    vehicleHandleTimeMap.put(vehicleName, Tuple2.tuple(timeStamp, laneType));
    return false;
  }
}

