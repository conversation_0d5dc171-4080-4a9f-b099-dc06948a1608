/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.vehicle;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.VehicleOperateEventDTO;
import com.jdx.rover.monitor.api.domain.enums.OperateStateEnum;
import com.jdx.rover.monitor.api.domain.enums.VehicleOperateTypeEnum;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * <p>
 *  车辆事件推送
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class VehicleOperateEventService {

  @Autowired
  private JmqProducerService jmqProducerService;
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private UserStatusRepository userStatusRepository;

  /**
   * <p>
   * 推送监控用户操作事件.
   * </p>
   */
  public void pushVehicleEvent(MonitorUserOperationEntity monitorUserOperationEntity) {
    VehicleOperateTypeEnum operateTypeEnum = VehicleOperateTypeEnum.of(monitorUserOperationEntity.getOperationType());
    if (Objects.isNull(operateTypeEnum)) {
      return;
    }
    VehicleOperateEventDTO event = new VehicleOperateEventDTO();
    event.setRecordTime(new Date());
    event.setVehicleName(monitorUserOperationEntity.getVehicleName());
    event.setUserName(monitorUserOperationEntity.getUserName());
    event.setOperateSource(monitorUserOperationEntity.getOperationSource());
    event.setOperateState(OperateStateEnum.SUCCESS.getState());
    event.setOperateTime(monitorUserOperationEntity.getTimestamp());
    event.setOperateType(operateTypeEnum.getValue());
    UserStatusDO userStatus = userStatusRepository.get(monitorUserOperationEntity.getUserName());
    if (!Objects.isNull(userStatus)) {
      event.setCockpitNumber(userStatus.getCockpitNumber());
    }
    event.setOperateMessage(monitorUserOperationEntity.getOperationMessage());
    log.info("推送车辆用户操作{}", JsonUtils.writeValueAsString(event));
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_VEHICLE_COMMAND_OPERATION.getTopic(), monitorUserOperationEntity.getVehicleName(), event);
  }

}
