/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.dto.kafka.ErrorCodeTranslateMessageDTO;
import com.jdx.rover.metadata.api.domain.dto.technical.ErrorCodeTranslateInfoDTO;
import com.jdx.rover.monitor.manager.errorcode.MetadataErrorCodeTranslateApiManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 监听错误码映射变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataErrorCodeTranslateJmqConsumer implements MessageListener {
    private final MetadataErrorCodeTranslateApiManager metadataErrorCodeTranslateApiManager;

    private static final String KEY = "errorCodeTranslateInfo";

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息并执行错误码转换操作。
     * @param message 待处理的消息内容。
     */
    private void handleOneMessage(String message) {
        CommonMessageDTO commonMessage = JsonUtils.readValue(message, CommonMessageDTO.class);
        if (Objects.isNull(commonMessage)) {
            return;
        }
        Map<String, Object> translateMap = commonMessage.getExtendedInfo();
        if (MapUtils.isEmpty(translateMap) || Objects.isNull(translateMap.get(KEY))) {
            return;
        }
        ErrorCodeTranslateMessageDTO translateMessage = JsonUtils.readValue(JsonUtils.writeValueAsString(translateMap.get(KEY)), ErrorCodeTranslateMessageDTO.class);
        if (StringUtils.isNotBlank(translateMessage.getOldErrorCode())) {
            metadataErrorCodeTranslateApiManager.getErrorCodeTranslateInfo(translateMessage.getOldErrorCode());
        }

        List<ErrorCodeTranslateInfoDTO> errorCodeTranslateInfoList = metadataErrorCodeTranslateApiManager.getErrorCodeTranslateInfo(translateMessage.getErrorCode());
    }
}

