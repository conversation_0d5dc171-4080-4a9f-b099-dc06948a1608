/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.worker.job;

import com.jdx.rover.monitor.service.pda.PdaService;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 定时更新缓存设备在线统计
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PdaRealtimeStatusStatisticJob implements ScheduleFlowTask {

    private final PdaService pdaService;

    @Override
    public TaskResult doTask(ScheduleContext scheduleContext) {
        pdaService.refreshPdaStatus();
        return TaskResult.success();
    }
}