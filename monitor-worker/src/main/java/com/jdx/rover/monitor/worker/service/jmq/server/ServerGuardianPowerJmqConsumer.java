/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.worker.service.vehicle.PowerScoreService;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePowerDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接收guardian电量信息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerGuardianPowerJmqConsumer implements MessageListener {

  private final PowerScoreService powerScoreService;

  /**
   * 处理消息列表
   * @param messages 消息列表
   * @throws Exception 处理消息时抛出的异常
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理工单统计消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个车辆消息并更新相应的评分。
   */
  private void handleOneMessage(String message) {
    VehiclePowerDTO vehiclePowerDTO = JsonUtils.readValue(message, VehiclePowerDTO.class);
    if (vehiclePowerDTO.getPower() == null) {
      return;
    }
    powerScoreService.updateScore(vehiclePowerDTO.getVehicleName(), vehiclePowerDTO.getPower());
  }
}
