/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.device.jsfapi.domain.jmq.CommonMessageData;
import com.jdx.rover.device.jsfapi.domain.jmq.DeviceMessageData;
import com.jdx.rover.monitor.service.robot.IRobotDeviceService;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 接收基础设备信息变更信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetadataDeviceChangeListener implements MessageListener {

    public void onMessage(List<Message> messages) {
        Map<String, List<DeviceMessageData>> mapResult = new ConcurrentHashMap<>();
        if (messages.size() < 50) {
            log.info("收到基础设备变化通知消息{}", JsonUtils.writeValueAsString(messages));
        }

        for (Message message : messages) {
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                CommonMessageData commonMessageData = JsonUtils.readValue(message.getText(), new TypeReference<>() {
                });
                if (Objects.isNull(commonMessageData) || CollectionUtil.isEmpty(commonMessageData.getExtendedInfo())) {
                    continue;
                }
                DeviceMessageData deviceMessageData = JsonUtils.readValue(JsonUtils.writeValueAsString(commonMessageData.getExtendedInfo().get("deviceInfo")), DeviceMessageData.class);
                List<DeviceMessageData> deviceList = mapResult.getOrDefault(commonMessageData.getOperation(), new ArrayList<>());
                deviceList.add(deviceMessageData);
                mapResult.put(commonMessageData.getOperation(), deviceList);
                ProductTypeEnum productTypeEnum = ProductTypeEnum.getByValue(commonMessageData.getProductKey());
                if (Objects.isNull(productTypeEnum) || CollectionUtils.isEmpty(deviceList)) {
                    continue;
                }
                IRobotDeviceService robotService = (IRobotDeviceService)SpringUtil.getBean(productTypeEnum.getClazz());
                robotService.batchUpdateDeviceGroup(mapResult);
                mapResult.clear();
            } catch (Exception e) {
                log.info("同步设备基础信息异常", e);
            }
        }

    }
}


