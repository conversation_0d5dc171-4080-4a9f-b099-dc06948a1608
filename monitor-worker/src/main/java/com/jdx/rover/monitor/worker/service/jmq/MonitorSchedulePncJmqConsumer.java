package com.jdx.rover.monitor.worker.service.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.MonitorStationVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleSchedulePncRouteRepository;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;


/**
 * 接收三方车路径规划信息
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MonitorSchedulePncJmqConsumer implements MessageListener {

  /**
   * 车辆规划路径缓存
   */
  private final VehicleSchedulePncRouteRepository pncRouteRepository;

  /**
   * 消费调度信息变更
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理工单统计消息失败!{}", message.getText(), e);
      }
    }
  }

  private void handleOneMessage(String message) {
    VehiclePncRouteDTO vehiclePncInfoDto = JsonUtils.readValue(message, VehiclePncRouteDTO.class);
    if (vehiclePncInfoDto == null || StringUtils.isBlank(vehiclePncInfoDto.getVehicleName())) {
      return;
    }

    List<MonitorRoutingPointEntity> routingPointEntityList = new LinkedList<>();
    vehiclePncInfoDto.getNavigationStop().stream().filter(stop -> stop.getRouting() != null).forEach(stop -> {
      stop.getRouting().stream().map(point -> TransformUtility.toGCJ02Point(point.getLatitude(), point.getLongitude())).filter(
              mapPoint -> mapPoint != null).forEach(mapPoint -> {
        MonitorRoutingPointEntity entity = new MonitorRoutingPointEntity();
        entity.setLon(mapPoint.getLongitude());
        entity.setLat(mapPoint.getLatitude());
        routingPointEntityList.add(entity);
      });
    });
    if (CollectionUtils.isEmpty(routingPointEntityList)) {
      return;
    }
    pncRouteRepository.push(vehiclePncInfoDto.getVehicleName(), routingPointEntityList);
    // 通知车辆导航路径变更
    MonitorStationVehicleMapInfoDTO vehicleMapInfoDto = new MonitorStationVehicleMapInfoDTO();
    vehicleMapInfoDto.setName(vehiclePncInfoDto.getVehicleName());
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_UPDATE.getValue(), vehicleMapInfoDto);
    String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + vehiclePncInfoDto.getVehicleName();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    rTopic.publish(JsonUtils.writeValueAsString(wsResult));
  }

  @Data
  static class VehiclePncRouteDTO {
    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 规划停靠点
     */
    private List<VehiclePncStopRouteDTO> navigationStop;

  }

  @Data
  static class VehiclePncStopRouteDTO {
    /**
     * 停靠点ID
     */
    private Integer stopId;

    /**
     * 规划路径
     */
    private List<VehiclePncRoutePositionDTO> routing;

  }

  @Data
  static class VehiclePncRoutePositionDTO {
    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

  }

}
