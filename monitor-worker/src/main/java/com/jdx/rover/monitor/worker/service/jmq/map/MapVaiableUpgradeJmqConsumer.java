/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.worker.service.jmq.map;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.map.api.domain.dto.MapVariableUpgradeDTO;
import com.jdx.rover.map.api.domain.enums.VariableMapTypeEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 动态地图版本升级信息
 *
 * <AUTHOR>
 * @date 2024/02/05
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MapVaiableUpgradeJmqConsumer implements MessageListener {

    /**
     * 处理单个消息并发布到指定主题。
     * @param message 要处理的消息。
     */
    private void handleOneMessage(String message) {
        MapVariableUpgradeDTO dto = JsonUtils.readValue(message, MapVariableUpgradeDTO.class);
        if (dto == null) {
            return;
        }
        if (!Objects.equals(dto.getVariableType(), VariableMapTypeEnum.ATTENTION_REGION_WARN.getValue())) {
            return;
        }
        String topicName = RedisTopicEnum.MAP_VARIABLE_VERSION_UPGRADE.getValue();
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        rTopic.publish(message);
    }

    /**
     * 处理从消息队列接收到的消息。
     * @param messages 接收到的消息列表。
     * @throws Exception 如果处理消息时发生异常。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理动态地图升级变化消息失败!{}", message.getText(), e);
            }
        }
    }
}


