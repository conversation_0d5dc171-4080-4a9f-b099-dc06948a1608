package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.message.UserStationChangeMessage;
import com.jdx.rover.monitor.enums.UserAttentionTypeEnum;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.repository.redis.UserAttentionEventRepository;
import com.jdx.rover.monitor.repository.redis.UserAttentionStationRepository;
import com.jdx.rover.monitor.repository.redis.UserStopRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 接收基础服务用户站点信息变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataUserStationJmqConsumer implements MessageListener {

  private final UserAttentionStationRepository userAttentionStationRepository;
  private final UserStopRepository userStopRepository;
  private final MetadataStationApiManager stationApiManager;
  private final UserAttentionEventRepository userAttentionEventRepository;

  /**
   * 处理接收到的消息列表。
   * @param messages 消息列表。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理用户站点变更消息
   */
  private void handleOneMessage(String message) {
    UserStationChangeMessage userStationChangeMessage = JsonUtils.readValue(message, UserStationChangeMessage.class);
    if (Objects.isNull(userStationChangeMessage)) {
      return;
    }
    if (CollectionUtils.isEmpty(userStationChangeMessage.getStationIdList())) {
      return;
    }
    if (CollectionUtils.isNotEmpty(userStationChangeMessage.getDecreaseUserNameList())) {
      for (Integer stationId : userStationChangeMessage.getStationIdList()) {
        userAttentionStationRepository.delete(stationId, UserAttentionTypeEnum.BUMP.getType(), userStationChangeMessage.getDecreaseUserNameList());
        userStationChangeMessage.getDecreaseUserNameList().stream().forEach(userName ->
                userAttentionEventRepository.delete(UserAttentionTypeEnum.BUMP.getType(), userName, String.valueOf(stationId)));
      }
      for (String userName : userStationChangeMessage.getDecreaseUserNameList()) {
        userStopRepository.remove(userName);
        stationApiManager.clearUserStationLocalCache(userName);
      }
    }
    if (CollectionUtils.isNotEmpty(userStationChangeMessage.getIncreaseUserNameList())) {
      for (String userName : userStationChangeMessage.getIncreaseUserNameList()) {
        userStopRepository.remove(userName);
        stationApiManager.clearUserStationLocalCache(userName);
      }
    }
  }
}

