/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.VehicleAlarmChangeDTO;
import com.jdx.rover.monitor.service.robot.alarm.RobotAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 机器人告警变化监听
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MonitorRobotAlarmEventJmqConsumer implements MessageListener {

  /**
   * 机器人告警服务
   */
  private final RobotAlarmService robotAlarmService;

  /**
   * 处理接收到消息。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理告警事件变化消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个车辆报警消息
   */
  private void handleOneMessage(String message) {
    VehicleAlarmChangeDTO robotAlarmChangeDto = JsonUtils.readValue(message, VehicleAlarmChangeDTO.class);
    if (Objects.isNull(robotAlarmChangeDto)) {
      return;
    }
    robotAlarmService.handleRobotAlarmChange(robotAlarmChangeDto);
  }
}
