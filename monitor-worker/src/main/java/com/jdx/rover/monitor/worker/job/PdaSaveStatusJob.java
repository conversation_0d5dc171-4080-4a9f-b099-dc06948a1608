/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.worker.job;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.monitor.entity.device.DeviceStatusChangeDO;
import com.jdx.rover.monitor.repository.redis.pda.DeviceBatchUpdateStatusRepository;
import com.jdx.rover.monitor.service.robot.IRobotDeviceService;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 同步设备状态信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PdaSaveStatusJob implements ScheduleFlowTask {

    /**
     * 批量更新pad实时状态对象
     */
    public final DeviceBatchUpdateStatusRepository deviceBatchUpdateRepository;

    @Override
    public TaskResult doTask(ScheduleContext scheduleContext) {
        List<DeviceStatusChangeDO> listDevice = deviceBatchUpdateRepository.get();
        if (CollectionUtils.isEmpty(listDevice)) {
            return TaskResult.success();
        }
        deviceBatchUpdateRepository.delete();
        if (listDevice.size() > 2000) {
            log.error("批量更新设备数大于2000条，数据量{}服务降级", listDevice.size());
            listDevice = listDevice.stream().filter(deviceStatusChangeDO ->
                    !StringUtils.equals(ProductTypeEnum.PDA.getValue(), deviceStatusChangeDO.getProductKey())).collect(Collectors.toList());
        }
        try {
            Map<String, List<String>> deviceMap = listDevice.stream().collect(
                    Collectors.groupingBy(DeviceStatusChangeDO::getProductKey, Collectors.mapping(DeviceStatusChangeDO::getDeviceName, Collectors.toList())));
            for (Map.Entry<String, List<String>> entry : deviceMap.entrySet()) {
                ProductTypeEnum productTypeEnum = ProductTypeEnum.getByValue(entry.getKey());
                if (Objects.isNull(productTypeEnum) || CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                IRobotDeviceService robotService = (IRobotDeviceService) SpringUtil.getBean(productTypeEnum.getClazz());
                robotService.batchUpdateDeviceRealtimeInfo(entry.getValue());

            }
        }catch (Exception e) {
            log.error("执行定时任务更新设备实时状态异常", e);
        }
        return TaskResult.success();
    }
}