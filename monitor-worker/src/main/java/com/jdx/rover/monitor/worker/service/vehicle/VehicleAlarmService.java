/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.worker.service.vehicle;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.VehicleAlarmChangeDTO;
import com.jdx.rover.monitor.api.domain.enums.AlarmSourceEnum;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmCategoryEnum;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import com.jdx.rover.monitor.common.utils.alarm.AlarmUtil;
import com.jdx.rover.monitor.dto.vehicle.AlarmEventRealtimeDTO;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.OnlineStatusEnum;
import com.jdx.rover.monitor.manager.vehicle.AlarmEventManager;
import com.jdx.rover.monitor.po.GuardianVehicleAlarmInfo;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleGuardianConnectRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.service.alarm.notice.AlarmNoticeProcessInstance;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.service.web.MonitorAccidentService;
import com.jdx.rover.monitor.service.web.MonitorGuardianInfoService;
import com.jdx.rover.server.api.domain.dto.guardian.GuardianConnectDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.GuardianConnectStatusEnum;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车辆告警服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleAlarmService {
  private final VehicleRealtimeRepository vehicleRealtimeRepository;
  private final VehicleAlarmRepository vehicleAlarmRepository;
  private final VehicleGuardianConnectRepository vehicleGuardianConnectRepository;
  private final AlarmEventManager alarmEventManager;
  private final MonitorGuardianInfoService guardianInfoService;
  private final TrackingEventCollectService trackingEventService;
  private final MonitorAccidentService monitorAccidentService;
  private final VehicleSortService vehicleSortService;
  private final JmqProducerService jmqProducerService;

  /*
   * 消费车辆kafka告警处理消息
   *
   * @param vehicleAlarmDto 车辆实时告警
   */
  public void handleVehicleAlarm(VehicleAlarmDTO vehicleAlarmDto) {
    String vehicleName = vehicleAlarmDto.getVehicleName();
    List<VehicleAlarmDO.VehicleAlarmEventDO> lastAlarm = vehicleAlarmRepository.get(vehicleName);
    // 保存或删除告警事件
    List<VehicleAlarmDO.VehicleAlarmEventDO> currentAlarmList =
            alarmEventManager.saveOrRemoveAlarmEvent(lastAlarm, vehicleAlarmDto.getVehicleName(), vehicleAlarmDto.getAlarmEventList(), AlarmSourceEnum.VEHICLE_ALARM);
    VehicleRealtimeInfoDTO realtimeInfo = vehicleRealtimeRepository.get(vehicleName);
    VehicleAlarmChangeDTO changeDto = new VehicleAlarmChangeDTO();
    changeDto.setVehicleName(vehicleName);
    changeDto.setRecordTime(new Date());
    if (!Objects.isNull(realtimeInfo)) {
      changeDto.setLatitude(realtimeInfo.getLat());
      changeDto.setLongitude(realtimeInfo.getLon());
    }
    List<AlarmInfoDTO> currentAlarm = currentAlarmList.stream().map(alarm ->
            buildAlarmInfo(vehicleName, alarm)).collect(Collectors.toList());
    changeDto.setCurrentAlarm(currentAlarm);
    List<AlarmInfoDTO> disappearAlarmList = buildDisappearAlarm(vehicleName, currentAlarm, lastAlarm);
    changeDto.setDisappearAlarm(disappearAlarmList);
    List<AlarmInfoDTO> addAlarmList = buildAddedAlarm(currentAlarm, lastAlarm);
    changeDto.setAddAlarm(addAlarmList);
    log.info("发送车辆告警变化通知{}", JsonUtils.writeValueAsString(changeDto));
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_VEHICLE_EVENT_CHANGE.getTopic(), vehicleName, changeDto);
  }

  /*
   * 消费车辆kafka告警处理消息
   *
   * @param vehicleAlarmDto 车辆实时告警
   */
  public void handleGuardianConnectAlarm(GuardianConnectDTO guardianConnectDto, boolean isTodaySchedule) {
    String vehicleName = guardianConnectDto.getVehicleName();
    Date reportTime = guardianConnectDto.getRecordTime();
    VehicleRealtimeInfoDTO realtimeInfo = vehicleRealtimeRepository.get(vehicleName);
    VehicleAlarmChangeDTO changeDto = new VehicleAlarmChangeDTO();
    changeDto.setVehicleName(vehicleName);
    changeDto.setRecordTime(new Date());
    if (!Objects.isNull(realtimeInfo)) {
      changeDto.setLatitude(realtimeInfo.getLat());
      changeDto.setLongitude(realtimeInfo.getLon());
    }
    VehicleAlarmEventDTO guardianConnectAlarm = vehicleGuardianConnectRepository.get(vehicleName);
    if (isTodaySchedule && Objects.equals(guardianConnectDto.getGuardianConnectStatus(), GuardianConnectStatusEnum.DISCONNECT.getValue()) &&
            Objects.isNull(guardianConnectAlarm)) {
      guardianConnectAlarm = new VehicleAlarmEventDTO();
      guardianConnectAlarm.setType(VehicleAlarmEnum.GUARDIAN_LOST.getValue());
      guardianConnectAlarm.setReportTime(reportTime);
      vehicleGuardianConnectRepository.add(vehicleName, guardianConnectAlarm);
      // 发送失联告警
      AlarmInfoDTO alarmInfoDto = new AlarmInfoDTO();
      alarmInfoDto.setAlarmType(VehicleAlarmEnum.GUARDIAN_LOST.getValue());
      alarmInfoDto.setAlarmNumber(AlarmUtil.buildAlarmNumber(vehicleName, reportTime));
      alarmInfoDto.setStartTime(reportTime);
      changeDto.setAddAlarm(Lists.newArrayList(alarmInfoDto));
      String jsonStr = JsonUtils.writeValueAsString(changeDto);
      log.info("新增失联告警，发送内容{}", jsonStr);
      jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_VEHICLE_EVENT_CHANGE.getTopic(), vehicleName, changeDto);
    } else if (Objects.equals(guardianConnectDto.getGuardianConnectStatus(), GuardianConnectStatusEnum.CONNECT.getValue()) && !Objects.isNull(guardianConnectAlarm)){
      vehicleGuardianConnectRepository.remove(vehicleName);
      AlarmInfoDTO alarmInfoDto = buildAlarmInfo(vehicleName, guardianConnectAlarm);
      alarmInfoDto.setEndTime(reportTime);
      changeDto.setDisappearAlarm(Lists.newArrayList(alarmInfoDto));
      String jsonStr = JsonUtils.writeValueAsString(changeDto);
      log.info("失联告警消失，发送内容{}", jsonStr);
      jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_VEHICLE_EVENT_CHANGE.getTopic(),vehicleName, changeDto);
    }

  }

  /*
   * 消费车辆预警区域kafka告警处理消息
   *
   * @param vehicleAlarmDto 车辆实时告警
   */
  public void handleVehicleAlarm(com.jdx.rover.monitor.api.domain.dto.VehicleAlarmDTO vehicleAlarmDto) {
    String vehicleName = vehicleAlarmDto.getVehicleName();
    List<AlarmInfoDTO> alarmEventList = vehicleAlarmDto.getAlarmEventList();
    if (CollectionUtils.isEmpty(alarmEventList)) {
      return;
    }
    List<VehicleAlarmDO.VehicleAlarmEventDO> lastAlarm = vehicleAlarmRepository.get(vehicleName);
    Map<String, VehicleAlarmDO.VehicleAlarmEventDO> currentAlarmMap = lastAlarm.stream().collect(Collectors.toMap(VehicleAlarmDO.VehicleAlarmEventDO :: getType, Function.identity()));
    // 保存或删除告警事件
    List<AlarmInfoDTO> addAlarmList = new ArrayList<>();
    List<AlarmInfoDTO> disappearAlarmList = new ArrayList<>();
    List<AlarmInfoDTO> updateAlarmList = new ArrayList<>();

    alarmEventList.stream().forEach(event -> {
      if (Objects.isNull(event.getEndTime())) {
        VehicleAlarmDO.VehicleAlarmEventDO addAlarm = buildAlarmDo(event, vehicleAlarmDto.getAlarmSource());
        VehicleAlarmDO.VehicleAlarmEventDO previesAlarm = currentAlarmMap.put(addAlarm.getType(), addAlarm);
        if (Objects.isNull(previesAlarm)) {
          addAlarmList.add(buildAlarmInfo(vehicleName, addAlarm));
        } else {
          // 更新告警
          addAlarm.setReportTime(previesAlarm.getReportTime());
          updateAlarmList.add(buildAlarmInfo(vehicleName, addAlarm));
        }
      } else {
        VehicleAlarmDO.VehicleAlarmEventDO disappearAlarm = buildAlarmDo(event, vehicleAlarmDto.getAlarmSource());
        disappearAlarmList.add(buildAlarmInfo(vehicleName, disappearAlarm));
        currentAlarmMap.remove(disappearAlarm.getType());
      }
    });
    VehicleRealtimeInfoDTO realtimeInfo = vehicleRealtimeRepository.get(vehicleName);
    VehicleAlarmChangeDTO changeDto = new VehicleAlarmChangeDTO();
    changeDto.setVehicleName(vehicleName);
    changeDto.setRecordTime(new Date());
    if (!Objects.isNull(realtimeInfo)) {
      changeDto.setLatitude(realtimeInfo.getLat());
      changeDto.setLongitude(realtimeInfo.getLon());
    }
    changeDto.setAddAlarm(addAlarmList);
    changeDto.setUpdateAlarm(updateAlarmList);
    changeDto.setDisappearAlarm(disappearAlarmList);
    List<VehicleAlarmDO.VehicleAlarmEventDO> currentAlarmList = currentAlarmMap.values().stream().sorted(Comparator.comparing(tmp -> {
      VehicleAlarmCategoryEnum categoryEnum = VehicleAlarmCategoryEnum.of(tmp.getType());
      return categoryEnum.getPriority();
    })).collect(Collectors.toList());
    VehicleAlarmDO vehicleAlarmDo = new VehicleAlarmDO();
    vehicleAlarmDo.setVehicleName(vehicleName);
    vehicleAlarmDo.setAlarmEventList(currentAlarmList);
    vehicleAlarmDo.setRecordTime(new Date());
    vehicleAlarmRepository.add(vehicleName, vehicleAlarmDo);
    changeDto.setCurrentAlarm(currentAlarmList.stream().map(alarm -> buildAlarmInfo(vehicleName, alarm)).toList());
    log.info("发送车辆告警变化通知{}", JsonUtils.writeValueAsString(changeDto));
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_VEHICLE_EVENT_CHANGE.getTopic(), vehicleName, changeDto);

  }


  /**
   * 告警变化(告警新增或消失)
   *
   * @param changeDto 告警变化数据
   */
  public void handleVehicleAlarmChange(VehicleAlarmChangeDTO changeDto) {
    String vehicleName = changeDto.getVehicleName();
    List<AlarmInfoDTO> addAlarm = changeDto.getAddAlarm();
    List<AlarmInfoDTO> disappearAlarm = changeDto.getDisappearAlarm();
    List<AlarmInfoDTO> updateAlarm = changeDto.getUpdateAlarm();
    List<AlarmInfoDTO> currentAlarm = changeDto.getCurrentAlarm();
    // 推送小铃铛告警信息
    AlarmEventRealtimeDTO alarmEventRealtimeDTO = new AlarmEventRealtimeDTO();
    alarmEventRealtimeDTO.setVehicleName(vehicleName);
    if (CollectionUtils.isNotEmpty(currentAlarm)) {
      AlarmInfoDTO alarmInfoDTO = currentAlarm.get(0);
      alarmEventRealtimeDTO.setAlarmEvent(alarmInfoDTO.getAlarmType());
      alarmEventRealtimeDTO.setReportTime(alarmInfoDTO.getStartTime());
    }
    alarmEventManager.pushChangeAlarmEventData(alarmEventRealtimeDTO);
    alarmEventManager.pushSingleVehicleAlarmData(vehicleName, currentAlarm);
    vehicleSortService.updateVehicleRealtimeInfo(vehicleName);
    if (CollectionUtils.isNotEmpty(addAlarm)) {
      AlarmNoticeProcessInstance.INSTANCE.handleVehicleAlarm(vehicleName, addAlarm);
      guardianInfoService.addVehicleAlarmRecord(vehicleName, addAlarm);
      trackingEventService.pushAlarmEvent(vehicleName, addAlarm);
      monitorAccidentService.autoAccidentReport(vehicleName,addAlarm);
    }
    if (CollectionUtils.isNotEmpty(disappearAlarm)) {
      guardianInfoService.endVehicleAlarmRecord(vehicleName, disappearAlarm, new Date());
    }
    if (CollectionUtils.isNotEmpty(updateAlarm)) {
      updateAlarm.stream().forEach(alarm -> {
        GuardianVehicleAlarmInfo vehicleAlarmInfo = new GuardianVehicleAlarmInfo();
        vehicleAlarmInfo.setVehicleName(vehicleName);
        vehicleAlarmInfo.setAlarmNumber(alarm.getAlarmNumber());
        vehicleAlarmInfo.setDescription(alarm.getErrorMessage());
        guardianInfoService.updateByAlarmNumber(vehicleAlarmInfo);
      });
      trackingEventService.pushAlarmEvent(vehicleName, updateAlarm);
    }

  }

  /**
   * 构造新增告警
   */
  private List<AlarmInfoDTO> buildAddedAlarm(List<AlarmInfoDTO> alarmEventList, List<VehicleAlarmDO.VehicleAlarmEventDO> lastAlarm) {
    if (CollectionUtils.isNotEmpty(alarmEventList)) {
      // 新增告警
      return alarmEventList.stream().filter(
                      alarm -> lastAlarm.stream().map(
                              VehicleAlarmDO.VehicleAlarmEventDO::getType).noneMatch(
                              alarmType -> Objects.equals(alarm.getAlarmType(), alarmType)))
              .collect(Collectors.toList());
    }
    return new ArrayList<>();
  }

  /**
   * 构造消失告警
   */
  private List<AlarmInfoDTO> buildDisappearAlarm(String vehicleName, List<AlarmInfoDTO> alarmEventList, List<VehicleAlarmDO.VehicleAlarmEventDO> lastAlarm) {
    Date endTime = new Date();
    List<AlarmInfoDTO> disappearAlarmList = lastAlarm.stream().filter(
                    alarm -> StringUtils.equals(alarm.getSource(), AlarmSourceEnum.VEHICLE_ALARM.getSource())).filter(
                    alarm -> Objects.isNull(alarmEventList) ||
                            alarmEventList.stream().map(AlarmInfoDTO::getAlarmType)
                                    .noneMatch(alarmType -> Objects.equals(alarm.getType(), alarmType)))
                                    .map(alarm -> {
                                        AlarmInfoDTO alarmInfo = buildAlarmInfo(vehicleName, alarm);
                                        alarmInfo.setEndTime(endTime);
                                        return alarmInfo;
                                    }).collect(Collectors.toList());
    return disappearAlarmList;
  }

  /**
   * 端云告警转换监控告警
   */
  private AlarmInfoDTO buildAlarmInfo(String vehicleName, VehicleAlarmEventDTO alarmEvent) {
    AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
    alarmInfo.setAlarmNumber(AlarmUtil.buildAlarmNumber(vehicleName, alarmEvent.getReportTime()));
    alarmInfo.setAlarmType(alarmEvent.getType());
    alarmInfo.setStartTime(alarmEvent.getReportTime());
    alarmInfo.setErrorCode(alarmEvent.getErrorCode());
    alarmInfo.setErrorLevel(alarmEvent.getErrorLevel());
    alarmInfo.setErrorMessage(alarmEvent.getErrorMessage());
    return alarmInfo;
  }

  /**
   * 缓存告警发送对外告警
   */
  private AlarmInfoDTO buildAlarmInfo(String vehicleName, VehicleAlarmDO.VehicleAlarmEventDO alarmEvent) {
    AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
    alarmInfo.setAlarmNumber(AlarmUtil.buildAlarmNumber(vehicleName, alarmEvent.getReportTime()));
    alarmInfo.setAlarmType(alarmEvent.getType());
    alarmInfo.setStartTime(alarmEvent.getReportTime());
    alarmInfo.setEndTime(alarmEvent.getEndTime());
    alarmInfo.setErrorCode(alarmEvent.getErrorCode());
    alarmInfo.setErrorLevel(alarmEvent.getErrorLevel());
    alarmInfo.setErrorMessage(alarmEvent.getErrorMessage());
    return alarmInfo;
  }

  /**
   * 构建缓存告警
   */
  private VehicleAlarmDO.VehicleAlarmEventDO buildAlarmDo(AlarmInfoDTO event, String alarmSource) {
    VehicleAlarmDO.VehicleAlarmEventDO alarmDo = new VehicleAlarmDO.VehicleAlarmEventDO();
    alarmDo.setType(event.getAlarmType());
    alarmDo.setReportTime(event.getStartTime());
    alarmDo.setEndTime(event.getEndTime());
    alarmDo.setSource(alarmSource);
    alarmDo.setErrorCode(event.getErrorCode());
    alarmDo.setErrorLevel(event.getErrorLevel());
    alarmDo.setErrorMessage(event.getErrorMessage());
    return alarmDo;
  }

}
