package com.jdx.rover.monitor.worker.service.vehicle;

import com.jdx.rover.monitor.bo.vehicle.VehicleScoreBO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleSortTypeEnum;
import com.jdx.rover.monitor.repository.redis.*;
import com.jdx.rover.monitor.repository.util.vehicle.VehicleRealtimeUtils;
import com.jdx.rover.monitor.service.score.SortScoreTypeService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 实时信息分数服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RealtimeScoreService {
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;
  @Autowired
  private VehicleBasicRepository vehicleBasicRepository;
  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;
  @Autowired
  private AllVehicleNameSortRepository allVehicleNameSortRepository;
  @Autowired
  private AllStationSortRepository allStationSortRepository;
  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;
  @Autowired
  private SortScoreTypeService sortScoreTypeService;

  /**
   * 初始化所有车辆基础数据
   */
  public void updateVehicleRealtime(String vehicleName) {
    log.info("初始化单个实时信息开始!={}", vehicleName);
    VehicleBasicDTO dto = vehicleBasicRepository.get(vehicleName);
    List<String> stationSortList = allStationSortRepository.get();
    List<String> vehicleNameSortList = allVehicleNameSortRepository.get();
    updateRealtimeScore(dto, vehicleNameSortList, stationSortList);
    log.info("初始化单个实时信息结束!={}", vehicleName);
  }

  /**
   * 初始化车辆基础数据
   */
  public void updateRealtimeScore(VehicleBasicDTO dto, List<String> vehicleNameSortList, List<String> stationSortList) {
    if (dto == null || dto.getName() == null) {
      log.error("dto对象为空,不更新分数,直接返回!");
      return;
    }
    String vehicleName = dto.getName();
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
    String systemState = VehicleRealtimeUtils.getSystemState(vehicleRealtimeInfoDTO);
    String alarmEvent = vehicleAlarmRepository.getHighPriorityAlarm(vehicleName);
    String scheduleState = vehicleScheduleRepository.getScheduleState(vehicleName);
    double power = VehicleRealtimeUtils.getPower(vehicleRealtimeInfoDTO);

    VehicleScoreBO bo = new VehicleScoreBO();
    bo.setVehicleName(vehicleName);
    bo.setSystemState(systemState);
    bo.setBusinessType(dto.getBusinessType());
    bo.setStationName(Optional.ofNullable(dto.getStationName()).orElse(""));
    bo.setPower(power);
    bo.setAlarmEvent(alarmEvent);
    bo.setVehicleNameSortList(vehicleNameSortList);
    bo.setScheduleState(scheduleState);
    bo.setStationSortList(stationSortList);
    updateRealtimeScore(bo);
  }

  /**
   * 更新分数
   */
  private void updateRealtimeScore(VehicleScoreBO bo) {
    for (VehicleSortTypeEnum itemEnum : VehicleSortTypeEnum.values()) {
      bo.setScoreType(itemEnum.getValue());
      sortScoreTypeService.updateScore(bo);
    }
  }
}
