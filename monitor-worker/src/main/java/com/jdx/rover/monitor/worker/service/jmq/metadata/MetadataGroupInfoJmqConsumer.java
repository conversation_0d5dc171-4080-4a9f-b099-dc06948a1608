/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.dto.kafka.IntegrateStationMessageDTO;
import com.jdx.rover.monitor.entity.device.RobotGroupInfoDO;
import com.jdx.rover.monitor.repository.redis.metadata.RobotGroupInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 监听分组信息变更信息(metadata_integrate_station_message)
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataGroupInfoJmqConsumer implements MessageListener {

    /**
     * 机器人分组信息仓库
     */
    private final RobotGroupInfoRepository robotGroupInfoRepository;

    /**
     * 批量处理接收到的消息。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息并更新车辆异常信息。
     * @param message 消息内容。
     */
    private void handleOneMessage(String message) {
        CommonMessageDTO groupChangeMessage = JsonUtils.readValue(message, new TypeReference<CommonMessageDTO>() {
        });
        if (Objects.isNull(groupChangeMessage)) {
            return;
        }
        Map<String, Object> mapInfo = groupChangeMessage.getExtendedInfo();
        if (MapUtils.isEmpty(mapInfo) || Objects.isNull(mapInfo.get("integrateInfo"))) {
            return;
        }
        IntegrateStationMessageDTO integrateInfo = JsonUtils.readValue(
                JsonUtils.writeValueAsString(mapInfo.get("integrateInfo")), IntegrateStationMessageDTO.class);
        RobotGroupInfoDO groupInfoDo = new RobotGroupInfoDO();
        groupInfoDo.setGroupNo(integrateInfo.getGroupNo());
        groupInfoDo.setProductKey(integrateInfo.getProductType());
        groupInfoDo.setAutoChargeLimit(integrateInfo.getAutoChargeLimit());
        groupInfoDo.setStationName(integrateInfo.getStationName());
        groupInfoDo.setMissionChargeLimit(integrateInfo.getMissionChargeLimit());
        groupInfoDo.setForceChargeLimit(integrateInfo.getForceChargeLimit());
        groupInfoDo.setStationId(integrateInfo.getStationId());
        robotGroupInfoRepository.set(groupInfoDo);
    }
}

