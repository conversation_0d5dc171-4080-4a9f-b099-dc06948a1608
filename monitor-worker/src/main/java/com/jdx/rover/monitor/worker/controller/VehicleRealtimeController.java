package com.jdx.rover.monitor.worker.controller;

/**
 * <AUTHOR>
 */

import com.jdx.rover.monitor.dto.vehicle.MultiVehicleRealtimeDTO;
import com.jdx.rover.monitor.vo.vehicle.MultiVehicleRealtimeVO;
import com.jdx.rover.monitor.service.vehicle.MultiVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试多车页controller类
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/monitor/worker/vehicle/realtime")
public class VehicleRealtimeController {
  @Autowired
  private MultiVehicleService multiVehicleService;

  /**
   * 通过车辆名称获取对象
   *
   * @param vo 多车页入参
   * @return
   */
  @GetMapping("/multi")
  public MultiVehicleRealtimeDTO listMultiVehicle(MultiVehicleRealtimeVO vo) {
    return multiVehicleService.listMultiVehicle(vo);
  }
}
