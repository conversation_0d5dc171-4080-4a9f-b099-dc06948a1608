/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.enums.kafka.SubjectEnum;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitInfoDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleStationInfoDTO;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.manager.cockpit.CockpitManager;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.metadata.CockpitVehicleNameRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 接收基础设备座席车辆信息变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataCockpitVehicleJmqConsumer implements MessageListener {

    private final CockpitManager cockpitManager;
    private final CockpitStatusRepository cockpitStatusRepository;
    private final CockpitVehicleNameRepository cockpitVehicleNameRepository;

    /**
     * 处理接收到的消息列表。
     * @param messages 消息列表，不能为 null。
     */
    public void onMessage(List<Message> messages) {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息，更新驾驶舱状态和关联的车辆名称。
     */
    private void handleOneMessage(String message) {
        CommonMessageDTO commonMessageDTO = JsonUtils.readValue(message, CommonMessageDTO.class);
        if (Objects.isNull(commonMessageDTO)) {
            return;
        }
        if (!Objects.equals(SubjectEnum.COCKPIT_VEHICLE.getValue(), commonMessageDTO.getSubject())) {
            return;
        }

        String cockpitNumber = commonMessageDTO.getKey();
        CockpitInfoDTO cockpitInfoDTO = cockpitManager.getCockpitInfo(cockpitNumber);
        if (Objects.isNull(cockpitInfoDTO)) {
            //删除暂不处理
            return;
        }
        cockpitStatusRepository.putMapValue(cockpitNumber, CockpitStatusDO::getCockpitType, cockpitInfoDTO.getCockpitType());
        List<String> vehicleNameList =
                cockpitInfoDTO.getVehicleInfoList().stream().map(VehicleStationInfoDTO::getVehicleName).collect(Collectors.toList());
        cockpitVehicleNameRepository.initSet(cockpitNumber, vehicleNameList);
    }
}


