package com.jdx.rover.monitor.worker.quartz;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.issue.IssueStateEnum;
import com.jdx.rover.monitor.manager.issue.IssueManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.po.IssueRecord;
import com.jdx.rover.monitor.repository.mapper.IssueRecordMapper;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.sort.VehicleNameScoreSortedSetRepository;
import com.jdx.rover.monitor.search.IssueRecordSearch;
import com.jdx.rover.monitor.service.area.EarlyWarningAreaService;
import com.jdx.rover.monitor.service.cockpit.CockpitChangeService;
import com.jdx.rover.monitor.service.mobile.VehiclePositionService;
import com.jdx.rover.monitor.service.vehicle.SingleVehicleService;
import com.jdx.rover.monitor.service.web.MonitorGuardianInfoService;
import com.jdx.rover.monitor.worker.service.jmq.server.ServerGuardianConnectJmqConsumer;
import com.jdx.rover.server.api.domain.dto.guardian.GuardianConnectDTO;
import com.jdx.rover.server.api.domain.enums.guardian.GuardianConnectStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DependsOn("jtsModule")
public class QuartzScheduleService {
  @Autowired
  private Environment environment;
  @Autowired
  private SingleVehicleService singleVehicleService;
  @Autowired
  private SingleVehicleManager singleVehicleManager;
  @Autowired
  private IssueManager issueManager;
  @Autowired
  private IssueRecordMapper issueRecordMapper;
  @Autowired
  private VehicleNameScoreSortedSetRepository vehicleNameScoreSortedSetRepository;
  @Autowired
  private ServerGuardianConnectJmqConsumer serverGuardianConnectJmqConsumer;
  @Autowired
  private MonitorGuardianInfoService guardianInfoService;
  @Autowired
  private CockpitChangeService cockpitChangeService;
  @Autowired
  private EarlyWarningAreaService earlyWarningAreaService;
  @Autowired
  private VehiclePositionService vehiclePositionService;
  @Autowired
  private VehicleStatusRepository vehicleStatusRepository;
  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;
  @Value("${spring.profiles.active}")
  private String profileActive;


  /**
   * 推送单车页数据
   */
  @Scheduled(cron = "${project.quartz.cron.singleVehicle:0/2 * * * * ?}")
  public void pushSingleVehicleTask() {
    Collection<String> vehicleNameCollection = singleVehicleService.getAndUpdatePushVehicleCollection();
    while (CollectionUtils.isNotEmpty(vehicleNameCollection)) {
      singleVehicleService.pushSingleVehicleRealtime(Lists.newArrayList(vehicleNameCollection));
      vehicleNameCollection = singleVehicleService.getAndUpdatePushVehicleCollection();
    }
  }

  /**
   * 每天0点1分,清除没有当天调度的车辆系统状态,失联变为离线
   */
  @Scheduled(cron = "0 1 0 * * ?")
  public void serverGuardianConnectTask() {
    Collection<String> vehicleNameCollection = vehicleNameScoreSortedSetRepository.getConnectionLostVehicleCollection();
    if (CollectionUtils.isEmpty(vehicleNameCollection)) {
      return;
    }
    for (String vehicleName : vehicleNameCollection) {
      GuardianConnectDTO guardianConnectDTO = new GuardianConnectDTO();
      guardianConnectDTO.setVehicleName(vehicleName);
      guardianConnectDTO.setGuardianConnectStatus(GuardianConnectStatusEnum.DISCONNECT.getValue());
      guardianConnectDTO.setRecordTime(new Date());
      String jsonStr = JsonUtils.writeValueAsString(guardianConnectDTO);
      List<Message> messages = new ArrayList<>();
      Message message = new Message();
      message.setTopic("server_guardian_connect");
      if (!StringUtils.equals(profileActive, "product")) {
        message.setTopic("server_guardian_connect_" + profileActive);
      }
      message.setText(jsonStr);
      messages.add(message);
      try {
        serverGuardianConnectJmqConsumer.onMessage(messages);
      } catch (Exception e) {
        log.error("每日零点清除失联车辆消息异常", e);
        continue;
      }
      log.info("每日零点清除失联车辆消息={}", jsonStr);
    }
  }

  /**
   * 每天0点0分,修改状态为待提报的工单为未提报
   */
  @Scheduled(cron = "0 0 0 * * ?")
  public void issueStateChangeTask() {
    IssueRecordSearch issueRequestVo = new IssueRecordSearch();
    List<IssueRecord> finishedIssueList = issueManager.listFinishedIssue(issueRequestVo);
    if (CollectionUtils.isEmpty(finishedIssueList)) {
      return;
    }
    issueRecordMapper.updateState(IssueStateEnum.FINISH.getIssueState(), IssueStateEnum.UNREPORTED.getIssueState());
    finishedIssueList.stream().forEach(issueRecord -> {
      issueRecord.setState(IssueStateEnum.UNREPORTED.getIssueState());
      issueManager.pushIssueStateUpdateEvent(issueRecord, issueRecord.getStartTime());
    });
    log.info("每日零点清除待提报工单消息={}", finishedIssueList.size());
  }

  /**
   * 每天0点0分,修改用户时长
   */
  @Scheduled(cron = "0 0 0 * * ?")
  public void userTimeChangeTask() {
    cockpitChangeService.userTimeChangeTask();
  }

  /**
   * 每天0点59分,修改状态为未结束的告警增加结束时间
   */
  @Scheduled(cron = "0 58 23 * * ? ")
  public void alarmEndTimeChangeTask() {
    Date endTime = DateUtil.endOfDay(new Date()).toJdkDate();
    guardianInfoService.endVehicleAlarmRecord(null, new ArrayList<>(), endTime);
    log.info("每日零点清除未结束告警信息");
  }

  /**
   * 每5秒钟处理一次预警区域报警
   */
  @Scheduled(cron = "0/5 * * * * ? ")
  public void handEarlyWarning() {
    earlyWarningAreaService.handEarlyWarning();
  }

  /**
   * 每60秒钟处理一次
   */
  @Scheduled(cron = "0 0/1 * * * ? ")
  public void handleVehiclePosition() {
    vehiclePositionService.saveInMotionVehicle();
  }

  /**
   * 每天0点1分,每日零点清除失联车辆接管消息
   */
  @Scheduled(cron = "0 1 0 * * ?")
  public void removeTakeOverTask() {
    log.info("每日零点清除失联和离线车辆接管消息开始");
    Collection<String> vehicleNameCollection = vehicleNameScoreSortedSetRepository.getConnectionLostAndOfflineCollection();
    if (CollectionUtils.isEmpty(vehicleNameCollection)) {
      return;
    }
    Map<String, VehicleTakeOverEntity> vehicleTakeOverEntityMap = vehicleTakeOverRepository.listMap(Lists.newArrayList(vehicleNameCollection));
    for (String vehicleName : vehicleTakeOverEntityMap.keySet()) {
      vehicleTakeOverRepository.remove(vehicleName);
      vehicleStatusRepository.fastRemoveMapKey(vehicleName, VehicleStatusDO::getCockpitNumber, VehicleStatusDO::getCockpitUserName);
    }
    log.info("每日零点清除失联车辆接管消息结束={}", vehicleTakeOverEntityMap);
  }
}