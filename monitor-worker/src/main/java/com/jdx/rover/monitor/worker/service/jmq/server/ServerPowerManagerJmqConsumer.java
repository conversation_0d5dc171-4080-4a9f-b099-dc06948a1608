/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.manager.vehicle.PowerManagerManager;
import com.jdx.rover.server.api.domain.dto.power.ServerPowerManagerDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 接收电量管理信息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerPowerManagerJmqConsumer implements MessageListener {

  private final PowerManagerManager powerManagerManager;

  /**
   * 处理接收到的消息列表。
   * @param messages 消息列表。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理远程下电消息{}失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个消息并更新相关记录和推送信息。
   */
  private void handleOneMessage(String message) {
    ServerPowerManagerDTO serverPowerManagerDTO = JsonUtils.readValue(message, ServerPowerManagerDTO.class);
    if (Objects.isNull(serverPowerManagerDTO)) {
      return;
    }
    powerManagerManager.updateOperationRecordLog(serverPowerManagerDTO);
    powerManagerManager.pushSingleVehiclePowerManagerDTO(serverPowerManagerDTO.getVehicleName());
    powerManagerManager.pushPowerManagerMessage(serverPowerManagerDTO);
    powerManagerManager.handleCockpit(serverPowerManagerDTO);
  }

}
