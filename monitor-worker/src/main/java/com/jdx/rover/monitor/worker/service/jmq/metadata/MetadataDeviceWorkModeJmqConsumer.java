/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.dto.kafka.IntegrateDeviceMessageDTO;
import com.jdx.rover.metadata.api.domain.enums.WarehouseWorkModeEnum;
import com.jdx.rover.monitor.service.robot.RobotRealtimeService;
import com.jdx.rover.monitor.service.robot.alarm.RobotScheduleAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 监听设备工作模式变更信息(metadata_wh_work_mode_message)
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataDeviceWorkModeJmqConsumer implements MessageListener {

    /**
     * 机器人实时信息服务
     */
    private final RobotRealtimeService robotRealtimeService;

    /**
     * 机器人告警服务
     */
    private final RobotScheduleAlarmService robotScheduleAlarmService;

    /**
     * 批量处理接收到的消息。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息并更新车辆异常信息。
     * @param message 消息内容。
     */
    private void handleOneMessage(String message) {
        CommonMessageDTO commonMessageDto = JsonUtils.readValue(message, new TypeReference<CommonMessageDTO>() {
        });
        if (Objects.isNull(commonMessageDto)) {
            return;
        }
        Map<String, Object> mapInfo = commonMessageDto.getExtendedInfo();
        if (MapUtils.isEmpty(mapInfo) || Objects.isNull(mapInfo.get("integrateDeviceInfo"))) {
            return;
        }
        IntegrateDeviceMessageDTO deviceBaseInfo = JsonUtils.readValue(
                JsonUtils.writeValueAsString(mapInfo.get("integrateDeviceInfo")), IntegrateDeviceMessageDTO.class);
        if (Objects.equals(WarehouseWorkModeEnum.SA.getValue(), deviceBaseInfo.getWorkMode())) {
            //单机模式, 消除远程呼叫运维+用户处理记录
            robotScheduleAlarmService.processReportTaskState(deviceBaseInfo.getSerialNo(), new Date(), new Date());
        }

        robotRealtimeService.processRobotWorkMode(deviceBaseInfo.getSerialNo(), deviceBaseInfo.getWorkMode());
    }
}

