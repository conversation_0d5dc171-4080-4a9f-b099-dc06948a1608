/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq;

import cn.hutool.json.JSONUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.dto.accident.AccidentFlowDTO;
import com.jdx.rover.monitor.worker.service.kafka.shadow.AbstractEventListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 事故处理流程事件通知，京ME消息推送逻辑处理
 * <AUTHOR>
 * @date 2024/12/25
 * */
@Service
@Slf4j
public class MonitorAccidentFlowEventJmqConsumer extends AbstractEventListener implements MessageListener {

    /**
     * 处理接收到的 MQTT 消息。
     * @param messages 要处理的消息列表。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理MQTT变化消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息
     * @param message 要处理的消息内容
     */
    private void handleOneMessage(String message) {
        //发送京ME消息推送
        AccidentFlowDTO accidentFlow = JSONUtil.toBean(message, AccidentFlowDTO.class);
        String accidentNo = accidentFlow.getAccidentNo();
        String accidentFlowType = accidentFlow.getAccidentFlowEnum();
        String operator = accidentFlow.getOperator();
        executeTask(accidentNo, accidentFlowType, operator);
    }
}
