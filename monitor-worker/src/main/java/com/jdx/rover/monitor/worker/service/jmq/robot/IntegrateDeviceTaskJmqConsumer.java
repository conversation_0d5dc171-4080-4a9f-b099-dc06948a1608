/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.robot;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.integrate.api.domain.message.IntegrateTaskInfoMessage;
import com.jdx.rover.monitor.entity.device.DeviceStatusChangeDO;
import com.jdx.rover.monitor.service.robot.RobotScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * 接收设备业务变更信息(integrate_task_info_message)
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Component
@Slf4j
public class IntegrateDeviceTaskJmqConsumer implements MessageListener {

    /**
     * 机器人调度任务缓存
     */
    @Autowired
    public RobotScheduleService robotScheduleService;

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for(Message message : messages) {
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            log.info("收到设备{}任务变化通知", message.getText());
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }

    }

    /**
     * 处理单个消息
     * @param messageText 消息文本
     */
    private void handleOneMessage(String messageText) {
        IntegrateTaskInfoMessage taskInfoMessage = JsonUtils.readValue(messageText, new TypeReference<>() {
        });
        if (Objects.isNull(taskInfoMessage) || StringUtils.isBlank(taskInfoMessage.getRobotName())) {
            return;
        }
        robotScheduleService.addSchedule(taskInfoMessage);
    }

}


