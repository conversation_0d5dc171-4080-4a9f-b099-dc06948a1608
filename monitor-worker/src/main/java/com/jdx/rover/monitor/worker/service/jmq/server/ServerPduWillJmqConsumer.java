/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.service.vehicle.VehicleStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * PDU遗嘱监听
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerPduWillJmqConsumer implements MessageListener {

    /**
     * 车辆状态服务
     */
    private final VehicleStatusService vehicleStatusService;

    /**
     * 处理接收到的 MQTT 消息。
     * @param messages 接收到的消息列表。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理MQTT变化消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息。
     * @param message 要处理的消息。
     */
    private void handleOneMessage(String message) {
        vehicleStatusService.handleByPduWill(message);
    }
}
