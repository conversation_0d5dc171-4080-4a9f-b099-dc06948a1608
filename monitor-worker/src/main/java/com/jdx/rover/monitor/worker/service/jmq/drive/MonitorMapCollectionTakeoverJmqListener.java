/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.service.jmq.drive;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dataobject.mapcollection.MonitorMapCollectionTakeoverDTO;
import com.jdx.rover.monitor.enums.drive.command.DriveRemoteCommandTypeEnum;
import com.jdx.rover.monitor.service.drive.DriveRemoteCommandService;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 中控地图采集清除接管消息
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MonitorMapCollectionTakeoverJmqListener implements MessageListener {
    /**
     * 远程指令服务
     */
    private final DriveRemoteCommandService driveRemoteCommandService;

    @Override
    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }

        if (messageList.size() > 1) {
            log.info("Received messageSize={}", messageList.size());
        }

        messageList.forEach(message -> {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message, e);
            }
        });
    }

    private void handleOneMessage(String message) {
        MonitorMapCollectionTakeoverDTO takeoverDTO = JsonUtils.readValue(message, MonitorMapCollectionTakeoverDTO.class);
        DriveRemoteCommandVO remoteCommandVO = new DriveRemoteCommandVO();
        remoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.name());
        remoteCommandVO.setVehicleName(takeoverDTO.getVehicleName());
        remoteCommandVO.setCockpitNumber(takeoverDTO.getCockpitNumber());
        remoteCommandVO.setCockpitUserName(takeoverDTO.getCockpitUserName());
        driveRemoteCommandService.sendCommand(remoteCommandVO);
    }
}