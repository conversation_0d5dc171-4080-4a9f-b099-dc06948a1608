/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.worker.service.vehicle.VehicleAlarmService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车端告警监听
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ServerGuardianAlarmJmqConsumer implements MessageListener {
  /**
  * 车辆报警服务
  */
 private final VehicleAlarmService vehicleAlarmService;

  /**
   * 处理接收到的消息。
   * @param messages 消息列表，包含多个 Message 对象。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理车辆状态变化消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个车辆报警消息
   * @param message 报警消息内容
   */
  private void handleOneMessage(String message) {
      VehicleAlarmDTO vehicleAlarmDTO = JsonUtils.readValue(message, VehicleAlarmDTO.class);
      vehicleAlarmService.handleVehicleAlarm(vehicleAlarmDTO);
  }

}
