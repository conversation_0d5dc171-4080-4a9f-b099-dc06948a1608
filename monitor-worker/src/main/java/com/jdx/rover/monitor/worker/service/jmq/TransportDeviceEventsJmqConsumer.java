/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.service.robot.IRobotDeviceService;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 接收端云设备状态变更信息
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Component
@Slf4j
public class TransportDeviceEventsJmqConsumer implements MessageListener {

    /**
     * 处理设备事件变化通知的消息。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for(Message message : messages) {
            if (Objects.isNull(message) || StringUtils.isBlank(message.getText())) {
                continue;
            }
            log.info("收到设备事件变化通知{}", message.getText());
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个设备消息并上报事件。
     */
    private void handleOneMessage(String message) {
        try {
            TransportDeviceMessage transportDeviceMessage = JsonUtils.readValue(message, new TypeReference<>() {
            });
            if (Objects.isNull(transportDeviceMessage) || Objects.isNull(transportDeviceMessage.getData())) {
                return;
            }
            ProductTypeEnum productTypeEnum = ProductTypeEnum.getByValue(transportDeviceMessage.getHeader().getProductKey());
            if (Objects.isNull(productTypeEnum) || StringUtils.isBlank(transportDeviceMessage.getHeader().getDeviceName())) {
                return;
            }
            IRobotDeviceService robotService = (IRobotDeviceService) SpringUtil.getBean(productTypeEnum.getClazz());
            robotService.eventsReport(transportDeviceMessage);

        } catch (Exception e) {
            log.error("执行设备事件上报异常", e);
        }

    }

}


