package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.monitor.repository.redis.UserStopRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 接收基础设备用户停靠点信息变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataUserStopJmqConsumer implements MessageListener {

  private final UserStopRepository userStopRepository;

  private static final String stopKey = "stopInfo";

  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个消息，清除用户停车信息。
   */
  private void handleOneMessage(String message) {
    CommonMessageDTO userStopChangeMessage = JsonUtils.readValue(message, new TypeReference<CommonMessageDTO>() {
    });
    if (Objects.isNull(userStopChangeMessage)) {
      return;
    }
    Map<String, Object> stopMapInfo = userStopChangeMessage.getExtendedInfo();
    if (MapUtils.isEmpty(stopMapInfo) || Objects.isNull(stopMapInfo.get(stopKey))) {
      return;
    }
    userStopRepository.clearAll();
  }
}

