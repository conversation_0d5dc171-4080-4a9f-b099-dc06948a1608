/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.entity.device.DeviceStatusChangeDO;
import com.jdx.rover.monitor.repository.redis.pda.DeviceBatchUpdateStatusRepository;
import com.jdx.rover.monitor.service.robot.IRobotDeviceService;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import com.jdx.rover.transport.api.domain.dto.status.PropertyChangeDTO;
import com.jdx.rover.transport.api.domain.message.device.TransportDeviceHeader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * 接收端云设备状态变更信息
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Component
@Slf4j
public class TransportDeviceStatusChangeListener implements MessageListener {

    /**
     * 批量更新pad实时状态对象
     */
    @Autowired
    public DeviceBatchUpdateStatusRepository deviceBatchUpdateRepository;

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        HashSet<DeviceStatusChangeDO> deviceList = new HashSet<>();
        for(Message message : messages) {
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            log.info("收到设备{}变化通知", message.getText());
            PropertyChangeDTO propertyChangeDto = JsonUtils.readValue(message.getText(), new TypeReference<>() {
            });
            if (Objects.isNull(propertyChangeDto) || StringUtils.isBlank(propertyChangeDto.getDeviceName())) {
                continue;
            }
            DeviceStatusChangeDO changeDo = new DeviceStatusChangeDO();
            changeDo.setProductKey(propertyChangeDto.getProductKey());
            changeDo.setDeviceName(propertyChangeDto.getDeviceName());
            deviceList.add(changeDo);
            handleOneMessage(propertyChangeDto);
        }
        try {
            deviceBatchUpdateRepository.batchAdd(deviceList);

        }catch (Exception e) {
            log.error("执行更新设备实时状态异常", e);
        }

    }

    /**
     * 处理单个消息
     * @param propertyChangeDto 属性更改DTO对象，包含消息的详细信息
     */
    private void handleOneMessage(PropertyChangeDTO propertyChangeDto) {
        ProductTypeEnum productTypeEnum = ProductTypeEnum.getByValue(propertyChangeDto.getProductKey());
        if (Objects.isNull(productTypeEnum) || StringUtils.isBlank(propertyChangeDto.getDeviceName())) {
            return;
        }
        IRobotDeviceService robotService = (IRobotDeviceService) SpringUtil.getBean(productTypeEnum.getClazz());
        robotService.statusChange(propertyChangeDto);
    }

}


