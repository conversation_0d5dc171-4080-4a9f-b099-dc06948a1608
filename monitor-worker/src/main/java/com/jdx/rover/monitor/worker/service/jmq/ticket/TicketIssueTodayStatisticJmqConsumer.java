/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.ticket;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.service.issue.IssueTodayStatisticService;
import com.jdx.rover.ticket.api.kafka.IssueTodayStatisticDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工单统计信息监听
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TicketIssueTodayStatisticJmqConsumer implements MessageListener {

    /**
     * 工单统计服务
     */
    private final IssueTodayStatisticService issueTodayStatisticService;

    /**
     * 处理从消息队列中接收到的消息。
     * @param messages 消息列表。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理工单统计消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息当天的统计数据。
     */
    private void handleOneMessage(String message) {
        IssueTodayStatisticDTO dto = JsonUtils.readValue(message, IssueTodayStatisticDTO.class);
        if (dto == null) {
            return;
        }
        issueTodayStatisticService.handleOneMessage(dto);
    }

}

