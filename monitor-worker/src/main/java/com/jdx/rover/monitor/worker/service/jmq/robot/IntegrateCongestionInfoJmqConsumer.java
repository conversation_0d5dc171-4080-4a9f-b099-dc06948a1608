/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.robot;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.integrate.api.domain.message.AmrCongestionInfoMessage;
import com.jdx.rover.monitor.service.robot.RobotCongestionInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 接收设备拥堵检测信息(amr_congestion_info_message)
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Component
@Slf4j
public class IntegrateCongestionInfoJmqConsumer implements MessageListener {

    /**
     * 机器人拥堵信息服务
     */
    @Autowired
    public RobotCongestionInfoService robotCongestionInfoService;

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            log.info("收到设备{}拥堵信息变化通知", message.getText());
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }

    }

    /**
     * 处理单个消息
     * @param messageText 消息文本
     */
    private void handleOneMessage(String messageText) {
        AmrCongestionInfoMessage congestionInfoMessage = JsonUtils.readValue(messageText, new TypeReference<>() {
        });
        if (Objects.isNull(congestionInfoMessage) || StringUtils.isBlank(congestionInfoMessage.getMapId())) {
            return;
        }
        robotCongestionInfoService.processCongestionInfoChange(congestionInfoMessage);
    }

}


