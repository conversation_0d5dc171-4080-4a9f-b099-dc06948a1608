package com.jdx.rover.monitor.worker.job;

import com.jdx.rover.monitor.service.web.MonitorAccidentReportService;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 每周事故简报
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AccidentWeeklyReportJob implements ScheduleFlowTask {

    private final MonitorAccidentReportService monitorAccidentReportService;

    @Override
    public TaskResult doTask(ScheduleContext scheduleContext) throws Exception {
        monitorAccidentReportService.accidentWeeklyReportJob();
        return TaskResult.success();
    }
}
