/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.enums.kafka.SubjectEnum;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitInfoDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamInfoDTO;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.entity.cockpit.CockpitTeamStatusDO;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamMqttManager;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitTeamStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 接收基础设备座席团队信息变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataCockpitTeamJmqConsumer implements MessageListener {

    private final CockpitTeamManager cockpitTeamManager;

    private final CockpitTeamMqttManager cockpitTeamMqttManager;

    private final CockpitTeamStatusRepository cockpitTeamStatusRepository;

    /**
     * 处理接收到的消息列表。
     * @param messages 消息列表，不能为 null。
     */
    public void onMessage(List<Message> messages) {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
           handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息，包括更新坐席团队座席列表缓存和推送工单抽屉mqtt消息。
     * @param message 消息内容，类型为String
     */
    private void handleOneMessage(String message) {
        CommonMessageDTO commonMessageDTO = JsonUtils.readValue(message, CommonMessageDTO.class);
        if (Objects.isNull(commonMessageDTO)) {
            return;
        }
        if (!Objects.equals(SubjectEnum.COCKPIT_TEAM_INFO.getValue(), commonMessageDTO.getSubject())) {
            return;
        }
        String cockpitTeamNumber = commonMessageDTO.getKey();
        //1、更新坐席团队座席列表缓存
        CockpitTeamInfoDTO cockpitTeamInfoDTO = cockpitTeamManager.getCockpitTeamInfo(cockpitTeamNumber);
        if (!Objects.isNull(cockpitTeamInfoDTO) && CollectionUtils.isNotEmpty(cockpitTeamInfoDTO.getCockpitInfoList())) {
            // https://github.com/FasterXML/jackson-databind/issues/3404
            // 不能用.toList();返回Collections.unmodifiableList,redis中直接写["a","b"],在redis里面不会写类型["java.util.ArrayList",["a","b"]],否则jackson不能反序列化
            List<String> cockpitList = cockpitTeamInfoDTO.getCockpitInfoList().stream().map(CockpitInfoDTO::getCockpitNumber)
                    .collect(Collectors.toList());
            Map<String, Object> paramMap = new ParamMap<CockpitTeamStatusDO>()
                    .addProperty(CockpitTeamStatusDO::getCockpitTeamName, cockpitTeamInfoDTO.getCockpitTeamName())
                    .addProperty(CockpitTeamStatusDO::getCockpitList, cockpitList)
                    .toMap();
            cockpitTeamStatusRepository.putAllMapObject(cockpitTeamNumber, paramMap);
        }

        //2、推送工单抽屉mqtt消息
        cockpitTeamMqttManager.pushCockpitTeam(cockpitTeamNumber);
    }
}


