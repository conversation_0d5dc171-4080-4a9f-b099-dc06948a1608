<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.jdx.rover.monitor</groupId>
    <artifactId>rover-monitor</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>monitor-common</module>
        <module>monitor-domain</module>
        <module>monitor-api</module>
        <module>monitor-repository</module>
        <module>monitor-worker</module>
        <module>monitor-manager</module>
        <module>monitor-service</module>
        <module>monitor-web</module>
        <module>monitor-client</module>
        <module>mobile-web</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <jdk.version>17</jdk.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>

        <spring-cloud-dependencies.version>2023.0.2</spring-cloud-dependencies.version>
        <spring-cloud-alibaba-dependencies.version>2023.0.1.0</spring-cloud-alibaba-dependencies.version>
        <druid-spring-boot-3-starter.version>1.2.23</druid-spring-boot-3-starter.version>
        <jakartaee-api.version>10.0-M2</jakartaee-api.version>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <guava.version>33.2.1-jre</guava.version>
        <hutool-all.version>5.8.28</hutool-all.version>
        <rover-common.version>1.2.0-SNAPSHOT</rover-common.version>
        <rover-geometry.version>1.0.0-SNAPSHOT</rover-geometry.version>
        <pfinder.version>1.2.2-FINAL</pfinder.version>
        <aws-java-sdk-s3.version>1.12.75</aws-java-sdk-s3.version>
        <jira-rest-java-client-core.version>5.2.0</jira-rest-java-client-core.version>
        <fugue.version>4.7.2</fugue.version>
        <awaitility.version>4.1.0</awaitility.version>
        <jsoup.version>1.14.2</jsoup.version>
        <httpclient5.version>5.3.1</httpclient5.version>
        <liteflow-spring-boot-starter.version>2.12.1</liteflow-spring-boot-starter.version>
        <laf.client.version>1.4.2</laf.client.version>
        <!--redisson版本号配置 -->
        <redisson-spring-boot-starter.version>3.31.0</redisson-spring-boot-starter.version>
        <!-- rover版本 -->
        <rover-schedule-api.version>1.0.0-SNAPSHOT</rover-schedule-api.version>
        <rover-metadata-api.version>1.0.0-SNAPSHOT</rover-metadata-api.version>
        <rover-datacenter-api.version>1.0.0-SNAPSHOT</rover-datacenter-api.version>
        <rover-local-cache-boot-starter.version>1.2.0-SNAPSHOT</rover-local-cache-boot-starter.version>
        <rover-ota-api.version>1.0.0-SNAPSHOT</rover-ota-api.version>
        <rover-map-api.version>1.0.0-SNAPSHOT</rover-map-api.version>
        <rover-permission-api.version>1.0.0-SNAPSHOT</rover-permission-api.version>
        <rover-transport-api.release.version>1.1.3</rover-transport-api.release.version>
        <rover-monitor-api.release.version>1.1.32</rover-monitor-api.release.version>
        <rover-server-proto.release.version>1.1.33</rover-server-proto.release.version>
        <rover-server-api.release.version>1.1.26</rover-server-api.release.version>
        <rover-ticket-api.release.version>1.0.5</rover-ticket-api.release.version>
        <rover-infrastructure-api.release.version>1.0.17</rover-infrastructure-api.release.version>
        <rover-indoor-map.release.version>1.0.0</rover-indoor-map.release.version>
        <rover-integrated-api.release.version>1.0.0-SNAPSHOT</rover-integrated-api.release.version>
        <mariaDB4j.version>2.5.3</mariaDB4j.version>
        <mariaDB4j-springboot.version>2.5.1</mariaDB4j-springboot.version>
        <mariadb-java-client.version>2.7.4</mariadb-java-client.version>
        <embedded-redis.version>0.6</embedded-redis.version>
        <Java-WebSocket.version>1.5.2</Java-WebSocket.version>
        <!-- 公司log4j2安全版本-->
        <rover-shadow-api.release.version>1.0.0-SNAPSHOT</rover-shadow-api.release.version>
        <rover-jsf-provider.version>1.1.0-SNAPSHOT</rover-jsf-provider.version>
        <intelligent-device-api.version>1.0.0-SNAPSHOT</intelligent-device-api.version>
        <schedule-client-starter.version>2.4.3</schedule-client-starter.version>
        <disruptor.version>4.0.0</disruptor.version>
        <jts-core.version>1.19.0</jts-core.version>
        <geotools.version>29.2</geotools.version>
        <jackson-datatype-jts.version>2.14</jackson-datatype-jts.version>
        <dynamic-datasource-spring-boot3-starter.version>4.3.0</dynamic-datasource-spring-boot3-starter.version>
        <postgresql.version>42.7.3</postgresql.version>
        <postgis-jdbc.version>2023.1.0</postgis-jdbc.version>
        <gt-main.version>29.2</gt-main.version>
        <expiringmap.version>0.5.11</expiringmap.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <jmq.version>2.3.5</jmq.version>
        <!-- jsf导入版本-->
        <jaxb-runtime.version>2.3.2</jaxb-runtime.version>
        <javax.annotation.version>1.3.2</javax.annotation.version>
        <jaxb-impl.version>4.0.4</jaxb-impl.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
        <jaxb-core.version>4.0.4</jaxb-core.version>
        <jakarta.xml.bind.version>2.3.2</jakarta.xml.bind.version>
        <fastjson.version>2.0.32</fastjson.version>
        <profiler.version>20240630</profiler.version>
    </properties>
    <!-- 打包配置信息 -->
    <profiles>
        <profile>
            <!-- 开发环境 -->
            <id>develop</id>
            <!-- 默认 -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>develop</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-monitor-api.version>${rover-monitor-api.release.version}-BETA-SNAPSHOT</rover-monitor-api.version>
                <rover-ticket-api.version>${rover-ticket-api.release.version}-BETA-SNAPSHOT</rover-ticket-api.version>
                <rover-infrastructure-api.version>${rover-infrastructure-api.release.version}-SNAPSHOT</rover-infrastructure-api.version>
                <rover-schedule-api.version>1.0.0-TEST-SNAPSHOT</rover-schedule-api.version>
                <rover-metadata-api.version>1.0.0-TEST-SNAPSHOT</rover-metadata-api.version>
                <rover-ota-api.version>1.0.0-TEST-SNAPSHOT</rover-ota-api.version>
                <rover-datacenter-api.version>1.0.0-TEST-SNAPSHOT</rover-datacenter-api.version>
                <rover-shadow-api.release.version>1.0.0-BETA-SNAPSHOT</rover-shadow-api.release.version>
                <rover-permission-api.version>1.0.0-TEST-SNAPSHOT</rover-permission-api.version>
                <rover-transport-api.version>${rover-transport-api.release.version}-BETA-SNAPSHOT</rover-transport-api.version>
                <intelligent-device-api.version>1.0.0-TEST-SNAPSHOT</intelligent-device-api.version>
                <rover-indoor-map.version>${rover-indoor-map.release.version}-BETA-SNAPSHOT</rover-indoor-map.version>
                <rover-integrated-api.version>1.0.0-BETA-SNAPSHOT</rover-integrated-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>test</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-monitor-api.version>${rover-monitor-api.release.version}-BETA-SNAPSHOT</rover-monitor-api.version>
                <rover-ticket-api.version>${rover-ticket-api.release.version}-BETA-SNAPSHOT</rover-ticket-api.version>
                <rover-infrastructure-api.version>${rover-infrastructure-api.release.version}-SNAPSHOT</rover-infrastructure-api.version>
                <rover-schedule-api.version>1.0.0-TEST-SNAPSHOT</rover-schedule-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
                <rover-ota-api.version>1.0.0-TEST-SNAPSHOT</rover-ota-api.version>
                <rover-datacenter-api.version>1.0.0-BETA-SNAPSHOT</rover-datacenter-api.version>
                <rover-shadow-api.release.version>1.0.0-BETA-SNAPSHOT</rover-shadow-api.release.version>
                <rover-permission-api.version>1.0.0-TEST-SNAPSHOT</rover-permission-api.version>
                <rover-transport-api.version>${rover-transport-api.release.version}-BETA-SNAPSHOT</rover-transport-api.version>
                <intelligent-device-api.version>1.0.0-TEST-SNAPSHOT</intelligent-device-api.version>
                <rover-indoor-map.version>${rover-indoor-map.release.version}-BETA-SNAPSHOT</rover-indoor-map.version>
                <rover-integrated-api.version>1.0.0-BETA-SNAPSHOT</rover-integrated-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 预发测试环境 -->
            <id>beta</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>beta</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-monitor-api.version>${rover-monitor-api.release.version}-BETA-SNAPSHOT</rover-monitor-api.version>
                <rover-ticket-api.version>${rover-ticket-api.release.version}-BETA-SNAPSHOT</rover-ticket-api.version>
                <rover-infrastructure-api.version>${rover-infrastructure-api.release.version}-SNAPSHOT</rover-infrastructure-api.version>
                <rover-schedule-api.version>1.0.0-BETA-SNAPSHOT</rover-schedule-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
                <rover-ota-api.version>1.0.0-BETA-SNAPSHOT</rover-ota-api.version>
                <rover-datacenter-api.version>1.0.0-BETA-SNAPSHOT</rover-datacenter-api.version>
                <rover-shadow-api.release.version>1.0.0-BETA-SNAPSHOT</rover-shadow-api.release.version>
                <rover-permission-api.version>1.0.0-BETA-SNAPSHOT</rover-permission-api.version>
                <rover-transport-api.version>${rover-transport-api.release.version}-BETA-SNAPSHOT</rover-transport-api.version>
                <intelligent-device-api.version>1.0.0-BETA-SNAPSHOT</intelligent-device-api.version>
                <rover-indoor-map.version>${rover-indoor-map.release.version}-BETA-SNAPSHOT</rover-indoor-map.version>
                <rover-integrated-api.version>1.0.0-BETA-SNAPSHOT</rover-integrated-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境1 -->
            <id>test1</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>test1</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-monitor-api.version>${rover-monitor-api.release.version}-BETA-SNAPSHOT</rover-monitor-api.version>
                <rover-ticket-api.version>${rover-ticket-api.release.version}-BETA-SNAPSHOT</rover-ticket-api.version>
                <rover-infrastructure-api.version>${rover-infrastructure-api.release.version}-SNAPSHOT</rover-infrastructure-api.version>
                <rover-schedule-api.version>1.0.0-BETA-SNAPSHOT</rover-schedule-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
                <rover-ota-api.version>1.0.0-BETA-SNAPSHOT</rover-ota-api.version>
                <rover-datacenter-api.version>1.0.0-BETA-SNAPSHOT</rover-datacenter-api.version>
                <rover-shadow-api.release.version>1.0.0-BETA-SNAPSHOT</rover-shadow-api.release.version>
                <rover-permission-api.version>1.0.0-BETA-SNAPSHOT</rover-permission-api.version>
                <rover-transport-api.version>${rover-transport-api.release.version}-BETA-SNAPSHOT</rover-transport-api.version>
                <intelligent-device-api.version>1.0.0-TEST-SNAPSHOT</intelligent-device-api.version>
                <rover-indoor-map.version>${rover-indoor-map.release.version}-BETA-SNAPSHOT</rover-indoor-map.version>
                <rover-integrated-api.version>1.0.0-BETA-SNAPSHOT</rover-integrated-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境2 -->
            <id>test2</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>test2</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-monitor-api.version>${rover-monitor-api.release.version}-BETA-SNAPSHOT</rover-monitor-api.version>
                <rover-ticket-api.version>${rover-ticket-api.release.version}-BETA-SNAPSHOT</rover-ticket-api.version>
                <rover-infrastructure-api.version>${rover-infrastructure-api.release.version}-SNAPSHOT</rover-infrastructure-api.version>
                <rover-schedule-api.version>1.0.0-BETA-SNAPSHOT</rover-schedule-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
                <rover-ota-api.version>1.0.0-BETA-SNAPSHOT</rover-ota-api.version>
                <rover-datacenter-api.version>1.0.0-BETA-SNAPSHOT</rover-datacenter-api.version>
                <rover-shadow-api.release.version>1.0.0-BETA-SNAPSHOT</rover-shadow-api.release.version>
                <rover-permission-api.version>1.0.0-BETA-SNAPSHOT</rover-permission-api.version>
                <rover-transport-api.version>${rover-transport-api.release.version}-BETA-SNAPSHOT</rover-transport-api.version>
                <intelligent-device-api.version>1.0.0-TEST-SNAPSHOT</intelligent-device-api.version>
                <rover-indoor-map.version>${rover-indoor-map.release.version}-BETA-SNAPSHOT</rover-indoor-map.version>
                <rover-integrated-api.version>1.0.0-BETA-SNAPSHOT</rover-integrated-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境3 -->
            <id>test3</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>test3</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-monitor-api.version>${rover-monitor-api.release.version}-BETA-SNAPSHOT</rover-monitor-api.version>
                <rover-ticket-api.version>${rover-ticket-api.release.version}-BETA-SNAPSHOT</rover-ticket-api.version>
                <rover-infrastructure-api.version>${rover-infrastructure-api.release.version}-SNAPSHOT</rover-infrastructure-api.version>
                <rover-schedule-api.version>1.0.0-BETA-SNAPSHOT</rover-schedule-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
                <rover-ota-api.version>1.0.0-BETA-SNAPSHOT</rover-ota-api.version>
                <rover-datacenter-api.version>1.0.0-BETA-SNAPSHOT</rover-datacenter-api.version>
                <rover-shadow-api.release.version>1.0.0-BETA-SNAPSHOT</rover-shadow-api.release.version>
                <rover-permission-api.version>1.0.0-BETA-SNAPSHOT</rover-permission-api.version>
                <rover-transport-api.version>${rover-transport-api.release.version}-BETA-SNAPSHOT</rover-transport-api.version>
                <intelligent-device-api.version>1.0.0-TEST-SNAPSHOT</intelligent-device-api.version>
                <rover-indoor-map.version>${rover-indoor-map.release.version}-BETA-SNAPSHOT</rover-indoor-map.version>
                <rover-integrated-api.version>1.0.0-BETA-SNAPSHOT</rover-integrated-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>product</id>
            <properties>
                <activatedProperties>product</activatedProperties>
                <rover-monitor-api.version>${rover-monitor-api.release.version}</rover-monitor-api.version>
                <rover-server-proto.version>${rover-server-proto.release.version}</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}</rover-server-api.version>
                <rover-ticket-api.version>${rover-ticket-api.release.version}</rover-ticket-api.version>
                <rover-infrastructure-api.version>${rover-infrastructure-api.release.version}</rover-infrastructure-api.version>
                <rover-transport-api.version>${rover-transport-api.release.version}</rover-transport-api.version>
                <rover-indoor-map.version>${rover-indoor-map.release.version}-SNAPSHOT</rover-indoor-map.version>
                <rover-integrated-api.version>1.0.0-SNAPSHOT</rover-integrated-api.version>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-common</artifactId>
                <version>${rover-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid-spring-boot-3-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource-spring-boot3-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>net.postgis</groupId>
                <artifactId>postgis-jdbc</artifactId>
                <version>${postgis-jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.geotools</groupId>
                <artifactId>gt-main</artifactId>
                <version>${gt-main.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.jira</groupId>
                <artifactId>jira-rest-java-client-core</artifactId>
                <version>${jira-rest-java-client-core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.atlassian.fugue</groupId>
                <artifactId>fugue</artifactId>
                <version>${fugue.version}</version>
            </dependency>
            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility</artifactId>
                <version>${awaitility.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.laf.config</groupId>
                <artifactId>laf-config-client-jd-spring</artifactId>
                <version>${laf.client.version}</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-jsf-provider</artifactId>
                <version>${rover-jsf-provider.version}</version>
            </dependency>

            <!--redisson配置 -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--rover-schedule配置 -->
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-schedule-api</artifactId>
                <version>${rover-schedule-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-monitor-api</artifactId>
                <version>${rover-monitor-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-server-api</artifactId>
                <version>${rover-server-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-server-proto</artifactId>
                <version>${rover-server-proto.version}</version>
            </dependency>

            <!--rover-metadata配置 -->
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-metadata-api</artifactId>
                <version>${rover-metadata-api.version}</version>
            </dependency>
            <!--rover-ota配置 -->
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-ota-api</artifactId>
                <version>${rover-ota-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-datacenter-api</artifactId>
                <version>${rover-datacenter-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>map-api</artifactId>
                <version>${rover-map-api.version}</version>
            </dependency>
            <!--rover-permission配置 -->
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-permission-api</artifactId>
                <version>${rover-permission-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-ticket-api</artifactId>
                <version>${rover-ticket-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>intelligent-device-api</artifactId>
                <version>${intelligent-device-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-transport-api</artifactId>
                <version>${rover-transport-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.k2</groupId>
                <artifactId>k2-indoor-map-api</artifactId>
                <version>${rover-indoor-map.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover.integrate</groupId>
                <artifactId>integrate-api</artifactId>
                <version>${rover-integrated-api.version}</version>
            </dependency>
            <!--mock mysql-->
            <dependency>
                <groupId>ch.vorburger.mariaDB4j</groupId>
                <artifactId>mariaDB4j</artifactId>
                <version>${mariaDB4j.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.craftercms.mariaDB4j</groupId>
                <artifactId>mariaDB4j-springboot</artifactId>
                <version>${mariaDB4j-springboot.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb-java-client.version}</version>
                <scope>test</scope>
            </dependency>
            <!--mock redis-->
            <dependency>
                <groupId>com.github.kstyrc</groupId>
                <artifactId>embedded-redis</artifactId>
                <version>${embedded-redis.version}</version>
                <scope>test</scope>
            </dependency>

            <!--WebSocket客户端 -->
            <dependency>
                <groupId>org.java-websocket</groupId>
                <artifactId>Java-WebSocket</artifactId>
                <version>${Java-WebSocket.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover.local.cache</groupId>
                <artifactId>rover-local-cache-boot-starter</artifactId>
                <version>${rover-local-cache-boot-starter.version}</version>
            </dependency>
            <dependency>
                <artifactId>rover-shadow-api</artifactId>
                <groupId>com.jdx.rover</groupId>
                <version>${rover-shadow-api.release.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>infrastructure-api</artifactId>
                <version>${rover-infrastructure-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomee</groupId>
                        <artifactId>jakartaee-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.tomee</groupId>
                <artifactId>jakartaee-api</artifactId>
                <version>${jakartaee-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wangyin.schedule</groupId>
                <artifactId>schedule-client-starter</artifactId>
                <version>${schedule-client-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.quartz-scheduler</groupId>
                        <artifactId>quartz</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <dependency>
                <groupId>org.locationtech.jts</groupId>
                <artifactId>jts-core</artifactId>
                <version>${jts-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.geotools</groupId>
                <artifactId>gt-geojson</artifactId>
                <version>${geotools.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jgridshift-core</artifactId>
                        <groupId>jgridshift</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.graphhopper.external</groupId>
                <artifactId>jackson-datatype-jts</artifactId>
                <version>${jackson-datatype-jts.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover.geometry</groupId>
                <artifactId>rover-geometry</artifactId>
                <version>${rover-geometry.version}</version>
            </dependency>
            <!--pfinder-->
            <dependency>
                <groupId>com.jd.pfinder</groupId>
                <artifactId>pfinder-profiler-sdk</artifactId>
                <version>${pfinder.version}</version>
            </dependency>

            <dependency>
                <groupId>net.jodah</groupId>
                <artifactId>expiringmap</artifactId>
                <version>${expiringmap.version}</version>
            </dependency>
            <!--easyexcel-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- JMQ 对应2.3.3-RC2版本客户端 -->
            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-spring</artifactId>
                <version>${jmq.version}</version>
            </dependency>

            <!-- 一下XML 包主要是用来解决高版本JDK（17+ ） jsf存在的问题 -->
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb-runtime.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>${javax.annotation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb-impl.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb-core.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jakarta.xml.bind.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.ump</groupId>
                <artifactId>profiler</artifactId>
                <version>${profiler.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>portable-1.8.6</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.1.0</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>so</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>