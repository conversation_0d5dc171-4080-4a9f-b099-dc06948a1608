package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSelectTypeRepository;
import java.util.Map;
import java.util.Set;
import org.assertj.core.util.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 */
class VehicleOwnerUseCaseManagerTest {

  @Mock
  private UserVehicleNameRepository userVehicleNameRepository;

  @Mock
  private VehicleSelectTypeRepository vehicleSelectTypeRepository;

  @InjectMocks
  private VehicleOwnerUseCaseManager vehicleOwnerUseCaseManager;

  @BeforeEach
  void beforeEach() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void getOwnerUseCaseCountByUsername() {
    String username = "test";
    Set<String> userVehicleNameSet = Sets.set("JD0001", "JD0002", "JD0003", "JD0004");
    Set<String> vehicleOwnerUseCaseSet = Sets.set("JD0001", "JD0002", "JD0003");
    Mockito.when(userVehicleNameRepository.get(username)).thenReturn(userVehicleNameSet);
    for (VehicleOwnerUseCaseEnum itemEnum : VehicleOwnerUseCaseEnum.values()) {
      Mockito.when(vehicleSelectTypeRepository.get(null, itemEnum.getValue())).thenReturn(vehicleOwnerUseCaseSet);
    }
    Map<String, Integer> result = vehicleOwnerUseCaseManager.getOwnerUseCaseCountByUsername(username);
    assertThat(result).hasSize(VehicleOwnerUseCaseEnum.values().length);
    for (Map.Entry<String, Integer> entry : result.entrySet()) {
      assertThat(entry.getValue()).isEqualTo(vehicleOwnerUseCaseSet.size());
    }
  }
}