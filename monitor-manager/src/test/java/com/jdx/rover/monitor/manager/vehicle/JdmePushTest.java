package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.monitor.dto.jdme.*;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfig;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

@Deprecated
public class JdmePushTest {

    public String testCreateGroup(AccidentJdmePushManager manager) {
//        JdmeGroupUser owner = new JdmeGroupUser();
//        owner.setPin("huangxuejun6");

        JdmeGroupUser user1 = new JdmeGroupUser();
        user1.setPin("huangxuejun6");

        JdmeGroupUser user3 = new JdmeGroupUser();
        user3.setPin("zhaoyusha1");

        List<JdmeGroupUser> members = new ArrayList<>();
        members.add(user1);
        members.add(user3);

        JdmeGroup jdmeGroup = new JdmeGroup();
        jdmeGroup.setName("JDZ0110&JR123123131的事故处理群8");
        jdmeGroup.setNotice("JDZ0110&JR123123131的事故处理群8");
        jdmeGroup.setIntro("JDZ0110&JR123123131的事故处理群8");
        jdmeGroup.setUniqueKey("JR123123148");
//        jdmeGroup.setOwner(owner);
        jdmeGroup.setMembers(members);

        String groupId = manager.createGroup(jdmeGroup);
        System.out.println("新群号：" + groupId);
        return groupId;
    }


    public void testSendJueMessage(AccidentJdmePushManager manager, String targetGroupId) throws Exception {
        JueCardDataHeaderTitle headerTitle = new JueCardDataHeaderTitle();
        headerTitle.setContent("事故报备-主动碰撞-严重");

        JueCardDataHeader header = new JueCardDataHeader();
        header.setTheme(JueCardDataEnums.LabelColorType.RED.getCode());
        header.setTitle(headerTitle);

        //elements
        List<JueCardDataElementText> elements = new ArrayList<>();

        JueCardDataElementTextItem elementTextItem = new JueCardDataElementTextItem();
        elementTextItem.setLabel("车牌号");
        elementTextItem.setValue("JDZ0110");
        elementTextItem.setValueType(JueCardDataEnums.ElementValueType.TEXT);

        JueCardDataElementText elementText = new JueCardDataElementText();
        elementText.setContent(elementTextItem);
        elements.add(elementText);

        elementTextItem = new JueCardDataElementTextItem();
        elementTextItem.setLabel("缺陷标题");
        elementTextItem.setValue("测试测试");
        elementTextItem.setValueType(JueCardDataEnums.ElementValueType.TEXT);

        elementText = new JueCardDataElementText();
        elementText.setContent(elementTextItem);
        elements.add(elementText);

        elementText = new JueCardDataElementText();
        elementText.setContent(new JueCardDataElementTextItem("问题跟进人", "huangxuejun6", "", JueCardDataEnums.ElementValueType.TEXT));
        elements.add(elementText);

        //buttons
        List<JueCardDataButton> buttonList = new ArrayList<>();
        //button1
        JueCardDataElementTextItem buttonTextItem = new JueCardDataElementTextItem();
        buttonTextItem.setLabel("加入处理群");

        JueCardDataButtonText buttonText = new JueCardDataButtonText();
        buttonText.setContent(buttonTextItem);

        JueCardDataButtonHref buttonHref = new JueCardDataButtonHref();
        buttonHref.setPc("");
        buttonHref.setMobile("");

        JueCardDataButtonBehavior buttonBehavior =  new JueCardDataButtonBehavior();
        buttonBehavior.setMethod(JueCardDataEnums.ButtonBehaviorMethod.JOIN_GROUP_CHAT);
        buttonBehavior.setParams(targetGroupId);

        JueCardDataButton button = new JueCardDataButton();
        button.setType(JueCardDataEnums.ButtonType.PRIMARY.getCode());
        button.setEnable(true);
        button.setText(buttonText);
        button.setHref(buttonHref);
        button.setBehavior(buttonBehavior);
        buttonList.add(button);

        //button2
        buttonTextItem = new JueCardDataElementTextItem();
        buttonTextItem.setLabel("咨询人工");

        buttonText = new JueCardDataButtonText();
        buttonText.setContent(buttonTextItem);

        buttonHref = new JueCardDataButtonHref();
        buttonHref.setPc("timline://chat/?topin=huangxuejun6");
        buttonHref.setMobile("jdme://jm/biz/im/contact/details?mparam={\"erp\":\"huangxuejun6\"}");

        button = new JueCardDataButton();
        button.setType(JueCardDataEnums.ButtonType.DEFAULT.getCode());
        button.setEnable(true);
        button.setText(buttonText);
        button.setHref(buttonHref);
        buttonList.add(button);

        JueCardDataButtons buttons = new JueCardDataButtons();
        buttons.setLayout(JueCardDataEnums.ButtonLayout.ROW);
        buttons.setButtons(buttonList);
        //发送领导群
        JueCardData cardData = new JueCardData();
        cardData.setGroupId("10210990056");
        cardData.setHeader(header);
        cardData.setElements(elements);
        cardData.setButtons(buttons);

        manager.sendJUEMsg(cardData);
        //发送事故群
        cardData.setGroupId(targetGroupId);
        cardData.getButtons().getButtons().remove(0);
        manager.sendJUEMsg(cardData);
    }

    public void testAddGroupMember(AccidentJdmePushManager manager) {
        String groupId = "10211196955";
        List<JdmeGroupUser> memberList = new ArrayList<>();
        JdmeGroupUser user = new JdmeGroupUser();
        user.setPin("huangxuejun6");
        user.setNickname("黄学军");
        memberList.add(user);

        boolean bool = manager.addGroupMember(memberList, groupId);
        System.out.println(bool ? "添加群成员成功！" : "添加群成员失败");
    }


    public static void main(String[] args) throws Exception {
        AccidentJdmePushManager manager = new AccidentJdmePushManager();
        //设置初始化参数值
        JdmeConfig jdmeConfig = new JdmeConfig();
        Field field = AccidentJdmePushManager.class.getDeclaredField("jdmeConfig");
        field.setAccessible(true);
        field.set(manager, jdmeConfig);

        JdmePushTest test = new JdmePushTest();
        //创建群
//        String newGroupId = test.testCreateGroup(manager);
        //发送群卡片消息
//        test.testSendJueMessage(manager, newGroupId);
        //给群添加成员
        test.testAddGroupMember(manager);//https://storage.360buyimg.com/jd-news-pc/img/dongdong2.png
    }
}
