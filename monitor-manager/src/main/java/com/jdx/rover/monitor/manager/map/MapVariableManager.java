/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.map;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.map.api.domain.dto.MapVariableApiJsfDTO;
import com.jdx.rover.map.api.domain.enums.VariableMapTypeEnum;
import com.jdx.rover.map.api.domain.vo.MapVariableApiVO;
import com.jdx.rover.map.api.service.MapVariableJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 动态地图服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/22
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MapVariableManager {

    private final MapVariableJsfService mapVariableJsfService;

    /**
     * 通过地图服务获取动态关注预警区域
     */
    public List<MapVariableApiJsfDTO> listAttentionAlarm() {
        List<MapVariableApiJsfDTO> result = new ArrayList<>();
        try {
            MapVariableApiVO pageVO = new MapVariableApiVO();
            pageVO.setPageNum(1);
            pageVO.setPageSize(2000);
            pageVO.setType(VariableMapTypeEnum.ATTENTION_REGION_WARN.getValue());
            do {
                HttpResult<List<MapVariableApiJsfDTO>> httpResult = mapVariableJsfService.getMapVariablePageList(pageVO);
                if (!HttpResult.isSuccess(httpResult)) {
                    log.info("调用返回失败!{}", httpResult);
                    result.clear();
                    break;
                }
                List<MapVariableApiJsfDTO> dataList = httpResult.getData();
                if (CollectionUtils.isEmpty(dataList)) {
                    break;
                }
                result.addAll(dataList);
                if (dataList.size() < pageVO.getPageSize()) {
                    break;
                }
                pageVO.setPageNum(pageVO.getPageNum() + 1);
            } while (true);
        } catch (Exception e) {
            log.warn("通过地图服务获取动态关注预警区域失败!{}", e.toString());
        }
        log.info("通过地图服务获取动态关注预警区域调用完成,结果={}", JsonUtils.writeValueAsString(result));
        return result;
    }

    /**
     * 通过地图服务获取动态关注预警区域
     */
    public MapVariableApiJsfDTO getByMapVariableId(Integer mapVariableId) {
        MapVariableApiJsfDTO result = null;
        try {
            HttpResult<MapVariableApiJsfDTO> httpResult = mapVariableJsfService.getByMapVariableId(mapVariableId);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        } catch (Exception e) {
            log.error("通过ID获取动态地图调用失败,{},{}", mapVariableId, e.toString());
        }
        return result;
    }
}
