
/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.OnlineStatusEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 车辆路口状态Manager
 *
 * <AUTHOR>
 */
@Slf4j
public class VehicleStatusManager {
    /**
     * 是否guardian在线
     *
     * @return
     */
    public static boolean isGuardianOnline(VehicleStatusDO vehicleStatus) {
        if (Objects.isNull(vehicleStatus)) {
            return false;
        }
        if (Objects.equals(vehicleStatus.getGuardianOnline(), OnlineStatusEnum.ONLINE.name())) {
            return true;
        }
        return false;
    }

    /**
     * 是否pdu离线
     *
     * @return
     */
    public static boolean isPduOffline(VehicleStatusDO vehicleStatus) {
        if (Objects.isNull(vehicleStatus)) {
            return true;
        }
      return !Objects.equals(vehicleStatus.getPduOnline(), OnlineStatusEnum.ONLINE.name());
    }
}
