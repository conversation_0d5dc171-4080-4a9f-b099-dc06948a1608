/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.monitor.enums.vehicle.sort.VehicleScheduleStateSortEnum;
import com.jdx.rover.monitor.repository.redis.sort.CockpitScoreSortedSetRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ScheduleStateManager {
    private final CockpitScoreSortedSetRepository cockpitScoreSortedSetRepository;

    /**
     * 获取任务中车辆数
     *
     * @return list
     */
    public int getHaveScheduleCount(List<String> vehicleNameList) {
        if (CollectionUtils.isEmpty(vehicleNameList)) {
            return 0;
        }
        Map<String, Double> map = cockpitScoreSortedSetRepository.getValueAndScoreMap();
        long result = vehicleNameList.stream().filter(s -> haveSchedule(map, s)).count();
        return (int) result;
    }

    /**
     * 是否有任务
     */
    private static boolean haveSchedule(Map<String, Double> map, String vehicleName) {
        return !isWaiting(map.get(vehicleName));
    }

    /**
     * 是否无任务
     */
    private static boolean isWaiting(Double score) {
        // 分数不存在不匹配,防止空指针
        if (score == null) {
            return true;
        }
        int scheduleStateScore = (int) (score % 10);
        return scheduleStateScore == VehicleScheduleStateSortEnum.WAITING.getValue();
    }
}
