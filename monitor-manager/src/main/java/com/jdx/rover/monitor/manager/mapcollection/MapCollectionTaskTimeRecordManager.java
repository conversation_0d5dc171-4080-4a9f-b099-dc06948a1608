package com.jdx.rover.monitor.manager.mapcollection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.monitor.domain.jsonhandler.RealRouteListTypeHandler;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTaskTimeRecord;
import com.jdx.rover.monitor.po.mapcollection.json.RealRoutePoint;
import com.jdx.rover.monitor.repository.mapper.mapcollection.MapCollectionTaskTimeRecordMapper;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 采图记录时间表manager
 */
@Service
@RequiredArgsConstructor
public class MapCollectionTaskTimeRecordManager extends BaseManager<MapCollectionTaskTimeRecordMapper, MapCollectionTaskTimeRecord> {

    /**
     * 新增任务记录
     *
     * @param taskId        taskId
     * @param vehicleName   vehicleName
     * @param cockpitNumber cockpitNumber
     * @param username      username
     * @param startTime     startTime
     */
    public void createRecord(Integer taskId, String vehicleName, String cockpitNumber, String username, Date startTime) {
        MapCollectionTaskTimeRecord record = new MapCollectionTaskTimeRecord();
        record.setTaskId(taskId);
        record.setVehicleName(vehicleName);
        record.setStartTime(startTime);
        record.setCockpitNumber(cockpitNumber);
        record.setCreateUser(username);
        record.setModifyUser(username);
        this.save(record);
    }

    /**
     * 更新任务记录
     *
     * @param drivingMileage drivingMileage
     * @param routePointList routePointList
     * @param username       username
     * @param taskId         taskId
     * @param vehicleName    vehicleName
     * @param paused         paused
     * @return endTime
     */
    public Date updateRecord(Double drivingMileage, List<RealRoutePoint> routePointList, String username, Integer taskId, String vehicleName, boolean paused) {
        Date endTime = new Date();
        this.lambdaUpdate()
                .set(MapCollectionTaskTimeRecord::getEndTime, endTime)
                .set(MapCollectionTaskTimeRecord::getRealRoute, routePointList, "typeHandler=" + RealRouteListTypeHandler.class.getName())
                .set(MapCollectionTaskTimeRecord::getDrivingMileage, drivingMileage)
                .set(MapCollectionTaskTimeRecord::getModifyUser, username)
                .set(MapCollectionTaskTimeRecord::getPaused, paused)
                .eq(MapCollectionTaskTimeRecord::getTaskId, taskId)
                .eq(MapCollectionTaskTimeRecord::getVehicleName, vehicleName)
                .isNull(MapCollectionTaskTimeRecord::getEndTime)
                .update();
        return endTime;
    }

    /**
     * 查询任务记录（获取最新的任务记录，开始时间和结束时间都不为空）
     *
     * @param taskId      taskId
     * @param vehicleName vehicleName
     * @return List<MapCollectionTaskTimeRecord>
     */
    public MapCollectionTaskTimeRecord queryRecord(Integer taskId, String vehicleName) {
        LambdaQueryWrapper<MapCollectionTaskTimeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MapCollectionTaskTimeRecord::getTaskId, taskId)
            .eq(MapCollectionTaskTimeRecord::getVehicleName, vehicleName)
            .isNotNull(MapCollectionTaskTimeRecord::getStartTime)
            .isNotNull(MapCollectionTaskTimeRecord::getEndTime)
            .orderByDesc(MapCollectionTaskTimeRecord::getId)
            .last(" limit 1");
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 获取任务车辆实际路径
     *
     * @param taskId taskId
     * @return List<MapCollectionTaskTimeRecord>
     */
    public List<MapCollectionTaskTimeRecord> getRealRoute(Integer taskId) {
        return this.lambdaQuery()
                .select(MapCollectionTaskTimeRecord::getRealRoute, MapCollectionTaskTimeRecord::getPaused)
                .eq(MapCollectionTaskTimeRecord::getTaskId, taskId)
                .orderByAsc(MapCollectionTaskTimeRecord::getId)
                .list();
    }

    /**
     * 查询任务记录
     *
     * @param taskId taskId
     * @return List<MapCollectionTaskTimeRecord>
     */
    public List<MapCollectionTaskTimeRecord> queryRecordByTaskId(Integer taskId) {
        return this.lambdaQuery()
            .eq(MapCollectionTaskTimeRecord::getTaskId, taskId)
            .select(MapCollectionTaskTimeRecord::getStartTime, MapCollectionTaskTimeRecord::getEndTime)
            .list();
    }

    /**
     * 计算车辆今日地图采集用时，单位：分钟
     *
     * @param vehicleName vehicleName
     * @return workingTime
     */
    public long calculateVehicleWorkingTime(String vehicleName) {
        long workingTime = 0;

        // 查询车辆今日记录
        List<MapCollectionTaskTimeRecord> mapCollectionTaskTimeRecords = lambdaQuery()
            .eq(MapCollectionTaskTimeRecord::getVehicleName, vehicleName)
            .gt(MapCollectionTaskTimeRecord::getStartTime, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE))
            .select(MapCollectionTaskTimeRecord::getStartTime, MapCollectionTaskTimeRecord::getEndTime)
            .list();
        if (CollUtil.isNotEmpty(mapCollectionTaskTimeRecords)) {
            for (MapCollectionTaskTimeRecord mapCollectionTaskTimeRecord : mapCollectionTaskTimeRecords) {
                if (ObjectUtil.isNotNull(mapCollectionTaskTimeRecord.getStartTime()) && ObjectUtil.isNotNull(mapCollectionTaskTimeRecord.getEndTime())) {
                    workingTime += DateUtil.between(mapCollectionTaskTimeRecord.getStartTime(), mapCollectionTaskTimeRecord.getEndTime(), DateUnit.MINUTE);
                } else if (ObjectUtil.isNull(mapCollectionTaskTimeRecord.getEndTime())) {
                    workingTime += DateUtil.between(mapCollectionTaskTimeRecord.getStartTime(), DateUtil.date(), DateUnit.MINUTE);
                }
            }
        }
        return workingTime;
    }
}