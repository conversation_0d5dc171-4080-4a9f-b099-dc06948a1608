/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.monitor.entity.MonitorScheduleStopOrderEntity;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.enums.schedule.OrderDeliveryStateUpdateEnum;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleStopOrderRepository;
import com.jdx.rover.schedule.api.domain.enums.DeliveryStatus;
import com.jdx.rover.schedule.api.domain.enums.StopAction;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleOrderForMonitor;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <p>
 * This is a vehicle realtime state service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@LiteflowComponent(id = "updateScheduleStopOrder", name = "更新调度停靠点订单")
public class ScheduleStopOrderUpdateComponent extends NodeComponent {

  @Autowired
  private VehicleScheduleStopOrderRepository vehicleScheduleStopOrderRepository;

  @Override
  public boolean isAccess() {
    Map<String, OrderDeliveryStateUpdateEnum> orderUpdateMap = getSlot().getChainReqData(LiteFlowTaskEnum.UPDATESCHEDULESTOPORDER.getType());
    return !(orderUpdateMap == null || orderUpdateMap.isEmpty());
  }

  /**
   * <p>
   * Handle realtime schedule message.
   * </p>
   */
  @Override
  public void process() throws Exception {
    ScheduleOrderForMonitor scheduleTaskOrder = getSlot().getRequestData();
    Map<String, OrderDeliveryStateUpdateEnum> orderUpdateMap = getSlot().getChainReqData(LiteFlowTaskEnum.UPDATESCHEDULESTOPORDER.getType());
    Map<String, MonitorScheduleStopOrderEntity> redisMap = vehicleScheduleStopOrderRepository.getMap(scheduleTaskOrder.getVehicleName());
    if (redisMap.isEmpty()) {
      return;
    }
    scheduleTaskOrder.getScheduleOrder().stream().forEach(order -> {
      String taskType = order.getScheduleTaskType();
      String deliveryStatus = order.getDeliveryStatus();
      String stopAction = StopAction.PICKUP.getStopAction();
      Integer stopId = order.getStopId();
      if (StringUtils.equals(TaskType.LOADTASK.getTaskType(), taskType)) {
        stopId = order.getLoadStopId();
        stopAction = StopAction.LOAD.getStopAction();
      } else if (StringUtils.equals(TaskType.UNLOADTASK.getTaskType(), taskType)) {
        stopId = order.getLoadStopId();
        stopAction = StopAction.UNLOAD.getStopAction();
      } else if (StringUtils.equals(TaskType.DROPOFF.getTaskType(), taskType)) {
        stopAction = StopAction.DROPOFF.getStopAction();
      }
      String hashKey = new StringBuilder(stopId + "_").append(stopAction).toString();
      MonitorScheduleStopOrderEntity stopOrderEntity = redisMap.get(hashKey);
      if (stopOrderEntity == null) {
        return;
      }
      OrderDeliveryStateUpdateEnum updateEnum = orderUpdateMap.get(order.getOrderId());
      if (updateEnum != null) {
        updateEnum.getFunction().apply(stopOrderEntity);
      }
      redisMap.put(hashKey, stopOrderEntity);
      if (StringUtils.equals(TaskType.UNLOADTASK.getTaskType(), taskType) &&
              StringUtils.equals(DeliveryStatus.COMPLETED.getDeliveryStatus(), deliveryStatus)) {
        String abnormalKey = new StringBuilder(order.getStopId() + "_").append(StopAction.PICKUP.getStopAction()).toString();
        MonitorScheduleStopOrderEntity unNormalStopOrder = redisMap.get(abnormalKey);
        unNormalStopOrder.updateFinishedDeliveryOrderNum(1);
        redisMap.put(abnormalKey, unNormalStopOrder);
      }
    });
    vehicleScheduleStopOrderRepository.batchSet(scheduleTaskOrder.getVehicleName(), redisMap);
  }
}

