/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * 车辆状态Manager
 *
 * <AUTHOR>
 */
@Slf4j
public class SystemStateManager {
  /**
   * 是否离线或者失联
   *
   * @param systemState
   * @return
   */
  public static boolean isOfflineOrLost(String systemState) {
    if (Objects.equals(SystemStateEnum.CONNECTION_LOST.getSystemState(), systemState)) {
      return true;
    }
    if (Objects.equals(SystemStateEnum.OFFLINE.getSystemState(), systemState)) {
      return true;
    }
    return false;
  }
}
