/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.manager.mqtt;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestHeader;
import com.jdx.rover.server.api.domain.enums.mqtt.MqttQosEnum;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * mqtt消息发送服务
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
@Service
@RequiredArgsConstructor
public class MqttManager {

    /**
     * mqtt 消息发送客户端
     */
    private final MqttSendJsfService mqttSendJsfService;

    /**
     * 发送mqtt二进制消息
     */
    public void sendMqttBytes(String topic, byte[] data) {
        MqttMessageVO<byte[]> mqttMessageVO = new MqttMessageVO<>();
        mqttMessageVO.setMessage(data);
        mqttMessageVO.setTopic(topic);
        mqttSendJsfService.sendBytes(mqttMessageVO);
    }

    /**
     * 发送mqtt消息
     */
    public <T> void sendQos1NoRetainNoResponse(String clientName, String topic
            , String messageType, T data) {
        sendMqtt(clientName, topic, MqttQosEnum.AT_LEAST_ONCE.getValue(), false, false, messageType, data);
    }

    /**
     * 发送mqtt消息
     */
    public <T> void sendQos1RetainNoResponse(String clientName, String topic
            , String messageType, T data) {
        sendMqtt(clientName, topic, MqttQosEnum.AT_LEAST_ONCE.getValue(), true, false, messageType, data);
    }

    /**
     * 发送mqtt消息
     */
    public <T> void sendQos0NoRetainNoResponse(String clientName, String topic
            , String messageType, T data) {
        sendMqtt(clientName, topic, MqttQosEnum.AT_MOST_ONCE.getValue(), false, false, messageType, data);
    }

    /**
     * 发送mqtt消息
     */
    public <T> void sendMqtt(String clientName, String topic, int qos, boolean retained
            , boolean needResponse, String messageType, T data) {
        MqttRequestHeader mqttRequestHeader = new MqttRequestHeader();
        // 车辆名称,定义为clientName
        mqttRequestHeader.setClientName(clientName);
        mqttRequestHeader.setVehicleName(clientName);
        mqttRequestHeader.setRequestId(RequestIdUtils.getRequestId());
        mqttRequestHeader.setRequestTime(System.currentTimeMillis());
        mqttRequestHeader.setNeedResponse(needResponse);
        mqttRequestHeader.setMessageType(messageType);
        MqttRequestDTO<T> mqttRequestDTO = new MqttRequestDTO<>();
        mqttRequestDTO.setHeader(mqttRequestHeader);
        mqttRequestDTO.setData(data);
        MqttMessageVO<String> mqttMessageVO = new MqttMessageVO<>();
        mqttMessageVO.setMessage(JsonUtils.writeValueAsString(mqttRequestDTO));
        mqttMessageVO.setTopic(topic);
        mqttMessageVO.setQos(qos);
        mqttMessageVO.setRetained(retained);
        mqttSendJsfService.sendString(mqttMessageVO);
    }
}
