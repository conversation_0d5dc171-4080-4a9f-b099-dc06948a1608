/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager.pda;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.PdaStatusStatisticInfoDTO;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.pda.PdaRealtimeInfo;
import com.jdx.rover.monitor.repository.mapper.PdaRealtimeInfoMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 车辆异常manager类
 *
 * <AUTHOR>
 */
@Service
public class PdaRealtimeInfoManager extends BaseManager<PdaRealtimeInfoMapper, PdaRealtimeInfo> {

  /**
   * 批量保存实体。
   * @param dataList PdaRealtimeInfo实体列表。
   */
  @Transactional(rollbackFor = Exception.class)
  @LocalCacheEvict(value = LocalCacheConstant.PDA_DEVICE_TOTAL_COUNT, key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).PDA_DEVICE_TOTAL_COUNT")
  public void saveBatchData(List<PdaRealtimeInfo> dataList) {
    this.saveBatch(dataList);
  }

  /**
   * 批量更新实体。
   * @param dataList PdaRealtimeInfo实体列表。
   */
  public void updateBatch(List<PdaRealtimeInfo> dataList) {
    getBaseMapper().batchUpdateGroup(dataList);
  }

  /**
   * 批量删除实体。
   * @param dataList PdaRealtimeInfo实体列表。
   */
  @LocalCacheEvict(value = LocalCacheConstant.PDA_DEVICE_TOTAL_COUNT, key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).PDA_DEVICE_TOTAL_COUNT")
  public void deleteBatch(List<PdaRealtimeInfo> dataList) {
    getBaseMapper().batchDelete(dataList);
  }

  /**
   * 批量更新设备状态。
   * @param deviceList 设备信息列表。
   */
  public void batchUpdateState(List<PdaRealtimeInfo> deviceList) {
    getBaseMapper().batchUpdateState(deviceList);
  }

  /**
   * 状态分组统计设备数
   * @param pdaRealtimeInfoQueryWrapper 设备查询条件。
   */
  public List<PdaStatusStatisticInfoDTO> getGroupCountByStatus(LambdaQueryWrapper<PdaRealtimeInfo> pdaRealtimeInfoQueryWrapper) {
    return getBaseMapper().getGroupCountByStatus(pdaRealtimeInfoQueryWrapper);
  }

  /**
   * 型号分组统计设备数
   * @param pdaRealtimeInfoQueryWrapper 设备查询条件。
   */
  public List<PdaStatusStatisticInfoDTO> getGroupCountByModuleNo(@Param(Constants.WRAPPER) LambdaQueryWrapper<PdaRealtimeInfo> pdaRealtimeInfoQueryWrapper) {
    return getBaseMapper().getGroupCountByModuleNo(pdaRealtimeInfoQueryWrapper);
  }

  /**
   * 统计设备数
   * @param pdaRealtimeInfoQueryWrapper 设备查询条件。
   */
  @Cacheable(value = LocalCacheConstant.PDA_DEVICE_TOTAL_COUNT, condition = "#pdaRealtimeInfoQueryWrapper?.isEmptyOfWhere()", key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).PDA_DEVICE_TOTAL_COUNT")
  public Long getQueryCount(LambdaQueryWrapper<PdaRealtimeInfo> pdaRealtimeInfoQueryWrapper) {
    return getBaseMapper().selectCount(pdaRealtimeInfoQueryWrapper);
  }


}