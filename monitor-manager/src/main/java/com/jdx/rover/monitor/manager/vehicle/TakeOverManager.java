
/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.enums.vehicle.operate.AllowOperateEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 接管Manager
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Slf4j
public class TakeOverManager {
    /**
     * 是否被其它接管
     *
     * @param entity 当前车辆状态
     */
    public static String getAllowTakeOver(String cockpitNumber, VehicleTakeOverEntity entity) {
        if (Objects.isNull(entity)) {
            return AllowOperateEnum.ALLOW.name();
        }
        if (Objects.equals(entity.getCockpitNumber(), cockpitNumber)) {
            return AllowOperateEnum.EXIT.name();
        }
        return AllowOperateEnum.FORBID.name();
    }
}
