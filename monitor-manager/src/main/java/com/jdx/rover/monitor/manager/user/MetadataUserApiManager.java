/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.user;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import com.jdx.rover.permission.domain.dto.basic.UserInfoDTO;
import com.jdx.rover.permission.domain.dto.basic.UserMaskPhoneDTO;
import com.jdx.rover.permission.domain.vo.basic.UserExtendInfoGetVO;
import com.jdx.rover.permission.domain.vo.basic.UserMaskPhoneVO;
import com.jdx.rover.permission.jsf.service.basic.PermissionUserInfoBasicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 监控端获取用户信息
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetadataUserApiManager {

  @Autowired
  private PermissionUserInfoBasicService permissionUserInfoBasicService;

  /**
   * 通过用户名获取用户信息
   *
   * @param userName 用户名
   * @return list
   */
  public UserExtendInfoDTO getUserExtendInfoByName(String userName) {
    UserExtendInfoDTO result = null;
    try {
      UserExtendInfoGetVO extendInfoGetVo = new UserExtendInfoGetVO();
      extendInfoGetVo.setUserName(userName);
      extendInfoGetVo.setOperator(userName);
      HttpResult<UserExtendInfoDTO> httpResult = permissionUserInfoBasicService.getUserExtendInfo(extendInfoGetVo);
      if (HttpResult.isSuccess(httpResult)) {
        result = httpResult.getData();
      }
    } catch (Exception e) {
      log.warn("元数据通过用户名获取用户扩展调用失败,{}", userName, e);
    }
    log.info("元数据通过用户名{}获取用户扩展调用完成,结果={}", userName, result);
    return result;
  }

  /**
   * 通过JD Erp获取用户信息
   *
   * @param userErp 用户ERP
   * @return list
   */
  public UserInfoDTO getUserInfoByJdErp(String userErp) {
    UserInfoDTO result = null;
    try {
      HttpResult<List<UserInfoDTO>> httpResult = permissionUserInfoBasicService.getUserInfoByJdErp(userErp);
      if (HttpResult.isSuccess(httpResult) && !Objects.isNull(httpResult.getData())) {
        result = httpResult.getData().get(0);
      }
    } catch (Exception e) {
      log.warn("元数据通过Erp获取用户调用失败,{}", userErp, e);
    }
    log.info("元数据通过Erp{}获取用户调用完成,结果={}", userErp, result);
    return result;
  }

  /**
   * 通过用户名获取用户信息
   *
   * @param userName 用户名列表
   * @return list
   */
  public List<UserMaskPhoneDTO> getUserPhoneInfoByName(List<String> userName) {
    List<UserMaskPhoneDTO> result = new ArrayList<>();
    String userNameStr = JsonUtils.writeValueAsString(userName);
    try {
      UserMaskPhoneVO userMaskPhoneVo = new UserMaskPhoneVO();
      userMaskPhoneVo.setUserNameList(userName);
      HttpResult<List<UserMaskPhoneDTO>> httpResult = permissionUserInfoBasicService.getMaskPhoneByUserNameList(userMaskPhoneVo);
      if (HttpResult.isSuccess(httpResult) && !Objects.isNull(httpResult.getData())) {
        result = httpResult.getData();
      }
    } catch (Exception e) {
      log.warn("元数据通过用户名列表{}获取用户联系方式调用失败", userNameStr, e);
    }
    log.info("数据通过用户名列表{}获取用户联系方式调用完成,结果={}", userNameStr, result);
    return result;
  }

  /**
   * 通过phone获取用户信息
   *
   * @param phone 用户手机号
   * @return list
   */
  public UserInfoDTO getUserInfoByPhone(String phone) {
    UserInfoDTO result = null;
    try {
      HttpResult<List<UserInfoDTO>> httpResult = permissionUserInfoBasicService.getUserInfoByPhone(phone);
      if (HttpResult.isSuccess(httpResult) && CollectionUtils.isNotEmpty(httpResult.getData())) {
        result = httpResult.getData().get(0);
      }
    } catch (Exception e) {
      log.warn("元数据通过手机号获取用户调用失败,{}", phone, e);
    }
    log.info("元数据通过手机号获取用户调用完成,结果={}", phone, result);
    return result;
  }

}
