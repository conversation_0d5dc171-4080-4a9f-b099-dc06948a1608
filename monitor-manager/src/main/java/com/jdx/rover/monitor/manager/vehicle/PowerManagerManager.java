package com.jdx.rover.monitor.manager.vehicle;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Optional;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.drive.CommandExecuteResultDTO;
import com.jdx.rover.monitor.dto.message.MessageDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePowerManagerDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.drive.ControlPadRequestIdDO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.drive.command.DriveRemoteCommandTypeEnum;
import com.jdx.rover.monitor.enums.message.MessageLevelEnum;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttMessageTypeEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.enums.vehicle.command.RemoteOperationStateEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.command.RemoteCommandRecordManager;
import com.jdx.rover.monitor.manager.message.CommonMessageManager;
import com.jdx.rover.monitor.manager.mobile.CommandManager;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.manager.operation.OperationRecordLogManager;
import com.jdx.rover.monitor.manager.user.UserInfoMqttManager;
import com.jdx.rover.monitor.po.MonitorUserOperationLog;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.drive.CockpitRequestIdRepository;
import com.jdx.rover.server.api.domain.dto.power.ServerPowerManagerDTO;
import com.jdx.rover.server.api.domain.enums.mqtt.MqttMessageStateEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerAckEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerActionEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerModuleEnum;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import com.jdx.rover.server.api.jsf.service.command.PowerManagerCommandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 单车页manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PowerManagerManager {
  @Autowired
  private SingleVehicleManager singleVehicleManager;

  @Autowired
  private OperationRecordLogManager operationRecordLogManager;

  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;

  @Autowired
  private PowerManagerCommandService powerManagerCommandJsfService;

  @Autowired
  private CommonMessageManager commonMessageManager;
  @Autowired
  private RemoteCommandRecordManager remoteCommandRecordManager;

  @Autowired
  private CockpitRequestIdRepository cockpitRequestIdRepository;

  @Autowired
  private CockpitStatusRepository cockpitStatusRepository;

  @Autowired
  private MqttManager mqttManager;

  @Autowired
  private UserInfoMqttManager userInfoMqttManager;

  @Autowired
  private CommandManager commandManager;

  @Value("${spring.profiles.active}")
  private String profileActive;

  /**
   * 推送异常增量消息
   *
   * @param vehicleName
   */
  public void pushSingleVehiclePowerManagerDTO(String vehicleName) {
    WsResult<SingleVehiclePowerManagerDTO> wsResult = this.getPowerManager(vehicleName);
    singleVehicleManager.pushSingleVehiclePageData(vehicleName, wsResult);
  }

  /**
   * 通过车辆名称获取单车页调度信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public WsResult<SingleVehiclePowerManagerDTO> getPowerManager(String vehicleName) {
    LambdaQueryWrapper<MonitorUserOperationLog> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MonitorUserOperationLog::getVehicleName, vehicleName);
    wrapper.in(MonitorUserOperationLog::getOperationType, WebsocketEventTypeEnum.REMOTE_CONTROL_POWER_REBOOT.getValue()
        , WebsocketEventTypeEnum.REMOTE_CONTROL_POWER_OFF.getValue());
    Date endTime = new Date();
    Date startTime = DateUtils.addMinutes(endTime, -5);
    wrapper.ge(MonitorUserOperationLog::getOperateTimestamp, startTime);
    wrapper.lt(MonitorUserOperationLog::getOperateTimestamp, endTime);
    wrapper.orderByDesc(MonitorUserOperationLog::getId);
    MonitorUserOperationLog monitorUserOperationLog = operationRecordLogManager.getOne(wrapper, false);

    SingleVehiclePowerManagerDTO dto = new SingleVehiclePowerManagerDTO();
    dto.setVehicleName(vehicleName);
    if (monitorUserOperationLog == null) {
      return null;
    }
    dto.setAction(monitorUserOperationLog.getOperationType());
    dto.setOperateTimestamp(monitorUserOperationLog.getOperateTimestamp());
    dto.setState(monitorUserOperationLog.getState());
    WsResult<SingleVehiclePowerManagerDTO> wsResult = WsResult.success(monitorUserOperationLog.getOperationType(), dto);
    return wsResult;
  }

  public void updateOperationRecordLog(ServerPowerManagerDTO serverPowerManagerDTO) {
    String vehicleName = serverPowerManagerDTO.getVehicleName();
    LambdaQueryWrapper<MonitorUserOperationLog> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MonitorUserOperationLog::getVehicleName, vehicleName);
    wrapper.eq(MonitorUserOperationLog::getRequestId, serverPowerManagerDTO.getRequestId());
    wrapper.in(MonitorUserOperationLog::getOperationType, WebsocketEventTypeEnum.REMOTE_CONTROL_POWER_REBOOT.getValue()
        , WebsocketEventTypeEnum.REMOTE_CONTROL_POWER_OFF.getValue());
    MonitorUserOperationLog monitorUserOperationLogDb = operationRecordLogManager.getOne(wrapper, false);
    if (monitorUserOperationLogDb == null) {
      log.error("数据库中不存在操作记录={}", serverPowerManagerDTO);
      return;
    }

    MonitorUserOperationLog monitorUserOperationLog = new MonitorUserOperationLog();
    monitorUserOperationLog.setVehicleName(serverPowerManagerDTO.getVehicleName());
    monitorUserOperationLog.setState(serverPowerManagerDTO.getAck());
    monitorUserOperationLog.setFailMessage(serverPowerManagerDTO.getAckInfo());
    monitorUserOperationLog.setId(monitorUserOperationLogDb.getId());
    operationRecordLogManager.updateById(monitorUserOperationLog);
  }

  /**
   * 电源管理
   *
   * @param powerManagerAction
   * @param vehicleName
   * @return
   */
  public HttpResult<PowerManagerCommandVO> powerManager(String powerManagerAction, String vehicleName, String userName) {
    PowerManagerCommandVO powerManagerCommandVO = initPowerManagerCommandVO(powerManagerAction, vehicleName);
    MonitorUserOperationEntity monitorUserOperationLog = initMonitorUserOperationLog(powerManagerCommandVO, userName);
    remoteCommandRecordManager.addRecordLog(monitorUserOperationLog);

    HttpResult httpResult = HttpResult.error();
    if (PowerManagerActionEnum.POWER_OFF.getValue().equals(powerManagerAction)) {
      MonitorErrorEnum powerEnum = commandManager.remoteShutdown(vehicleName);
      MonitorErrorEnum pduEnum = commandManager.sendPdu(vehicleName, CommandTypeEnum.REMOTE_SHUTDOWN);
      if (MonitorErrorEnum.OK.equals(powerEnum) || MonitorErrorEnum.OK.equals(pduEnum)) {
        httpResult = HttpResult.success();
      } else {
        httpResult = HttpResult.error(MonitorErrorEnum.ERROR_PDU_CONNECT.getCode(), MonitorErrorEnum.ERROR_PDU_CONNECT.getMessage());
      }
    }
    if (PowerManagerActionEnum.REBOOT.getValue().equals(powerManagerAction)) {
      MonitorErrorEnum powerEnum = commandManager.vehicleReboot(vehicleName);
      MonitorErrorEnum pduEnum = commandManager.sendPdu(vehicleName, CommandTypeEnum.VEHICLE_REBOOT);
      if (MonitorErrorEnum.OK.equals(powerEnum) || MonitorErrorEnum.OK.equals(pduEnum)) {
        httpResult = HttpResult.success();
      } else {
        httpResult = HttpResult.error(MonitorErrorEnum.ERROR_PDU_CONNECT.getCode(), MonitorErrorEnum.ERROR_PDU_CONNECT.getMessage());
      }
    }

    if (!HttpResult.isSuccess(httpResult)) {
      // 远程调用失败,此次数据库记录直接修改为失败
      ServerPowerManagerDTO serverPowerManagerDTO = new ServerPowerManagerDTO();
      serverPowerManagerDTO.setVehicleName(vehicleName);
      serverPowerManagerDTO.setRequestId(powerManagerCommandVO.getRequestTime() + "");
      serverPowerManagerDTO.setAck(PowerManagerAckEnum.FAIL.getValue());
      serverPowerManagerDTO.setAckInfo(httpResult.getMessage());
      updateOperationRecordLog(serverPowerManagerDTO);
      log.info("电源管理失败!{},{}", httpResult, powerManagerCommandVO);
    }
    singleVehicleManager.pushSingleVehicleOperation(vehicleName);
    return httpResult;
  }

  public PowerManagerCommandVO initPowerManagerCommandVO(String powerManagerAction, String vehicleName) {
    PowerManagerCommandVO powerManagerCommandVO = new PowerManagerCommandVO();
    powerManagerCommandVO.setVehicleName(vehicleName);
    powerManagerCommandVO.setModuleName(PowerManagerModuleEnum.CHASSIS.getValue());
    powerManagerCommandVO.setAction(powerManagerAction);
    int nanoTime = RandomUtil.randomInt(1000000);
    Long requestTime = System.currentTimeMillis() * 1000000 + nanoTime;
    powerManagerCommandVO.setRequestTime(requestTime);
    return powerManagerCommandVO;
  }

  /**
   * 是否可以电源管理,返回值null,可以管理,否则表示返回错误信息
   *
   * @param vehicleName        车辆名称
   * @param powerManagerAction 操作类型
   * @return
   */
  private HttpResult canPowerManager(String powerManagerAction, String vehicleName, String userName) {
    PowerManagerActionEnum powerManagerActionEnum = PowerManagerActionEnum.valueOf(StringUtils.upperCase(powerManagerAction));
    if (powerManagerActionEnum == null) {
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode(), "错误操作类型" + powerManagerAction);
    }
    Optional<VehicleTakeOverEntity> op = vehicleTakeOverRepository.getByKey(vehicleName);
    if (!op.isPresent()) {
      log.info("车辆={},操作人员={},未急停,忽略操作", vehicleName, userName);
      return HttpResult.error(MonitorErrorEnum.NOT_EMERGENCY_STOP.getCode(), MonitorErrorEnum.NOT_EMERGENCY_STOP.getMessage());
    }
    String takeOverUsername = op.get().getUserName();
    if (!Objects.equals(userName, takeOverUsername)) {
      log.info("车辆={},操作人员={},接管人={},操作人不是接管人,忽略操作", vehicleName, userName, takeOverUsername, userName);
      return HttpResult.error(MonitorErrorEnum.OPERATOR_MISMATCH_TAKEOVER.getCode(), MonitorErrorEnum.OPERATOR_MISMATCH_TAKEOVER.getMessage());
    }
    return null;
  }

  /**
   * 构建MonitorUserOperationLog对象
   *
   * @param commandVo
   * @param commandVo
   */
  public MonitorUserOperationEntity initMonitorUserOperationLog(PowerManagerCommandVO commandVo, String userName) {
    MonitorUserOperationEntity monitorUserOperationLog = new MonitorUserOperationEntity();
    String eventType = WebsocketEventTypeEnum.REMOTE_CONTROL_POWER_REBOOT.getValue();
    if (Objects.equals(commandVo.getAction(), PowerManagerActionEnum.POWER_OFF.getValue())) {
      eventType = WebsocketEventTypeEnum.REMOTE_CONTROL_POWER_OFF.getValue();
    }
    monitorUserOperationLog.setOperationType(eventType);
    monitorUserOperationLog.setUserName(userName);
    monitorUserOperationLog.setTimestamp(new Date());
    monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
    monitorUserOperationLog.setRequestId(commandVo.getRequestTime() + "");
    monitorUserOperationLog.setState(RemoteOperationStateEnum.INIT.getValue());
    monitorUserOperationLog.setOperationMessage(commandVo.getAction());
    monitorUserOperationLog.setOperationSource(RemoteCommandSourceEnum.MONITOR.getCommandSource());
    return monitorUserOperationLog;
  }

  /**
   * 推送消息,只推送当前车辆的
   *
   * @param serverPowerManagerDTO
   * @return
   */
  public void pushPowerManagerMessage(ServerPowerManagerDTO serverPowerManagerDTO) {
    MessageDTO messageDTO = buildPowerManagerMessage(serverPowerManagerDTO);
    WsResult<MessageDTO> wsResult = WsResult.success(WebsocketEventTypeEnum.COMMON_MESSAGE.getValue(), messageDTO);
    singleVehicleManager.pushSingleVehiclePageData(serverPowerManagerDTO.getVehicleName(), wsResult);
  }

  /**
   * 构建推送的消息
   *
   * @param serverPowerManagerDTO
   * @return
   */
  private MessageDTO buildPowerManagerMessage(ServerPowerManagerDTO serverPowerManagerDTO) {
    MessageDTO messageDTO = new MessageDTO();
    String vehicleName = serverPowerManagerDTO.getVehicleName();
    LambdaQueryWrapper<MonitorUserOperationLog> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MonitorUserOperationLog::getVehicleName, vehicleName);
    wrapper.eq(MonitorUserOperationLog::getRequestId, serverPowerManagerDTO.getRequestId());
    wrapper.select(MonitorUserOperationLog::getUserName);
    MonitorUserOperationLog monitorUserOperationLogDb = operationRecordLogManager.getOne(wrapper, false);
    if (monitorUserOperationLogDb != null) {
      messageDTO.setUsername(monitorUserOperationLogDb.getUserName());
    }

    RemoteOperationStateEnum stateEnum = RemoteOperationStateEnum.of(serverPowerManagerDTO.getAck());
    PowerManagerActionEnum actionEnum = PowerManagerActionEnum.of(serverPowerManagerDTO.getAction());
    messageDTO.setVehicleName(vehicleName);
    String title = serverPowerManagerDTO.getVehicleName() + actionEnum.getTitle() + stateEnum.getTitle();
    messageDTO.setTitle(title);
    if (Objects.equals(serverPowerManagerDTO.getAck(), RemoteOperationStateEnum.FAIL.getValue())) {
      messageDTO.setMessageLevel(MessageLevelEnum.ERROR.getValue());
      messageDTO.setMessageText("失败原因:" + serverPowerManagerDTO.getAckInfo());
    } else {
      messageDTO.setMessageLevel(MessageLevelEnum.INFO.getValue());
    }
    return messageDTO;
  }

  public void handleCockpit(ServerPowerManagerDTO serverPowerManagerDTO) {
    ControlPadRequestIdDO dto = cockpitRequestIdRepository.get(serverPowerManagerDTO.getRequestId());
    if (Objects.isNull(dto)) {
      return;
    }
    PowerManagerAckEnum stateEnum = PowerManagerAckEnum.valueOf(serverPowerManagerDTO.getAck());
    PowerManagerActionEnum actionEnum = PowerManagerActionEnum.of(serverPowerManagerDTO.getAction());
    String title = serverPowerManagerDTO.getVehicleName() + actionEnum.getTitle() + stateEnum.getTitle();
    CommandExecuteResultDTO result = new CommandExecuteResultDTO();
    result.setVehicleName(dto.getVehicleName());
    if (Objects.equals(actionEnum, PowerManagerActionEnum.POWER_OFF)) {
      result.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_CONTROL_POWER_OFF.name());
    } else if (Objects.equals(actionEnum, PowerManagerActionEnum.REBOOT)) {
      result.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_CONTROL_POWER_REBOOT.name());
    } else {
      result.setRemoteCommandType(serverPowerManagerDTO.getAction());
    }
    result.setRequestTime(dto.getRequestTime());
    result.setOperationMessage(title);
    result.setMessageState(stateEnum.name());
    if (Objects.equals(stateEnum, PowerManagerAckEnum.FAIL)) {
      result.setMessageState(MqttMessageStateEnum.FAIL.name());
      result.setFailInfo("失败原因:" + serverPowerManagerDTO.getAckInfo());
    }

    CockpitStatusDO cockpitStatusDTO = cockpitStatusRepository.get(dto.getCockpitNumber());
    if (Objects.isNull(cockpitStatusDTO)) {
      log.error("座舱信息为空!{}", dto);
      return;
    }
    if (Objects.isNull(cockpitStatusDTO.getCockpitUserName())) {
      log.error("车辆接管人信息为空!{},{}", dto, cockpitStatusDTO);
      return;
    }
    String topic = String.format(MqttTopicEnum.USER_INFO.getTopic(), profileActive) + cockpitStatusDTO.getCockpitUserName();
    mqttManager.sendQos1NoRetainNoResponse(dto.getCockpitNumber(), topic, MqttMessageTypeEnum.SCREEN_COMMAND_RESULT.name(), result);
    if (Objects.equals(stateEnum, PowerManagerAckEnum.FAIL) || Objects.equals(stateEnum, PowerManagerAckEnum.SUCCESS)) {
      cockpitRequestIdRepository.remove(serverPowerManagerDTO.getRequestId());
    }
  }
}
