/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager.abnormal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.GuardianVehicleAbnormal;
import com.jdx.rover.monitor.repository.mapper.GuardianVehicleAbnormalMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 车辆异常manager类
 *
 * <AUTHOR>
 */
@Service
public class GuardianVehicleAbnormalManager extends BaseManager<GuardianVehicleAbnormalMapper, GuardianVehicleAbnormal> {
  /**
   * 批量保存或者更新列表
   *
   * @param list
   */
  @Transactional(rollbackFor = Exception.class)
  public void saveOrUpdateBatchList(List<GuardianVehicleAbnormal> list) {
    List<GuardianVehicleAbnormal> saveList = new ArrayList<>();
    List<GuardianVehicleAbnormal> updateList = new ArrayList<>();
    for (GuardianVehicleAbnormal guardianVehicleAbnormal : list) {
      GuardianVehicleAbnormal db = this.getIdByStartTimeAndErrorCode(guardianVehicleAbnormal);
      if (db == null || db.getId() == null) {
        saveList.add(guardianVehicleAbnormal);
      } else {
        guardianVehicleAbnormal.setId(db.getId());
        updateList.add(guardianVehicleAbnormal);
      }
    }

    if (CollectionUtils.isNotEmpty(saveList)) {
      this.saveBatch(saveList);
    }
    if (CollectionUtils.isNotEmpty(updateList)) {
      this.updateBatchById(updateList);
    }
  }

  /**
   * 通过开始时间和错误码,获取异常ID
   *
   * @param guardianVehicleAbnormal
   * @return
   */
  public GuardianVehicleAbnormal getIdByStartTimeAndErrorCode(GuardianVehicleAbnormal guardianVehicleAbnormal) {
    LambdaQueryWrapper<GuardianVehicleAbnormal> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(GuardianVehicleAbnormal::getVehicleName, guardianVehicleAbnormal.getVehicleName());
    queryWrapper.eq(GuardianVehicleAbnormal::getModuleName, guardianVehicleAbnormal.getModuleName());
    queryWrapper.eq(GuardianVehicleAbnormal::getErrorCode, guardianVehicleAbnormal.getErrorCode());
    queryWrapper.eq(GuardianVehicleAbnormal::getStartTime, guardianVehicleAbnormal.getStartTime());
    queryWrapper.select(GuardianVehicleAbnormal::getId);
    GuardianVehicleAbnormal result = this.getOne(queryWrapper, false);
    return result;
  }

  /**
   * 查询车辆今天异常,最多显示100条
   *
   * @param
   */
  public List<GuardianVehicleAbnormal> listTodayAbnormal(String vehicleName) {
    LambdaQueryWrapper<GuardianVehicleAbnormal> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(GuardianVehicleAbnormal::getVehicleName, vehicleName);

    LocalDate nowLocalDate = LocalDate.now();
    Date nowDate = Date.from(nowLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    wrapper.ge(GuardianVehicleAbnormal::getStartTime, nowDate);
    wrapper.isNull(GuardianVehicleAbnormal::getEndTime);

    wrapper.orderByDesc(GuardianVehicleAbnormal::getStartTime);
    wrapper.last("limit 100");

    List<GuardianVehicleAbnormal> result = this.list(wrapper);
    return result;
  }

  /**
   * 查询车辆开始时间之后的列表数据
   *
   * @param
   */
  public List<GuardianVehicleAbnormal> listAbnormalBeforeTime(String vehicleName, Date startDate, Date endDate) {
    LambdaQueryWrapper<GuardianVehicleAbnormal> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(GuardianVehicleAbnormal::getVehicleName, vehicleName);
    wrapper.ge(startDate != null, GuardianVehicleAbnormal::getStartTime, startDate);
    wrapper.le(endDate != null, GuardianVehicleAbnormal::getStartTime, endDate);
    wrapper.orderByDesc(GuardianVehicleAbnormal::getStartTime);
    wrapper.last("limit 10");
    List<GuardianVehicleAbnormal> result = this.list(wrapper);
    return result;
  }
}