
/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.server.api.domain.enums.guardian.IntersectionStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 车辆路口状态Manager
 *
 * <AUTHOR>
 */
@Slf4j
public class IntersectionManager {
    /**
     * 是否停靠近路口或者在路口中
     *
     * @return
     */
    public static boolean isNearAndInIntersection(VehicleStatusDO vehicleStatus) {
        if (Objects.isNull(vehicleStatus)) {
            return false;
        }
        if (StringUtils.equalsAny(vehicleStatus.getIntersectionStatus(), IntersectionStatusEnum.IN_INTERSECTION.getValue()
                , IntersectionStatusEnum.NEAR_INTERSECTION.getValue())) {
            return true;
        }
        return false;
    }

    /**
     * 是否停靠在路口中
     *
     * @return
     */
    public static boolean isInIntersection(VehicleStatusDO vehicleStatus) {
        if (Objects.isNull(vehicleStatus)) {
            return false;
        }
        return Objects.equals(vehicleStatus.getIntersectionStatus(), IntersectionStatusEnum.IN_INTERSECTION.getValue());
    }
}
