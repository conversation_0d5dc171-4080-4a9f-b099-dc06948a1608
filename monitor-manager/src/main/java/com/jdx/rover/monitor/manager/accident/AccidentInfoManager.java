///*
// * Copyright (c) 2022 www.jd.com All rights reserved.
// * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
// */
//package com.jdx.rover.monitor.manager.accident;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.jdx.rover.monitor.enums.accident.AccidentStatusEnum;
//import com.jdx.rover.monitor.manager.base.BaseManager;
//import com.jdx.rover.monitor.po.AccidentInfo;
//import com.jdx.rover.monitor.repository.mapper.AccidentInfoMapper;
//import org.springframework.stereotype.Service;
//
//import java.time.LocalDate;
//import java.time.ZoneId;
//import java.util.Date;
//import java.util.List;
//
///**
// * <p>
// * 事故信息 Manager
// * </p>
// *
// * <AUTHOR>
// * @date 2023/06/13
// */
//@Service
//public class AccidentInfoManager extends BaseManager<AccidentInfoMapper, AccidentInfo> {
//    /**
//     * 查询车辆今天待排除事故信息列表,最多显示100条
//     *
//     * @param
//     */
//    public List<AccidentInfo> listTodayWaitAccident(String vehicleName) {
//        LambdaQueryWrapper<AccidentInfo> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(AccidentInfo::getVehicleName, vehicleName);
//        wrapper.eq(AccidentInfo::getAccidentStatus, AccidentStatusEnum.WAIT.getValue());
//
//        LocalDate nowLocalDate = LocalDate.now();
//        Date nowDate = Date.from(nowLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
//        wrapper.ge(AccidentInfo::getCreatedTime, nowDate);
//
//        wrapper.orderByDesc(AccidentInfo::getCreatedTime);
//        wrapper.last("limit 100");
//
//        List<AccidentInfo> accidentInfoList = this.list(wrapper);
//        return accidentInfoList;
//    }
//
//    /**
//     * 根据事故编号查询事故信息
//     *
//     * @param accidentNo 事故编号
//     */
//    public AccidentInfo getAccidentByNo(String accidentNo) {
//        return lambdaQuery().eq(AccidentInfo::getAccidentNo,accidentNo).one();
//    }
//}