/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.drive;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.function.Function;

/**
 * 平行驾驶指令枚举类
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RemoteCommandEnum {
    /**
     * 远程指令类型
     */
    REMOTE_REQUEST_AS_ARRIVED(SpringUtil.getBean(RemoteCommandService.class)::publishAsArrivedCommand, "视同到达"),
    REMOTE_REQUEST_RELIEVE_BUTTON_STOP(SpringUtil.getBean(RemoteCommandService.class)::publishRelieveButtonStopCommand, "退出按钮急停"),
    REMOTE_REQUEST_RECOVERY(SpringUtil.getBean(RemoteCommandService.class)::publishRecoveryCommand, "远程急停恢复指令"),
    REMOTE_REQUEST_REMOTE_RESTART(SpringUtil.getBean(RemoteCommandService.class)::publishRestartCommand, "软件重启"),
    RUN_HAVE_MAP(SpringUtil.getBean(RemoteCommandService.class)::runHaveMap, "切换有图模式"),
    RUN_NO_MAP(SpringUtil.getBean(RemoteCommandService.class)::runNoMap, "切换无图模式"),
    ;

    /**
     * function
     */
    private final Function<RemoteCommandVO, HttpResult<Void>> function;

    /**
     * 标题
     */
    private final String title;
}
