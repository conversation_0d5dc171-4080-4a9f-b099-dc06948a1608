/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.device.indoormap;

import com.jdx.k2.indoor.map.jsf.domain.dto.mapInfo.PointInfoDTO;
import com.jdx.k2.indoor.map.jsf.service.MapPointInfoJsfService;
import com.jdx.rover.common.utils.result.HttpResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 室内地图服务管理
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class IndoorMapApiManager {

  /**
   * 室内地图服务
   */
  public final MapPointInfoJsfService mapPointInfoJsfService;

  /**
   * 获取停靠点详情
   */
  public PointInfoDTO getPointInfoByPointNo(String pointNo) {
    PointInfoDTO result = null;
    try {
      HttpResult<PointInfoDTO> httpResult = mapPointInfoJsfService.getPointInfoByPointNo(pointNo);;
      if (HttpResult.isSuccess(httpResult)) {
        result = httpResult.getData();
      }
      log.info("室内地图服务通过停靠点获取基本信息调用完成,结果={}", result);
    } catch (Exception e) {
      log.warn("室内地图服务通过停靠点{}获取基本信息调用失败", pointNo, e);
    }
    return result;
  }


}
