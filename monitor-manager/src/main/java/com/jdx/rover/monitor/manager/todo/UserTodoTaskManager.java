package com.jdx.rover.monitor.manager.todo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.AccidentAttachment;
import com.jdx.rover.monitor.po.UserTodoTask;
import com.jdx.rover.monitor.repository.mapper.AccidentAttachmentMapper;
import com.jdx.rover.monitor.repository.mapper.UserTodoTaskMapper;
import org.springframework.stereotype.Service;

/**
 * 用户代办manager
 */
@Service
public class UserTodoTaskManager extends BaseManager<UserTodoTaskMapper, UserTodoTask> {

    public boolean updateStatusById(Integer messageId, String status) {
        return lambdaUpdate().eq(UserTodoTask::getId, messageId).set(UserTodoTask::getStatus, status).update();
    }

    public boolean updateOwner(Integer messageId, String userName) {
        return lambdaUpdate().eq(UserTodoTask::getId, messageId).set(UserTodoTask::getOwner, userName).update();
    }

    public boolean updateOwnerAndStatus(Integer messageId, String userName, String status) {
        return lambdaUpdate()
                .eq(UserTodoTask::getId, messageId)
                .set(UserTodoTask::getOwner, userName)
                .set(UserTodoTask::getStatus, status)
                .update();
    }

    /**
     * 判断指定模块和类型下是否已经存在指定业务的消息
     * @param module
     * @param type
     * @param businessId
     * @return
     */
    public boolean existsTodoTask(String module, String type, String businessId) {
        LambdaQueryWrapper<UserTodoTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserTodoTask::getModule, module);
        wrapper.eq(UserTodoTask::getType, type);
        wrapper.eq(UserTodoTask::getBusinessKey, businessId);
        return !list(wrapper).isEmpty();
    }
}
