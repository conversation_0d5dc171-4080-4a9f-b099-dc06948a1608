/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.datacollection;

import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.jsf.BusinessCheckUtil;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionScene;
import com.jdx.rover.monitor.repository.mapper.datacollection.DataCollectionSceneMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;


/**
 * 数采场景manager
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Service
@RequiredArgsConstructor
public class DataCollectionSceneManager extends BaseManager<DataCollectionSceneMapper, DataCollectionScene> {

    /**
     * 根据id和车号获取场景
     * @param id 场景id
     * @param vehicleName 车辆名称
     * @return DataCollectionScene
     */
    public DataCollectionScene getByIdVehicleNameAndCheck(Integer id, String vehicleName) {
        DataCollectionScene scene = lambdaQuery()
            .eq(DataCollectionScene::getId, id)
            .eq(DataCollectionScene::getVehicleName, vehicleName)
            .one();
        BusinessCheckUtil.checkNull(scene, MonitorErrorEnum.ERROR_COLLECTION_SCENE_NOT_EXIST);
        return scene;
    }

    /**
     * 根据id和车号获取场景是否存在
     * @param vehicleName 车辆名称
     * @return DataCollectionScene
     */
    public boolean getSceneListByVehiceAndTime(String vehicleName, Date startTime, Date endTime) {
        return lambdaQuery()
                .between(DataCollectionScene::getReportTime, startTime, endTime)
                .eq(DataCollectionScene::getVehicleName, vehicleName)
                        .exists();
    }

    /**
     * 更新语音信息
     * @param id 场景id
     * @param audioFileKey 语音文件key
     * @param speechRecognitionResult 语音识别结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAudio(Integer id, String audioFileKey, String speechRecognitionResult) {
        lambdaUpdate()
            .set(StringUtils.isNotBlank(audioFileKey), DataCollectionScene::getAudioFileKey, audioFileKey)
            .set(StringUtils.isNotBlank(speechRecognitionResult), DataCollectionScene::getAudioRecognitionResult,
                speechRecognitionResult)
            .eq(DataCollectionScene::getId, id)
            .update();
    }


}