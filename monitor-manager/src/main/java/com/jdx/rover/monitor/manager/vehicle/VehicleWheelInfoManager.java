package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.api.jsf.service.vehicle.ServerVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 车辆胎压manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleWheelInfoManager {

  @Autowired
  private ServerVehicleService serverVehicleJsfService;

  /**
   * 获取车辆胎压
   * @param vehicleName
   */
  public List<VehicleRealtimeWheelInfoDTO> getVehicleWheelInfo(String vehicleName) {
    HttpResult<List<VehicleRealtimeWheelInfoDTO>> wheelInfoHttpResult = serverVehicleJsfService.getWheelInfo(vehicleName);
    if (HttpResult.isSuccess(wheelInfoHttpResult)) {
      List<VehicleRealtimeWheelInfoDTO> wheelInfo = wheelInfoHttpResult.getData();
      return wheelInfo;
    }
    return new ArrayList<>();
  }

}
