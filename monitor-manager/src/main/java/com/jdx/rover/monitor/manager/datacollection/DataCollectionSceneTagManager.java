/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.datacollection;

import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionSceneTag;
import com.jdx.rover.monitor.repository.mapper.datacollection.DataCollectionSceneTagMapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * 数采场景关联标签manager
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Service
@RequiredArgsConstructor
public class DataCollectionSceneTagManager extends BaseManager<DataCollectionSceneTagMapper, DataCollectionSceneTag> {

    /**
     * 根据场景ID获取关联的标签
     * @param sceneId 场景ID
     * @return List<DataCollectionSceneTag>
     */
    public List<DataCollectionSceneTag> getBySceneId(Integer sceneId) {
        return lambdaQuery()
            .eq(DataCollectionSceneTag::getSceneId, sceneId)
            .list();
    }
}
