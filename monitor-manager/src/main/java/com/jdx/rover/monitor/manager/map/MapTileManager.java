package com.jdx.rover.monitor.manager.map;

import cn.hutool.core.date.DateUtil;
import com.amazonaws.HttpMethod;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.manager.utils.map.OctreeTest;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.net.URL;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 地图瓦片manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MapTileManager {

    @Autowired
    private S3Properties s3Properties;
    private static final ReentrantLock reentrantLock = new ReentrantLock();
    private static final float PCD_FILTER_RATE = 0.25f;

  /**
   * 获取瓦片下载链接
   *
   * @param
   */
  @Cacheable(value = LocalCacheConstant.VEHICLE_TILE_URL, unless = "#result == null", key = "#tileId")
  public String getCloudMapTile(Long tileId, String url, String bucketName, String storeKey,  String path) {
      try {
          log.info("开始解压缩地图瓦片{}", tileId);
          File file = File.createTempFile(path + "/" + tileId + "-compress", ".pcd");
          File outFile = File.createTempFile(path + "/" + tileId + "-uncompress", ".pcd");
          FileUtils.copyURLToFile(new URL(url), file);
          try {
              if (reentrantLock.tryLock(3, TimeUnit.SECONDS)) {
                  log.info("解压缩地图瓦片号{}准备, 数据量{}", tileId, file.length());
                  boolean result = new OctreeTest().OctreeUncompressFile(file.getPath(), outFile.getPath(), PCD_FILTER_RATE);
                  log.info("解压缩地图瓦片号{}完成{},解压后瓦片大小{}", tileId, result, outFile.length());
              } else {
                  log.error("瓦片{}解压缩未获取到锁!!", tileId);
                  return "";
              }
          } finally {
              log.info("解压缩地图瓦片号{}完成, 数据量{}", tileId, file.length());
              reentrantLock.unlock();
          }
          S3Utils.uploadFile(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getEndpoint(),
                  bucketName,storeKey,outFile);
          final Date expiration = DateUtil.offsetHour(new Date(), 24);
          URL downloadUrl = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), bucketName, storeKey, HttpMethod.GET, expiration);
          return downloadUrl.toString();
      } catch (Exception e) {
          log.error("解压缩地图瓦片{}异常", tileId, e);
          return "";
      }
  }
}