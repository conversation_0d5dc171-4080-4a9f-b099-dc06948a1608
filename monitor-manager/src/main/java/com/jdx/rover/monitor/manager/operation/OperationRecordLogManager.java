package com.jdx.rover.monitor.manager.operation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.monitor.dto.MonitorOperationRecordDTO;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.MonitorUserOperationLog;
import com.jdx.rover.monitor.repository.mapper.MonitorUserOperationLogMapper;
import com.jdx.rover.monitor.repository.util.PageUtils;
import com.jdx.rover.monitor.search.MonitorDataListSearch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 操作记录manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OperationRecordLogManager extends BaseManager<MonitorUserOperationLogMapper, MonitorUserOperationLog> {
  /**
   * 查询操作记录
   *
   * @param
   */
  public PageDTO<MonitorOperationRecordDTO> search(PageVO pageVo, MonitorDataListSearch monitorDataListSearch) {
    if (monitorDataListSearch == null) {
      monitorDataListSearch = new MonitorDataListSearch();
    }
    LambdaQueryWrapper<MonitorUserOperationLog> wrapper = new LambdaQueryWrapper<>();
    if (monitorDataListSearch.getVehicleName() != null) {
      wrapper.like(MonitorUserOperationLog::getVehicleName, monitorDataListSearch.getVehicleName());
    }
    if (monitorDataListSearch.getCityName() != null) {
      wrapper.like(MonitorUserOperationLog::getCityName, monitorDataListSearch.getCityName());
    }
    if (monitorDataListSearch.getStationName() != null) {
      wrapper.like(MonitorUserOperationLog::getStationName, monitorDataListSearch.getStationName());
    }
    if (monitorDataListSearch.getStartTime() != null && monitorDataListSearch.getEndTime() != null) {
      wrapper.ge(MonitorUserOperationLog::getOperateTimestamp, monitorDataListSearch.getStartTime());
      wrapper.le(MonitorUserOperationLog::getOperateTimestamp, monitorDataListSearch.getEndTime());
    }
    wrapper.orderByDesc(MonitorUserOperationLog::getOperateTimestamp);
    IPage<MonitorUserOperationLog> iPage = PageUtils.toMpPage(pageVo,MonitorUserOperationLog.class);
    IPage<MonitorUserOperationLog> operationLogs = this.page(iPage, wrapper);
    PageDTO<MonitorOperationRecordDTO> pageInfo = new PageDTO<>();
    pageInfo.setTotal(operationLogs.getTotal());
    pageInfo.setPageNum((int) operationLogs.getPages());
    pageInfo.setPageSize((int) operationLogs.getSize());
    pageInfo.setList(operationLogs.getRecords().stream().map(MonitorUserOperationLog::toOperationRecordDto).collect(Collectors.toList()));
    return pageInfo;
  }

  /**
   * 查询车辆今天操作记录,最多显示100条
   *
   * @param
   */
  public List<MonitorUserOperationLog> listTodayOperationLog(String vehicleName) {
    LambdaQueryWrapper<MonitorUserOperationLog> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MonitorUserOperationLog::getVehicleName, vehicleName);

    LocalDate nowLocalDate = LocalDate.now();
    Date nowDate = Date.from(nowLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    wrapper.ge(MonitorUserOperationLog::getOperateTimestamp, nowDate);

    wrapper.orderByDesc(MonitorUserOperationLog::getOperateTimestamp);
    wrapper.last("limit 100");

    List<MonitorUserOperationLog> monitorUserOperationLogList = this.list(wrapper);
    return monitorUserOperationLogList;
  }
}