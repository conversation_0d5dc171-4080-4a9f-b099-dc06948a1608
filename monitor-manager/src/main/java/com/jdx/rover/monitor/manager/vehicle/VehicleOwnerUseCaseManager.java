package com.jdx.rover.monitor.manager.vehicle;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdx.rover.metadata.api.domain.enums.CockpitTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSelectTypeRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.metadata.CockpitVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 单车页manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleOwnerUseCaseManager {
  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;
  @Autowired
  private UserStatusRepository userStatusRepository;
  @Autowired
  private CockpitStatusRepository cockpitStatusRepository;
  @Autowired
  private CockpitVehicleNameRepository cockpitVehicleNameRepository;
  @Autowired
  private VehicleSelectTypeRepository vehicleSelectTypeRepository;

  /**
   * 通过用户名称,获取车辆归属方车辆数目
   *
   * @param username
   */
  public Map<String, Integer> getOwnerUseCaseCountByUsername(String username) {
    Set<String> vehicleNameSet = new HashSet<>();
    vehicleNameSet.addAll(userVehicleNameRepository.get(username));
    UserStatusDO userStatusDo = userStatusRepository.get(username);
    if (!Objects.isNull(userStatusDo) && StringUtils.isNotBlank(userStatusDo.getCockpitNumber())) {
      CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(userStatusDo.getCockpitNumber());
      if (!Objects.isNull(cockpitStatusDO) && StringUtils.equals(CockpitTypeEnum.MONITOR_SEAT.getValue(),cockpitStatusDO.getCockpitType())) {
        vehicleNameSet.clear();
        vehicleNameSet.addAll(cockpitVehicleNameRepository.get(userStatusDo.getCockpitNumber()));
      }
    }
    if (CollectionUtils.isEmpty(vehicleNameSet)) {
      log.info("用户[{}]没有关联车辆", username);
      return Maps.newLinkedHashMap();
    }
    Map<String, Integer> result = Maps.newLinkedHashMap();
    for (VehicleOwnerUseCaseEnum itemEnum : VehicleOwnerUseCaseEnum.values()) {
      Set<String> selectTypeSet = vehicleSelectTypeRepository.get(null, itemEnum.getValue());
      int size = Sets.intersection(vehicleNameSet, selectTypeSet).size();
      result.put(itemEnum.getValue(), size);
    }
    return result;
  }
  /**
   * 通过用户名称,获取车辆业务类型车辆数目
   *
   * @param username
   */
  public Map<String, Integer> getBusinessTypeCountByUsername(String username) {
    Set<String> vehicleNameSet = new HashSet<>();
    vehicleNameSet.addAll(userVehicleNameRepository.get(username));
    UserStatusDO userStatusDo = userStatusRepository.get(username);
    if (!Objects.isNull(userStatusDo) && StringUtils.isNotBlank(userStatusDo.getCockpitNumber())) {
      CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(userStatusDo.getCockpitNumber());
      if (!Objects.isNull(cockpitStatusDO) && StringUtils.equals(CockpitTypeEnum.MONITOR_SEAT.getValue(),cockpitStatusDO.getCockpitType())) {
        vehicleNameSet.clear();
        vehicleNameSet.addAll(cockpitVehicleNameRepository.get(userStatusDo.getCockpitNumber()));
      }
    }
    if (CollectionUtils.isEmpty(vehicleNameSet)) {
      log.info("用户[{}]没有关联车辆", username);
      return Maps.newLinkedHashMap();
    }
    Map<String, Integer> result = Maps.newLinkedHashMap();
    for (VehicleBusinessTypeEnum itemEnum : VehicleBusinessTypeEnum.values()) {
      Set<String> selectTypeSet = vehicleSelectTypeRepository.get(itemEnum.getValue(), null);
      int size = Sets.intersection(vehicleNameSet, selectTypeSet).size();
      result.put(itemEnum.getValue(), size);
    }
    return result;
  }
}
