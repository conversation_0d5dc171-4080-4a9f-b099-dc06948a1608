/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.vehicle;

import com.google.common.collect.Lists;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.enums.VehicleHardwareStatusEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.StationVehicleBasicDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleBasicDto;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleTypeInfoDto;
import com.jdx.rover.metadata.domain.vo.station.StationBasicVO;
import com.jdx.rover.metadata.domain.vo.vehicle.VehicleNameListVo;
import com.jdx.rover.metadata.jsf.service.station.MetadataStationBasicService;
import com.jdx.rover.metadata.jsf.service.user.MetadataUserBasicService;
import com.jdx.rover.metadata.jsf.service.vehicle.MetadataVehicleBasicService;
import com.jdx.rover.metadata.jsf.service.vehicle.MetadataVehicleTypeBasicService;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MetadataVehicleApiManager {
    private final MetadataVehicleBasicService metadataVehicleBasicService;
    private final MetadataStationBasicService metadataStationBasicService;
    private final MetadataUserBasicService metadataUserBasicService;
    private final MetadataVehicleTypeBasicService metadataVehicleTypeBasicService;

    /**
     * 根据用户名获取车辆名称信息
     *
     * @return
     */
    public List<String> getVehicleInUser(String username) {
        List<String> result = new ArrayList<>();
        try {
            HttpResult<List<VehicleBasicDto>> httpResult = metadataUserBasicService.getVehicleInfoListByUserName(username);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData().stream().map(VehicleBasicDto::getVehicleName).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("元数据根据用户名获取车辆名称信息调用失败,{},{}", username, e.toString());
        }
        log.info("元数据根据用户名获取车辆名称信息调用完成,结果={}", result);
        return result;
    }

    /**
     * 获取所有车辆
     *
     * @return
     */
    public List<VehicleBasicDTO> listAll() {
        List<VehicleBasicDTO> result = new ArrayList<>();
        try {
            PageVO pageVO = new PageVO();
            AtomicInteger pageNum = new AtomicInteger(1);
            pageVO.setPageNum(pageNum.get());
            pageVO.setPageSize(200);
            do {
                HttpResult<List<VehicleBasicDto>> httpResult = metadataVehicleBasicService.getVehicleBasicInfoPage(pageVO);
                if (!HttpResult.isSuccess(httpResult)) {
                    log.info("调用返回失败!{}", httpResult);
                    result.clear();
                    break;
                }
                List<VehicleBasicDto> dataList = httpResult.getData();
                if (CollectionUtils.isEmpty(dataList)) {
                    break;
                }
                log.info("请求结果大小={},参数={}", dataList.size(), pageVO);
                for (VehicleBasicDto tmp : dataList) {
                    if (Objects.isNull(tmp.getStationId())) {
                        continue;
                    }
                    VehicleBasicDTO dto = new VehicleBasicDTO();
                    dto.setStationId(tmp.getStationId());
                    dto.setBusinessType(tmp.getVehicleBusinessType());
                    dto.setOwnerUseCase(tmp.getOwnerUseCase());
                    dto.setName(tmp.getVehicleName());
                    dto.setSupplier(tmp.getSupplier());
                    result.add(dto);
                }
                pageVO.setPageNum(pageNum.incrementAndGet());
            } while (true);
            setStationInfo(result);
        } catch (Exception e) {
            log.warn("元数据通过名称获取车辆信息调用失败,{}", e.toString());
        }
        log.info("元数据通过名称获取车辆信息调用完成,结果={}", result);
        return result;
    }

    /**
     * 通过车辆名称获取车辆基本信息
     *
     * @param vehicleName 车辆名称
     * @return list
     */
    public VehicleBasicDTO getByName(String vehicleName) {
        VehicleBasicDTO result = null;
        try {
            HttpResult<VehicleBasicDto> httpResult = metadataVehicleBasicService.getVehicleByName(vehicleName);
            if (HttpResult.isSuccess(httpResult) && !Objects.isNull(httpResult.getData())) {
                result = new VehicleBasicDTO();
                result.setName(vehicleName);
                result.setBusinessType(httpResult.getData().getVehicleBusinessType());
                result.setOwnerUseCase(httpResult.getData().getOwnerUseCase());
                result.setVehicleTypeId(httpResult.getData().getVehicleTypeId());
                result.setTagList(httpResult.getData().getTagList());
                result.setBoxId(httpResult.getData().getBoxId());
                result.setSupplier(httpResult.getData().getSupplier());
                Integer stationId = httpResult.getData().getStationId();
                HttpResult<StationBasicDTO> stationHttpResult = metadataStationBasicService.getStationInfoById(stationId);
                if (HttpResult.isSuccess(stationHttpResult) && !Objects.isNull(stationHttpResult.getData())) {
                    result.setStationId(stationHttpResult.getData().getStationId());
                    result.setCityId(stationHttpResult.getData().getAddressInfo().getCityId());
                    result.setStationName(stationHttpResult.getData().getStationName());
                    result.setCityName(stationHttpResult.getData().getAddressInfo().getCityName());
                }
            }
        } catch (Exception e) {
            log.warn("元数据通过车辆名称获取车辆基本信息调用失败,{},{}", vehicleName, e.toString());
        }
        log.info("元数据通过车辆名称获取车辆基本信息完成,结果={}", result);
        return result;
    }

    /**
     * 通过车辆名称获取车辆基本信息
     *
     * @param vehicleName 车辆名称
     * @return list
     */
    public VehicleBasicDto getVehicleInfo(String vehicleName) {
        VehicleBasicDto result = null;
        try {
            HttpResult<VehicleBasicDto> httpResult = metadataVehicleBasicService.getVehicleByName(vehicleName);
            if (HttpResult.isSuccess(httpResult) && !Objects.isNull(httpResult.getData())) {
                result = httpResult.getData();
            }
        } catch (Exception e) {
            log.warn("元数据通过车辆名称获取车辆基本信息调用失败,{},{}", vehicleName, e.toString());
        }
        log.info("元数据通过车辆名称获取车辆基本信息完成,结果={}", result);
        return result;
    }

    /**
     * 通过车辆名称列表获取车辆基本信息列表
     *
     * @param vehicleNameList 车辆名称列表
     */
    public List<VehicleBasicDTO> listByName(List<String> vehicleNameList) {
        List<VehicleBasicDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(vehicleNameList)) {
            return result;
        }
        try {
            List<List<String>> vehicleNamePartitionList = Lists.partition(vehicleNameList, 200);
            for (List<String> item : vehicleNamePartitionList) {
                VehicleNameListVo vehicleNameListVo = new VehicleNameListVo();
                vehicleNameListVo.setVehicleNameList(item);
                HttpResult<List<VehicleBasicDto>> httpResult = metadataVehicleBasicService.getVehicleByVehicleNameList(vehicleNameListVo);
                if (HttpResult.isSuccess(httpResult) && CollectionUtils.isNotEmpty(httpResult.getData())) {
                    for (VehicleBasicDto metaBasic : httpResult.getData()) {
                        VehicleBasicDTO dto = new VehicleBasicDTO();
                        dto.setName(metaBasic.getVehicleName());
                        dto.setBusinessType(metaBasic.getVehicleBusinessType());
                        dto.setOwnerUseCase(metaBasic.getOwnerUseCase());
                        dto.setStationId(metaBasic.getStationId());
                        dto.setSupplier(metaBasic.getSupplier());
                        result.add(dto);
                    }
                }
            }
            setStationInfo(result);
        } catch (Exception e) {
            log.warn("元数据通过车辆名称列表获取车辆基本信息列表调用失败,{},{}", vehicleNameList, e.toString());
        }
        log.info("元数据通过车辆名称列表获取车辆基本信息列表调用完成,结果={}", result);
        return result;
    }

    /**
     * 设置站点信息
     *
     * @param vehicleBasicList
     */
    private void setStationInfo(List<VehicleBasicDTO> vehicleBasicList) {
        Set<Integer> stationIdSet = vehicleBasicList.stream().map(VehicleBasicDTO::getStationId).collect(Collectors.toSet());
        Map<Integer, StationBasicDTO> stationMap = getStationBasicMap(Lists.newArrayList(stationIdSet));
        for (VehicleBasicDTO vehicle : vehicleBasicList) {
            StationBasicDTO stationBasic = stationMap.get(vehicle.getStationId());
            if (Objects.isNull(stationBasic)) {
                continue;
            }
            vehicle.setStationName(stationBasic.getStationName());
            vehicle.setStationId(stationBasic.getStationId());
            if (Objects.isNull(stationBasic.getAddressInfo())) {
                continue;
            }
            vehicle.setCityId(stationBasic.getAddressInfo().getCityId());
            vehicle.setCityName(stationBasic.getAddressInfo().getCityName());
        }
    }

    /**
     * 通过站点和车辆类型获取车辆列表
     *
     * @param stationId    站点Id
     * @param businessType 车辆业务类型
     */
    public List<StationVehicleBasicDTO> listByStation(Integer stationId, String businessType) {
        List<StationVehicleBasicDTO> result = new ArrayList<>();
        try {
            HttpResult<List<StationVehicleBasicDTO>> httpResult = metadataStationBasicService.getLocalVehicleListByStationId(stationId);
            if (HttpResult.isSuccess(httpResult) && CollectionUtils.isNotEmpty(httpResult.getData())) {
                result = httpResult.getData().stream().filter(vehicle -> {
                    if (StringUtils.isNotBlank(businessType)) {
                        return StringUtils.equals(businessType, vehicle.getVehicleBusinessType());
                    }
                    return true;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("元数据通过站点获取车辆基本信息列表调用失败,{},{}", stationId, e.toString());
        }
        log.info("元数据通过站点获取车辆基本信息列表调用完成,结果={}", result);
        return result;
    }

    /**
     * 通过车辆获取城市站点负责人咚咚。
     */
    public StationBasicDTO getStationInfoByVehicle(String vehicleName) {
        StationBasicDTO vehicleBasicDTO = null;
        try {
            HttpResult<VehicleBasicDto> httpResult = metadataVehicleBasicService.getVehicleByName(vehicleName);
            if (HttpResult.isSuccess(httpResult) && httpResult.getData() != null) {
                VehicleBasicDto vehicleBasicDto = httpResult.getData();
                HttpResult<StationBasicDTO> stationHttpResult = metadataStationBasicService.getStationInfoById(vehicleBasicDto.getStationId());
                if (HttpResult.isSuccess(stationHttpResult) && stationHttpResult.getData() != null) {
                    vehicleBasicDTO = stationHttpResult.getData();
                }
            }
        } catch (Exception e) {
            log.warn("元数据通过车辆获取站点基本信息调用失败,{}", vehicleName, e);
        }
        log.info("元数据通过站点获取车辆基本信息列表调用完成,结果={}", vehicleBasicDTO);
        return vehicleBasicDTO;
    }

    /**
     * 获取站点信息Map
     *
     * @param stationIdList
     * @return
     */
    private Map<Integer, StationBasicDTO> getStationBasicMap(List<Integer> stationIdList) {
        Map<Integer, StationBasicDTO> stationMap = new HashMap<>();
        if (CollectionUtils.isEmpty(stationIdList)) {
            return stationMap;
        }
        List<List<Integer>> stationIdPartitionList = Lists.partition(stationIdList, 200);
        for (List<Integer> idList : stationIdPartitionList) {
            StationBasicVO stationBasicVo = new StationBasicVO();
            stationBasicVo.setStationIdList(idList);
            HttpResult<List<StationBasicDTO>> stationHttpResult = metadataStationBasicService.getStationInfoByIdList(stationBasicVo);
            if (HttpResult.isSuccess(stationHttpResult) && !Objects.isNull(stationHttpResult.getData())) {
                stationMap.putAll(stationHttpResult.getData().stream()
                        .collect(Collectors.toMap(StationBasicDTO::getStationId, Function.identity())));
            }
        }
        return stationMap;
    }

  /**
   * 获取车辆类型名称
   *
   * @param typeId 车辆类型ID
   * @return 车辆类型名称
   */
  @Cacheable(value = LocalCacheConstant.VEHICLE_TYPE, key = "#typeId")
  public String getVehicleTypeName(Integer typeId){
    try {
      HttpResult<VehicleTypeInfoDto> vehicleTypeById = metadataVehicleTypeBasicService.getVehicleTypeById(typeId);
        VehicleTypeInfoDto data = vehicleTypeById.getData();
      log.warn("元数据获取车辆类型结果,{},{}", typeId,data);
      return data.getVehicleTypeName();
    } catch (Exception e) {
      log.warn("元数据获取车辆类型结果,{},{}", typeId, e.toString());
    }
    return null;
  }

    /**
     * 校验车辆生命周期
     * @param vehicleName
     * @return
     */
    public Boolean vehicleRequireCheck(String vehicleName) {
        Boolean result = false;
        try {
            HttpResult<VehicleBasicDto> httpResult = metadataVehicleBasicService.getVehicleByName(vehicleName);
            if (HttpResult.isSuccess(httpResult) && !Objects.isNull(httpResult.getData())) {
                VehicleBasicDto vehicleBasicDto = httpResult.getData();
                if (vehicleBasicDto.getHardwareStatus().equals(VehicleHardwareStatusEnum.RESERVE.getValue()) || vehicleBasicDto.getHardwareStatus().equals(VehicleHardwareStatusEnum.USE.getValue())
                        || vehicleBasicDto.getHardwareStatus().equals(VehicleHardwareStatusEnum.MAINTAINING.getValue())) {
                    result = true;
                }
            }
        } catch (Exception e) {
            log.error("调用元数据获取车辆失败,vehicleName:{}", vehicleName);
        }
        return result;
    }
}
