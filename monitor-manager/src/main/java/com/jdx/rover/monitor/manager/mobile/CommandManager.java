package com.jdx.rover.monitor.manager.mobile;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.manager.mqtt.PduMqttManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleStatusManager;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import com.jdx.rover.server.api.domain.enums.light.SwitchLightEnum;
import com.jdx.rover.server.api.domain.enums.light.SwitchTypeEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerActionEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerModuleEnum;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchLightCommandVO;
import com.jdx.rover.server.api.jsf.service.command.PowerManagerCommandService;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @description: CommandManager
 * @author: wangguotai
 * @create: 2024-07-18 18:29
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class CommandManager {

    private final PowerManagerCommandService powerManagerCommandJsfService;

    private final RemoteCommandService remoteCommandJsfService;

    private final VehicleStatusRepository vehicleStatusRepository;

    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    private final PduMqttManager pduMqttManager;

    /**
     * 远程开机
     *
     * @param vehicleName vehicleName
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum remotePowerOn(String vehicleName) {
        try {
//            // 校验车辆是否离线
//            String systemState = vehicleRealtimeRepository.getSystemState(vehicleName);
//            if (!StringUtils.equalsAny(systemState, SystemStateEnum.OFFLINE.getSystemState(),
//                SystemStateEnum.CONNECTION_LOST.getSystemState())) {
//                return MonitorErrorEnum.ERROR_POWER_ON_VEHICLE_ON;
//            }

            // 校验PDU mqtt连接是否存在
            VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
            if (VehicleStatusManager.isPduOffline(vehicleStatusDO)) {
                return MonitorErrorEnum.ERROR_POWER_MANAGER_ABSENT;
            }

            pduMqttManager.forceSendPowerCommand(vehicleName, CommandTypeEnum.REMOTE_POWER_ON);
            return MonitorErrorEnum.OK;
        } catch (Exception e) {
            log.error("PDU远程开机异常：", e);
            return MonitorErrorEnum.ERROR_CALL_SERVICE;
        }
    }

    /**
     * PDU指令发送
     *
     * @param vehicleName     vehicleName
     * @param commandTypeEnum commandTypeEnum
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum sendPdu(String vehicleName, CommandTypeEnum commandTypeEnum) {
        // 校验PDU mqtt连接是否存在
        VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
        if (VehicleStatusManager.isPduOffline(vehicleStatusDO)) {
            return MonitorErrorEnum.ERROR_PDU_CONNECT;
        }

        pduMqttManager.forceSendPowerCommand(vehicleName, commandTypeEnum);
        return MonitorErrorEnum.OK;
    }

    /**
     * 远程关机
     *
     * @param vehicleName vehicleName
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum remoteShutdown(String vehicleName) {
        try {
            // 校验车辆是否离线
            String systemState = vehicleRealtimeRepository.getSystemState(vehicleName);
            if (StringUtils.equals(systemState, SystemStateEnum.OFFLINE.getSystemState())) {
                return MonitorErrorEnum.ERROR_SHUTDOWN_VEHICLE_OFFLINE;
            }

            PowerManagerCommandVO commandVO = new PowerManagerCommandVO();
            commandVO.setVehicleName(vehicleName);
            commandVO.setModuleName(PowerManagerModuleEnum.CHASSIS.getValue());
            commandVO.setAction(PowerManagerActionEnum.POWER_OFF.getValue());
            commandVO.setRequestTime(System.nanoTime());
            log.info("PowerManagerCommandApiClient call publishPowerManagerCommand request:[{}].", commandVO);
            HttpResult<Void> httpResult = powerManagerCommandJsfService.publishPowerManagerCommand(commandVO);
            log.info("PowerManagerCommandApiClient call publishPowerManagerCommand response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return MonitorErrorEnum.OK;
            } else {
                return MonitorErrorEnum.ERROR_POWER_MANAGER_ABSENT;
            }
        } catch (Exception e) {
            log.error("PowerManagerCommandApiClient call publishPowerManagerCommand exception.", e);
            return MonitorErrorEnum.ERROR_CALL_SERVICE;
        }
    }

    /**
     * 断电重启
     *
     * @param vehicleName vehicleName
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum vehicleReboot(String vehicleName) {
        try {
            PowerManagerCommandVO commandVO = new PowerManagerCommandVO();
            commandVO.setVehicleName(vehicleName);
            commandVO.setModuleName(PowerManagerModuleEnum.CHASSIS.getValue());
            commandVO.setAction(PowerManagerActionEnum.REBOOT.getValue());
            commandVO.setRequestTime(System.nanoTime());
            log.info("PowerManagerCommandApiClient call publishPowerManagerCommand request:[{}].", commandVO);
            HttpResult<Void> httpResult = powerManagerCommandJsfService.publishPowerManagerCommand(commandVO);
            log.info("PowerManagerCommandApiClient call publishPowerManagerCommand response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return MonitorErrorEnum.OK;
            } else {
                return MonitorErrorEnum.ERROR_POWER_MANAGER_ABSENT;
            }
        } catch (Exception e) {
            log.error("PowerManagerCommandApiClient call publishPowerManagerCommand exception.", e);
            return MonitorErrorEnum.ERROR_CALL_SERVICE;
        }
    }

    /**
     * ROVER重启
     *
     * @param vehicleName vehicleName
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum roverReboot(String vehicleName, Date operateTime) {
        try {
            RemoteCommandVO commandVO = new RemoteCommandVO();
            commandVO.setVehicleName(vehicleName);
            commandVO.setReceiveTimeStamp(operateTime);
            commandVO.setTransitTimeStamp(new Date());
            log.info("RemoteCommandApiClient call publishRestartCommand request:[{}].", commandVO);
            HttpResult httpResult = remoteCommandJsfService.publishRestartCommand(commandVO);
            log.info("RemoteCommandApiClient call publishRestartCommand response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return MonitorErrorEnum.OK;
            } else {
                return MonitorErrorEnum.ERROR_COMMAND_SEND;
            }
        } catch (Exception e) {
            log.error("RemoteCommandApiClient call publishRestartCommand exception.", e);
            return MonitorErrorEnum.ERROR_CALL_SERVICE;
        }
    }

    /**
     * 开关近光灯
     *
     * @param vehicleName vehicleName
     * @param type        type
     * @param status      status
     * @param operateTime operateTime
     * @return MonitorErrorEnum
     */
    public MonitorErrorEnum switchLight(String vehicleName, SwitchTypeEnum type, SwitchLightEnum status, Date operateTime) {
        try {
            SwitchLightCommandVO commandVO = new SwitchLightCommandVO();
            commandVO.setVehicleName(vehicleName);
            commandVO.setSwitchType(type.name());
            commandVO.setSwitchLight(status.name());
            commandVO.setReceiveTimeStamp(operateTime);
            commandVO.setTransitTimeStamp(new Date());
            log.info("RemoteCommandApiClient call switchLight request:[{}].", commandVO);
            HttpResult httpResult = remoteCommandJsfService.switchLight(commandVO);
            log.info("RemoteCommandApiClient call switchLight response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return MonitorErrorEnum.OK;
            } else {
                return MonitorErrorEnum.ERROR_COMMAND_SEND;
            }
        } catch (Exception e) {
            log.error("RemoteCommandApiClient call switchLight exception.", e);
            return MonitorErrorEnum.ERROR_CALL_SERVICE;
        }
    }
}