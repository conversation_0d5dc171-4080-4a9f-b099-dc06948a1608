package com.jdx.rover.monitor.manager.datacenter;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.warehouse.mileage.HistoryMileageQueryJsfDTO;
import com.jdx.rover.datacenter.domain.dto.warehouse.mileage.VehicleMileageQueryJsfDTO;
import com.jdx.rover.datacenter.domain.vo.warehouse.mileage.HistoryMileageQueryJsfVO;
import com.jdx.rover.datacenter.domain.vo.warehouse.mileage.VehicleMileageQueryJsfVO;
import com.jdx.rover.datacenter.jsf.service.warehouse.VehicleMileageJsfService;
import com.jdx.rover.device.jsfapi.domain.dto.device.DeviceDetailDTO;
import com.jdx.rover.device.jsfapi.domain.vo.device.DeviceDetailGetByNameServerVO;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 里程相关服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MileageManager {

    private final VehicleMileageJsfService vehicleMileageJsfService;

    public List<VehicleMileageQueryJsfDTO> vehicleMileageQuery(Date startTime, Date endTime) {
        List<VehicleMileageQueryJsfDTO> result = new ArrayList<>();
        try {
            VehicleMileageQueryJsfVO vehicleMileageQueryJsfVO = new VehicleMileageQueryJsfVO();
            vehicleMileageQueryJsfVO.setStartTime(startTime);
            vehicleMileageQueryJsfVO.setEndTime(endTime);
            HttpResult<List<VehicleMileageQueryJsfDTO>> httpResult = vehicleMileageJsfService.vehicleMileageQuery(vehicleMileageQueryJsfVO);

            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
            log.info("调用datacenter获取实时行驶里程完成,结果={}", result);
        } catch (Exception e) {
            log.error("调用datacenter获取实时行驶里程失败,{}-{}", startTime, endTime, e);
        }
        return result;
    }

    public List<HistoryMileageQueryJsfDTO> historyMileageQuery(Date startTime, Date endTime) {
        List<HistoryMileageQueryJsfDTO> result = new ArrayList<>();
        try {
            HistoryMileageQueryJsfVO historyMileageQueryJsfVO = new HistoryMileageQueryJsfVO();
            historyMileageQueryJsfVO.setStartTime(startTime);
            historyMileageQueryJsfVO.setEndTime(endTime);
            List<String> vehicleOwnerUseCase = Arrays.asList(VehicleOwnerUseCaseEnum.SOLUTION.getValue(), VehicleOwnerUseCaseEnum.OPEN.getValue(),
                    VehicleOwnerUseCaseEnum.RD.getValue(), VehicleOwnerUseCaseEnum.TEST.getValue(), VehicleOwnerUseCaseEnum.FIX.getValue(), VehicleOwnerUseCaseEnum.HARDWARE.getValue());
            historyMileageQueryJsfVO.setVehicleOwnerUseCaseList(vehicleOwnerUseCase);
            HttpResult<List<HistoryMileageQueryJsfDTO>> httpResult = vehicleMileageJsfService.historyMileageQuery(historyMileageQueryJsfVO);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
            log.info("调用datacenter获取历史行驶里程完成,结果={}", result);
        } catch (Exception e) {
            log.error("调用datacenter获取历史行驶里程失败,{}-{}", startTime, endTime, e);
        }
        return result;
    }
}
