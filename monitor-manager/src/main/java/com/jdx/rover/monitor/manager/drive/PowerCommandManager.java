/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.drive;

import cn.hutool.core.util.RandomUtil;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.entity.drive.ControlPadRequestIdDO;
import com.jdx.rover.monitor.enums.drive.command.DriveRemoteCommandTypeEnum;
import com.jdx.rover.monitor.repository.redis.drive.CockpitRequestIdRepository;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerActionEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerModuleEnum;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import com.jdx.rover.server.api.jsf.service.command.PowerManagerCommandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 远程驾驶连接服务
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PowerCommandManager {
    /**
     * 电源管理服务
     */
    private final PowerManagerCommandService powerManagerCommandService;
    /**
     * 驾舱操作ID Repository
     */
    private final CockpitRequestIdRepository cockpitRequestIdRepository;

    /**
     * 电源重启
     */
    public HttpResult<Void> sendCommand(DriveRemoteCommandVO vo) {
        PowerManagerCommandVO powerManagerCommandVO = initPowerManagerCommandVO(vo.getRemoteCommandType(), vo.getVehicleName());
        HttpResult<Void> result = powerManagerCommandService.publishPowerManagerCommand(powerManagerCommandVO);
        if (!HttpResult.isSuccess(result)) {
            return result;
        }
        ControlPadRequestIdDO dto = new ControlPadRequestIdDO();
        dto.setRequestId(powerManagerCommandVO.getRequestTime() + "");
        dto.setVehicleName(powerManagerCommandVO.getVehicleName());
        dto.setCockpitNumber(vo.getCockpitNumber());
        dto.setCockpitUserName(vo.getCockpitUserName());
        dto.setRemoteCommandType(vo.getRemoteCommandType());
        dto.setRequestTime(vo.getRequestTime());
        cockpitRequestIdRepository.set(dto);
        return HttpResult.success();
    }

    /**
     * 初始化电源管理请求vo
     */
    private PowerManagerCommandVO initPowerManagerCommandVO(String powerManagerAction, String vehicleName) {
        PowerManagerCommandVO powerManagerCommandVO = new PowerManagerCommandVO();
        powerManagerCommandVO.setVehicleName(vehicleName);
        powerManagerCommandVO.setModuleName(PowerManagerModuleEnum.CHASSIS.getValue());
        String action = PowerManagerActionEnum.REBOOT.getValue();
        if (StringUtils.equals(DriveRemoteCommandTypeEnum.REMOTE_CONTROL_POWER_OFF.name(), powerManagerAction)) {
            action = PowerManagerActionEnum.POWER_OFF.getValue();
        }
        powerManagerCommandVO.setAction(action);
        int nanoTime = RandomUtil.randomInt(1000000);
        Long requestTime = System.currentTimeMillis() * 1000000 + nanoTime;
        powerManagerCommandVO.setRequestTime(requestTime);
        return powerManagerCommandVO;
    }
}
