package com.jdx.rover.monitor.manager.accident;

import com.jdx.rover.monitor.enums.video.VideoDirectionEnum;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.AccidentAttachment;
import com.jdx.rover.monitor.repository.mapper.AccidentAttachmentMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 事故附件manager
 */
@Service
public class AccidentAttachmentManager extends BaseManager<AccidentAttachmentMapper, AccidentAttachment> {


    public List<AccidentAttachment> selectListByAccidentNoAndSource(String accidentNo, String source) {
        return lambdaQuery().eq(AccidentAttachment::getAccidentNo, accidentNo).eq(AccidentAttachment::getSource, source).list();
    }

    public List<AccidentAttachment> selectOrderedListByAccidentNoAndSource(String accidentNo, String source) {
        List<String> locationList = new ArrayList<>();
        locationList.add(VideoDirectionEnum.FRONT.getValue());
        locationList.add(VideoDirectionEnum.BACK.getValue());
        locationList.add(VideoDirectionEnum.LEFT.getValue());
        locationList.add(VideoDirectionEnum.RIGHT.getValue());
        String fieldOrder = locationList.stream()
                .map(location -> "'" + location.replace("'", "''") + "'") // 防止SQL注入
                .collect(Collectors.joining(","));
        String orderByClause = "ORDER BY FIELD(location, " + fieldOrder + ")";
        return lambdaQuery().eq(AccidentAttachment::getAccidentNo, accidentNo).eq(AccidentAttachment::getSource, source)
                .last(orderByClause)
                .list();
    }
}
