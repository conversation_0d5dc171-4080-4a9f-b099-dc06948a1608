package com.jdx.rover.monitor.manager.xata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.BusinessException;
import com.jdx.rover.monitor.dto.mapcollection.VehicleSolidifiedTaskDTO;
import com.jdx.rover.monitor.dataobject.mapcollection.VehicleStorageSpaceInfoDO;
import com.jdx.rover.monitor.dto.VehicleStorageResultDTO;
import com.jdx.rover.monitor.dto.mapcollection.XataSolidifiedTaskResultDTO;
import com.jdx.rover.monitor.dto.xata.DataCopyTaskDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;

import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * @description: xata平台交互
 * @author: wangguotai
 * @create: 2024-12-24 09:37
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class XataManager {

    /**
     * RestTemplate
     */
    private final RestTemplate restTemplate;

    /**
     * 1、自动创建数据需求
     */
    private static final String CREATE_DATA_COPY_URL = "http://atumu-meta.jd.com/meta/datatrigger/instance_data";

    /**
     * 2、查询无人车硬盘情况
     */
    private static final String QUERY_VEHICLE_STORAGE_URL = "http://atumu-meta.jd.com/meta/vehicle?";

    /**
     * 3、操作任务接口
     */
    private static final String QUERY_VEHICLE_SOLIDIFIED_TASK_URL = "http://atumu-meta.jd.com/meta/query_vehicle?";

    /**
     * 4、查询车号固化的任务
     */
    private static final String OPERATE_SOLIDIFIED_TASK_URL = "http://atumu-meta.jd.com/meta/operator_task?";

    /**
     * 单位换算
     */
    private static final long ONE_GIGABYTE = 1024 * 1024 * 1024;

    /**
     * 固化任务状态
     */
    private static final String TASK_SOLID_STATUS = "TASK_SOLID";

    /**
     * 创建数据合并任务
     */
    private static final String CREATE_DATA_MERGE_URL = "http://atumu-meta.jd.com/meta/datatrigger/instance_data_v1";

    /**
     * 创建数据拷贝任务
     */
    @Retryable(retryFor = {RestClientException.class, BusinessException.class}, recover = "recoverCreateDataCopyTask", maxAttempts = 2, backoff = @Backoff(delay = 1000))
    public boolean createDataCopyTask(DataCopyTaskDTO dataCopyTaskDTO) {
        String requestData = JsonUtils.writeValueAsString(dataCopyTaskDTO);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<>(requestData, httpHeaders);
        log.info("--->调用xata创建数据拷贝任务, 请求参数: [{}].", requestEntity);

        ResponseEntity<String> responseEntity = restTemplate.exchange(CREATE_DATA_COPY_URL, HttpMethod.POST, requestEntity, String.class);
        log.info("<---调用xata创建数据拷贝任务, 响应结果: [{}].", responseEntity);

        return true;
    }

    @Recover
    public boolean recoverCreateDataCopyTask(Throwable e, DataCopyTaskDTO dataCopyTaskDTO) {
        log.error("[告警]调用xata创建数据拷贝任务失败, 请求参数: [{}].", dataCopyTaskDTO, e);
        return false;
    }

    /**
     * 查询车辆硬盘空间
     *
     * @param vehicleName 车牌号
     * @return 储存空间对象
     */
    public VehicleStorageSpaceInfoDO queryVehicleStorage(String vehicleName) {
        StringBuilder requestBuilder = new StringBuilder(QUERY_VEHICLE_STORAGE_URL)
                .append("vehicleId=").append(vehicleName)
                .append("&").append("pageNo=").append(1)
                .append("&").append("pageSize=").append(10);
        String requestUrl = requestBuilder.toString();
        try {
            log.info("调用XATA查询无人车硬盘情况,url:{}", requestUrl);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(requestUrl, String.class);
            String body = responseEntity.getBody();
            log.info("调用XATA查询无人车硬盘情况结果:{}", body);
            VehicleStorageResultDTO vehicleStorageResultDTO = JsonUtils.readValue(body, new TypeReference<>() {
            });
            if (vehicleStorageResultDTO != null && CollectionUtils.isNotEmpty(vehicleStorageResultDTO.getRows())) {
                VehicleStorageSpaceInfoDO vehicleStorageSpaceInfoDO = new VehicleStorageSpaceInfoDO();
                double free = Optional.ofNullable(vehicleStorageResultDTO.getRows().get(0))
                        .map(VehicleStorageResultDTO.VehicleStorage::getVehicleDiskInfoVo)
                        .map(VehicleStorageResultDTO.VehicleDiskInfo::getFree)
                        .map(freeSpace -> freeSpace / ONE_GIGABYTE)
                        .orElse(0.0);
                BigDecimal freeBigDecimal = new BigDecimal(free);
                free = freeBigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                double total = Optional.ofNullable(vehicleStorageResultDTO.getRows().get(0))
                        .map(VehicleStorageResultDTO.VehicleStorage::getVehicleDiskInfoVo)
                        .map(VehicleStorageResultDTO.VehicleDiskInfo::getTotal)
                        .map(freeSpace -> freeSpace / ONE_GIGABYTE)
                        .orElse(0.0);
                BigDecimal totalBigDecimal = new BigDecimal(total);
                total = totalBigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                vehicleStorageSpaceInfoDO.setTotal(total);
                vehicleStorageSpaceInfoDO.setFree(free);
                return vehicleStorageSpaceInfoDO;
            }
        } catch (Exception ex) {
            log.error("调用XATA查询无人车硬盘情况失败:", ex);
        }
        return null;
    }

    /**
     * 获取车辆固化任务列表
     *
     * @param vehicleName vehicleName
     * @return List<VehicleSolidifiedTaskDO>
     */
    public List<VehicleSolidifiedTaskDTO> queryVehicleSolidifiedTask(String vehicleName) {
        String requestUrl = QUERY_VEHICLE_SOLIDIFIED_TASK_URL + "vehicleName=" + vehicleName;
        List<VehicleSolidifiedTaskDTO> taskList = Lists.newArrayList();
        try {
            log.info("调用XATA查询无人车固化任务 - [url: {}]", requestUrl);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(requestUrl, String.class);
            String body = responseEntity.getBody();
            log.info("调用XATA查询无人车固化任务结果 - [{}]", body);

            XataSolidifiedTaskResultDTO result = JsonUtils.readValue(body, new TypeReference<>() {
            });
            if (ObjectUtil.isNotNull(result) && CollUtil.isNotEmpty(result.getData())) {
                taskList.addAll(result.getData().stream()
                        .filter(item -> StrUtil.equals(item.getStatus(), TASK_SOLID_STATUS))
                        .map(item -> {
                            VehicleSolidifiedTaskDTO vehicleSolidifiedTaskDTO = new VehicleSolidifiedTaskDTO();
                            vehicleSolidifiedTaskDTO.setTaskId(item.getId());
                            vehicleSolidifiedTaskDTO.setDescription(item.getDescription());
                            vehicleSolidifiedTaskDTO.setCreateTime(item.getCreateTime());
                            return vehicleSolidifiedTaskDTO;
                        })
                        .collect(Collectors.toList()));
            }
        } catch (Exception ex) {
            log.error("调用XATA查询无人车固化任务失败 - ", ex);
        }
        return taskList;
    }

    /**
     * 取消固化任务
     *
     * @param taskId taskId
     */
    public void cancelSolidifiedTask(Integer taskId) {
        String requestUrl = OPERATE_SOLIDIFIED_TASK_URL + "taskId=" + taskId + "&action=stop";
        try {
            log.info("调用XATA取消固化任务 - [url: {}]", requestUrl);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(requestUrl, String.class);
            log.info("调用XATA取消固化任务结果 - [{}]", responseEntity);
        } catch (Exception ex) {
            log.error("调用XATA取消固化任务失败 - ", ex);
            throw new BusinessException(MonitorErrorEnum.ERROR_CANCEL_VEHICLE_SOLIDIFIED_TASK_FAIL.getCode(), MonitorErrorEnum.ERROR_CANCEL_VEHICLE_SOLIDIFIED_TASK_FAIL.getMessage());
        }
    }

    /**
     * 创建数据合并任务
     */
    public boolean createDataMergeTask(DataCopyTaskDTO dataCopyTaskDTO) {
        String requestData = JsonUtils.writeValueAsString(dataCopyTaskDTO);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<>(requestData, httpHeaders);
        log.info("--->调用xata创建数据合并任务, 请求参数: [{}].", requestEntity);

        ResponseEntity<String> responseEntity = restTemplate.exchange(CREATE_DATA_MERGE_URL, HttpMethod.POST, requestEntity, String.class);
        log.info("<---调用xata创建数据合并任务, 响应结果: [{}].", responseEntity);

        return true;
    }
}