/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.manager.shadow;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.shadow.api.domain.dto.ShadowTrackingEventDTO;
import com.jdx.rover.shadow.api.domain.vo.ShadowTrackingEventVO;
import com.jdx.rover.shadow.api.jsf.ShadowTrackingEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 影子系统 事故相关manager
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShadowAccidentManager {

    private final ShadowTrackingEventService shadowTrackingEventJsfService;

    /**
     * 增加影子事件
     * @param shadowTrackingEventVO
     * @return
     */
    public ShadowTrackingEventDTO addTrackingEvent(ShadowTrackingEventVO shadowTrackingEventVO){
        try {
            log.info("事故增加影子事件入参:{}", shadowTrackingEventVO);
            HttpResult<ShadowTrackingEventDTO> httpResult = shadowTrackingEventJsfService.addTrackingEvent(shadowTrackingEventVO);
            log.info("事故发布同步影子系统结果:{}",httpResult);
            if (HttpResult.isSuccess(httpResult) && httpResult.getData() !=null) {
                return httpResult.getData();
            }
            throw new AppException("增加影子事件系统异常！");
        } catch (Exception e) {
            log.error("增加影子事件系统异常",e);
            throw new AppException("增加影子事件失败！");
        }
    }


//    /**
//     * <p>
//     * 实体转换
//     * </p>
//     *
//     * @param accidentInfo 本系统事故数据
//     * @return 影子系统事故对象
//     */
//    private ShadowAccidentEventVO convert(AccidentInfo accidentInfo){
//        ShadowAccidentEventVO shadowAccidentEventVO = new ShadowAccidentEventVO();
//        shadowAccidentEventVO.setEventType(ShadowTypeEnum.ACCIDENT.getName());
//        shadowAccidentEventVO.setVehicleName(accidentInfo.getVehicleName());
//        shadowAccidentEventVO.setAccidentType(accidentInfo.getAccidentType());
//
//        //枚举转换
//        AccidentValidateEnum validateEnum = AccidentValidateEnum.of(accidentInfo.getAccidentStatus());
//        shadowAccidentEventVO.setAccidentValidity(validateEnum == null ? null : validateEnum.getValue());
//        shadowAccidentEventVO.setReportUser(accidentInfo.getCreatedUser());
//        shadowAccidentEventVO.setReportTime(accidentInfo.getAccidentTime());
//        shadowAccidentEventVO.setStartTime(accidentInfo.getAccidentStartTime());
//        shadowAccidentEventVO.setEndTime(accidentInfo.getAccidentEndTime());
//        shadowAccidentEventVO.setEventMessage(accidentInfo.getAccidentDesc());
//        shadowAccidentEventVO.setJiraNo(accidentInfo.getJiraNo());
//        shadowAccidentEventVO.setXataNo(accidentInfo.getXataTaskId());
//        shadowAccidentEventVO.setStationName(accidentInfo.getStationName());
//        shadowAccidentEventVO.setCityName(accidentInfo.getCityName());
//        //影子事件编号填写固定值
//        shadowAccidentEventVO.setEventNo("PC_MONITOR624738927");
//        return shadowAccidentEventVO;
//    }
}
