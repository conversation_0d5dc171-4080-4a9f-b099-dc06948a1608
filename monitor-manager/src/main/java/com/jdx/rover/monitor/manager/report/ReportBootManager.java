/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.manager.report;

import com.jdx.rover.monitor.repository.redis.ReportBootRepository;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.ModuleBootDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.ModuleBootDetailDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.NodeBootDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 启动上报Manager类
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
@RequiredArgsConstructor
@Service
public class ReportBootManager {
    /**
     * 启动信息
     */
    private final ReportBootRepository reportBootRepository;

    /**
     * 地图升级状态
     */
    public String getMapUpdaterStatus(VehicleRealtimeInfoDTO vehicleRealtimeInfoDto) {
        if (Objects.isNull(vehicleRealtimeInfoDto)) {
            return null;
        }
        // 只有初始化的时候才获取地图升级状态
        if (!Objects.equals(VehicleStateEnum.INITIALIZATION.getVehicleState(), vehicleRealtimeInfoDto.getVehicleState())) {
            return null;
        }
        return getMapUpdaterStatus(vehicleRealtimeInfoDto.getVehicleName());
    }

    /**
     * 地图升级状态
     */
    public String getMapUpdaterStatus(String vehicleName) {
        VehicleBootDTO vehicleBoot = reportBootRepository.get(vehicleName);
        if (Objects.isNull(vehicleBoot) || CollectionUtils.isEmpty(vehicleBoot.getNodeBootList())) {
            return null;
        }
        for (int i = vehicleBoot.getNodeBootList().size() - 1; i >= 0; i--) {
            NodeBootDTO nodeBoot = vehicleBoot.getNodeBootList().get(i);
            // control才有地图升级模块
            if (Objects.isNull(nodeBoot) || !"Control".equalsIgnoreCase(nodeBoot.getNodeName())) {
                continue;
            }
            List<ModuleBootDTO> moduleBootList = nodeBoot.getModuleBootList();
            if (CollectionUtils.isEmpty(moduleBootList)) {
                continue;
            }

            for (int j = moduleBootList.size() - 1; j >= 0; j--) {
                ModuleBootDTO moduleBoot = moduleBootList.get(j);
                if (Objects.isNull(moduleBoot) || CollectionUtils.isEmpty(moduleBoot.getModuleBootDetailList())) {
                    continue;
                }
                for (ModuleBootDetailDTO moduleBootDetail : moduleBoot.getModuleBootDetailList()) {
                    if (!Objects.isNull(moduleBootDetail) && "MAP_UPDATER".equalsIgnoreCase(moduleBootDetail.getName())) {
                        return moduleBootDetail.getBootStatus();
                    }
                }
            }
        }
        return null;
    }
}
