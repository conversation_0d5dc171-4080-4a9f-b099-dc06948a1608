/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorScheduleDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleStopDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleStopTravelDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleTravelDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.schedule.api.domain.dto.monitor.MonitorTransportPointDTO;
import com.jdx.rover.schedule.api.domain.enums.StopTravelStatus;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMonitorJsfService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a vehicle realtime order manager.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class VehicleScheduleManager {

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private ScheduleMonitorJsfService scheduleMonitorJsfService;

  /**
   * <p>
   * Get realtime schedule.
   * </p>
   *
   * @param
   */
  public Map<String, MonitorScheduleDTO> getScheduleDetailMap(List<String> vehicleNames) {
    Map<String, MonitorScheduleDTO> scheduleDtoMap = new HashMap<>();
    List<MonitorScheduleEntity> scheduleEntitys = vehicleScheduleRepository.list(vehicleNames);
    if (scheduleEntitys.isEmpty()) {
      return scheduleDtoMap;
    }
    for (MonitorScheduleEntity scheduleEntity : scheduleEntitys) {
      MonitorScheduleDTO scheduleDto = new MonitorScheduleDTO();
      scheduleDto.setScheduleNo(scheduleEntity.getScheduleNo());
      scheduleDto.setScheduleState(scheduleEntity.getScheduleState());
      scheduleDto.setStartDateTime(scheduleEntity.getStartDateTime());
      scheduleDto.setGlobalMileage(scheduleEntity.getGlobalMileage());
      scheduleDto.setFinishedMileage(scheduleEntity.getFinishedMileage());
      scheduleDto.setTotalFinishedMileage(scheduleEntity.getTotalFinishedMileage());
      scheduleDto.setTaskType(scheduleEntity.getTaskType());
      scheduleDto.setStop(scheduleEntity.getStop().stream().map(stop -> {
        MonitorScheduleStopDTO stopDto = new MonitorScheduleStopDTO();
        stopDto.setArrivedTime(stop.getArrivedTime());
        stopDto.setDepartTime(stop.getDepartTime());
        stopDto.setId(stop.getId());
        stopDto.setGoalId(stop.getGoalId());
        stopDto.setStopAction(stop.getStopAction());
        stopDto.setStopType(stop.getType());
        stopDto.setTravelStatus(stop.getTravelStatus());
        stopDto.setLon(stop.getLon());
        stopDto.setLat(stop.getLat());
        stopDto.setStopName(stop.getName());
        stopDto.setWaitingTime(stop.getWaitingTime());
        stopDto.setRoutingMileage(stop.getGlobalMileage());
        return stopDto;
      }).collect(Collectors.toList()));
      scheduleDtoMap.put(scheduleEntity.getVehicleName(), scheduleDto);
    }
    return scheduleDtoMap;
  }


  /**
   * <p>
   * Get realtime schedule.
   * </p>
   *
   * @param
   */
  public MonitorScheduleTravelDTO getScheduleTravelInfo(String vehicleName) {
    MonitorScheduleTravelDTO scheduleDto = new MonitorScheduleTravelDTO();
    MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.get(vehicleName);
    if (Objects.isNull(scheduleEntity) || CollectionUtils.isEmpty(scheduleEntity.getStop())) {
      return scheduleDto;
    }
    scheduleDto.setScheduleNo(scheduleEntity.getScheduleNo());
    scheduleDto.setScheduleStartTime(scheduleEntity.getStartDateTime());
    scheduleDto.setScheduleState(scheduleEntity.getScheduleState());
    LinkedList<MonitorScheduleStopEntity> stopList = scheduleEntity.getStop();
    Date currentDate = new Date();
    LinkedList<MonitorScheduleStopTravelDTO> stopTravelDtoList = new LinkedList<>();
    for (MonitorScheduleStopEntity stop : stopList) {
        if (StringUtils.equals(stop.getStopAction(), "START")) {
            continue;
        }
        MonitorScheduleStopTravelDTO stopDto = new MonitorScheduleStopTravelDTO();
        stopDto.setStopType(stop.getType());
        stopDto.setTravelStatus(stop.getTravelStatus());
        stopDto.setFinishedMileage(stop.getGlobalMileage());
        try {
            if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.ARRIVED.getTravelStatus())) {
                stopDto.setWaitingTime(DateUtil.between(stop.getArrivedTime(), currentDate, DateUnit.MS));
                stopDto.setTravelTime(DateUtil.between(stop.getStartTime(), stop.getArrivedTime(), DateUnit.MS));
            } else if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.START.getTravelStatus())) {
                stopDto.setTravelTime(DateUtil.between(stop.getStartTime(), currentDate, DateUnit.MS));
                VehicleRealtimeInfoDTO realtime = vehicleRealtimeRepository.get(vehicleName);
                stopDto.setFinishedMileage(Objects.nonNull(realtime)? realtime.getCurrentStopFinishedMileage(): 0.0);
            } else if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.DEPART.getTravelStatus())) {
                stopDto.setTravelTime(DateUtil.between(stop.getStartTime(), stop.getArrivedTime(), DateUnit.MS));
                stopDto.setWaitingTime(DateUtil.between(stop.getArrivedTime(), stop.getDepartTime(), DateUnit.MS));
            } else if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.STAY.getTravelStatus())) {
                stopDto.setTravelTime(DateUtil.between(stop.getStartTime(), stop.getArrivedTime(), DateUnit.MS));
                stopDto.setWaitingTime(DateUtil.between(stop.getArrivedTime(), currentDate, DateUnit.MS));
            } else if (StringUtils.equals(stop.getTravelStatus(), StopTravelStatus.INIT.getTravelStatus())) {
                stopDto.setFinishedMileage(0.0);
            }
            stopDto.setStopName(stop.getName());
            stopDto.setRoutingMileage(stop.getGlobalMileage());
            stopTravelDtoList.add(stopDto);
        } catch (Exception e) {
            log.error("获取车辆调度停靠点信息异常{}", JsonUtils.writeValueAsString(stop));
            stopTravelDtoList.add(stopDto);
            continue;
        }
    }
    scheduleDto.setStop(stopTravelDtoList);
    return scheduleDto;
  }


    /**
     * 查询指定车辆的虚拟调度状态。
     * @param vehicleName 车辆名称。
     * @return true 车辆有虚拟调度，false 没有虚拟调度。
     */
    public boolean queryVehicleVirtualSchedule(String vehicleName) {
        boolean result = false;
        try {
            HttpResult<Boolean> httpResult = scheduleMonitorJsfService.queryVehicleVirtualSchedule(vehicleName);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
            log.info("获取车辆是否有虚拟调度,vehicleName={},result={}", vehicleName, JsonUtils.writeValueAsString(httpResult));
        } catch (Exception e) {
            log.error("获取车辆是否有虚拟调度失败,vehicleName={}", vehicleName, e);
        }
        return result;
    }

    /**
     * 根据调度目标点ID获取接驳点名称。
     * @param scheduleGoalId 调度目标点ID。
     * @return 运输点名称。
     */
    public String getTransportPointNameByScheduleGoalId(Integer scheduleGoalId) {
        String result = null;
        try {
            HttpResult<MonitorTransportPointDTO> httpResult = scheduleMonitorJsfService.getTransportPointNameByScheduleGoalId(scheduleGoalId);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData().getTransportPointName();
            }
            log.info("获取车辆是否有虚拟调度,scheduleGoalId={},result={}", scheduleGoalId, JsonUtils.writeValueAsString(httpResult));
        } catch (Exception e) {
            log.error("获取车辆是否有虚拟调度失败,scheduleGoalId={}", scheduleGoalId, e);
        }
        return result;
    }


}
