/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.entity.MonitorScheduleStopOrderEntity;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleStopOrderRepository;
import com.jdx.rover.schedule.api.domain.dto.schedule.ScheduleOrderDto;
import com.jdx.rover.schedule.api.domain.enums.DeliveryMode;
import com.jdx.rover.schedule.api.domain.enums.DeliveryStatus;
import com.jdx.rover.schedule.api.domain.enums.StopAction;
import com.jdx.rover.schedule.api.domain.enums.StopTravelStatus;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTaskGoal;
import com.jdx.rover.schedule.api.domain.vo.schedule.ScheduleOrderQueryVo;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMonitorJsfService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a vehicle realtime state service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@LiteflowComponent(id = "initScheduleStopOrder", name = "初始化调度停靠点订单")
public class ScheduleStopOrderInitComponent extends NodeComponent {

  @Autowired
  private VehicleScheduleStopOrderRepository vehicleScheduleStopOrderRepository;

  @Autowired
  private ScheduleMonitorJsfService scheduleMonitorJsfService;

  private static Map<String, String> taskToActionMap = new HashMap<>();

  @Override
  public void beforeProcess() {
    taskToActionMap.put(TaskType.DELIVERY.getTaskType(),StopAction.PICKUP.getStopAction());
    taskToActionMap.put(TaskType.LOADTASK.getTaskType(),StopAction.LOAD.getStopAction());
    taskToActionMap.put(TaskType.UNLOADTASK.getTaskType(),StopAction.UNLOAD.getStopAction());
    taskToActionMap.put(TaskType.DROPOFF.getTaskType(),StopAction.DROPOFF.getStopAction());
  }

  @Override
  public boolean isAccess() {
    return getSlot().getChainReqData(LiteFlowTaskEnum.INITSCHEDULESTOPORDER.getType()) != null;
  }

  /**
   * <p>
   * Handle realtime schedule message.
   * </p>
   */
  @Override
  public void process() throws Exception {
    ScheduleTask scheduleTask = getSlot().getRequestData();
    if (CollectionUtils.isEmpty(scheduleTask.getScheduleTaskGoals())) {
      return;
    }
    initScheduleStopOrder(scheduleTask);
  }

  private void initScheduleStopOrder(ScheduleTask scheduleTask) {
    Map<String, MonitorScheduleStopOrderEntity> mapData = new HashMap<>();
    String scheduleName = scheduleTask.getScheduleName();
    String taskType = scheduleTask.getScheduleTaskType();
    for (ScheduleTaskGoal taskGoal : scheduleTask.getScheduleTaskGoals()) {
      if (!StringUtils.equalsAny(taskGoal.getTravelStatus(),
              StopTravelStatus.START.getTravelStatus(), StopTravelStatus.INIT.getTravelStatus())) {
        continue;
      } else if (!StringUtils.equals(taskGoal.getStopAction(), taskToActionMap.get(taskType))) {
        continue;
      }
      MonitorScheduleStopOrderEntity scheduleStopEntity = new MonitorScheduleStopOrderEntity();
      scheduleStopEntity.setId(taskGoal.getStopId());
      scheduleStopEntity.setGoalId(taskGoal.getGoalId());
      scheduleStopEntity.setStopAction(taskGoal.getStopAction());
      List<ScheduleOrderDto> scheduleOrderDtos = getDeliveryOrderList(scheduleName, taskGoal.getGoalId(), taskGoal.getStopAction());
      int totalCollectOrderNum = (int)scheduleOrderDtos.stream().filter(order
              -> StringUtils.equals(order.getDeliveryModel(), DeliveryMode.DROP_OFF.getDeliveryMode())).count();
      scheduleStopEntity.setTotalCollectOrderNum(totalCollectOrderNum);
      scheduleStopEntity.setTotalDeliveryOrderNum(scheduleOrderDtos.size() - totalCollectOrderNum);
      String hashKey = new StringBuilder(taskGoal.getStopId() + "_").append(taskGoal.getStopAction()).toString();
      mapData.put(hashKey, scheduleStopEntity);
    }
    vehicleScheduleStopOrderRepository.batchSet(scheduleTask.getVehicleName(), mapData);
  }

  /**
   * <p>
   * Init schedule total order number when schedule created.
   * </p>
   *
   * @param scheduleNo The no of schedule
   * @param stopAction The action of stop
   */
  private List<ScheduleOrderDto> getDeliveryOrderList(String scheduleNo, Integer goalId, String stopAction) {
    ScheduleOrderQueryVo orderQueryVo = new ScheduleOrderQueryVo();
    orderQueryVo.setScheduleNo(scheduleNo);
    orderQueryVo.setGoalId(goalId);
    if (StringUtils.equals(StopAction.UNLOAD.getStopAction(), stopAction)) {
      orderQueryVo.setDeliveryStatus(DeliveryStatus.OVERDUE.getDeliveryStatus());
    }
    HttpResult<List<ScheduleOrderDto>> deliveryOrderInfos = scheduleMonitorJsfService.queryScheduleOrders(orderQueryVo);
    if (!HttpResult.isSuccess(deliveryOrderInfos) || CollectionUtils.isEmpty(deliveryOrderInfos.getData())) {
      return new ArrayList<>();
    }
    return deliveryOrderInfos.getData().stream().filter(order ->
            !StringUtils.equals(order.getDeliveryStatus(), DeliveryStatus.DELETE.getDeliveryStatus())).collect(Collectors.toList());
  }

}

