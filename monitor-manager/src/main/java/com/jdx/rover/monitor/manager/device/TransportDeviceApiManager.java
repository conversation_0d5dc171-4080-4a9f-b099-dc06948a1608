/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.device;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.transport.api.domain.vo.device.TransportDevicePropertyJsfVO;
import com.jdx.rover.transport.api.domain.vo.device.TransportDevicePropertyListJsfVO;
import com.jdx.rover.transport.api.service.TransportDevicePropertyJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 设备端云服务
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransportDeviceApiManager {

    /**
     * 提供设备端云服务
     * <AUTHOR>
     */
    private final TransportDevicePropertyJsfService transportDeviceService;

    /**
     * 获取设备详情
     */
    public Map<String, Object> getDeviceStatusDetail(String deviceName, List<String> propertyList) {
        Map<String, Object> result = null;
        try {
            TransportDevicePropertyJsfVO requestVo = new TransportDevicePropertyJsfVO();
            requestVo.setDeviceName(deviceName);
            requestVo.setPropertyList(propertyList);
            HttpResult<Map<String, Object>> httpResult = transportDeviceService.getDeviceProperty(requestVo);;
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        } catch (Exception e) {
            log.warn("端云设备服务通过设备号{}获取全部实时信息调用失败", deviceName, e);
        }
        log.info("端云设备服务通过设备号获取实时信息调用完成,结果={}", result);
        return result;
    }

    /**
     * 获取设备列表详情
     */
    public List<Map<String, Object>> getDeviceStatusList(List<String> deviceNameList, List<String> propertyList) {
        List<Map<String, Object>> result = null;
        try {
            TransportDevicePropertyListJsfVO requestVo = new TransportDevicePropertyListJsfVO();
            requestVo.setDeviceNameList(deviceNameList);
            requestVo.setPropertyList(propertyList);
            HttpResult<List<Map<String, Object>>> httpResult = transportDeviceService.getDevicePropertyList(requestVo);;
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        } catch (Exception e) {
            log.warn("端云设备服务通过设备号列表{}获取基本信息调用失败", deviceNameList.toArray(), e);
        }
        log.info("端云设备服务通过设备列表获取实时信息调用完成,结果={}", result);
        return result;
    }

}
