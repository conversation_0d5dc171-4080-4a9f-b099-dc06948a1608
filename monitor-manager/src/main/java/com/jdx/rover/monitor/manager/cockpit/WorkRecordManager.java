/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.manager.cockpit;

import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.WorkRecord;
import com.jdx.rover.monitor.repository.mapper.WorkRecordMapper;
import org.springframework.stereotype.Service;

/**
 * 工作记录manager类
 *
 * <AUTHOR>
 */
@Service
public class WorkRecordManager extends BaseManager<WorkRecordMapper, WorkRecord> {

}