/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.vo.MonitorScheduleStopUpdateVO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * This is a schedule stop service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class VehicleScheduleStopManager {

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  /**
   * <p>
   * 更新调度停靠点实时信息。
   * </p>
   *
   */
  public HttpResult updateScheduleStop(MonitorScheduleStopUpdateVO scheduleStopUpdateVo) {
    log.info("Update vehicle schedule stop {}", scheduleStopUpdateVo);
    String key = RedisKeyEnum.SCHEDULE_UPDATE_LOCK_PREFIX.getValue() + scheduleStopUpdateVo.getVehicleName();
    try {
      if (RedissonUtils.tryLock(key)) {
        MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.getFromRedis(scheduleStopUpdateVo.getVehicleName());
        if (scheduleEntity == null || !StringUtils.equals(scheduleStopUpdateVo.getScheduleNo(), scheduleEntity.getScheduleNo())) {
          return HttpResult.error(MonitorErrorEnum.ERROR_SCHEDULE_ABSENT.getCode(), MonitorErrorEnum.ERROR_SCHEDULE_ABSENT.getMessage());
        }
        Optional<MonitorScheduleStopEntity> op = scheduleEntity.getStop().stream().filter(stop -> stop.getGoalId().equals(scheduleStopUpdateVo.getGoalId()))
                .findFirst();
        if (!op.isPresent()) {
          return HttpResult.error(MonitorErrorEnum.ERROR_SCHEDULE_STOP_ABSENT.getCode(), MonitorErrorEnum.ERROR_SCHEDULE_STOP_ABSENT.getMessage());
        }
        if (scheduleStopUpdateVo.getWaitingTime() != null) {
          op.get().setWaitingTime(scheduleStopUpdateVo.getWaitingTime());
        }
        vehicleScheduleRepository.set(scheduleEntity);
      }
    } catch (Exception e) {
      log.error("Update vehicle schedule stop exception ", e);
      return HttpResult.error(MonitorErrorEnum.ERROR_SCHEDULE_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_SCHEDULE_CALL_SERVICE.getMessage());
    } finally {
      RedissonUtils.unLock(key);
    }
    return HttpResult.success();
  }

}
