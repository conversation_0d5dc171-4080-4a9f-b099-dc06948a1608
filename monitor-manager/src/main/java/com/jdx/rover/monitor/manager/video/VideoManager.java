package com.jdx.rover.monitor.manager.video;

import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.bo.video.MultiVideoBO;
import com.jdx.rover.monitor.dto.RoverVideoUrlDTO;
import com.jdx.rover.monitor.manager.config.RoverVideoProperties;
import com.jdx.rover.shadow.api.domain.dto.ShadowVideoHttpResult;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 视频处理服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class VideoManager {

    @Resource
    private RoverVideoProperties roverVideoProperties;


    /**
     * <p>
     * 提交生成视频url任务
     * </p>
     */
    public String generateUrl(String url, String ossKey) {
        Map<String, Object> mapData = new HashMap<>();
        mapData.put("jss_url", ossKey);
        String videoResult = HttpUtil.get(url, mapData);
        log.info("生成视频地址返回结果： {}", videoResult);
        ShadowVideoHttpResult<String> taskResult =
                JsonUtils.readValue(videoResult, new TypeReference<ShadowVideoHttpResult<String>>() {});
        return taskResult.getData();
    }

    /**
     * 获取多车列表
     */
    public List<MultiVideoBO> getMultiUrl(List<String> vehicleNameList, String front, String right, String back, String left) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(roverVideoProperties.getMultiUrl());
        List<MultiVideoBO> result = new ArrayList<>();
        try {
            RoverVideoUrlDTO<List<MultiVideoBO>> dto = new RoverVideoUrlDTO<>();
            String url = builder.toUriString();
            log.info("获取多车视频地址请求URL:{}", url);
            MultiVideoUrlVO requestBody = MultiVideoUrlVO.builder().front(front).right(right).back(back).left(left).carIdList(vehicleNameList).build();
            String requestBodyResult = HttpUtil.post(url, JsonUtils.writeValueAsString(requestBody));
            log.info("获取多车视频地址返回结果:{}", requestBodyResult);
            dto = JsonUtils.readValue(requestBodyResult, new TypeReference<>() {
            });
            if (dto != null && CollectionUtils.isNotEmpty(dto.getData())) {
                result = dto.getData();
            }
        } catch (Exception e) {
            log.error("Load multi video url exception", e);
        }
        return result;
    }

    @Data
    @Builder
    static class MultiVideoUrlVO {
        /**
         * <p>
         * 请求标识
         * </p>
         */
        private String uid = "jdx";

        /**
         * <p>
         * 请求视频
         * </p>
         */
        private String front = "false";


        /**
         * <p>
         * 请求视频
         * </p>
         */
        private String right = "false";


        /**
         * <p>
         * 请求视频
         * </p>
         */
        private String back = "false";

        /**
         * <p>
         * 请求视频
         * </p>
         */
        private String left = "false";

        /**
         * <p>
         * 请求车辆列表
         * </p>
         */
        private List<String> carIdList;
    }
}
