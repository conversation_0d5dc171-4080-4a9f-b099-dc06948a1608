/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager.utils.map;

import lombok.extern.slf4j.Slf4j;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.geotools.util.factory.Hints;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Envelope;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.operation.buffer.BufferOp;
import org.locationtech.jts.operation.buffer.BufferParameters;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 地图瓦片工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class TileUtils {

    private static final int kMorTonTileNum = 64;
    private static final int kUTMPow = 50;
    private static final int kWGS84Pow = 31;
    private static final int kPNGLength = 1024;
    private static final int level = 19;
    private static final int kBaseLevelNum = 20;
    private static final int kTotalLevelNum = 40;
    private static final double kCustomPixel = 26214.4;
    private static int kJDMCoordNum = 32;
    private static double kJDMCoordConstant = Math.pow(2, kJDMCoordNum) / 360.0;

    private static GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory(
            new Hints(Hints.CRS, DefaultGeographicCRS.WGS84));

    /**
     * 经纬度获取瓦片id
     */
    public static Long getTileId(Double longitude, Double latitude) {
        int map_x = Double.valueOf(longitude * kJDMCoordConstant).intValue();
        int map_y = Double.valueOf(latitude * kJDMCoordConstant).intValue();
        long morton_code = getMortonCodeFromJDMCoord(map_x, map_y);
        long level_num = Double.valueOf(Math.pow(2, kBaseLevelNum + level)).longValue();
        long tile_num = morton_code >> (kMorTonTileNum - 2 * (level + 1));
        return level_num + tile_num;
    }

    /**
     * 经纬度获取瓦片id
     */
    public static Long getTileId(Long map_x, Long map_y) {
        long morton_code = getMortonCodeFromJDMCoord(map_x.intValue(), map_y.intValue());
        long level_num = Double.valueOf(Math.pow(2, kBaseLevelNum + level)).longValue();
        long tile_num = morton_code >> (kMorTonTileNum - 2 * (level + 1));
        return level_num + tile_num;
    }

    public static List<Long> getTileIdList(Double longitude, Double latitude, Double distance) {
        Set<Long> tileIdList = new HashSet<>();
        long tile_length = Double.valueOf(Math.pow(2, kWGS84Pow - level)).longValue();
        Coordinate coordinate = new Coordinate(longitude, latitude);
        // 执行缓冲区计算
        BufferParameters bufferParam = new BufferParameters();
        bufferParam.setEndCapStyle(BufferParameters.CAP_ROUND);
        bufferParam.setJoinStyle(BufferParameters.JOIN_BEVEL);
//        bufferParam.setMitreLimit(5);
//        bufferParam.setSimplifyFactor(0.01);
//        bufferParam.setQuadrantSegments(8);
        Geometry bufferedGeometry = BufferOp.bufferOp(geometryFactory.createPoint(coordinate), distance, bufferParam);
        Envelope envelope = bufferedGeometry.getEnvelopeInternal();
        double min_lon = envelope.getMinX();
        double max_lon = envelope.getMaxX();
        double min_lat = envelope.getMinY();
        double max_lat = envelope.getMaxY();
        Integer min_jdm_x = Double.valueOf(min_lon * kJDMCoordConstant).intValue();
        Integer min_jdm_y = Double.valueOf(min_lat * kJDMCoordConstant).intValue();
        Integer max_jdm_x = Double.valueOf(max_lon * kJDMCoordConstant).intValue();
        Integer max_jdm_y = Double.valueOf(max_lat * kJDMCoordConstant).intValue();
        long row_num = Double.valueOf(max_jdm_y / tile_length).longValue() -
                Double.valueOf(min_jdm_y / tile_length).longValue() + 1;
        long col_num = Double.valueOf(max_jdm_x / tile_length).longValue() -
                Double.valueOf(min_jdm_x / tile_length).longValue() + 1;
        for (int i = 0; i < row_num; ++i) {
            for (int j = 0; j < col_num; ++j) {
                long tileId =
                        getTileId(min_jdm_x + j * tile_length, min_jdm_y + i * tile_length);
                tileIdList.add(tileId);
            }
        }
        return new ArrayList<>(tileIdList);
    }

    public static Long getMortonCodeFromJDMCoord(Integer jdm_x, Integer jdm_y) {
        long morton_code = 0;
        for (int i = 0; i < (kJDMCoordNum - 1); ++i) {
            long xbit = ((jdm_x >> i) & 0x01);
            long ybit = ((jdm_y >> i) & 0x01);
            morton_code += (xbit << (2 * i));
            morton_code += (ybit << (2 * i + 1));
        }
        long xbit = ((jdm_x >> (kJDMCoordNum - 1)) & 0x01);
        morton_code += (xbit << (2 * (kJDMCoordNum - 1)));
        return morton_code;
    }

    public static void main(String[] args) {

        /*
        [{"tileVersion":200969,"tileId":632324762485},{"tileVersion":200969,"tileId":632324763168},{"tileVersion":200969,"tileId":632324762486},{"tileVersion":200969,"tileId":632324762484},{"tileVersion":200969,"tileId":632324763170},{"tileVersion":200969,"tileId":632324762487},{"tileVersion":200969,"tileId":632324762483},{"tileVersion":200969,"tileId":632324763146},{"tileVersion":200969,"tileId":632324762463},{"tileVersion":200969,"tileId":632324762460},{"tileVersion":200969,"tileId":632324762459},{"tileVersion":200969,"tileId":632324762462},{"tileVersion":200969,"tileId":632324763144},{"tileVersion":200969,"tileId":632324762481},{"tileVersion":200969,"tileId":632324762461},{"tileVersion":200969,"tileId":632324762457}]
         */
        Double longitude = 120.77249;
        Double latitude = 31.592545;

        //31.5905- 31.5909
        System.out.println(TileUtils.getTileId(longitude, latitude));
        System.out.println(TileUtils.getTileIdList(longitude, latitude, 0.0003));
    }
 }
