/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.vehicle;

import com.google.common.collect.Lists;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleManager {
  @Autowired
  private MetadataVehicleApiManager metadataVehicleApiManager;
  @Autowired
  private VehicleBasicRepository vehicleBasicRepository;
  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;

  /**
   * 通过车辆名称获取车辆基本信息
   *
   * @param vehicleName 车辆名称
   * @return list
   */
  public VehicleBasicDTO getBasicByName(String vehicleName) {
    VehicleBasicDTO result = vehicleBasicRepository.get(vehicleName);
    if (result != null) {
      return result;
    }

    result = metadataVehicleApiManager.getByName(vehicleName);
    if (result != null) {
      vehicleBasicRepository.set(result);
    }
    return result;
  }

  /**
   * 通过车辆名称列表获取车辆基本信息列表
   *
   * @param vehicleNameList 车辆名称列表
   */
  public List<VehicleBasicDTO> listBasicByName(List<String> vehicleNameList) {
    List<VehicleBasicDTO> result = vehicleBasicRepository.list(vehicleNameList);
    return result;
  }

  /**
   * 通过用户名获取所有车辆基本信息
   *
   * @param userName 用户名称
   * @return list
   */
  public List<VehicleBasicDTO> listVehicleByUserName(String userName) {
    Set<String> vehicleListInUser = userVehicleNameRepository.get(userName);
    if (CollectionUtils.isNotEmpty(vehicleListInUser)) {
      return listBasicByName(Lists.newArrayList(vehicleListInUser));
    }

    List<String> vehicleNameList = metadataVehicleApiManager.getVehicleInUser(userName);
    if (CollectionUtils.isNotEmpty(vehicleNameList)) {
      userVehicleNameRepository.set(userName, vehicleNameList);
    }
    return metadataVehicleApiManager.listByName(vehicleNameList);
  }

}
