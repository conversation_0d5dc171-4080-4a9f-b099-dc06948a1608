/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.metadata.api.domain.dto.vehicle.VehicleBootUpExceptionInfoDTO;
import com.jdx.rover.monitor.bo.vehicle.VehicleAbnormalBootBO;
import com.jdx.rover.monitor.enums.ModuleBootStatusEnum;
import com.jdx.rover.monitor.po.VehicleAbnormalBoot;
import com.jdx.rover.monitor.repository.redis.ReportBootRepository;
import com.jdx.rover.monitor.repository.redis.metadata.VehicleBootModuleRepository;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import com.jdx.rover.server.api.domain.enums.report.BootStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车辆启动manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleBootManager {
  @Autowired
  private ReportBootRepository reportBootRepository;
  @Autowired
  private VehicleBootModuleRepository vehicleBootModuleRepository;

  /**
   * 获取启动模块配置详情
   */
  public VehicleAbnormalBootBO getAbnormalModuleBootList(String vehicleName) {
    VehicleAbnormalBootBO abnormalBootBo = new VehicleAbnormalBootBO();
    VehicleBootDTO vehicleBootDto = reportBootRepository.get(vehicleName);
    if (Objects.isNull(vehicleBootDto) || CollectionUtils.isEmpty(vehicleBootDto.getNodeBootList())) {
      return abnormalBootBo;
    }
    abnormalBootBo.setVehicleName(vehicleName);
    long endTime = vehicleBootDto.getEndTime();
    if (endTime < NumberConstant.MILLION) {
      endTime = System.currentTimeMillis() * NumberConstant.MILLION;
    }
    long duration = (endTime - vehicleBootDto.getStartTime()) / NumberConstant.MILLION;
    abnormalBootBo.setDuration((int)duration/1000);
    List<VehicleBootUpExceptionInfoDTO> bootUpConfigList = vehicleBootModuleRepository.get();
    if (CollectionUtils.isEmpty(bootUpConfigList)) {
        return abnormalBootBo;
    }
    Map<String, VehicleBootUpExceptionInfoDTO> bootMap =
            bootUpConfigList.stream().collect(Collectors.toMap(obj -> obj.getDevice() + "_" + obj.getModuleField(), Function.identity()));
    List<VehicleAbnormalBoot> vehicleAbnormalBootList = new ArrayList<>();
    final Long uuid = vehicleBootDto.getRuntimeUuid();
    long bootId = vehicleBootDto.getBootId();
    vehicleBootDto.getNodeBootList().stream().filter(node -> StringUtils.isNotBlank(node.getBootStatus()) && CollectionUtils.isNotEmpty(node.getModuleBootList())).forEach(nodeBoot -> {
      String nodeName = nodeBoot.getNodeName();
      nodeBoot.getModuleBootList().stream().forEach(moduleBoot -> {
        Integer bootCount = moduleBoot.getBootCount();
        moduleBoot.getModuleBootDetailList().stream().forEach(moduleBootDetail -> {
          VehicleBootUpExceptionInfoDTO bootConf = bootMap.get(nodeName + "_" + moduleBootDetail.getName());
          if (Objects.isNull(bootConf)) {
            return;
          }
          VehicleAbnormalBoot abnormalBoot = new VehicleAbnormalBoot();
          abnormalBoot.setVehicleName(vehicleName);
          abnormalBoot.setBootUuid(String.valueOf(uuid));
          abnormalBoot.setBootId((int)bootId);
          abnormalBoot.setBootStatus(moduleBootDetail.getBootStatus());
          abnormalBoot.setNodeName(nodeName);
          abnormalBoot.setBootCount(bootCount);
          abnormalBoot.setModuleName(moduleBootDetail.getName());
          abnormalBoot.setUsers(StringUtils.join(bootConf.getPrincipalErps(), ";"));
          long moduleEndTime = moduleBootDetail.getEndTime();
          if (moduleEndTime < NumberConstant.MILLION) {
            moduleEndTime = System.currentTimeMillis() * NumberConstant.MILLION;
          }
          long moduleDuration = (moduleEndTime - moduleBootDetail.getStartTime()) / NumberConstant.MILLION;
          abnormalBoot.setDuration((int)moduleDuration/1000);
          if (BootStatusEnum.valueOf(moduleBootDetail.getBootStatus()) == BootStatusEnum.START_FAILED) {
            abnormalBoot.setErrorCode(moduleBootDetail.getErrorCode());
            abnormalBoot.setErrorMessage(moduleBootDetail.getErrorMsg());
            vehicleAbnormalBootList.add(abnormalBoot);
          } else if (BootStatusEnum.valueOf(moduleBootDetail.getBootStatus()) == BootStatusEnum.START_SUCCESS) {
            if (abnormalBoot.getDuration() > bootConf.getOvertimeThreshold()) {
              abnormalBoot.setBootStatus(ModuleBootStatusEnum.START_TIMEOUT.getBootStatus());
              vehicleAbnormalBootList.add(abnormalBoot);
            }
          }
        });
      });
      if (!Objects.isNull(nodeBoot.getHardwareModule()) && CollectionUtils.isNotEmpty(nodeBoot.getHardwareModule().getModuleBootDetailList())) {
          Integer bootCount = nodeBoot.getHardwareModule().getBootCount();
          nodeBoot.getHardwareModule().getModuleBootDetailList().stream().forEach(moduleBootDetail -> {
            VehicleBootUpExceptionInfoDTO bootConf = bootMap.get(nodeName + "_" + moduleBootDetail.getName());
            if (Objects.isNull(bootConf)) {
              return;
            }
            VehicleAbnormalBoot abnormalBoot = new VehicleAbnormalBoot();
            abnormalBoot.setVehicleName(vehicleName);
            abnormalBoot.setBootUuid(String.valueOf(uuid));
            abnormalBoot.setBootId((int)bootId);
            abnormalBoot.setBootStatus(moduleBootDetail.getBootStatus());
            abnormalBoot.setNodeName(nodeName);
            abnormalBoot.setBootCount(bootCount);
            abnormalBoot.setModuleName(moduleBootDetail.getName());
            abnormalBoot.setUsers(StringUtils.join(bootConf.getPrincipalErps(), ";"));
            long hardEndTime = moduleBootDetail.getEndTime();
            if (hardEndTime < NumberConstant.MILLION) {
              hardEndTime = System.currentTimeMillis() * NumberConstant.MILLION;
            }
            long moduleDuration = (hardEndTime - moduleBootDetail.getStartTime()) / NumberConstant.MILLION;
            abnormalBoot.setDuration((int)moduleDuration/1000);
            if (BootStatusEnum.valueOf(moduleBootDetail.getBootStatus()) == BootStatusEnum.START_FAILED) {
              abnormalBoot.setErrorCode(moduleBootDetail.getErrorCode());
              abnormalBoot.setErrorMessage(moduleBootDetail.getErrorMsg());
              vehicleAbnormalBootList.add(abnormalBoot);
            } else if (BootStatusEnum.valueOf(moduleBootDetail.getBootStatus()) == BootStatusEnum.START_SUCCESS) {
              if (moduleDuration > bootConf.getNormalTime()) {
                vehicleAbnormalBootList.add(abnormalBoot);
              }
            }
        });
      }
    });
    abnormalBootBo.setAbnormalModuleBootList(vehicleAbnormalBootList);
    return abnormalBootBo;
  }

}
