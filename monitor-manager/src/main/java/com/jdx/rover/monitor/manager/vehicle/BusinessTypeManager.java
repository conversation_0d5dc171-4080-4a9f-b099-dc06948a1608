/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleBusinessTypeScoreEnum;
import com.jdx.rover.monitor.repository.redis.sort.BusinessTypeScoreSortedSetRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BusinessTypeManager {
  @Autowired
  private BusinessTypeScoreSortedSetRepository businessTypeScoreSortedSetRepository;

  /**
   * 通过businessType获取车辆名称
   *
   * @return list
   */
  public List<String> getVehicleNameByBusinessType(String businessType) {
    List<String> result = new ArrayList<>();
    Map<String, Double> map = businessTypeScoreSortedSetRepository.getValueAndScoreMap();
    map.forEach((vehicleName, score) -> {
      if (isMatchBusinessType(businessType, score)) {
        result.add(vehicleName);
      }
    });
    return result;
  }

  /**
   * 是否匹配车辆业务类型
   *
   * @param businessType
   * @param score
   * @return
   */
  private boolean isMatchBusinessType(String businessType, Double score) {
    // 没有业务类型全匹配
    if (StringUtils.isBlank(businessType)) {
      return true;
    }
    // 分数不存在不匹配,防止空指针
    if (score == null) {
      return false;
    }
    int businessTypeScore = (int) (score % VehicleBusinessTypeScoreEnum.VENDING.getValue());
    if (StringUtils.equals(businessType, VehicleBusinessTypeEnum.DISPATCH.getValue())
        && businessTypeScore >= VehicleBusinessTypeScoreEnum.DISPATCH.getValue()) {
      return true;
    }
    if (StringUtils.equals(businessType, VehicleBusinessTypeEnum.VENDING.getValue())
        && businessTypeScore < VehicleBusinessTypeScoreEnum.DISPATCH.getValue()) {
      return true;
    }
    return false;
  }
}
