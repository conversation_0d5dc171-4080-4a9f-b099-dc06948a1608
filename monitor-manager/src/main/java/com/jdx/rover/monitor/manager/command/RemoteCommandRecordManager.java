/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.command;

import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.enums.vehicle.command.RemoteOperationTypeEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.po.MonitorUserOperationLog;
import com.jdx.rover.monitor.repository.mapper.MonitorUserOperationLogMapper;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * This is a monitor remote record manager.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class RemoteCommandRecordManager {

  @Autowired
  private MonitorUserOperationLogMapper userOperationLogMapper;

  @Autowired
  private VehicleManager vehicleManager;

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  /**
   * <p>
   * Save remote command record.
   * </p>
   *
   * @param monitorUserOperationEntity the remote command record
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public void addRecordLog(MonitorUserOperationEntity monitorUserOperationEntity) {
    VehicleBasicDTO basicDto = vehicleManager.getBasicByName(monitorUserOperationEntity.getVehicleName());
    if (basicDto == null) {
      return;
    }
    MonitorUserOperationLog monitorUserOperationLog = new MonitorUserOperationLog();
    if (StringUtils.equalsAny(monitorUserOperationEntity.getOperationType(), WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue(), WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue()
            ,RemoteOperationTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.getValue(), RemoteOperationTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.getValue())) {
      MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.get(monitorUserOperationEntity.getVehicleName());
      Optional.ofNullable(scheduleEntity).ifPresent(entity -> monitorUserOperationLog.setScheduleState(entity.getScheduleState()));
    }
    monitorUserOperationLog.setVehicleName(monitorUserOperationEntity.getVehicleName());
    monitorUserOperationLog.setUserName(monitorUserOperationEntity.getUserName());
    monitorUserOperationLog.setOperateTimestamp(monitorUserOperationEntity.getTimestamp());
    monitorUserOperationLog.setOperationSource(monitorUserOperationEntity.getOperationSource());
    monitorUserOperationLog.setStationId(basicDto.getStationId());
    monitorUserOperationLog.setStationName(basicDto.getStationName());
    monitorUserOperationLog.setCityId(basicDto.getCityId());
    monitorUserOperationLog.setCityName(basicDto.getCityName());
    monitorUserOperationLog.setOperationType(monitorUserOperationEntity.getOperationType());
    monitorUserOperationLog.setOperationMessage(monitorUserOperationEntity.getOperationMessage());
    monitorUserOperationLog.setRequestId(monitorUserOperationEntity.getRequestId());
    monitorUserOperationLog.setState(monitorUserOperationEntity.getState());
    userOperationLogMapper.insert(monitorUserOperationLog);
  }

}

