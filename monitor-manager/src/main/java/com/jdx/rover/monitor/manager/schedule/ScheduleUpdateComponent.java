/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.enums.ScheduleTaskTypeEnum;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Optional;

/**
 * <p>
 * This is a vehicle realtime state service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@LiteflowComponent(id = "addSchedule", name = "更新调度")
public class ScheduleUpdateComponent extends NodeComponent {

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  /**
   * <p>
   * Handle realtime schedule message.
   * </p>
   */
  @Override
  public void process() throws Exception {
    ScheduleTask scheduleTask = getSlot().getRequestData();
    MonitorScheduleEntity scheduleEntity = Optional.ofNullable(
            vehicleScheduleRepository.getFromRedis(scheduleTask.getVehicleName())).orElse(new MonitorScheduleEntity());
    if (scheduleEntity.getRecordTime() != null && scheduleEntity.getRecordTime().after(scheduleTask.getRecordTime())) {
      return;
    }
    if (!StringUtils.equals(scheduleTask.getScheduleTaskType(), ScheduleTaskTypeEnum.DROPOFF.getValue()) &&
            StringUtils.equalsAny(scheduleEntity.getTaskType(), ScheduleTaskTypeEnum.LOADTASK.getValue(), ScheduleTaskTypeEnum.DELIVERY.getValue()) &&
            !StringUtils.equals(scheduleTask.getScheduleTaskType(), scheduleEntity.getTaskType())) {
      scheduleEntity.setFinishedDeliveryOrderNum(0);
      scheduleEntity.setCanceledDeliveryOrderNum(0);
      scheduleEntity.setTotalCollectOrderNum(0);
      scheduleEntity.setTotalDeliveryOrderNum(scheduleTask.getTotalOrderCount());
      getSlot().setChainReqData(LiteFlowTaskEnum.INITSCHEDULESTOPORDER.getType(), new ArrayList<>());
    } else if (scheduleTask.getCurrentGoalIndex() == 1 && scheduleTask.getIsArrived() == 0) {
      scheduleEntity.setFinishedDeliveryOrderNum(0);
      scheduleEntity.setCanceledDeliveryOrderNum(0);
      scheduleEntity.setTotalCollectOrderNum(StringUtils.equals(scheduleTask.getScheduleTaskType(),
              ScheduleTaskTypeEnum.DROPOFF.getValue()) ? scheduleTask.getTotalOrderCount():0);
      scheduleEntity.setTotalDeliveryOrderNum(StringUtils.equalsAny(scheduleTask.getScheduleTaskType(),
              ScheduleTaskTypeEnum.DELIVERY.getValue(), ScheduleTaskTypeEnum.LOADTASK.getValue()) ? scheduleTask.getTotalOrderCount():0);
      getSlot().setChainReqData(LiteFlowTaskEnum.INITSCHEDULESTOPORDER.getType(), new ArrayList<>());
    } else if (!StringUtils.equals(scheduleTask.getScheduleTaskType(), ScheduleTaskTypeEnum.DROPOFF.getValue()) &&
            StringUtils.equals(scheduleTask.getVehicleScheduleState(), VehicleScheduleState.ALREADY.getVehicleScheduleState())) {
      scheduleEntity.setTotalDeliveryOrderNum(scheduleTask.getTotalOrderCount() - scheduleEntity.getTotalCollectOrderNum());
    }
    scheduleEntity.setRecordTime(scheduleTask.getRecordTime());
    scheduleEntity.setTaskType(scheduleTask.getScheduleTaskType());
    scheduleEntity.setVehicleName(scheduleTask.getVehicleName());
    scheduleEntity.setScheduleState(scheduleTask.getVehicleScheduleState());
    scheduleEntity.setScheduleNo(scheduleTask.getScheduleName());
    scheduleEntity.setGlobalMileage(scheduleTask.getGlobalMileage());
    scheduleEntity.setStartDateTime(scheduleTask.getCreateTime());
    scheduleEntity.setEstDepartureTime(scheduleTask.getEstDepartureTime());
    getSlot().setResponseData(scheduleEntity);
    getSlot().setChainReqData(LiteFlowTaskEnum.UPDATESCHEDULE.getType(), scheduleEntity);
  }

}

