/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.issue;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jdx.rover.monitor.dto.issue.IssueOperateHistoryDTO;
import com.jdx.rover.monitor.po.IssueAlarmAttachment;
import com.jdx.rover.monitor.po.IssueOperateHistory;
import com.jdx.rover.monitor.repository.mapper.IssueAlarmAttachmentMapper;
import com.jdx.rover.monitor.repository.mapper.IssueOperateHistoryMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IssueOperateHistoryManager {

  @Autowired
  private IssueOperateHistoryMapper issueOperateHistoryMapper;

  @Autowired
  private IssueAlarmAttachmentMapper issueAlarmAttachmentMapper;

  public List<IssueOperateHistoryDTO> listOperateHistoryByIssueNo(String issueNo) {
    List<IssueOperateHistory> issueOperateHistories = issueOperateHistoryMapper.getByIssueNo(issueNo);
    if (issueOperateHistories == null) {
      return Lists.newArrayList();
    }
    List<IssueOperateHistoryDTO> monitorIssueOperateHistories = new ArrayList<>();
    for (IssueOperateHistory issueOperateHistory : issueOperateHistories) {
      IssueOperateHistoryDTO monitorIssueOperateHistory = issueOperateHistory.toIssueOperateHistoryDto();
      if (issueOperateHistory.getVehicleAlarmId() != null) {
        LambdaQueryWrapper<IssueAlarmAttachment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IssueAlarmAttachment:: getIssueNo, issueNo);
        wrapper.eq(IssueAlarmAttachment:: getAlarmId, issueOperateHistory.getVehicleAlarmId());
        List<IssueAlarmAttachment> attachmentList = issueAlarmAttachmentMapper.selectList(wrapper);
        if (attachmentList == null) {
          monitorIssueOperateHistory.setAttachmentList(Lists.newArrayList());
        } else {
          monitorIssueOperateHistory.setAttachmentList(attachmentList.stream().map(IssueAlarmAttachment::getUrl).collect(Collectors.toList()));
        }
      }
      monitorIssueOperateHistories.add(monitorIssueOperateHistory);
    }
    Collections.reverse(monitorIssueOperateHistories);
    return monitorIssueOperateHistories;
  }

  public void insert(IssueOperateHistory issueOperateHistory) {
    issueOperateHistoryMapper.insert(issueOperateHistory);
  }
}