package com.jdx.rover.monitor.manager.mqtt;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.exception.AppException;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.enums.mqtt.pdu.ISetter;
import com.jdx.rover.monitor.enums.mqtt.pdu.PduBaseCommand;
import com.jdx.rover.monitor.enums.mqtt.pdu.PduPower;
import com.jdx.rover.monitor.manager.vehicle.VehicleStatusManager;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestHeader;
import com.jdx.rover.server.api.domain.enums.mqtt.MqttQosEnum;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * mqtt消息发送服务
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PduMqttManager {

    private final MqttSendJsfService mqttSendJsfService;

    private final VehicleStatusRepository vehicleStatusRepository;

    /**
     * <p>
     * Send the mqtt command
     * </p>
     *
     * @param vehicleName the vehicle name
     * @param message the mqtt message
     * @return  result
     * @throws
     */
    public void sendCommand(String vehicleName, String message) {
        VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
        // 校验PDU mqtt连接是否存在
        if(VehicleStatusManager.isPduOffline(vehicleStatusDO)) {
            throw new AppException(MonitorErrorEnum.ERROR_POWER_MANAGER_ABSENT.getMessage());
        }

        forceSendCommand(vehicleName, message);
    }

    /**
     * <p>
     * Force Send the mqtt command
     * </p>
     *
     * @param vehicleName the vehicle name
     * @param message the mqtt message
     * @return  result
     * @throws
     */
    public void forceSendCommand(String vehicleName, String message) {
        MqttMessageVO messageVO = new MqttMessageVO<>();
        String topic = MqttTopicEnum.PDU_COMMAND.getTopic() + vehicleName;
        messageVO.setTopic(topic);
        messageVO.setMessage(message);
        messageVO.setQos(MqttQosEnum.ONLY_ONCE.getValue());
        messageVO.setRetained(false);
        HttpResult result = mqttSendJsfService.sendString(messageVO);
        log.info("Send mqtt pdu command: {}, result:{}", messageVO, result);
    }

    /**
     * <p>
     * send the power command
     * </p>
     *
     * @param vehicleName the vehicle name
     * @param powerType 指令类型
     * @return  result
     * @throws
     */
    public void sendPowerCommand(String vehicleName, CommandTypeEnum powerType) {
        CommandBuilder<PduPower> builder = CommandBuilder.builder(vehicleName, PduCommandTypeEnum.POWER);
        builder.addColumn(PduPower::setType, powerType.getCode());
        sendCommand(vehicleName, JsonUtils.writeValueAsString(builder.build()));
    }

    /**
     * <p>
     * force send the power command
     * </p>
     *
     * @param vehicleName the vehicle name
     * @param powerType 指令类型
     * @return  result
     * @throws
     */
    public void forceSendPowerCommand(String vehicleName, CommandTypeEnum powerType) {
        CommandBuilder<PduPower> builder = CommandBuilder.builder(vehicleName, PduCommandTypeEnum.POWER);
        builder.addColumn(PduPower::setType, powerType.getCode());
        forceSendCommand(vehicleName, JsonUtils.writeValueAsString(builder.build()));
    }

    /**
     * <p>
     * PDU 指令枚举
     * </p>
     *
     * <AUTHOR>
     * @date 2022-05-26
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    @ToString
    public enum PduCommandTypeEnum {
        POWER("POWER", "上下电", PduPower.class),
        ;

        /**
         * 指令类型
         */
        private final String commandType;

        /**
         * 指令描述
         */
        private final String desc;

        /**
         * 具体的指令参数封装
         */
        private Class<? extends PduBaseCommand> commandData;

        /**
         * 根据指令类型获取实例
         * @param commandType 指令类型
         * @return 具体实例
         */
        public static PduCommandTypeEnum of(String commandType){
            for (PduCommandTypeEnum value : PduCommandTypeEnum.values()) {
                if(value.getCommandType().equals(commandType)){
                    return value;
                }
            }
            return null;
        }
    }

    /**
     * <p>
     * MQTT 指令基础 class
     * </p>
     *
     * <AUTHOR>
     * @date 2022-05-26
     */
    @Data
    public static class CommandBuilder<T> {

        /**
         * MqttRequest 实体
         */
        private MqttRequestDTO<T> mqttRequestDTO;

        /**
         * set值
         */
        public <T, U> CommandBuilder<T> addColumn(ISetter<T, U> fn, Object x) {
            fn.set((T) mqttRequestDTO.getData(), (U) x);
            return (CommandBuilder<T>) this;
        }

        /**
         * 构建对象
         * @return
         */
        public MqttRequestDTO<T> build() {
            return this.mqttRequestDTO;
        }

        /**
         * 创建一个简单的builder
         *
         * @param vehicleName
         * @param commandType
         * @param <T>
         * @return
         */
        public static <T extends PduBaseCommand> CommandBuilder<T> builder(String vehicleName, PduCommandTypeEnum commandType) {
            try {
                T data = (T) commandType.getCommandData().getDeclaredConstructor().newInstance();
                MqttRequestDTO<T> mqttRequestDTO = new MqttRequestDTO<>();
                MqttRequestHeader mqttRequestHeader = new MqttRequestHeader();
                mqttRequestHeader.setRequestId(RequestIdUtils.getRequestId());
                mqttRequestHeader.setRequestTime(System.currentTimeMillis());
                mqttRequestHeader.setReceiveName(vehicleName);
                mqttRequestHeader.setNeedResponse(true);
                mqttRequestHeader.setMessageType(commandType.getCommandType());
                mqttRequestDTO.setHeader(mqttRequestHeader);
                mqttRequestDTO.setData(data);

                CommandBuilder<T> commandBuilder = new CommandBuilder();
                commandBuilder.setMqttRequestDTO(mqttRequestDTO);

                return commandBuilder;
            } catch (Exception e) {
                throw new AppException("创建指令失败", e);
            }
        }
    }


}
