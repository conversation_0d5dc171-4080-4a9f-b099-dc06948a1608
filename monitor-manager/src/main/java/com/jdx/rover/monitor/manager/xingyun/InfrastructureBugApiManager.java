package com.jdx.rover.monitor.manager.xingyun;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.infrastructure.api.domain.entity.dto.xingyun.BugDTO;
import com.jdx.rover.infrastructure.api.domain.entity.dto.xingyun.CreateBugDTO;
import com.jdx.rover.infrastructure.api.domain.entity.vo.xingyun.CreateBugWithAttachmentVO;
import com.jdx.rover.infrastructure.jsf.service.InfraXingYunJsfService;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@NoArgsConstructor
public class InfrastructureBugApiManager {

    @Autowired
    private InfraXingYunJsfService infraXingYunJsfService;

    /**
     * 创建缺陷
     * @param createBugVO
     * @return
     */
    public CreateBugDTO createBug(CreateBugWithAttachmentVO createBugVO) {
        CreateBugDTO result = null;
        try {
            log.info("调用基础服务创建缺陷: {}", createBugVO);
            HttpResult<CreateBugDTO> httpResult = infraXingYunJsfService.createBug(createBugVO);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        }catch (Exception e) {
            log.error("调用基础服务创建缺陷失败,{}", e.toString());
        }
        log.info("调用基础服务创建缺陷完成,result:{}", JsonUtils.writeValueAsString(result));
        return result;
    }

    /**
     * 查询缺陷
     */
    public BugDTO queryBug(String bugCode) {
        log.info("调用基础服务查询缺陷,bugCode:{}", bugCode);
        BugDTO result = null;
        try {
            HttpResult<BugDTO> httpResult = infraXingYunJsfService.queryBugByCode(bugCode);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            } else {
                log.error("调用基础服务 queryBug接口失败, code:{}, message:{}", httpResult.getCode(), httpResult.getCode());
            }
        } catch (Exception e) {
            log.error("调用基础服务 queryBug接口异常 {}.", e.getMessage());
        }
        return result;
    }

}
