/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.manager.notice;

import com.jdx.rover.infrastructure.api.domain.entity.bo.notice.jdme.JdMeNoticeMessage;
import com.jdx.rover.infrastructure.jsf.service.InfraNoticeJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 京me消息推送
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class JdMeNoticeManager {

    private final InfraNoticeJsfService infraNoticeJsfService;

    /**
     * 发送消息
     * @param title 标题
     * @param content 内容
     * @param erps 接收人
     */
    public void sendNotice(String title, String content, String erps) {
        try {
            JdMeNoticeMessage jdMeNoticeMessage = new JdMeNoticeMessage();
            jdMeNoticeMessage.setTitle(title);
            jdMeNoticeMessage.setContent(content + "，请及时处理！");
            jdMeNoticeMessage.setErps(Arrays.asList(erps.split(",")));
            infraNoticeJsfService.sendJdMeNotice(jdMeNoticeMessage);
        } catch (Exception e) {
            log.error("咚咚消息[{}]发送失败！",content,e);
        }
    }
}
