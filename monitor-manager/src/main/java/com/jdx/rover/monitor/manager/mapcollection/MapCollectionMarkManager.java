/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.mapcollection;

import com.jdx.rover.monitor.vo.mapcollection.MarkCreateVO;
import com.jdx.rover.monitor.vo.mapcollection.MarkUpdateVO;
import com.jdx.rover.monitor.base.BaseModel;
import com.jdx.rover.monitor.domain.jsonhandler.AttachmentListTypeHandler;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionMark;
import com.jdx.rover.monitor.repository.mapper.mapcollection.MapCollectionMarkMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/10 16:00
 * @description 勘查标记表Manager
 */
@Service
@RequiredArgsConstructor
public class MapCollectionMarkManager extends BaseManager<MapCollectionMarkMapper, MapCollectionMark> {

    /**
     * MapCollectionMarkMapper
     */
    private final MapCollectionMarkMapper mapCollectionMarkMapper;

    /**
     * 创建标记
     *
     * @param markCreateVO markCreateVO
     * @return markId
     */
    public Integer createMark(MarkCreateVO markCreateVO) {
        MapCollectionMark mapCollectionMark = new MapCollectionMark();
        mapCollectionMark.setLatitude(markCreateVO.getLatitude());
        mapCollectionMark.setLongitude(markCreateVO.getLongitude());
        mapCollectionMark.setAddressName(markCreateVO.getAddressName());
        mapCollectionMark.setMarkType(markCreateVO.getMarkType());
        mapCollectionMarkMapper.insert(mapCollectionMark);
        return mapCollectionMark.getId();
    }

    /**
     * 更新标记
     *
     * @param markUpdateVO markUpdateVO
     * @return update result
     */
    public boolean updateMark(MarkUpdateVO markUpdateVO) {
        return lambdaUpdate().eq(BaseModel::getId, markUpdateVO.getMarkId())
            .set(MapCollectionMark::getLatitude, markUpdateVO.getLatitude())
            .set(MapCollectionMark::getLongitude, markUpdateVO.getLongitude())
            .set(MapCollectionMark::getAddressName, markUpdateVO.getAddressName())
            .set(MapCollectionMark::getMarkType, markUpdateVO.getMarkType())
            .set(MapCollectionMark::getRemark, markUpdateVO.getRemark())
            .set(MapCollectionMark::getAttachmentList, markUpdateVO.getAttachmentList(), "typeHandler=" + AttachmentListTypeHandler.class.getName())
            .update();
    }

    /**
     * 删除标记
     *
     * @param markId markId
     */
    public void deleteMark(Integer markId) {
        lambdaUpdate().eq(BaseModel::getId, markId).remove();
    }
}
