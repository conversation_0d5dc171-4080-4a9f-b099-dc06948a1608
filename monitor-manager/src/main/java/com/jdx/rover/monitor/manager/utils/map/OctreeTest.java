/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.utils.map;

import lombok.extern.slf4j.Slf4j;

/**
 * 解压缩PCB文件
 *
 * <AUTHOR>
 */
@Slf4j
public class OctreeTest {

    public native boolean OctreeCompressFile(String path, String output);

    public native boolean OctreeUncompressFile(String path, String output, float leafSize);

}
