package com.jdx.rover.monitor.manager.mobile;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.dto.vehicle.mobile.VehicleRequireDTO;
import com.jdx.rover.metadata.api.domain.dto.vehicle.mobile.VehicleStopDTO;
import com.jdx.rover.metadata.api.domain.vo.vehicle.mobile.VehicleRequireVO;
import com.jdx.rover.metadata.api.domain.vo.vehicle.mobile.VehicleStopVO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.repository.feign.metadata.MetadataVehicleMobileApiClient;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: MetadataVehicleMobileApiManager
 * @author: wangguotai
 * @create: 2024-07-16 17:37
 **/
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataVehicleMobileApiManager {

    private final MetadataVehicleMobileApiClient metadataVehicleMobileApiClient;

    /**
     * 获取车辆是否存在影响运营维修
     *
     * @param vehicleRequireVO vehicleRequireVO
     * @return List<VehicleRequireDTO>
     */
    public List<VehicleRequireDTO> getRequire(VehicleRequireVO vehicleRequireVO) {
        try {
            log.info("MetadataVehicleMobileApiClient call getRequire request:[{}].", vehicleRequireVO);
            HttpResult<List<VehicleRequireDTO>> httpResult = metadataVehicleMobileApiClient.getRequire(vehicleRequireVO);
            log.info("MetadataVehicleMobileApiClient call getRequire response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataVehicleMobileApiClient call getRequire exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取车辆绑定停靠点交集（包含跨站停靠点）
     *
     * @param vehicleStopVO vehicleStopVO
     * @return List<VehicleStopDTO>
     */
    public List<VehicleStopDTO> getCommonStop(VehicleStopVO vehicleStopVO) {
        try {
            log.info("MetadataVehicleMobileApiClient call getCommonStop request:[{}].", vehicleStopVO);
            HttpResult<List<VehicleStopDTO>> httpResult = metadataVehicleMobileApiClient.getCommonStop(vehicleStopVO);
            log.info("MetadataVehicleMobileApiClient call getCommonStop response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataVehicleMobileApiClient call getCommonStop exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取车辆是否存在影响运营维修
     *
     * @param vehicleName vehicleName
     * @return boolean
     */
    public boolean getRequire(String vehicleName) {
        VehicleRequireVO requireVO = new VehicleRequireVO();
        List<String> vehicleNameList = new ArrayList<>();
        vehicleNameList.add(vehicleName);
        requireVO.setVehicleNameList(vehicleNameList);
        List<VehicleRequireDTO> requireDTOList = getRequire(requireVO);
        if (CollectionUtils.isEmpty(requireDTOList)) {
          return false;
        }
        return requireDTOList.get(0).getRequired();
    }

    /**
     * 获取车辆是否存在影响运营维修
     * @param vehicleNameList vehicleNameList
     * @return Map<String, VehicleRequireDTO>
     */
    public Map<String, VehicleRequireDTO> listRequireMap(List<String> vehicleNameList) {
        VehicleRequireVO vehicleRequireVO = new VehicleRequireVO();
        vehicleRequireVO.setVehicleNameList(vehicleNameList);
        List<VehicleRequireDTO> vehicleRequireDTOS = getRequire(vehicleRequireVO);
        Map<String, VehicleRequireDTO> vehicleRequireDTOMap = new HashMap<>();
        vehicleRequireDTOS.forEach(vehicleRequireDTO -> vehicleRequireDTOMap.put(vehicleRequireDTO.getVehicleName(), vehicleRequireDTO));
        return vehicleRequireDTOMap;
    }
}