package com.jdx.rover.monitor.manager.vehicle;

import cn.hutool.core.date.DateUtil;
import com.amazonaws.HttpMethod;
import com.google.common.base.Optional;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitBasicInfoDTO;
import com.jdx.rover.monitor.dto.accident.MonitorAccidentAttachmentDTO;
import com.jdx.rover.monitor.dto.vehicle.*;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.IssueCacheEntity;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttMessageTypeEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.video.VideoDirectionEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.abnormal.GuardianVehicleAbnormalManager;
import com.jdx.rover.monitor.manager.accident.AccidentAttachmentManager;
import com.jdx.rover.monitor.manager.accident.AccidentDetailManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.manager.config.RoverVideoProperties;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.manager.operation.OperationRecordLogManager;
import com.jdx.rover.monitor.manager.video.VideoManager;
import com.jdx.rover.monitor.po.*;
import com.jdx.rover.monitor.repository.redis.IssueCacheRepository;
import com.jdx.rover.monitor.repository.redis.NoSignalIntersectionRepository;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 单车页manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SingleVehicleManager {
  @Autowired
  private IssueCacheRepository issueCacheRepository;

  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;

  @Autowired
  private OperationRecordLogManager operationRecordLogManager;

  @Autowired
  private GuardianVehicleAbnormalManager guardianVehicleAbnormalManager;

  @Autowired
  private CockpitStatusRepository cockpitStatusRepository;
  @Autowired
  private CockpitTeamManager cockpitTeamManager;
  @Autowired
  private NoSignalIntersectionRepository noSignalIntersectionRepository;

  @Autowired
  private AccidentManager accidentManager;
  @Autowired
  private AccidentDetailManager accidentDetailManager;
  @Autowired
  private MqttManager mqttManager;
  @Value("${spring.profiles.active}")
  private String profileActive;

  @Autowired
  private AccidentAttachmentManager accidentAttachmentManager;

  @Autowired
  private VideoManager videoManager;

  @Autowired
  private RoverVideoProperties roverVideoProperties;

  @Autowired
  private S3Properties s3Properties;

  /**
   * 推送工单和接管消息
   *
   * @param vehicleName
   */
  public void pushSingleVehicleTakeOverAndIssue(String vehicleName) {
    WsResult<SingleVehicleTakeOverAndIssueDTO> wsResult = this.getTakeOverAndIssue(vehicleName);
    this.pushSingleVehiclePageData(vehicleName, wsResult);
  }

  /**
   * 推送操作记录消息
   *
   * @param vehicleName
   */
  public void pushSingleVehicleOperation(String vehicleName) {
    WsResult<VehicleNameAndDataListDTO<SingleVehicleOperationDTO>> wsResult = this.listSingleVehicleOperation(vehicleName);
    this.pushSingleVehiclePageData(vehicleName, wsResult);
  }

  /**
   * 推送事故消息
   *
   * @param vehicleName
   */
  public void pushSingleVehicleAccident(String vehicleName) {
    WsResult<VehicleNameAndDataListDTO<SingleVehicleAccidentDTO>> wsResult = this.listSingleVehicleAccident(vehicleName);
    this.pushSingleVehiclePageData(vehicleName, wsResult);
  }

  /**
   * 推送调度消息
   *
   * @param vehicleName
   */
  public void pushPncTaskAndTrafficLight(String vehicleName) {
    WsResult<SingleVehiclePncTaskAndTrafficLightDTO> wsResult = this.listPncTaskAndTrafficLight(vehicleName);
    this.pushSingleVehiclePageData(vehicleName, wsResult);
  }

  /**
   * 通过车辆名称获取单车页调度信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public WsResult<SingleVehiclePncTaskAndTrafficLightDTO> listPncTaskAndTrafficLight(String vehicleName) {
    String redisKey = RedisKeyEnum.TRAFFIC_LIGHT_SET_VEHICLE.getValue() + vehicleName;
    SingleVehiclePncTaskAndTrafficLightDTO dto = RedissonUtils.getObject(redisKey);
    WsResult<SingleVehiclePncTaskAndTrafficLightDTO> wsResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_TRAFFIC_LIGHT.getValue(), dto);
    return wsResult;
  }

  /**
   * 推送单车页数据
   *
   * @param vehicleName
   * @param object
   */
  public long pushSingleVehiclePageData(String vehicleName, Object object) {
    String topicName = RedisTopicEnum.SINGLE_VEHICLE_PREFIX.getValue() + vehicleName;
    RTopic topic = RedissonUtils.getRTopic(topicName);
    String jsonStr = JsonUtils.writeValueAsString(object);
    long publishCnt = topic.publish(jsonStr);
    if (publishCnt == 0) {
        log.info("发送[{}]条[{}]单车页数据!{}", publishCnt, vehicleName, jsonStr);
      if (topic.countSubscribers() <= 0) {
        // TODO 最好有锁
        RScoredSortedSet<String> scoredSortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(RedisKeyEnum.SORTED_SET_PUSH_SINGLE_VEHICLE.getValue());
        scoredSortedSet.remove(vehicleName);
        log.info("没有订阅者,忽略!vehicleName={},topicName={}", vehicleName, topicName);
        return 0L;
      }
    }
    // 推送机器人
    String robotTopicName = RedisTopicEnum.MAP_ROBOT_CONGESTION_PREFIX.getValue() + vehicleName;
    RedissonUtils.getRTopic(robotTopicName).publish(jsonStr);
    return publishCnt;
  }


  /**
   * 获取工单信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public WsResult<SingleVehicleTakeOverAndIssueDTO> getTakeOverAndIssue(String vehicleName) {
    SingleVehicleTakeOverAndIssueDTO singleVehicleTakeOverAndIssueDTO = getIssue(vehicleName);
    singleVehicleTakeOverAndIssueDTO.setVehicleName(vehicleName);
    Optional<VehicleTakeOverEntity> op = vehicleTakeOverRepository.getByKey(vehicleName);
    if (op.isPresent()) {
      singleVehicleTakeOverAndIssueDTO.setTakeOverUser(op.get().getUserName());
      singleVehicleTakeOverAndIssueDTO.setTakeOverSource(op.get().getCommandSource());
      singleVehicleTakeOverAndIssueDTO.setTakeOverStatus(op.get().getOperationStatus());
    }
    WsResult<SingleVehicleTakeOverAndIssueDTO> wsResult =
        WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_TAKEOVER_ISSUE.getValue(), singleVehicleTakeOverAndIssueDTO);
    return wsResult;
  }

  /**
   * 获取工单信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  private SingleVehicleTakeOverAndIssueDTO getIssue(String vehicleName) {
    IssueCacheEntity issueCacheEntity = issueCacheRepository.getByKey(vehicleName);
    SingleVehicleTakeOverAndIssueDTO singleVehicleTakeOverAndIssueDTO = new SingleVehicleTakeOverAndIssueDTO();
    if (issueCacheEntity != null) {
      singleVehicleTakeOverAndIssueDTO.setIssueNo(issueCacheEntity.getIssueNo());
      singleVehicleTakeOverAndIssueDTO.setIssueState(issueCacheEntity.getIssueState());
    }
    return singleVehicleTakeOverAndIssueDTO;
  }

  /**
   * 通过车辆名称获取单车页操作记录信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public WsResult<VehicleNameAndDataListDTO<SingleVehicleOperationDTO>> listSingleVehicleOperation(String vehicleName) {
    List<MonitorUserOperationLog> monitorUserOperationLogList = operationRecordLogManager.listTodayOperationLog(vehicleName);
    List<SingleVehicleOperationDTO> operationList = new ArrayList<>(monitorUserOperationLogList.size());
    if (CollectionUtils.isNotEmpty(monitorUserOperationLogList)) {
      for (MonitorUserOperationLog tmp : monitorUserOperationLogList) {
        SingleVehicleOperationDTO singleVehicleOperationDTO = new SingleVehicleOperationDTO();
        singleVehicleOperationDTO.setOperateTime(tmp.getOperateTimestamp());
        singleVehicleOperationDTO.setOperateUserName(tmp.getUserName());
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(tmp.getOperationMessage());
        if (StringUtils.isNotBlank(tmp.getState())) {
          stringBuilder.append(":").append(tmp.getState());
        }
        if (StringUtils.isNotBlank(tmp.getFailMessage())) {
          stringBuilder.append(":").append(tmp.getFailMessage());
        }
        singleVehicleOperationDTO.setOperateInfo(stringBuilder.toString());
        operationList.add(singleVehicleOperationDTO);
      }
    }

    VehicleNameAndDataListDTO<SingleVehicleOperationDTO> dto = new VehicleNameAndDataListDTO<>(vehicleName, operationList);
    WsResult<VehicleNameAndDataListDTO<SingleVehicleOperationDTO>> wsResult =
        WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_OPERATION.getValue(), dto);
    return wsResult;
  }

  /**
   * 通过车辆名称获取单车页事故信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public WsResult<VehicleNameAndDataListDTO<SingleVehicleAccidentDTO>> listSingleVehicleAccident(String vehicleName) {
    List<Accident> accidentList = accidentManager.listTodayWaitAccident(vehicleName);
    List<SingleVehicleAccidentDTO> singleVehicleAccidentDTOList = new ArrayList<>(accidentList.size());
    if (CollectionUtils.isNotEmpty(accidentList)) {
      for (Accident item : accidentList) {
        SingleVehicleAccidentDTO singleVehicleAccidentDTO = new SingleVehicleAccidentDTO();
        singleVehicleAccidentDTO.setAccidentSource(item.getAccidentSource());
        singleVehicleAccidentDTO.setAccidentNo(item.getAccidentNo());
        singleVehicleAccidentDTO.setAccidentDesc(item.getAccidentDesc());
        singleVehicleAccidentDTO.setAccidentTime(item.getAccidentReportTime());
        singleVehicleAccidentDTO.setVehicleName(item.getVehicleName());
        singleVehicleAccidentDTO.setJiraNo(item.getBugCode());
        singleVehicleAccidentDTO.setShadowEventId(item.getShadowEventId());
        singleVehicleAccidentDTO.setCreateUser(item.getCreateUser());
        singleVehicleAccidentDTO.setCreateTime(item.getCreateTime());
        List<MonitorAccidentAttachmentDTO> snapshotAttachmentUrlList = new ArrayList<>();
        List<AccidentAttachment> accidentAttachmentList = accidentAttachmentManager.selectOrderedListByAccidentNoAndSource(item.getAccidentNo(), AccidentAttachmentSourceEnum.VIDEO_SNAPSHOT.getValue());
        for (AccidentAttachment accidentAttachment : accidentAttachmentList) {
          if (!Objects.equals(accidentAttachment.getSource(), AccidentAttachmentSourceEnum.VIDEO_SNAPSHOT.getValue())) {
            continue;
          }
          MonitorAccidentAttachmentDTO monitorAccidentAttachmentDTO = new MonitorAccidentAttachmentDTO();
          monitorAccidentAttachmentDTO.setFileKey(accidentAttachment.getFileKey());
          monitorAccidentAttachmentDTO.setType(accidentAttachment.getType());
          final Date expiration = DateUtil.offsetHour(new Date(), 24);
          String videoUrl = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), accidentAttachment.getBucket(), accidentAttachment.getFileKey(), HttpMethod.GET, expiration).toString();
          monitorAccidentAttachmentDTO.setUrl(videoUrl);
          monitorAccidentAttachmentDTO.setName(VideoDirectionEnum.getNameByValue(accidentAttachment.getLocation()));
          snapshotAttachmentUrlList.add(monitorAccidentAttachmentDTO);
        }
        singleVehicleAccidentDTO.setSnapshotAttachmentUrlList(snapshotAttachmentUrlList);

        //新增事故推送时携带事故等级和事故类型属性,事故排查卡片中需要回显和控制是否以文本展示
        singleVehicleAccidentDTO.setBugCode(item.getBugCode());
        singleVehicleAccidentDTO.setVideoCaptureStatus(item.getVideoCaptureStatus());
        AccidentDetail accidentDetail = accidentDetailManager.selectByAccidentNo(item.getAccidentNo());
        if(null != accidentDetail) {
          singleVehicleAccidentDTO.setAccidentType(accidentDetail.getTechnicalSupportAccidentType());
          singleVehicleAccidentDTO.setAccidentLevel(accidentDetail.getTechnicalSupportAccidentLevel());
        }

        singleVehicleAccidentDTOList.add(singleVehicleAccidentDTO);
      }
    }
    VehicleNameAndDataListDTO<SingleVehicleAccidentDTO> dto = new VehicleNameAndDataListDTO<>(vehicleName, singleVehicleAccidentDTOList);
    WsResult<VehicleNameAndDataListDTO<SingleVehicleAccidentDTO>> wsResult =
            WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_ACCIDENT.getValue(), dto);
    return wsResult;
  }

  /**
   * 推送异常增量消息
   *
   * @param vehicleName
   */
  public void pushSingleVehicleException(String vehicleName) {
    WsResult<VehicleNameAndDataListDTO<SingleVehicleExceptionDTO>> wsResult = this.listSingleVehicleException(vehicleName);
    this.pushSingleVehiclePageData(vehicleName, wsResult);
  }

  /**
   * 通过车辆名称获取单车页异常信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public WsResult<VehicleNameAndDataListDTO<SingleVehicleExceptionDTO>> listSingleVehicleException(String vehicleName) {
    List<SingleVehicleExceptionDTO> list = getSingleVehicleExceptionList(vehicleName);
    VehicleNameAndDataListDTO<SingleVehicleExceptionDTO> dto = new VehicleNameAndDataListDTO<>(vehicleName, list);
    WsResult<VehicleNameAndDataListDTO<SingleVehicleExceptionDTO>> wsResult =
        WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_EXCEPTION.getValue(), dto);
    return wsResult;
  }

  public List<SingleVehicleExceptionDTO> getSingleVehicleExceptionList(String vehicleName) {
    List<GuardianVehicleAbnormal> abnormalList = guardianVehicleAbnormalManager.listTodayAbnormal(vehicleName);
    List<SingleVehicleExceptionDTO> list = new ArrayList<>();
    for (GuardianVehicleAbnormal tmp : abnormalList) {
      SingleVehicleExceptionDTO singleVehicleExceptionDTO = new SingleVehicleExceptionDTO();
      singleVehicleExceptionDTO.setReportTime(tmp.getStartTime());
      singleVehicleExceptionDTO.setErrorLevel(tmp.getErrorLevel());
      singleVehicleExceptionDTO.setErrorCode(tmp.getErrorCode());
      singleVehicleExceptionDTO.setModuleName(tmp.getModuleName());
      singleVehicleExceptionDTO.setErrorMessage(tmp.getErrorMessage());
      singleVehicleExceptionDTO.setTranslateMessage(tmp.getTranslateMessage());
      list.add(singleVehicleExceptionDTO);
    }
    return list;
  }

  /**
   * 推送无信号路口消息
   *
   * @param vehicleName
   */
  public void pushNoSignalIntersectionDTO(String vehicleName) {
    SingleVehicleNoSignalIntersectionDTO dto = noSignalIntersectionRepository.get(vehicleName);
    this.pushNoSignalIntersectionDTO(vehicleName, dto);
  }

  /**
   * 推送无信号路口消息
   *
   * @param vehicleName
   */
  public WsResult<SingleVehicleNoSignalIntersectionDTO> buildNoSignalIntersection(String vehicleName) {
    SingleVehicleNoSignalIntersectionDTO dto = noSignalIntersectionRepository.get(vehicleName);
    WsResult<SingleVehicleNoSignalIntersectionDTO> wsResult = WsResult.success(
            WebsocketEventTypeEnum.SINGLE_VEHICLE_NO_SIGNAL_INTERSECTION.getValue(), dto);
    return wsResult;
  }

  /**
   * 推送无信号路口消息
   *
   * @param vehicleName
   * @param dto
   */
  public void pushNoSignalIntersectionDTO(String vehicleName, SingleVehicleNoSignalIntersectionDTO dto) {
    WsResult<SingleVehicleNoSignalIntersectionDTO> wsResult = WsResult.success(
            WebsocketEventTypeEnum.SINGLE_VEHICLE_NO_SIGNAL_INTERSECTION.getValue(), dto);
    this.pushSingleVehiclePageData(vehicleName, wsResult);
  }

  /**
   * 推送车辆接管状态Mqtt消息
   *
   * @param vehicleName
   */
    public void sendVehicleControlMqttMsg(String operateType, Date occureTime, String vehicleName) {
      Optional<VehicleTakeOverEntity> op = vehicleTakeOverRepository.getByKey(vehicleName);
      VehicleTakeOverDTO takeOverDto = new VehicleTakeOverDTO();
      takeOverDto.setVehicleName(vehicleName);
      takeOverDto.setOperateType(operateType);
      takeOverDto.setReportTime(occureTime);
      if (op.isPresent()) {
        takeOverDto.setTakeOverUserName(op.get().getUserName());
        takeOverDto.setTakeOverSource(op.get().getCommandSource());
        takeOverDto.setTakeOverStatus(op.get().getOperationStatus());
        takeOverDto.setCockpitNumber(op.get().getCockpitNumber());
      }
      // 查询当前在线座舱用户
      List<CockpitBasicInfoDTO> cockpitBasicInfoList = cockpitTeamManager.getAllCockpitBasicInfoList();
      if (CollectionUtils.isEmpty(cockpitBasicInfoList)) {
        return;
      }
      List<String> cockpitNumerList = cockpitBasicInfoList.stream().map(cockpitBasicInfo -> cockpitBasicInfo.getCockpitNumber()).collect(Collectors.toList());
      List<CockpitStatusDO> cockpitStatusDOList = cockpitStatusRepository.list(cockpitNumerList);
      cockpitStatusDOList.stream().filter(cockpit ->
              StringUtils.isNotBlank(cockpit.getCockpitUserName())).forEach(cockpit -> {
        String topic = String.format(MqttTopicEnum.USER_INFO.getTopic(), profileActive) + cockpit.getCockpitUserName();
        log.info("通知用户{}车辆{}执行命令{}后接管状态{}", cockpit.getCockpitUserName(), vehicleName, operateType, JsonUtils.writeValueAsString(takeOverDto));
        mqttManager.sendQos1NoRetainNoResponse(vehicleName, topic, MqttMessageTypeEnum.VEHICLE_TAKE_OVER_INFO.name(), takeOverDto);
      });
    }
}