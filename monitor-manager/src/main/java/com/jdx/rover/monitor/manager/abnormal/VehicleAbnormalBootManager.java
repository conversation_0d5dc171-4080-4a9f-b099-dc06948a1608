/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager.abnormal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.VehicleAbnormalBoot;
import com.jdx.rover.monitor.repository.mapper.VehicleAbnormalBootMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆异常启动manager类
 *
 * <AUTHOR>
 */
@Service
public class VehicleAbnormalBootManager extends BaseManager<VehicleAbnormalBootMapper, VehicleAbnormalBoot> {

  /**
   * 查询车辆一次的启动列表数据
   *
   * @param
   */
  public List<VehicleAbnormalBoot> listAbnormalWithBootUuid(String bootUuid, Integer bootId) {
    LambdaQueryWrapper<VehicleAbnormalBoot> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(VehicleAbnormalBoot::getBootUuid, bootUuid);
    wrapper.eq(VehicleAbnormalBoot::getBootId, bootId);
    List<VehicleAbnormalBoot> result = this.list(wrapper);
    return result;
  }
}