package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.vehicle.VehicleConnectDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

/**
 * 连接变化事件服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleConnectManager {
  /**
   * 推送多车页连接变化数据
   *
   * @param vehicleName
   */
  public long pushChangeConnect(String vehicleName, String systemState) {
    VehicleConnectDTO vehicleConnectDTO = new VehicleConnectDTO();
    vehicleConnectDTO.setVehicleName(vehicleName);
    vehicleConnectDTO.setSystemState(systemState);
    String topicName = RedisTopicEnum.ALARM_EVENT_PREFIX.getValue();
    RTopic topic = RedissonUtils.getRTopic(topicName);
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.CHANGE_VEHICLE_CONNECT.getValue(), vehicleConnectDTO);
    String jsonStr = JsonUtils.writeValueAsString(wsResult);
    long publishCnt = topic.publish(jsonStr);
    log.info("发送[{}]条车辆连接状态[{}],连接状态消息={}", publishCnt, vehicleName, jsonStr);
    return publishCnt;
  }
}
