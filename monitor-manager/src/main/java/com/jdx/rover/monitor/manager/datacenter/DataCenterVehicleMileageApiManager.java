package com.jdx.rover.monitor.manager.datacenter;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.warehouse.mileage.HistoryMileageQueryJsfDTO;
import com.jdx.rover.datacenter.domain.vo.warehouse.mileage.HistoryMileageQueryJsfVO;
import com.jdx.rover.datacenter.jsf.service.warehouse.VehicleMileageJsfService;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@NoArgsConstructor
public class DataCenterVehicleMileageApiManager {

    @Autowired
    private VehicleMileageJsfService vehicleMileageJsfService;


    /**
     * 获取历史里程信息
     * @param historyMileageQueryVO
     * @return
     */
    public List<HistoryMileageQueryJsfDTO> historyMileageQuery(HistoryMileageQueryJsfVO historyMileageQueryVO) {
        List<HistoryMileageQueryJsfDTO> result = new ArrayList<>();
        try {
            HttpResult<List<HistoryMileageQueryJsfDTO>> httpResult = vehicleMileageJsfService.historyMileageQuery(historyMileageQueryVO);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        }catch (Exception e) {
            log.error("调用warehouse查询历史里程信息失败,{}", e.toString());
        }
        log.info("调用warehouse查询历史里程信息成功,result:{}", JsonUtils.writeValueAsString(result));
        return result;
    }
}
