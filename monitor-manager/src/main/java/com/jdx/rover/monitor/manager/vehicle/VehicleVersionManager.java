package com.jdx.rover.monitor.manager.vehicle;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.jd.rover.ota.api.domain.dto.AppIssueResultDto;
import com.jd.rover.ota.api.domain.dto.ApplicationIssueInfoDto;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.repository.redis.VehicleVersionRepository;
import com.jdx.rover.ota.jsf.OtaApplicationIssueInfoJsfService;
import com.jdx.rover.server.api.domain.dto.guardian.ModuleVersionInfoDTO;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import com.jdx.rover.server.api.jsf.service.vehicle.ServerVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 车辆版本manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleVersionManager {

  @Autowired
  private ServerVehicleService serverVehicleJsfService;

  @Autowired
  private OtaApplicationIssueInfoJsfService otaApplicationIssueInfoJsfService;

  @Autowired
  private VehicleVersionRepository vehicleVersionRepository;

  /**
   * 获取车辆版本
   * @param vehicleName
   */
  public Map<String, String> getVehicleVersion(String vehicleName) {
    Map<String, String> result = Maps.newLinkedHashMap();
    HttpResult httpResult = serverVehicleJsfService.getVehicleOtaVersionInfo(vehicleName);
    if (HttpResult.isSuccess(httpResult) && httpResult.getData() != null) {
      Map<String, ModuleVersionInfoDTO> mapResult = (Map<String, ModuleVersionInfoDTO>) httpResult.getData();
      for (OtaMoudleEnum otaMoudleEnum :OtaMoudleEnum.values()) {
        String versionName = otaMoudleEnum.getName();
        result.put(versionName, Optional.ofNullable(mapResult.get(versionName)).map(version -> version.getCurVersion()).
                orElse(""));
      }
    }
    return result;
  }

  /**
   * 获取车辆云端版本
   * @param vehicleName
   */
  public Map<String, String> getVehicleCloudVersion(String vehicleName) {
    Map<String, String> result = Maps.newLinkedHashMap();
    try {
      ApplicationIssueInfoDto issueInfoDto = vehicleVersionRepository.getCloudVersion(vehicleName);
      if (issueInfoDto == null) {
        HttpResult<com.jdx.rover.ota.domain.dto.ApplicationIssueInfoDto> httpResult = otaApplicationIssueInfoJsfService.getApplicationIssueInfoOfVehicle(vehicleName);
        log.info("获取车辆OTA云端版本{}", JsonUtils.writeValueAsString(httpResult));
        if (HttpResult.isSuccess(httpResult) && httpResult.getData() != null) {
          com.jdx.rover.ota.domain.dto.ApplicationIssueInfoDto issueInfoJsfDto = httpResult.getData();
          issueInfoDto = new ApplicationIssueInfoDto();
          issueInfoDto.setVehicleName(issueInfoJsfDto.getVehicleName());
          issueInfoDto.setSerialNo(issueInfoJsfDto.getSerialNo());
          issueInfoDto.setTimestamp(issueInfoJsfDto.getTimestamp());
          issueInfoDto.setVersionInfo(issueInfoJsfDto.getVersionInfo().stream().map(versionInfo -> {
            AppIssueResultDto appIssueResultDto = new AppIssueResultDto();
            appIssueResultDto.setVersion(versionInfo.getVersion());
            appIssueResultDto.setAppName(versionInfo.getAppName());
            appIssueResultDto.setFileName(versionInfo.getFileName());
            appIssueResultDto.setUrl(versionInfo.getUrl());
            appIssueResultDto.setIssueTaskNumber(versionInfo.getIssueTaskNumber());
            appIssueResultDto.setUpgradeType(versionInfo.getUpgradeType());
            return appIssueResultDto;
          }).collect(Collectors.toList()));
        }
      }
      if (issueInfoDto == null) {
        return result;
      }
      vehicleVersionRepository.set(issueInfoDto);
      List<AppIssueResultDto> cloudVersionList = issueInfoDto.getVersionInfo();
      if (CollectionUtil.isNotEmpty(cloudVersionList)) {
        return cloudVersionList.stream().filter(version -> version.getVersion() != null).
                collect(Collectors.toMap(AppIssueResultDto::getAppName, AppIssueResultDto::getVersion));
      }
    } catch (Exception e) {
    }
    return result;
  }
}
