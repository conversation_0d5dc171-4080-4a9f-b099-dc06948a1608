/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.datacollection;

import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionSceneRequirement;
import com.jdx.rover.monitor.repository.mapper.datacollection.DataCollectionSceneRequirementMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数采场景关联标签manager
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Service
@RequiredArgsConstructor
public class DataCollectionSceneRequirementManager extends BaseManager<DataCollectionSceneRequirementMapper, DataCollectionSceneRequirement> {

    /**
     * 根据需求ID获取关联的场景
     *
     * @param requirementId requirementId
     * @return List<DataCollectionSceneRequirement>
     */
    public List<DataCollectionSceneRequirement> getSceneByRequirementId(Integer requirementId) {
        return lambdaQuery()
            .eq(DataCollectionSceneRequirement::getRequirementId, requirementId)
            .eq(DataCollectionSceneRequirement::getLinked, true)
            .list();
    }

    /**
     * 根据场景ID获取关联的需求
     *
     * @param sceneId 场景ID
     * @return List<DataCollectionSceneRequirement>
     */
    public List<DataCollectionSceneRequirement> getRequirementBySceneId(Integer sceneId) {
        return lambdaQuery()
                .eq(DataCollectionSceneRequirement::getSceneId, sceneId)
                .list();
    }
}