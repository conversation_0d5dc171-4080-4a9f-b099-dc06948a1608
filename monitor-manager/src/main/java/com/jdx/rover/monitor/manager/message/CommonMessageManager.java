package com.jdx.rover.monitor.manager.message;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.message.MessageDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommonMessageManager {
  /**
   * 推送异常增量消息
   *
   * @param messageDTO
   */
  public long pushCommonMessage(MessageDTO messageDTO) {
    String vehicleName = messageDTO.getVehicleName();
    String topicName = RedisTopicEnum.ALARM_EVENT_PREFIX.getValue();
    RTopic topic = RedissonUtils.getRTopic(topicName);
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.COMMON_MESSAGE.getValue(), messageDTO);
    String jsonStr = JsonUtils.writeValueAsString(wsResult);
    long publishCnt = topic.publish(jsonStr);
    log.info("发送[{}]条公共消息[{}],内容={}", publishCnt, vehicleName, jsonStr);
    return publishCnt;
  }
}
