package com.jdx.rover.monitor.manager.ota;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.ota.jsf.OtaApplicationVersionJsfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class OtaApplicationVersionManager {

    @Autowired
    private OtaApplicationVersionJsfService otaApplicationVersionJsfService;

    public List<String> getRoverVersionList() {
        List<String> result = new ArrayList<>();
        try {
            HttpResult<List<String>> httpResult = otaApplicationVersionJsfService.applicationVersionList("rover");
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        }catch (Exception e) {
            log.error("调用OTA获取rover版本失败,{}", e.toString());
        }
        log.info("调用OTA获取rover版本完成,result:{}", JsonUtils.writeValueAsString(result));
        return result;
    }
}
