/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.issue;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jdx.rover.monitor.enums.AlarmEventErrorCodeEnum;
import com.jdx.rover.monitor.enums.issue.IssueAlarmTypeEnum;
import com.jdx.rover.monitor.po.IssueAlarmAttachment;
import com.jdx.rover.monitor.po.IssueAlarmRecord;
import com.jdx.rover.monitor.repository.mapper.IssueAlarmAttachmentMapper;
import com.jdx.rover.monitor.repository.mapper.IssueAlarmRecordMapper;
import com.jdx.rover.monitor.vo.MiniMonitorAlarmAttachmentAddVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IssueAlarmManager {

  @Autowired
  private IssueAlarmRecordMapper issueAlarmRecordMapper;

  @Autowired
  private IssueAlarmAttachmentMapper issueAlarmAttachmentMapper;

  /**
   * 通过工单号获取关联告警列表
   *
   * @param issueNo 工单号
   * @return list
   */
  public List<IssueAlarmRecord> getAlarmListByIssueNo(String issueNo, boolean isSelectd) {
    LambdaQueryWrapper<IssueAlarmRecord> wrapper = new LambdaQueryWrapper<>();
    wrapper.orderByDesc(IssueAlarmRecord::getId);
    wrapper.eq(IssueAlarmRecord::getIssueNo, issueNo);
    if (isSelectd) {
      wrapper.eq(IssueAlarmRecord::isSelected, isSelectd);
    }
    List<IssueAlarmRecord> issueAlarmList = issueAlarmRecordMapper.selectList(wrapper);
    return Optional.ofNullable(issueAlarmList).orElse(Lists.newArrayList());
  }

  /**
   * 通过工单号获取关联告警列表
   *
   * @param issueNo 工单号
   * @return list
   */
  public IssueAlarmRecord getAlarmByIssueNoAndAlarmType(String issueNo, String alarmType, boolean isSelectd) {
    LambdaQueryWrapper<IssueAlarmRecord> wrapper = new LambdaQueryWrapper<>();
    wrapper.orderByDesc(IssueAlarmRecord::getId);
    wrapper.eq(IssueAlarmRecord::getIssueNo, issueNo);
    wrapper.eq(IssueAlarmRecord::getAlarmType, alarmType);
    if (isSelectd) {
      wrapper.eq(IssueAlarmRecord::isSelected, isSelectd);
    }
    IssueAlarmRecord issueAlarm = issueAlarmRecordMapper.selectOne(wrapper);
    return issueAlarm;
  }

  public void updateSelectedVehicleAlarmRecord(IssueAlarmRecord issueAlarmRecord , List<Integer> alarmIdList) {
    LambdaQueryWrapper<IssueAlarmRecord> wrapper = new LambdaQueryWrapper<>();
    wrapper.in(IssueAlarmRecord:: getId, alarmIdList);
    issueAlarmRecordMapper.update(issueAlarmRecord, wrapper);
  }

  public void addOperateAlarmRecord(String title, String message, String vehicleName, List<MiniMonitorAlarmAttachmentAddVO> attachment, String issueNo) {
    IssueAlarmRecord vehicleAlarmRecord = new IssueAlarmRecord();
    vehicleAlarmRecord.setAlarmType(AlarmEventErrorCodeEnum.OPERATION_ALARM.getAlarmEventType());
    vehicleAlarmRecord.setAlarmCode(message);
    vehicleAlarmRecord.setAlarmNo(title);
    vehicleAlarmRecord.setSource(IssueAlarmTypeEnum.OPERATION_ALARM.getValue());
    vehicleAlarmRecord.setTimestamp(new Date());
    vehicleAlarmRecord.setVehicleName(vehicleName);
    vehicleAlarmRecord.setIssueNo(issueNo);
    vehicleAlarmRecord.setSelected(true);
    issueAlarmRecordMapper.insert(vehicleAlarmRecord);
    if (!CollectionUtils.isEmpty(attachment)) {
      for (MiniMonitorAlarmAttachmentAddVO attach : attachment) {
        IssueAlarmAttachment issueAlarmAttachment = new IssueAlarmAttachment();
        issueAlarmAttachment.setAlarmId(vehicleAlarmRecord.getId());
        issueAlarmAttachment.setIssueNo(issueNo);
        issueAlarmAttachment.setTimestamp(new Date());
        issueAlarmAttachment.setUrl(attach.getUrl());
        issueAlarmAttachmentMapper.insert(issueAlarmAttachment);
      }
    }
  }

  public void addVehicleAlarmRecord(String eventType, Date time, String vehicleName, String issueNo, boolean selected) {
    IssueAlarmRecord vehicleAlarmRecord = new IssueAlarmRecord();
    AlarmEventErrorCodeEnum errorCodeEnum = AlarmEventErrorCodeEnum.of(eventType);
    if (errorCodeEnum != null) {
      vehicleAlarmRecord.setAlarmType(errorCodeEnum.getAlarmEventType());
      vehicleAlarmRecord.setAlarmCode(errorCodeEnum.getErrorCode());
      vehicleAlarmRecord.setAlarmNo(errorCodeEnum.getErrorNo());
    } else {
      vehicleAlarmRecord.setAlarmType(eventType);
    }
    vehicleAlarmRecord.setSource(IssueAlarmTypeEnum.VEHICLE_ALARM.getValue());
    vehicleAlarmRecord.setTimestamp(time);
    vehicleAlarmRecord.setVehicleName(vehicleName);
    vehicleAlarmRecord.setIssueNo(issueNo);
    vehicleAlarmRecord.setSelected(selected);
    issueAlarmRecordMapper.insertIfAbsent(vehicleAlarmRecord);
  }

}
