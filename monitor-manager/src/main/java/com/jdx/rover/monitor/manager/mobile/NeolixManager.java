package com.jdx.rover.monitor.manager.mobile;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.infrastructure.jsf.domain.dto.ProxyHttpResponseDTO;
import com.jdx.rover.infrastructure.jsf.domain.vo.ProxyHttpRequestVO;
import com.jdx.rover.infrastructure.jsf.service.HttpProxyService;
import com.jdx.rover.monitor.dto.neolix.NeolixResultDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.vo.neolix.NeolixVehiclePowerOnOffVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 新石器相关模块
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NeolixManager {

    private final HttpProxyService httpProxyService;

    @Value("${neolix.baseurl:https://scapi.test.neolix.net}")
    private String neolixBaseUrl;

    /**
     * 开关机路径
     */
    private static final String POWER_ON_OFF = "/openapi-server/slvapi/powerOnOff";

    /**
     * 执行Neolix请求并返回响应数据。
     *
     * @param proxyHttpRequestVO 包含请求信息的VO对象。
     * @return 响应数据DTO对象。
     */
    public ProxyHttpResponseDTO executeNeolixRequest(ProxyHttpRequestVO proxyHttpRequestVO) {
        HttpResult<ProxyHttpResponseDTO> result = httpProxyService.executeNeolix(proxyHttpRequestVO);
        if (!HttpResult.isSuccess(result)) {
            log.error("[告警]公网接口调用响应码异常，请求信息：{},响应信息：{}", proxyHttpRequestVO, result);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }

        ProxyHttpResponseDTO data = result.getData();
        if (data.getStatusCode() != 200) {
            log.error("[告警]公网接口调用响应码异常，请求信息：{},响应信息：{}", proxyHttpRequestVO, data);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
        return data;
    }

    /**
     * 执行Neolix请求并将响应体转换为指定类型的对象。
     *
     * @param proxyHttpRequestVO 代理HTTP请求的参数对象。
     * @param typeReference      指定要将响应体转换为的类型。
     * @return 转换后的对象。
     */
    public <T> T executeNeolixRequest(ProxyHttpRequestVO proxyHttpRequestVO, TypeReference<T> typeReference) {
        return JsonUtils.readValue(executeNeolixRequest(proxyHttpRequestVO)
                .getBodyString(), typeReference);
    }

    /**
     * 远程控制指定车辆的电源开关状态。
     *
     * @param vehicleName 车辆的VIN码。
     * @param type        操作类型，1表示开启电源，0表示关闭电源。
     */
    public void remotePowerOnOff(String vehicleName, Integer type) {

        NeolixVehiclePowerOnOffVO neolixVehiclePowerOnOffVO = new NeolixVehiclePowerOnOffVO();
        neolixVehiclePowerOnOffVO.setVinId(vehicleName);
        neolixVehiclePowerOnOffVO.setHandleType(type);
        NeolixResultDTO<Object> result = executeNeolixRequest(ProxyHttpRequestVO.postJson(neolixBaseUrl + POWER_ON_OFF, JsonUtils.writeValueAsString(neolixVehiclePowerOnOffVO))
                , new TypeReference<NeolixResultDTO<Object>>() {
                });

        if (!result.isSuccess()) {
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), result.getMsg());
        }
    }
}
