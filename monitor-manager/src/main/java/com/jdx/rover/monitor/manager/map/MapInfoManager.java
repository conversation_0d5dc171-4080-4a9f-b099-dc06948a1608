/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager.map;

import cn.hutool.core.util.ObjectUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.map.api.domain.dto.GetLaneAndRoadTypeDTO;
import com.jdx.rover.map.api.domain.dto.MapInfoDTO;
import com.jdx.rover.map.api.domain.vo.GetLaneRoadTypeVO;
import com.jdx.rover.map.api.service.MapInfoJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 地图manager
 *
 * <AUTHOR>
 * @date 2024/11/22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MapInfoManager {

    /**
     * 地图服务
     */
    private final MapInfoJsfService mapInfoJsfService;

    /**
     * 根据位置获取lane类型和road类型
     *
     * @param
     */
    public GetLaneAndRoadTypeDTO getLaneAndRoadTypeByPosition(Double latitude, Double longitude) {
        GetLaneRoadTypeVO getLaneRoadTypeVo = new GetLaneRoadTypeVO();
        getLaneRoadTypeVo.setLatitude(latitude);
        getLaneRoadTypeVo.setLongitude(longitude);
        try {
            HttpResult<GetLaneAndRoadTypeDTO> httpResult = mapInfoJsfService.getLaneAndRoadTypeByPosition(getLaneRoadTypeVo);
            log.info("根据位置{}-{}获取lane类型和road类型结果{}", longitude, latitude, JsonUtils.writeValueAsString(httpResult));
            if (HttpResult.isSuccess(httpResult) && Objects.nonNull(httpResult.getData())) {
                return httpResult.getData();
            }
            return new GetLaneAndRoadTypeDTO();
        } catch (Exception e) {
            log.error("根据位置获取lane类型和road类型异常", e);
            return new GetLaneAndRoadTypeDTO();
        }
    }

    /**
     * 根据经纬度获取地图信息
     *
     * @param latitude latitude
     * @param longitude longitude
     * @return MapInfoDTO
     */
    public MapInfoDTO getMapByPosition(Double latitude, Double longitude) {
        if (Objects.isNull(latitude) || Objects.isNull(longitude)) {
            return new MapInfoDTO();
        }

        try {
            HttpResult<MapInfoDTO> result = mapInfoJsfService.getMapByPosition(longitude, latitude);
            if (HttpResult.isSuccess(result) && ObjectUtil.isNotNull(result.getData())) {
                return result.getData();
            }
        } catch (Exception e) {
            log.info("获取地图失败 - [{}, {}] - {}", latitude, longitude, e.getMessage(), e);
        }
        return new MapInfoDTO();
    }
}