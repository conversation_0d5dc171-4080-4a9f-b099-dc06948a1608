/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.user;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.monitor.dto.user.UserInfoMqttDTO;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.enums.mqtt.MqttMessageTypeEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;


/**
 * 驾驶舱团队服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class UserInfoMqttManager {
    private final CockpitStatusRepository cockpitStatusRepository;
    private final UserStatusRepository userStatusRepository;
    private final MqttManager mqttManager;

    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * 初始化推送用户信息,如果当天有过缓存变化,表示推送过,不再推送
     */
    public void pushInitUserInfo(String userName) {
        UserStatusDO userStatusDO = userStatusRepository.get(userName);
        if (!Objects.isNull(userStatusDO) && !Objects.isNull(userStatusDO.getRecordTime())
                && DateUtil.isSameDay(userStatusDO.getRecordTime(), new Date())) {
            return;
        }
        pushUserInfo(userName);
    }

    /**
     * 处理驾驶团队信息
     */
    public void pushUserInfo(String userName) {
        UserInfoMqttDTO userInfoMqttDTO = buildUserInfo(userName);

        sendUserInfoMqtt(userInfoMqttDTO);
    }

    /**
     * 构建控制屏驾驶团队信息
     */
    private UserInfoMqttDTO buildUserInfo(String userName) {
        UserInfoMqttDTO userInfoMqttDTO = new UserInfoMqttDTO();

        userInfoMqttDTO.setUserName(userName);
        userInfoMqttDTO.setRecordTime(new Date());

        UserStatusDO userStatusDO = userStatusRepository.get(userName);
        userInfoMqttDTO.setWorkTimeTotal(Optional.ofNullable(userStatusDO.getWorkTimeTotalHistory()).orElse(0));
        userInfoMqttDTO.setWorkStartTime(userStatusDO.getWorkStartTime());
        userInfoMqttDTO.setCockpitMode(userStatusDO.getCockpitMode());
        userInfoMqttDTO.setCockpitNumber(userStatusDO.getCockpitNumber());
        userInfoMqttDTO.setLastCockpitNumber(userStatusDO.getLastCockpitNumber());

        if (StringUtils.isNotBlank(userStatusDO.getCockpitNumber())) {
            CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(userStatusDO.getCockpitNumber());
            userInfoMqttDTO.setCockpitStatus(cockpitStatusDO.getCockpitStatus());
            userInfoMqttDTO.setAcceptIssueStatus(cockpitStatusDO.getAcceptIssueStatus());
            userInfoMqttDTO.setCockpitType(cockpitStatusDO.getCockpitType());
        }

        if (!Objects.equals(DateUtil.today(), userStatusDO.getIssueStatisticDate())) {
            userInfoMqttDTO.setCompleteIssueCount(0);
        } else {
            userInfoMqttDTO.setCompleteIssueCount(userStatusDO.getCompleteIssueCount());
        }
        return userInfoMqttDTO;
    }

    /**
     * 发送驾驶团队信息
     */
    private void sendUserInfoMqtt(UserInfoMqttDTO dto) {
        String topic = String.format(MqttTopicEnum.USER_INFO.getTopic(), profileActive) + dto.getUserName();
        mqttManager.sendQos1RetainNoResponse(dto.getUserName(), topic, MqttMessageTypeEnum.USER_INFO.name(), dto);
    }

    /**
     * 推送用户信息mqtt
     */
    public void sendUserInfoMqtt(String userName, Object dto) {
        String topic = String.format(MqttTopicEnum.USER_INFO.getTopic(), profileActive) + userName;
        mqttManager.sendQos1RetainNoResponse(userName, topic, MqttMessageTypeEnum.USER_INFO.name(), dto);
    }
}
