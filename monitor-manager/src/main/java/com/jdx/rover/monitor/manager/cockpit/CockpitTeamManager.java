/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.manager.cockpit;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitBasicInfoDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamBasicInfoDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamInfoDTO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamBasicUserVO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamListVO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamUserVO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamVO;
import com.jdx.rover.metadata.jsf.service.cockpit.MetadataCockpitInfoService;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 驾驶舱团队接口
 *
 * <AUTHOR>
 * @date 2023/05/20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CockpitTeamManager {

    /**
     * 主数据座舱JSF客户端
     */
    private final MetadataCockpitInfoService metadataCockpitInfoService;

    /**
     * 通过驾驶舱团队编号,获取驾驶舱团队信息
     *
     * @param cockpitTeamNumber cockpitTeamNumber
     * @return CockpitTeamInfoDTO
     */
    public CockpitTeamInfoDTO getCockpitTeamInfo(String cockpitTeamNumber) {
        CockpitTeamInfoDTO result = null;
        try {
            CockpitTeamVO cockpitTeamVo = new CockpitTeamVO();
            cockpitTeamVo.setCockpitTeamNumber(cockpitTeamNumber);
            HttpResult<CockpitTeamInfoDTO> httpResult = metadataCockpitInfoService.getCockpitTeamInfo(cockpitTeamVo);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        } catch (Exception e) {
            log.error("根据驾驶舱团队编号获取驾驶舱团队信息调用失败，{}.", cockpitTeamNumber, e);
        }
        return result;
    }

    /**
     * 根据用户名获取驾舱团队信息
     *
     * @param cockpitTeamUserVO cockpitTeamUserVO
     * @return CockpitTeamInfoDTO
     */
    public CockpitTeamInfoDTO getTeamInfoOfUserName(CockpitTeamUserVO cockpitTeamUserVO) {
        try {
            log.info("MetadatametadataCockpitInfoService call getTeamInfoOfUserName request:[{}].", cockpitTeamUserVO);
            HttpResult<CockpitTeamInfoDTO> httpResult = metadataCockpitInfoService.getTeamInfoOfUserName(cockpitTeamUserVO);
            log.info("MetadatametadataCockpitInfoService call getTeamInfoOfUserName response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadatametadataCockpitInfoService call getTeamInfoOfUserName exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取团队基本信息
     *
     * @param cockpitTeamBasicUserVO cockpitTeamBasicUserVO
     * @return CockpitTeamBasicInfoDTO
     */
    public CockpitTeamBasicInfoDTO getTeamBasicInfoOfUserName(CockpitTeamBasicUserVO cockpitTeamBasicUserVO) {
        try {
            log.info("MetadatametadataCockpitInfoService call getTeamBasicInfoOfUserName request:[{}].", cockpitTeamBasicUserVO);
            HttpResult<CockpitTeamBasicInfoDTO> httpResult = metadataCockpitInfoService.getTeamBasicInfoOfUserName(cockpitTeamBasicUserVO);
            log.info("MetadatametadataCockpitInfoService call getTeamBasicInfoOfUserName response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadatametadataCockpitInfoService call getTeamBasicInfoOfUserName exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取所有驾驶舱团队信息
     */
    public List<CockpitTeamInfoDTO> getCockpitTeamInfoList() {
        CockpitTeamListVO cockpitTeamListVO = new CockpitTeamListVO();
        HttpResult<List<CockpitTeamInfoDTO>> httpResult = metadataCockpitInfoService.getCockpitTeamInfoList(cockpitTeamListVO);
        if (HttpResult.isSuccess(httpResult)) {
            return httpResult.getData();
        }
        return null;
    }

    /**
     * 获取所有驾驶舱基础信息
     */
    public List<CockpitBasicInfoDTO> getAllCockpitBasicInfoList() {
        HttpResult<List<CockpitBasicInfoDTO>> httpResult = metadataCockpitInfoService.getAllCockpitBasicInfo();
        if (HttpResult.isSuccess(httpResult)) {
            return httpResult.getData();
        }
        return null;
    }

    /**
     * 根据驾舱团队编号获取绑定的车辆列表
     *
     * @param cockpitTeamNumber 驾舱团队编号
     * @return List<VehicleStationInfoDTO> 车辆列表
     */
    public CockpitTeamInfoDTO getTeamVehicleList(String cockpitTeamNumber) {
        CockpitTeamVO cockpitTeamVO = new CockpitTeamVO();
        cockpitTeamVO.setCockpitTeamNumber(cockpitTeamNumber);
        HttpResult<CockpitTeamInfoDTO> cockpitTeamInfo = metadataCockpitInfoService.getCockpitTeamInfo(cockpitTeamVO);
        if (HttpResult.isSuccess(cockpitTeamInfo)) {
            return cockpitTeamInfo.getData();
        }
        return null;
    }

    /**
     * 通过驾驶舱团队编号,获取驾驶舱团队信息
     *
     * @param cockpitNumber 座舱编号
     * @return CockpitTeamInfoDTO 座舱团队信息
     */
    public CockpitTeamBasicInfoDTO getTeamInfoBytCockpitNumber(String cockpitNumber) {
        try {
            HttpResult<CockpitTeamBasicInfoDTO> httpResult = metadataCockpitInfoService.getTeamBasicInfoOfCockpit(cockpitNumber);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            }
        } catch (Exception e) {
            log.error("根据驾驶舱编号获取驾驶舱团队信息调用失败，{}.", cockpitNumber, e);
        }
        return null;
    }
}