/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.drawer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.amazonaws.HttpMethod;
import com.google.common.collect.Lists;
import com.jdx.rover.metadata.api.domain.enums.RoverBucketEnum;
import com.jdx.rover.monitor.dto.drawer.DrawerMqttDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.user.UserIssueDO;
import com.jdx.rover.monitor.enums.mqtt.MqttMessageTypeEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.user.UserIssueRepository;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
 * 驾驶舱团队服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DrawerMqttManager {
    private final UserIssueRepository userIssueRepository;
    private final VehicleRealtimeRepository vehicleRealtimeRepository;
    private final VehicleAlarmRepository vehicleAlarmRepository;
    private final VehicleScheduleRepository vehicleScheduleRepository;
    private final MqttManager mqttManager;
    private final S3Properties s3Properties;

    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * 处理抽屉信息
     */
    public void pushDrawer(String userName) {
        DrawerMqttDTO drawerMqttDTO = buildDrawer(userName);

        sendDrawerMqtt(drawerMqttDTO);
    }

    /**
     * 构建控制屏驾驶团队信息
     */
    private DrawerMqttDTO buildDrawer(String userName) {
        DrawerMqttDTO drawerMqttDTO = new DrawerMqttDTO();
        drawerMqttDTO.setUserName(userName);
        drawerMqttDTO.setRecordTime(new Date());

        UserIssueDO userIssueDO = userIssueRepository.get(userName);
        if (Objects.isNull(userIssueDO) || CollectionUtils.isEmpty(userIssueDO.getIssueList())) {
            return drawerMqttDTO;
        }
        drawerMqttDTO.setCockpitNumber(userIssueDO.getCockpitNumber());
        List<DrawerMqttDTO.Issue> issueList = new ArrayList<>(userIssueDO.getIssueList().size());
        List<String> vehicleNameList = userIssueDO.getIssueList().stream().map(UserIssueDO.Issue::getVehicleName).toList();
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
        Map<String, MonitorScheduleEntity> scheduleMap = vehicleScheduleRepository.listMap(vehicleNameList);
        for (UserIssueDO.Issue issue : userIssueDO.getIssueList()) {
            DrawerMqttDTO.Issue result = new DrawerMqttDTO.Issue();
            result.setVehicleName(issue.getVehicleName());
            result.setStationName(issue.getStationName());
            result.setIssueNumber(issue.getIssueNumber());
            result.setIssueStatus(issue.getIssueStatus());
            result.setIssueReminder(issue.getIssueReminder());
            result.setIssueType(issue.getIssueType());
            result.setIssueStartTime(issue.getIssueStartTime());
            result.setIssuePendingTime(issue.getIssuePendingTime());
            result.setIssueSource(issue.getIssueSource());
            result.setIssueSourceUser(issue.getIssueSourceUser());
            result.setEventCount(issue.getEventCount());
            result.setNewIssue(issue.getNewIssue());
            result.setReportReminder(issue.getReportReminder());
            result.setEmergencyIssue(issue.getEmergencyIssue());
            if (!CollectionUtils.isEmpty(issue.getIssueEventList())) {
                result.setIssueEventList(issue.getIssueEventList().stream().map(event -> {
                    DrawerMqttDTO.IssueEvent issueEvent = new DrawerMqttDTO.IssueEvent();
                    issueEvent.setEventType(event.getEventType());
                    issueEvent.setStartTime(event.getStartTime());
                    return issueEvent;
                }).toList());
            }
            if (!CollectionUtils.isEmpty(issue.getIssueEventRecordList())) {
                result.setIssueEventRecordList(issue.getIssueEventRecordList().stream().limit(10).map(event -> {
                    DrawerMqttDTO.EventRecord eventRecord = new DrawerMqttDTO.EventRecord();
                    eventRecord.setReportTime(event.getReportTime());
                    eventRecord.setDescription(event.getDescription());
                    return eventRecord;
                }).toList());
            }
            if (null != issue.getBugReport()) {
                DrawerMqttDTO.BugReport bugReport = new DrawerMqttDTO.BugReport();
                bugReport.setStartTime(issue.getBugReport().getStartTime());
                bugReport.setEndTime(issue.getBugReport().getEndTime());
                bugReport.setDescription(issue.getBugReport().getDescription());
                if (CollUtil.isNotEmpty(issue.getBugReport().getAttachmentList())) {
                    List<DrawerMqttDTO.BugReport.Attachment> attachmentList = Lists.newArrayListWithCapacity(issue.getBugReport().getAttachmentList().size());
                    final Date expiration = DateUtil.offsetHour(new Date(), 24);
                    issue.getBugReport().getAttachmentList().forEach(attachment -> {
                        DrawerMqttDTO.BugReport.Attachment attachmentInfo = new DrawerMqttDTO.BugReport.Attachment();
                        attachmentInfo.setType(attachment.getType());
                        attachmentInfo.setFileKey(attachment.getFileKey());
                        attachmentInfo.setUrl(S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(),
                            RoverBucketEnum.ROVER_OPERATION.getName(), attachment.getFileKey(), HttpMethod.GET, expiration).toString());
                        attachmentList.add(attachmentInfo);
                    });
                    bugReport.setAttachmentList(attachmentList);
                }
                result.setBugReport(bugReport);
            }

            MonitorScheduleEntity scheduleEntity = scheduleMap.getOrDefault(issue.getVehicleName(), new MonitorScheduleEntity());
            result.setScheduleState(Optional.ofNullable(scheduleEntity.getScheduleState()).orElse(VehicleScheduleState.WAITING.getVehicleScheduleState()));
            VehicleRealtimeInfoDTO realtime = realtimeMap.get(issue.getVehicleName());
            if (!Objects.isNull(realtime)) {
                result.setVehicleState(realtime.getVehicleState());
                result.setSystemState(realtime.getSystemState());
                result.setSpeed(realtime.getSpeed());
            } else {
                result.setSystemState(SystemStateEnum.OFFLINE.getSystemStateName());
            }
            issueList.add(result);
        }
        drawerMqttDTO.setIssueList(issueList);
        return drawerMqttDTO;
    }

    /**
     * 发送驾驶团队信息
     */
    private void sendDrawerMqtt(DrawerMqttDTO dto) {
        String topic = String.format(MqttTopicEnum.DRAWER_INFO.getTopic(), profileActive) + dto.getUserName();
        mqttManager.sendQos0NoRetainNoResponse(dto.getUserName(), topic, MqttMessageTypeEnum.DRAWER_INFO.name(), dto);
    }
}
