/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.drive;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 远程驾驶连接服务
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DriveRemoteCommandManager {
    /**
     * 发送hermes指令
     */
    public HttpResult<?> sendCommand(DriveRemoteCommandVO vo) {
        RemoteCommandEnum remoteCommandEnum = RemoteCommandEnum.valueOf(vo.getRemoteCommandType());
        HttpResult<?> result = sendCommand(vo, remoteCommandEnum);
        if (!HttpResult.isSuccess(result)) {
            return result;
        }
        if (Objects.equals(remoteCommandEnum, RemoteCommandEnum.REMOTE_REQUEST_RELIEVE_BUTTON_STOP)) {
            return sendCommand(vo, RemoteCommandEnum.REMOTE_REQUEST_RECOVERY);
        }
        return HttpResult.success(vo);
    }

    /**
     * 发送hermes指令
     */
    private HttpResult<?> sendCommand(DriveRemoteCommandVO vo, RemoteCommandEnum remoteCommandEnum) {
        com.jdx.rover.server.api.domain.vo.RemoteCommandVO commandVO = buildServerRemoteCommandVO(vo);

        Function<com.jdx.rover.server.api.domain.vo.RemoteCommandVO, HttpResult<Void>> function = remoteCommandEnum.getFunction();
        HttpResult<?> result = function.apply(commandVO);

        if (!HttpResult.isSuccess(result)) {
            return result;
        }
        return HttpResult.success(vo);
    }

    /**
     * 构建hermes指令请求对象
     */
    private com.jdx.rover.server.api.domain.vo.RemoteCommandVO buildServerRemoteCommandVO(DriveRemoteCommandVO vo) {
        com.jdx.rover.server.api.domain.vo.RemoteCommandVO commandVO = new com.jdx.rover.server.api.domain.vo.RemoteCommandVO();
        commandVO.setVehicleName(vo.getVehicleName());
        commandVO.setReceiveTimeStamp(Optional.ofNullable(vo.getRequestTime()).orElse(new Date()));
        commandVO.setTransitTimeStamp(new Date());
        return commandVO;
    }
}
