package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.monitor.dataobject.mapcollection.VehicleStorageSpaceInfoDO;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.config.DuccMonitorProperties;
import com.jdx.rover.monitor.manager.xata.XataManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * 车辆储存空间管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VehicleStorageManager {

    private final DuccMonitorProperties duccMonitorProperties;

    private final XataManager xataManager;

    /**
     * 获取车辆存储空间
     *
     * @param vehicleName vehicleName
     * @return VehicleStorageSpaceInfoDO 存储空间对象
     */
    public VehicleStorageSpaceInfoDO getVehicleStorage(String vehicleName) {
        RMap<String, VehicleStorageSpaceInfoDO> rMap = RedissonUtils.getMap(RedisKeyEnum.VEHICLE_STORAGE_SPACE_INFO.getValue());
        if (rMap.isEmpty()) {
            Map<String, VehicleStorageSpaceInfoDO> vehicleStorageSpaceInfoDOMap = init();
            return vehicleStorageSpaceInfoDOMap.get(vehicleName);
        }
        return rMap.get(vehicleName);
    }

    /**
     * 获取车辆储存空间Map
     *
     * @param vehicleNameList vehicleNameList
     * @return Map<String, VehicleStorageSpaceInfoDO>
     */
    public Map<String, VehicleStorageSpaceInfoDO> getVehicleStorageListMap(List<String> vehicleNameList) {
        RMap<String, VehicleStorageSpaceInfoDO> rMap = RedissonUtils.getMap(RedisKeyEnum.VEHICLE_STORAGE_SPACE_INFO.getValue());
        if (rMap.isEmpty()) {
            Map<String, VehicleStorageSpaceInfoDO> vehicleStorageSpaceInfoDOMap = init();
            return vehicleStorageSpaceInfoDOMap;
        }
        Map<String, VehicleStorageSpaceInfoDO> vehicleStorageSpaceInfoDOMap = rMap.getAll(new HashSet<>(vehicleNameList));
        return vehicleStorageSpaceInfoDOMap;
    }

    /**
     * 车辆存储空间初始化
     * @return Map<String, VehicleStorageSpaceInfoDO>
     */
    public Map<String, VehicleStorageSpaceInfoDO> init() {
        log.info("执行车辆存储空间初始化信息");
        Map<String, VehicleStorageSpaceInfoDO> result = new HashMap<>();
        List<String> vehicleNameList = duccMonitorProperties.getVehicleNameList();
        for (String vehicleName : vehicleNameList) {
            if (duccMonitorProperties.getOpenXataQueryStorage()) {
                VehicleStorageSpaceInfoDO singleVehicleSpace = xataManager.queryVehicleStorage(vehicleName);
                if (singleVehicleSpace != null) {
                    result.put(vehicleName, singleVehicleSpace);
                }
            }else {
                VehicleStorageSpaceInfoDO vehicleStorageSpaceInfoDO = new VehicleStorageSpaceInfoDO();
                vehicleStorageSpaceInfoDO.setFree(0.0);
                vehicleStorageSpaceInfoDO.setTotal(0.0);
                result.put(vehicleName, vehicleStorageSpaceInfoDO);
            }
        }
        //放入到缓存中
        RMap<String, VehicleStorageSpaceInfoDO> map = RedissonUtils.getMap(RedisKeyEnum.VEHICLE_STORAGE_SPACE_INFO.getValue());
        map.putAll(result);
        //缓存过期时间
        map.expire(Duration.ofSeconds(60));
        return result;
    }
}
