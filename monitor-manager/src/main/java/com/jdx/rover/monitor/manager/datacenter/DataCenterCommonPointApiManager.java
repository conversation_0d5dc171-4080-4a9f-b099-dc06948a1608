/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.datacenter;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.api.enums.PointTypeEnum;
import com.jdx.rover.datacenter.domain.dto.map.PointQueryDTO;
import com.jdx.rover.datacenter.domain.vo.map.PointQueryVO;
import com.jdx.rover.datacenter.jsf.service.map.PointRangeQueryJsfService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/18 23:22
 * @description
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DataCenterCommonPointApiManager {

    /**
     * PointRangeQueryJsfService
     */
    private final PointRangeQueryJsfService pointRangeQueryJsfService;

    /**
     * 根据提供半径范围查询点位
     *
     * @param latitude latitude
     * @param longitude longitude
     * @param radius radius
     * @param pointTypeEnum pointTypeEnum
     * @return PointQueryDTO
     */
    public PointQueryDTO queryPointByRadius(Double latitude, Double longitude, Integer radius, PointTypeEnum pointTypeEnum) {
        try {
            PointQueryVO pointQueryVO = new PointQueryVO();
            pointQueryVO.setLatitude(latitude);
            pointQueryVO.setLongitude(longitude);
            pointQueryVO.setRadius(radius);
            if (null != pointTypeEnum) {
                pointQueryVO.setPointType(pointTypeEnum.getCode());
            }
            HttpResult<PointQueryDTO> httpResult = pointRangeQueryJsfService.queryPointByRadius(pointQueryVO);
            if (HttpResult.isSuccess(httpResult) && Objects.nonNull(httpResult.getData())) {
                return httpResult.getData();
            }
        } catch (Exception e) {
            log.error("数据中心查询点位信息失败，{}", e.getMessage(), e);
        }
        return null;
    }
}
