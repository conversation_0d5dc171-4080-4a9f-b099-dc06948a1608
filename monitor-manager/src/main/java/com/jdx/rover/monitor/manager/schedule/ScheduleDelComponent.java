/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.MonitorStationVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleSchedulePncRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRealRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleStopOrderRepository;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 * This is a vehicle realtime state service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@LiteflowComponent(id = "delSchedule", name = "删除调度")
public class ScheduleDelComponent extends NodeComponent {
  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @Autowired
  private VehicleScheduleRealRouteRepository vehicleScheduleRealRouteRepository;

  @Autowired
  private VehicleScheduleStopOrderRepository scheduleStopOrderRepository;

  @Autowired
  private VehicleSchedulePncRouteRepository pncRouteRepository;

  @Override
  public void process() throws Exception {
    ScheduleTask scheduleTask = getSlot().getRequestData();
    removeSchedule(scheduleTask.getVehicleName());
  }

  private void removeSchedule(String vehicleName) {
    boolean result = vehicleScheduleRepository.delete(vehicleName);
    if (!result) {
      log.error("Delete schedule {} error", vehicleName);
      vehicleScheduleRepository.delete(vehicleName);
    }
    scheduleStopOrderRepository.delete(vehicleName);
    pncRouteRepository.remove(vehicleName);
    vehicleScheduleRealRouteRepository.remove(vehicleName);
    pushVehicleScheduleDeleteEvent(vehicleName);
  }

  private void pushVehicleScheduleDeleteEvent(String vehicleName) {
    MonitorStationVehicleMapInfoDTO vehicleMapInfoDto = new MonitorStationVehicleMapInfoDTO();
    vehicleMapInfoDto.setName(vehicleName);
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_UPDATE.getValue(), vehicleMapInfoDto);
    String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    log.info("Send vehicle schedule delete event {}", topicName);
    rTopic.publish(JsonUtils.writeValueAsString(wsResult));
  }
}

