/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.mobile;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.deploy.DeviceEffectiveInsuranceDTO;
import com.jdx.rover.metadata.domain.dto.deploy.DeviceInsuranceDTO;
import com.jdx.rover.metadata.domain.dto.deploy.PolicyAttachmentDTO;
import com.jdx.rover.metadata.domain.vo.deploy.DeviceInsuranceVO;
import com.jdx.rover.metadata.jsf.service.deploy.DeviceInsuranceJsfService;
import com.jdx.rover.monitor.dto.mobile.map.InsuranceAttachmentDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleInsuranceDTO;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/11 18:18
 * @description 主数据保险信息
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MetadataInsuranceManager {

    /**
     * DeviceInsuranceJsfService
     */
    private final DeviceInsuranceJsfService deviceInsuranceJsfService;

    /**
     * 获取车辆是否有生效保单
     *
     * @param vehicleNameList vehicleNameList
     * @return Map -> key: vehicleName, value: effective
     */
    public Map<String, Boolean> hasDeviceEffectiveInsurance(List<String> vehicleNameList) {
        try {
            DeviceInsuranceVO deviceInsuranceVO = new DeviceInsuranceVO();
            deviceInsuranceVO.setDeviceNameList(vehicleNameList);
            HttpResult<List<DeviceEffectiveInsuranceDTO>> result = deviceInsuranceJsfService.hasDeviceEffectiveInsurance(deviceInsuranceVO);
            if (HttpResult.isSuccess(result)) {
                List<DeviceEffectiveInsuranceDTO> data = result.getData();
                if (!CollUtil.isEmpty(data)) {
                    return data.stream().collect(Collectors.toMap(DeviceEffectiveInsuranceDTO::getDeviceName, DeviceEffectiveInsuranceDTO::getEffective));
                }
            }
        } catch (Exception e) {
            log.error("主数据调用获取车辆是否有生效保单失败 {}", e.getMessage(), e);
        }
        return Maps.newHashMap();
    }

    /**
     * 获取车辆保单信息列表
     *
     * @param vehicleName vehicleName
     * @return List<VehicleInsuranceDTO>
     */
    public List<VehicleInsuranceDTO> getVehicleEffectiveInsuranceList(String vehicleName) {
        try {
            DeviceInsuranceVO deviceInsuranceVO = new DeviceInsuranceVO();
            deviceInsuranceVO.setDeviceName(vehicleName);
            HttpResult<List<DeviceInsuranceDTO>> result = deviceInsuranceJsfService.getDeviceEffectiveInsuranceList(deviceInsuranceVO);
            if (HttpResult.isSuccess(result)) {
                List<DeviceInsuranceDTO> data = result.getData();
                if (!CollUtil.isEmpty(data)) {
                    List<VehicleInsuranceDTO> vehicleInsuranceList = Lists.newArrayListWithCapacity(data.size());
                    data.forEach(deviceInsuranceDTO -> {
                        VehicleInsuranceDTO vehicleInsuranceDTO = new VehicleInsuranceDTO();
                        vehicleInsuranceDTO.setVehicleName(deviceInsuranceDTO.getDeviceName());
                        vehicleInsuranceDTO.setVehicleInsuranceId(deviceInsuranceDTO.getDeviceInsuranceId());
                        vehicleInsuranceDTO.setPolicyNumber(deviceInsuranceDTO.getPolicyNumber());
                        vehicleInsuranceDTO.setProvinceName(deviceInsuranceDTO.getProvinceName());
                        vehicleInsuranceDTO.setCityName(deviceInsuranceDTO.getCityName());
                        vehicleInsuranceDTO.setStationName(deviceInsuranceDTO.getStationName());
                        vehicleInsuranceDTO.setEffectiveStartTime(DateUtil.format(deviceInsuranceDTO.getEffectiveStartTime(), DatePattern.NORM_DATE_FORMAT));
                        vehicleInsuranceDTO.setEffectiveEndTime(DateUtil.format(deviceInsuranceDTO.getEffectiveEndTime(), DatePattern.NORM_DATE_FORMAT));
                        vehicleInsuranceList.add(vehicleInsuranceDTO);
                    });
                    return vehicleInsuranceList;
                }
            }
        } catch (Exception e) {
            log.error("主数据调用获取保单信息列表失败 {}", e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    /**
     * 获取保单文件外链
     *
     * @param insuranceId insuranceId
     * @return InsuranceAttachmentDTO
     */
    public InsuranceAttachmentDTO getPolicyAttachmentFileUrl(Integer insuranceId) {
        try {
            DeviceInsuranceVO deviceInsuranceVO = new DeviceInsuranceVO();
            deviceInsuranceVO.setDeviceInsuranceId(insuranceId);
            HttpResult<PolicyAttachmentDTO> result = deviceInsuranceJsfService.getPolicyAttachmentFileUrl(deviceInsuranceVO);
            if (HttpResult.isSuccess(result)) {
                PolicyAttachmentDTO policyAttachmentDTO = result.getData();
                InsuranceAttachmentDTO insuranceAttachmentDTO = new InsuranceAttachmentDTO();
                if (policyAttachmentDTO != null) {
                    BeanUtils.copyProperties(policyAttachmentDTO, insuranceAttachmentDTO);
                    return insuranceAttachmentDTO;
                }
            }
        } catch (Exception e) {
            log.error("主数据调用获取保单外链失败 {}", e.getMessage(), e);
        }
        return new InsuranceAttachmentDTO();
    }
}
