/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.manager.datacollection;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.monitor.base.BaseModel;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionStatusEnum;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionRequirement;
import com.jdx.rover.monitor.repository.mapper.datacollection.DataCollectionRequirementMapper;
import java.util.Date;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据采集需求表 Manager 类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class DataCollectionRequirementManager extends BaseManager<DataCollectionRequirementMapper, DataCollectionRequirement> {

    /**
     * 创建数据采集需求
     *
     * @param requiredDetail 必要事项
     * @param forbiddenDetail 禁忌事项
     * @param createUser 创建用户
     * @return DataCollectionRequirement
     */
    @Transactional(rollbackFor = Exception.class)
    public DataCollectionRequirement createRequirement(String description, String requiredDetail, String forbiddenDetail, String createUser) {
        DataCollectionRequirement requirement = new DataCollectionRequirement();
        requirement.setRequirementNumber(generateRequirementNumber());
        requirement.setStatus(DataCollectionStatusEnum.ONGOING.getValue());
        requirement.setProgress(0.0);
        requirement.setDescription(description);
        requirement.setRequiredDetail(requiredDetail);
        requirement.setForbiddenDetail(forbiddenDetail);
        requirement.setCreateUser(createUser);
        this.save(requirement);
        return requirement;
    }

    /**
     * 生成需求编号
     *
     * @return String
     */
    private String generateRequirementNumber() {
        // 生成格式：年月日时分 + 4位随机数字，例如：2025071410000001
        String prefix = DateUtil.format(new Date(), "yyyyMMddHHmm");

        // 生成4位随机数字
        Random random = new Random();
        String randomDigit = String.format("%04d", random.nextInt(10000));

        return prefix + randomDigit;
    }

    /**
     * 根据状态查询需求列表
     *
     * @param status 状态
     * @return List<DataCollectionRequirement>
     */
    public List<DataCollectionRequirement> listByStatus(String status) {
        LambdaQueryWrapper<DataCollectionRequirement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataCollectionRequirement::getStatus, status);
        return this.list(queryWrapper);
    }

    /**
     * 更新需求状态
     *
     * @param requirementId 需求ID
     * @param status 状态
     * @param modifyUser 修改用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Integer requirementId, String status, String modifyUser) {
        lambdaUpdate()
            .eq(BaseModel::getId, requirementId)
            .set(DataCollectionRequirement::getStatus, status)
            .set(BaseModel::getModifyUser, modifyUser)
            .update();
    }

    /**
     * 更新需求进度
     *
     * @param requirementId 需求ID
     * @param progress 进度
     * @param modifyUser 修改用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProgress(Integer requirementId, Double progress, String modifyUser) {
        lambdaUpdate()
            .eq(BaseModel::getId, requirementId)
            .set(DataCollectionRequirement::getProgress, progress)
            .set(BaseModel::getModifyUser, modifyUser)
            .update();
    }

    /**
     * 更新需求状态
     *
     * @param requirementId 需求ID
     * @param status 状态
     * @param modifyUser 修改用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusNotClosed(Integer requirementId, String status, String modifyUser) {
        lambdaUpdate()
            .eq(BaseModel::getId, requirementId)
            .set(DataCollectionRequirement::getStatus, status)
            .set(BaseModel::getModifyUser, modifyUser)
            .ne(DataCollectionRequirement::getStatus, DataCollectionStatusEnum.CLOSED.getValue())
            .update();
    }
}
