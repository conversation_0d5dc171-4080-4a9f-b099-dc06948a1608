package com.jdx.rover.monitor.manager.vehicle;

import cn.hutool.core.lang.func.LambdaUtil;
import com.google.common.collect.Maps;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 单车页manager
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleGpsManager {
    private final SingleVehicleManager singleVehicleManager;
    private final VehicleStatusRepository vehicleStatusRepository;

    /**
     * 推送单车增量消息
     *
     * @param vehicleName
     */
    public void pushSingleVehicle(String vehicleName) {
        WsResult<Map<String, Object>> wsResult = this.getGps(vehicleName);
        if (Objects.isNull(wsResult)) {
            return;
        }
        singleVehicleManager.pushSingleVehiclePageData(vehicleName, wsResult);
    }

    /**
     * 通过车辆名称获取单车页GPS信息
     *
     * @param vehicleName 车辆名称
     * @return
     */
    public WsResult<Map<String, Object>> getGps(String vehicleName) {
        VehicleStatusDO vehicleStatus = vehicleStatusRepository.get(vehicleName);
        if (Objects.isNull(vehicleStatus)) {
            return null;
        }

        Map<String, Object> dataMap = Maps.newHashMap();
        if (!Objects.isNull(vehicleStatus.getGpsSignal())) {
            Integer gpsSignal = Integer.valueOf(vehicleStatus.getGpsSignal());
            dataMap.put(LambdaUtil.getFieldName(VehicleStatusDO::getGpsSignal), gpsSignal);
        }

        if (!Objects.isNull(vehicleStatus.getSceneSignal())) {
            Double sceneSignal = Double.valueOf(vehicleStatus.getSceneSignal());
            dataMap.put(LambdaUtil.getFieldName(VehicleStatusDO::getSceneSignal), sceneSignal);
        }

        WsResult<Map<String, Object>> wsResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_GPS.getValue(), dataMap);
        return wsResult;
    }
}
