package com.jdx.rover.monitor.manager.accident;

import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentDetail;
import com.jdx.rover.monitor.repository.mapper.AccidentDetailMapper;
import com.jdx.rover.monitor.repository.mapper.AccidentMapper;
import org.springframework.stereotype.Service;

/**
 * 事故详情manager
 */
@Service
public class AccidentDetailManager extends BaseManager<AccidentDetailMapper, AccidentDetail> {

    public AccidentDetail selectByAccidentNo(String accidentNo) {
        return lambdaQuery().eq(AccidentDetail::getAccidentNo, accidentNo).one();
    }
}
