package com.jdx.rover.monitor.manager.mobile;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.schedule.api.domain.vo.schedule.MiniMonitorFreeNavigationVO;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMiniMonitorJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: ScheduleMiniMonitorFreeApiManager
 * @author: wangguotai
 * @create: 2024-07-17 09:01
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class ScheduleMiniMonitorFreeApiManager {

    private final ScheduleMiniMonitorJsfService scheduleMiniMonitorFreeApiClient;

    /**
     * 自由跑行立即出发
     *
     * @param miniMonitorFreeNavigationVO miniMonitorFreeNavigationVO
     * @return List<String>
     */
    public List<String> navigation(MiniMonitorFreeNavigationVO miniMonitorFreeNavigationVO) {
        try {
            log.info("ScheduleMiniMonitorFreeApiClient call navigation request:[{}].", miniMonitorFreeNavigationVO);
            HttpResult<List<String>> httpResult = scheduleMiniMonitorFreeApiClient.navigation(miniMonitorFreeNavigationVO);
            log.info("ScheduleMiniMonitorFreeApiClient call navigation response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("ScheduleMiniMonitorFreeApiClient call navigation exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }
}