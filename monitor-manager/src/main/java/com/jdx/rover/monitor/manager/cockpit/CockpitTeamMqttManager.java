/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.cockpit;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamBasicInfoDTO;
import com.jdx.rover.monitor.api.domain.enums.CockpitStatusEnum;
import com.jdx.rover.monitor.dto.cockpit.CockpitTeamMqttDTO;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.cockpit.CockpitTeamStatusDO;
import com.jdx.rover.monitor.enums.mqtt.MqttMessageTypeEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.manager.mqtt.MqttManager;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitTeamStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 驾驶舱团队服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class CockpitTeamMqttManager {
    private final CockpitTeamManager cockpitTeamManager;
    private final CockpitStatusRepository cockpitStatusRepository;
    private final CockpitTeamStatusRepository cockpitTeamStatusRepository;
    private final MqttManager mqttManager;

    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * 初始化推送座席团队信息,如果当天有过缓存变化,表示推送过,不再推送
     */
    public void pushInitCockpitTeam(String cockpitTeamNumber) {
        CockpitTeamStatusDO cockpitTeamDO = cockpitTeamStatusRepository.get(cockpitTeamNumber);
        if (!Objects.isNull(cockpitTeamDO) && !Objects.isNull(cockpitTeamDO.getRecordTime())
                && DateUtil.isSameDay(cockpitTeamDO.getRecordTime(), new Date())) {
            return;
        }
        pushCockpitTeam(cockpitTeamNumber);
    }

    /**
     * 推送座席团队消息
     */
    public void pushCockpitTeam(String cockpitTeamNumber) {
        CockpitTeamMqttDTO cockpitTeamMqttDTO = buildCockpitTeamMqtt(cockpitTeamNumber);

        sendCockpitTeamMqtt(cockpitTeamMqttDTO);
    }

    /**
     * 通过驾驶舱编号处理驾驶团队信息
     */
    public void pushCockpitTeamByCockpit(String cockpitNumber) {
        CockpitTeamBasicInfoDTO cockpitTeamInfo = cockpitTeamManager.getTeamInfoBytCockpitNumber(cockpitNumber);
        if (cockpitTeamInfo == null) {
            log.error("通过驾驶舱未获取到驾驶舱团队信息!{}", cockpitNumber);
            return;
        }

        pushCockpitTeam(cockpitTeamInfo.getCockpitTeamNumber());
    }

    /**
     * 构建控制屏驾驶团队信息
     */
    private CockpitTeamMqttDTO buildCockpitTeamMqtt(String cockpitTeamNumber) {
        CockpitTeamStatusDO cockpitTeamDO = cockpitTeamStatusRepository.get(cockpitTeamNumber);
        CockpitTeamMqttDTO cockpitTeamMqttDTO = new CockpitTeamMqttDTO();
        cockpitTeamMqttDTO.setCockpitTeamNumber(cockpitTeamNumber);
        if (Objects.isNull(cockpitTeamDO)) {
            return cockpitTeamMqttDTO;
        }
        cockpitTeamMqttDTO.setCockpitTeamName(cockpitTeamDO.getCockpitTeamName());
        int workTotal = 0;
        int restTotal = 0;
        int offlineTotal = 0;
        if (CollectionUtils.isNotEmpty(cockpitTeamDO.getCockpitList())) {
            List<String> cockpitNumberList = cockpitTeamDO.getCockpitList();
            Map<String, CockpitStatusDO> cockpitStatusMap = cockpitStatusRepository.listMap(cockpitNumberList);
            for (CockpitStatusDO item : cockpitStatusMap.values()) {
                if (Objects.isNull(item.getCockpitStatus())) {
                    continue;
                }
                switch (CockpitStatusEnum.valueOf(item.getCockpitStatus())) {
                    case WORK -> workTotal++;
                    case OFFLINE -> restTotal++;
                }
            }
            offlineTotal = cockpitNumberList.size() - workTotal - restTotal;

            List<CockpitTeamMqttDTO.Cockpit> cockpitList = new ArrayList<>(cockpitNumberList.size());
            for (String cockpitNumber : cockpitNumberList) {
                CockpitTeamMqttDTO.Cockpit cockpit = new CockpitTeamMqttDTO.Cockpit();
                cockpit.setCockpitNumber(cockpitNumber);
                CockpitStatusDO cockpitStatus = cockpitStatusMap.get(cockpitNumber);
                if (cockpitStatus != null) {
                    cockpit.setVehicleName(cockpitStatus.getVehicleName());
                    cockpit.setCockpitUserName(cockpitStatus.getCockpitUserName());
                    cockpit.setCockpitStatus(cockpitStatus.getCockpitStatus());
                    cockpit.setCockpitType(cockpitStatus.getCockpitType());
                }
                cockpitList.add(cockpit);
            }
            cockpitTeamMqttDTO.setCockpitList(cockpitList);
        }
        cockpitTeamMqttDTO.setWorkTotal(workTotal);
        cockpitTeamMqttDTO.setRestTotal(restTotal);
        cockpitTeamMqttDTO.setOfflineTotal(offlineTotal);

        if (!Objects.equals(DateUtil.today(), cockpitTeamDO.getIssueStatisticDate())) {
            cockpitTeamMqttDTO.setWaitAcceptIssueCount(0);
            cockpitTeamMqttDTO.setCompleteIssueCount(0);
        } else {
            cockpitTeamMqttDTO.setWaitAcceptIssueCount(cockpitTeamDO.getWaitAcceptIssueCount());
            cockpitTeamMqttDTO.setCompleteIssueCount(cockpitTeamDO.getCompleteIssueCount());
        }
        return cockpitTeamMqttDTO;
    }

    /**
     * 发送驾驶团队信息
     */
    private void sendCockpitTeamMqtt(CockpitTeamMqttDTO dto) {
        String topic = String.format(MqttTopicEnum.COCKPIT_TEAM_GROUP.getTopic(), profileActive) + dto.getCockpitTeamNumber();
        mqttManager.sendQos1RetainNoResponse(dto.getCockpitTeamNumber(), topic, MqttMessageTypeEnum.COCKPIT_TEAM.name(), dto);
    }
}
