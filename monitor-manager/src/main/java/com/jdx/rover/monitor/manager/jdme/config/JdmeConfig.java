package com.jdx.rover.monitor.manager.jdme.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 京ME开放平台参数配置
 */
@Configuration
@ConfigurationProperties(prefix = "jdme")
@Data
public class JdmeConfig {
    /**
     * 开放接口API
     */
    private JdmeConfigApi api = new JdmeConfigApi();
    /**
     * 机器人配置
     */
    private JdmeConfigRobot robot = new JdmeConfigRobot();
}
