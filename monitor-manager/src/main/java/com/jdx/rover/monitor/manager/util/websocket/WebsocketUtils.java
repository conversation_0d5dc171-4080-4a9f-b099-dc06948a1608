package com.jdx.rover.monitor.manager.util.websocket;


import jakarta.websocket.Session;

/**
 * <AUTHOR>
 */
public class WebsocketUtils {
  /**
   * IP关键字
   */
  private static final String CLIENT_IP_KEY = "CLIENT_IP";

  /**
   * 用戶名关键字
   */
  private static final String USER_NAME_KEY = "userName";

  /**
   * 获取webSocket ip
   *
   * @param session
   * @return
   */
  public static final String getWebSocketIp(Session session) {
    String clientIp = "N/A";
    if (session == null || session.getUserProperties() == null || session.getUserProperties().get(CLIENT_IP_KEY) == null) {
      return clientIp;
    }
    clientIp = String.valueOf(session.getUserProperties().get(CLIENT_IP_KEY));
    return clientIp;
  }

  /**
   * 获取webSocket user name
   *
   * @param session
   * @return
   */
  public static final String getWebSocketUserName(Session session) {
    String userName = "";
    if (session == null || session.getUserProperties() == null || session.getUserProperties().get(USER_NAME_KEY) == null) {
      return userName;
    }
    userName = String.valueOf(session.getUserProperties().get(USER_NAME_KEY));
    return userName;
  }
}
