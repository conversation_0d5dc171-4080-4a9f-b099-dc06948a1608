/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.station;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.metadata.domain.dto.station.CityStationInfoDTO;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.user.UserMonitorStationTreeDto;
import com.jdx.rover.metadata.domain.vo.station.StationBasicVO;
import com.jdx.rover.metadata.domain.vo.user.UserMonitorStationTreeVo;
import com.jdx.rover.metadata.jsf.service.station.MetadataStationBasicService;
import com.jdx.rover.metadata.jsf.service.user.MetadataUserBasicService;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 监控端获取站点信息
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetadataStationApiManager {

  @Autowired
  private MetadataUserBasicService metadataUserBasicJsfService;

  @Autowired
  private MetadataStationBasicService metadataStationBasicService;

  /**
   * 通过站点获取车辆基本信息
   *
   * @param stationId 站点Id
   * @return list
   */
  public Map<Integer, StationBasicDTO> getStationById(Integer... stationId) {
    Map<Integer, StationBasicDTO> result = null;
    try {
      StationBasicVO stationBasicVo = new StationBasicVO();
      stationBasicVo.setStationIdList(Lists.newArrayList(stationId));
      HttpResult<List<StationBasicDTO>> httpResult = metadataStationBasicService.getStationInfoByIdList(stationBasicVo);
      if (HttpResult.isSuccess(httpResult)) {
        result = httpResult.getData().stream().collect(Collectors.toMap(StationBasicDTO::getStationId, Function.identity()));
      }
    } catch (Exception e) {
      log.warn("元数据通过站点id获取站点基本信息调用失败,{},{}", stationId, e.toString());
    }
    log.info("元数据通过站点id获取站点基本信息调用完成,结果={}", result);
    return result;
  }

  /**
   * 通过用户名获取站点列表
   *
   * @param userName 用户名
   * @return list
   */
  @Cacheable(value = LocalCacheConstant.USER_STATION_LIST, unless = "#result?.size() == 0", key = "#userName")
  public List<StationBasicDTO> getStationListByUser(String userName) {
    List<StationBasicDTO> result = new ArrayList<>();
    try {
      HttpResult<Set<Integer>> userStationHttpResult = metadataUserBasicJsfService.getStationIdByUserName(userName);
      if (!HttpResult.isSuccess(userStationHttpResult) || userStationHttpResult.getData() == null) {
        return result;
      }
      Set<Integer> userStationIdSet = userStationHttpResult.getData();
      List<List<Integer>> vehicleNamePartitionList = Lists.partition(Lists.newArrayList(userStationIdSet), 200);
      for (List<Integer> item : vehicleNamePartitionList) {
        StationBasicVO stationBasicVo = new StationBasicVO();
        stationBasicVo.setStationIdList(item);
        HttpResult<List<StationBasicDTO>> httpResult = metadataStationBasicService.getStationInfoByIdList(stationBasicVo);
        if (HttpResult.isSuccess(httpResult) && CollectionUtils.isNotEmpty(httpResult.getData())) {
          result.addAll(httpResult.getData());
        }
      }
    } catch (Exception e) {
      log.warn("元数据通过用户获取站点基本信息列表调用失败,{},{}", userName, e.toString());
    }
    log.info("元数据通过用户获取车辆基本信息列表调用完成,结果={}", result);
    return result;
  }

  /**
   * 清除用户站点列表本地缓存
   *
   */
  @LocalCacheEvict(value = LocalCacheConstant.USER_STATION_LIST, key = "#userName")
  public void clearUserStationLocalCache(String userName) {
    log.info("用户站点权限变更{}", userName);
  }

  /**
   * 通过用户名获取城市站点车辆树
   *
   * @return list
   */
  public List<UserMonitorStationTreeDto> getStationTreeByUser(UserMonitorStationTreeVo stationTreeVo) {
    List<UserMonitorStationTreeDto> result = new ArrayList<>();
    try {
      HttpResult<List<UserMonitorStationTreeDto>> userStationHttpResult = metadataUserBasicJsfService.getUserStationTree(stationTreeVo);
      if (!HttpResult.isSuccess(userStationHttpResult) || Objects.isNull(userStationHttpResult.getData())) {
        return Lists.newArrayList();
      }
      return userStationHttpResult.getData();
    } catch (Exception e) {
      log.warn("元数据通过用户获取城市站点树调用失败,{},{}", stationTreeVo, e);
    }
    log.info("元数据通过用户获取城市站点树调用完成,结果={}", result);
    return result;
  }

  /**
   * 根据站点类型获取站点列表
   * @param stationType 站点类型
   * @return List<StationBasicDTO> 站点列表
   */
  public List<StationBasicDTO> getStationInfoListByType(String stationType) {
    try {

      HttpResult<List<StationBasicDTO>> stationInfo = metadataStationBasicService.getStationInfoListByType(stationType);

      if (HttpResult.isSuccess(stationInfo)) {
        List<StationBasicDTO> data = stationInfo.getData();
        log.info("元数据通过站点类型获取站点列表信息调用完成,结果={}", data);
        return data;
      }

      log.warn("元数据通过站点类型获取站点列表信息调用失败,{},{}", stationType, stationInfo);
    } catch (Exception e) {
      log.warn("元数据通过站点类型获取站点列表信息调用失败,{},{}", stationType, e.toString());
    }
    return null;
  }

  /**
   * 根据站点ID获取站点信息（新主数据）
   * TODO (缓存)
   * @param stationId 站点ID
   * @return StationBasicDTO 站点信息
   */
  public StationBasicDTO getById(Integer stationId) {
    try {

      HttpResult<StationBasicDTO> stationInfo = metadataStationBasicService.getStationInfoById(stationId);

      if (HttpResult.isSuccess(stationInfo)) {
        StationBasicDTO data = stationInfo.getData();
        log.info("元数据通过站点id获取站点基本信息调用完成,结果={}", data);
        return data;
      }

      log.warn("元数据通过站点id获取站点基本信息调用失败,{},{}", stationId, stationInfo);
    } catch (Exception e) {
      log.warn("元数据通过站点id获取站点基本信息调用失败,{},{}", stationId, e.toString());
    }
    return null;
  }

  /**
   * 获取全部城市和站点信息
   *
   * @return CityStationInfoDTO
   */
  public CityStationInfoDTO getAllCityAndStation() {
    try {
      HttpResult<CityStationInfoDTO> httpResult = metadataStationBasicService.getAllCityAndStation();
      if (HttpResult.isSuccess(httpResult) && Objects.nonNull(httpResult.getData())) {
        return httpResult.getData();
      }
      return new CityStationInfoDTO();
    } catch (Exception e) {
      log.error("主数据获取全部城市和站点信息失败，{}", e.getMessage(), e);
      return new CityStationInfoDTO();
    }
  }
}
