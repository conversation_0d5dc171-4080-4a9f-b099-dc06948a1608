/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.mapcollection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.base.BaseModel;
import com.jdx.rover.monitor.domain.jsonhandler.FourLaneListTypeHandler;
import com.jdx.rover.monitor.domain.jsonhandler.StringListTypeHandler;
import com.jdx.rover.monitor.domain.jsonhandler.TaskRouteListTypeHandler;
import com.jdx.rover.monitor.enums.mapcollection.RouteColorEnum;
import com.jdx.rover.monitor.enums.mapcollection.TabTypeEnum;
import com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTask;
import com.jdx.rover.monitor.repository.mapper.mapcollection.MapCollectionTaskMapper;
import com.jdx.rover.monitor.vo.mapcollection.TaskRouteSaveVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/10 16:00
 * @description 勘查任务表Manager
 */
@Service
@RequiredArgsConstructor
public class MapCollectionTaskManager extends BaseManager<MapCollectionTaskMapper, MapCollectionTask> {

    /**
     * MapCollectionTaskMapper
     */
    private final MapCollectionTaskMapper mapCollectionTaskMapper;


    /**
     * 获取勘查任务
     *
     * @param stationId stationId
     * @param username username
     * @param tabType tabType
     * @return List<MapCollectionTask>
     */
    public List<MapCollectionTask> queryTaskList(Integer stationId, String username, String tabType) {
        return lambdaQuery()
            .eq(ObjectUtil.isNotNull(stationId), MapCollectionTask::getStationId, stationId)
            .eq(StrUtil.isNotEmpty(username), MapCollectionTask::getTaskCreator, username)
            .in(MapCollectionTask::getTaskStatus, TabTypeEnum.mappingStatus(tabType))
            .list();
    }

    /**
     * 创建勘查任务，任务状态默认勘察中，线路颜色默认蓝色
     *
     * @param taskName taskName
     * @param cityId cityId
     * @param stationId stationId
     * @param username username
     * @return taskId
     */
    public Integer createTask(String taskName, Integer cityId, Integer stationId, String username) {
        MapCollectionTask mapCollectionTask = new MapCollectionTask();
        mapCollectionTask.setTaskName(taskName);
        mapCollectionTask.setTaskCreator(username);
        mapCollectionTask.setStationId(stationId);
        mapCollectionTask.setCityId(cityId);
        mapCollectionTask.setTaskStatus(TaskStatusEnum.UNDER_EXPLORATION.getCode());
        mapCollectionTask.setRouteColor(RouteColorEnum.YELLOW.getCode());
        mapCollectionTaskMapper.insert(mapCollectionTask);
        return mapCollectionTask.getId();
    }

    /**
     * 编辑勘查任务
     *
     * @param taskId taskId
     * @param taskName taskName
     * @param cityId cityId
     * @param stationId stationId
     */
    public void updateTask(Integer taskId, String taskName, Integer cityId, Integer stationId) {
        lambdaUpdate().eq(BaseModel::getId, taskId)
            .set(MapCollectionTask::getTaskName, taskName)
            .set(MapCollectionTask::getCityId, cityId)
            .set(MapCollectionTask::getStationId, stationId)
            .update();
    }

    /**
     * 提交勘查任务
     *
     * @param taskId taskId
     * @param username username
     */
    public void submitTask(Integer taskId, String username) {
        lambdaUpdate().eq(BaseModel::getId, taskId)
            .set(MapCollectionTask::getTaskStatus, TaskStatusEnum.PENDING_CLAIM.getCode())
            .set(MapCollectionTask::getTaskSubmitTime, DateUtil.now())
            .set(StrUtil.isNotBlank(username), BaseModel::getModifyUser, username)
            .update();
    }

    /**
     * 更新勘查任务状态
     *
     * @param taskId taskId
     */
    public void updateTaskStatus(Integer taskId, TaskStatusEnum taskStatusEnum) {
        lambdaUpdate().eq(BaseModel::getId, taskId)
            .set(MapCollectionTask::getTaskStatus, taskStatusEnum.getCode())
            .update();
    }

    /**
     * 查询任务状态，无id对应任务返回NULL
     *
     * @param taskId taskId
     * @return MapCollectionTask
     */
    public MapCollectionTask queryTaskStatus(Integer taskId) {
        try {
            return lambdaQuery().eq(BaseModel::getId, taskId).select(MapCollectionTask::getTaskStatus).one();
        } catch (Exception ignored) {}
        return null;
    }

    /**
     * 保存勘查线路
     *
     * @param taskRouteSaveVO taskRouteSaveVO
     */
    public void saveTaskRoute(TaskRouteSaveVO taskRouteSaveVO) {
        lambdaUpdate().eq(BaseModel::getId, taskRouteSaveVO.getTaskId())
            .set(MapCollectionTask::getTotalMileage, taskRouteSaveVO.getTotalMileage())
            .set(StrUtil.isNotBlank(taskRouteSaveVO.getTaskRouteColor()), MapCollectionTask::getRouteColor, taskRouteSaveVO.getTaskRouteColor())
            .set(MapCollectionTask::getTaskRoute, taskRouteSaveVO.getTaskRouteList(), "typeHandler=" + TaskRouteListTypeHandler.class.getName())
            .set(MapCollectionTask::getRoadNames, taskRouteSaveVO.getRoadNameList().stream().filter(StrUtil::isNotEmpty).toList(), "typeHandler=" + StringListTypeHandler.class.getName())
            .set(MapCollectionTask::getFourLane, taskRouteSaveVO.getFourLaneList(), "typeHandler=" + FourLaneListTypeHandler.class.getName())
            .update();
    }

    /**
     * 删除勘查任务线路
     *
     * @param taskId taskId
     */
    public void deleteTaskRoute(Integer taskId) {
        LambdaUpdateWrapper<MapCollectionTask> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapCollectionTask::getId, taskId);
        wrapper.set(MapCollectionTask::getTaskRoute, null);
        wrapper.set(MapCollectionTask::getTotalMileage, null);
        wrapper.set(MapCollectionTask::getFourLane, null);
        wrapper.set(MapCollectionTask::getRoadNames, null);
        update(wrapper);
    }

    /**
     * 获取任务
     *
     * @param taskId taskId
     * @return MapCollectionTask
     */
    public MapCollectionTask queryTask(Integer taskId) {
        try {
            return lambdaQuery().eq(BaseModel::getId, taskId).one();
        } catch (Exception ignored) {}
        return null;
    }

    /**
     * 获取未关闭的任务
     * 站点ID为空，返回全部站点的任务
     * 过滤掉状态任务：TASK_CLOSED、MAP_IN_PROGRESS、MAP_RELEASE, TASK_DELETED
     *
     * @return List<MapCollectionTask>
     */
    public List<MapCollectionTask> queryFilterItem(Integer stationId) {
        return lambdaQuery()
            .eq(stationId != null, MapCollectionTask::getStationId, stationId)
            .notIn(MapCollectionTask::getTaskStatus, List.of(TaskStatusEnum.TASK_CLOSED.getCode(), TaskStatusEnum.MAP_IN_PROGRESS.getCode(), TaskStatusEnum.MAP_RELEASE.getCode(), TaskStatusEnum.TASK_DELETED.getCode()))
            .select(MapCollectionTask::getTaskCreator, MapCollectionTask::getCityId, MapCollectionTask::getStationId)
            .list();
    }

    /**
     * 查询用户权限下站点关联任务列表
     *
     * @param stationList 用户权限站点列表
     * @return 任务列表
     */
    public List<MapCollectionTask> searchTaskByUserStation(List<StationBasicDTO> stationList) {
        if (CollUtil.isEmpty(stationList)) {
            return Lists.newArrayList();
        }

        return lambdaQuery()
            .in(MapCollectionTask::getStationId, stationList.stream().map(StationBasicDTO::getStationId).collect(Collectors.toList()))
            .ne(MapCollectionTask::getTaskStatus, TaskStatusEnum.TASK_DELETED.getCode())
            .select(BaseModel::getId, MapCollectionTask::getTaskName)
            .list();
    }
}
