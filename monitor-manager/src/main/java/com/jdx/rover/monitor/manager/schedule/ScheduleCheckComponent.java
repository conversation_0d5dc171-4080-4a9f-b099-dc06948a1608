/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.yomahub.liteflow.core.NodeSwitchComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <p>
 * This is a vehicle realtime state service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component("checkSchedule")
public class ScheduleCheckComponent extends NodeSwitchComponent {

  @Override
  public boolean isAccess() {
    ScheduleTask scheduleTask = getSlot().getRequestData();
    return checkScheduleTaskAndScheduleState(scheduleTask);
  }

  @Override
  public String processSwitch() throws Exception {
    ScheduleTask scheduleTask = getSlot().getRequestData();
    if (scheduleTask.getIsFinished() == 1 && StringUtils.equals(scheduleTask.getVehicleScheduleState(),
            VehicleScheduleState.WAITING.getVehicleScheduleState())) {
      return LiteFlowTaskEnum.DELSCHEDULE.getType();
    }
    return LiteFlowTaskEnum.UPDATESCHEDULE.getType();
  }

  private boolean checkScheduleTaskAndScheduleState(ScheduleTask scheduleTask) {
    if (StringUtils.isBlank(scheduleTask.getVehicleName())) {
      return false;
    } else if (StringUtils.isBlank(scheduleTask.getScheduleName())) {
      return false;
    } else if (StringUtils.equalsAny(scheduleTask.getVehicleScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(), VehicleScheduleState.DELIVERY.getVehicleScheduleState()) &&
            !StringUtils.equalsAny(scheduleTask.getScheduleTaskType(), TaskType.DELIVERY.getTaskType(), TaskType.DROPOFF.getTaskType())) {
      return false;
    } else if (StringUtils.equalsAny(scheduleTask.getVehicleScheduleState(), VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.LOADING.getVehicleScheduleState()) &&
            !StringUtils.equals(scheduleTask.getScheduleTaskType(), TaskType.LOADTASK.getTaskType())) {
      return false;
    } else if (StringUtils.equalsAny(scheduleTask.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(), VehicleScheduleState.UNLOADING.getVehicleScheduleState()) &&
            !StringUtils.equals(scheduleTask.getScheduleTaskType(), TaskType.UNLOADTASK.getTaskType())) {
      return false;
    }
    return true;
  }
}

