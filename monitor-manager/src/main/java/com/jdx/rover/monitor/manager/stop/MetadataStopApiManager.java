/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.stop;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.stop.StopBasicDTO;
import com.jdx.rover.metadata.domain.dto.user.UserStopInfoDto;
import com.jdx.rover.metadata.jsf.service.stop.MetadataStopBasicService;
import com.jdx.rover.metadata.jsf.service.user.MetadataUserBasicService;
import com.jdx.rover.monitor.entity.UserStopInfoEntity;
import com.jdx.rover.monitor.repository.redis.UserStopRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 监控端获取停靠点信息
 * <AUTHOR>
 * @Date(2023-06-18)
 */
@Service
@Slf4j
public class MetadataStopApiManager {

  @Autowired
  private MetadataStopBasicService metadataStopBasicJsfService;
  @Autowired
  private MetadataUserBasicService metadataUserBasicService;
  @Autowired
  private UserStopRepository userStopRepository;

  /**
   * 通过站点ID获取停靠点信息列表
   *
   * @param stationId 站点ID
   */
  public List<StopBasicDTO> getStopByStationId(Integer stationId) {
    List<StopBasicDTO> stopList = new ArrayList<>();
    try {
      HttpResult<List<StopBasicDTO>> result = metadataStopBasicJsfService.getStopByStationId(stationId, 1, 200);
      if (!HttpResult.isSuccess(result) || result.getData() == null) {
        return stopList;
      }
      log.info("元数据通过站点id{}获取停靠点基本信息调用完成,结果={}", JsonUtils.writeValueAsString(result));
      return result.getData();
    } catch (Exception e) {
      log.warn("元数据通过站点id{}获取停靠点基本信息调用失败", stationId);
    }
    return stopList;
  }

  public StopBasicDTO getStopInfoById(Integer stopId) {
    try {
      HttpResult<StopBasicDTO> result = metadataStopBasicJsfService.getStopById(stopId);
      if (!HttpResult.isSuccess(result) || result.getData() == null) {
        return null;
      }
      log.info("元数据通过停靠点id{}获取停靠点基本信息调用完成,结果={}", JsonUtils.writeValueAsString(result));
      return result.getData();
    } catch (Exception e) {
      log.warn("元数据通过停靠点id{}获取停靠点基本信息调用失败", stopId);
    }
    return null;
  }

  /**
   * 根据用户名获取停靠点列表信息（新主数据）
   * @param userName 用户名
   * @return UserStopInfoDto 用户站点信息
   */
  public List<UserStopInfoEntity> getStopListByUser(String userName) {
    List<UserStopInfoEntity> result = userStopRepository.get(userName);
    if (CollectionUtils.isNotEmpty(result)) {
      return result;
    }

    try {
      HttpResult<List<UserStopInfoDto>> stationInfo = metadataUserBasicService.getStopListByUserName(userName);
      if (HttpResult.isSuccess(stationInfo)) {
        List<UserStopInfoDto> data = stationInfo.getData();
        log.info("元数据通过用户名{}获取停靠点列表信息调用完成,结果={}", userName, data);
        if (CollectionUtils.isNotEmpty(data)) {
          List<UserStopInfoEntity> stopInfoList = data.stream().map(stopInfo -> {
            UserStopInfoEntity stopInfoEntity = new UserStopInfoEntity();
            stopInfoEntity.setId(stopInfo.getStopId());
            stopInfoEntity.setName(stopInfo.getStopName());
            stopInfoEntity.setStopSource(stopInfo.getStopSource());
            return stopInfoEntity;
          }).collect(Collectors.toList());
          userStopRepository.set(userName, stopInfoList);
          return stopInfoList;
        }
      }
      log.warn("元数据通过用户名{}获取停靠点列表信息调用完成,失败,{}", userName, JsonUtils.writeValueAsString(stationInfo));
    } catch (Exception e) {
      log.warn("元数据通过用户名{}获取停靠点列表信息调用失败,{},{}", userName, e);
    }
    return new ArrayList<>();
  }
}
