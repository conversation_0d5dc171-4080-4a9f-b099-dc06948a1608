/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <p>
 * This is static class that provides configuration properties for rover video.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "rover-video")
@Data
@Validated
public class RoverVideoProperties {
  /**
   * <p>
   * Represents the url.
   * </p>
   */
  @NotNull
  @NotBlank
  private String url = "https://rover-video-lbs-dev.jdl.cn/url/play/";

  /**
   * <p>
   * Represents the multi url.
   * </p>
   */
  private String multiUrl = "http://rover-video-lbs-dev.jdl.cn/video/lbs/url/play/quantity";

  private String snapshotUrl = "http://rover-video-lbs-staging.jd.local/video/process/snapshot/request";

  private String historyPicUrl = "http://rover-video-process-staging.jd.local/video/process/snapshot/history";

  private String historySearchUrl = "http://rover-video-process-staging.jd.local/video/process/history/contain";

  /**
   * <p>
   * Represents the uid.
   * </p>
   */
  @NotBlank
  private String uid = "jdx";

  /**
   * <p>
   * Represents the key.
   * </p>
   */
  @NotBlank
  private String key = "cb0f1cf005d18ad1757f1a739ace63b5c2f2a449";

}
