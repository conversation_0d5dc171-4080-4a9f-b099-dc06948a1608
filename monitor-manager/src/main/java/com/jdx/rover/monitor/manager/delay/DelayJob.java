/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.delay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * 延迟任务参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DelayJob implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入参
     */
    private Map<String, Object> jobParam;

    /**
     * 执行bean
     */
    private String beanName;
}
