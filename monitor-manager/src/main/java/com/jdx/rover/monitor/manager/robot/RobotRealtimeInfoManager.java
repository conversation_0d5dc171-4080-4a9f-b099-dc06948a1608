/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager.robot;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.PdaStatusStatisticInfoDTO;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.monitor.repository.mapper.RobotRealtimeInfoMapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 机器人设备实时信息manager类
 *
 * <AUTHOR>
 */
@Service
public class RobotRealtimeInfoManager extends BaseManager<RobotRealtimeInfoMapper, RobotRealtimeInfo> {

  /**
   * 批量保存实体。
   * @param dataList RobotRealtimeInfo实体列表。
   */
  @Transactional(rollbackFor = Exception.class)
  public void saveBatchData(List<RobotRealtimeInfo> dataList) {
    this.saveBatch(dataList);
  }

  /**
   * 批量更新实体。
   * @param dataList RobotRealtimeInfo实体列表。
   */
  public void updateBatch(List<RobotRealtimeInfo> dataList) {
    getBaseMapper().batchUpdateGroup(dataList);
  }

  /**
   * 批量删除实体。
   * @param dataList RobotRealtimeInfo实体列表。
   */
  public void deleteBatch(List<RobotRealtimeInfo> dataList) {
    getBaseMapper().batchDelete(dataList);
  }

  /**
   * 批量更新设备状态。
   * @param deviceList 设备信息列表。
   */
  public void batchUpdateState(List<RobotRealtimeInfo> deviceList) {
    getBaseMapper().batchUpdateState(deviceList);
  }

  /**
   * 批量更新设备任务模式。
   * @param deviceList 设备信息列表。
   */
  public void batchUpdateWorkMode(List<RobotRealtimeInfo> deviceList) {
    getBaseMapper().batchUpdateWorkMode(deviceList);
  }

  /**
   * 状态分组统计设备数
   * @param RobotRealtimeInfoQueryWrapper 设备查询条件。
   */
  public List<PdaStatusStatisticInfoDTO> getGroupCountByStatus(LambdaQueryWrapper<RobotRealtimeInfo> RobotRealtimeInfoQueryWrapper) {
    return getBaseMapper().getGroupCountByStatus(RobotRealtimeInfoQueryWrapper);
  }

  /**
   * 获取设备分组
   */
  @Cacheable(value = LocalCacheConstant.ROBOT_DEVICE_GROUP_NO, key =  "#productKey + '_' + #deviceName")
  public String getGroupNoNyDevice(String productKey, String deviceName) {
    LambdaQueryWrapper<RobotRealtimeInfo> lambdaQueryWrapper = new LambdaQueryWrapper();
    lambdaQueryWrapper.select(RobotRealtimeInfo::getGroupOne);
    lambdaQueryWrapper.eq(RobotRealtimeInfo::getDeviceName, deviceName);
    lambdaQueryWrapper.eq(RobotRealtimeInfo::getProductKey, productKey);
    RobotRealtimeInfo robotRealtimeInfo = this.getOne(lambdaQueryWrapper);
    return Optional.ofNullable(robotRealtimeInfo).map(robot -> robot.getGroupOne()).orElse("");
  }

  /**
   * 获取设备详情
   */
  public RobotRealtimeInfo getDeviceNyName(String productKey, String deviceName) {
    LambdaQueryWrapper<RobotRealtimeInfo> lambdaQueryWrapper = new LambdaQueryWrapper();
    lambdaQueryWrapper.eq(RobotRealtimeInfo::getDeviceName, deviceName);
    lambdaQueryWrapper.eq(RobotRealtimeInfo::getProductKey, productKey);
    RobotRealtimeInfo robotRealtimeInfo = this.getOne(lambdaQueryWrapper);
    return robotRealtimeInfo;
  }


}