/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.manager.shadow;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.shadow.api.domain.vo.ShadowDataCollectionSceneAddVO;
import com.jdx.rover.shadow.api.jsf.ShadowDataCollectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 影子系统数采相关manager
 * </p>
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShadowDataCollectionManager {

    /**
     * 影子数据采集服务接口，用于处理与影子系统数据采集相关的业务逻辑
     */
    private final ShadowDataCollectionService shadowDataCollectionService;

    /**
     * 增加影子场景事件
     * @param shadowDataCollectionSceneAddVo
     * @return
     */
    public void addTrackingSceneEvent(ShadowDataCollectionSceneAddVO shadowDataCollectionSceneAddVo){
        try {
            log.info("场景增加影子事件入参:{}", shadowDataCollectionSceneAddVo);
            HttpResult<Void> httpResult = shadowDataCollectionService.addDataCollectionScene(shadowDataCollectionSceneAddVo);
            log.info("场景发布同步影子系统结果:{}",httpResult);
            if (HttpResult.isSuccess(httpResult) && httpResult.getData() !=null) {
                return;
            }
        } catch (Exception e) {
            log.error("增加影子场景事件系统异常",e);
            throw new AppException("增加影子场景事件失败！");
        }
    }

}