package com.jdx.rover.monitor.manager.vehicle;

import com.google.common.collect.Maps;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.schedule.api.domain.dto.monitor.DadaWorkStatusDTO;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMonitorJsfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车辆达达状态
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleDadaStateManager {
  @Autowired
  private ScheduleMonitorJsfService scheduleMonitorJsfService;

  /**
   * 获取车辆开收工状态
   *
   * @param vehicleList
   */
  public Map<String, DadaWorkStatusDTO> getDadaWorkStatusByVehicleList(List<String> vehicleList) {
    Map<String, DadaWorkStatusDTO> result = Maps.newHashMap();
    if (CollectionUtils.isEmpty(vehicleList)) {
      return result;
    }
    try {
      HttpResult<List<DadaWorkStatusDTO>> httpResult = scheduleMonitorJsfService.queryVehicleWorkStatus(vehicleList);
      if (HttpResult.isSuccess(httpResult)) {
        result = httpResult.getData().stream().collect(Collectors.toMap(DadaWorkStatusDTO::getVehicleName, Function.identity()));
      }
    } catch (Exception e) {
      log.warn("调度服务通过车辆列表获取达达状态调用失败,{},{}", JsonUtils.writeValueAsString(vehicleList), e);
    }
    log.info("调度服务通过车辆列表{}获取站点基本信息调用完成,结果={}", JsonUtils.writeValueAsString(vehicleList), result);
    return result;
  }

}
