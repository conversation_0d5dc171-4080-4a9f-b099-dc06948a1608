/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.issue;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.issue.IssueChangeDTO;
import com.jdx.rover.monitor.dto.issue.IssueDetailDTO;
import com.jdx.rover.monitor.dto.issue.IssueProcessDataDTO;
import com.jdx.rover.monitor.dto.issue.IssueRecordDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.AlarmEventErrorCodeEnum;
import com.jdx.rover.monitor.enums.IssueProcessDataEnum;
import com.jdx.rover.monitor.enums.issue.IssueOperateResultEnum;
import com.jdx.rover.monitor.enums.issue.IssueSourceEnum;
import com.jdx.rover.monitor.enums.issue.IssueStateEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.po.IssueAlarmRecord;
import com.jdx.rover.monitor.po.IssueOperateHistory;
import com.jdx.rover.monitor.po.IssueRecord;
import com.jdx.rover.monitor.repository.mapper.IssueAlarmAttachmentMapper;
import com.jdx.rover.monitor.repository.mapper.IssueRecordMapper;
import com.jdx.rover.monitor.repository.redis.IssueCacheRepository;
import com.jdx.rover.monitor.repository.redis.IssueNoCacheRepository;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.util.PageUtils;
import com.jdx.rover.monitor.search.IssueRecordSearch;
import com.jdx.rover.monitor.vo.IssueAlarmVO;
import com.jdx.rover.monitor.vo.IssueOperateHistoryVO;
import com.jdx.rover.monitor.vo.IssueRecordListRequestVO;
import com.jdx.rover.monitor.vo.IssueVO;
import com.jdx.rover.monitor.vo.MiniMonitorAlarmAttentionAddVO;
import com.jdx.rover.monitor.vo.MiniMonitorAlarmSubmissionAddVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IssueManager {

  @Autowired
  private IssueRecordMapper issueRecordMapper;

  @Autowired
  private IssueCacheRepository issueCacheRepository;

  @Autowired
  private IssueNoCacheRepository issueNoCacheRepository;

  @Autowired
  private VehicleManager vehicleManager;
  
  @Autowired
  private SingleVehicleManager singleVehicleManager;

  @Autowired
  private IssueAlarmManager issueAlarmManager;

  @Autowired
  private IssueAlarmAttachmentMapper issueAlarmAttachmentMapper;

  @Autowired
  private IssueOperateHistoryManager issueOperateHistoryManager;

  /**
   * 分页获取工单列表
   *
   */
  public PageDTO<IssueRecordDTO> pageSearch(PageVO pageVo, IssueRecordSearch issueRecordSearch) {
    LambdaQueryWrapper<IssueRecord> wrapper = new LambdaQueryWrapper<>();
    wrapper.notLike(IssueRecord::getState, IssueStateEnum.WAITING.getIssueState());
    if (issueRecordSearch.getVehicleName() != null) {
      wrapper.eq(IssueRecord::getVehicleName, issueRecordSearch.getVehicleName());
    }
    if (issueRecordSearch.getCityName() != null) {
      wrapper.eq(IssueRecord::getCityName, issueRecordSearch.getCityName());
    }
    if (issueRecordSearch.getStationName() != null) {
      wrapper.eq(IssueRecord::getStationName, issueRecordSearch.getStationName());
    }
    if (issueRecordSearch.getState() != null) {
      wrapper.eq(IssueRecord::getState, issueRecordSearch.getState());
    }
    if (issueRecordSearch.getBusinessType() != null) {
      wrapper.eq(IssueRecord::getVehicleBusinessType, issueRecordSearch.getBusinessType());
    }
    if (issueRecordSearch.getReportUser() != null) {
      wrapper.eq(IssueRecord::getReportUser, issueRecordSearch.getReportUser());
    }
    if (issueRecordSearch.getAlarmType() != null) {
      wrapper.eq(IssueRecord::getAlarmType, issueRecordSearch.getAlarmType());
    }
    if (issueRecordSearch.getStartTime() != null && issueRecordSearch.getEndTime() != null) {
      wrapper.ge(IssueRecord::getReportTime, issueRecordSearch.getStartTime());
      wrapper.le(IssueRecord::getReportTime, issueRecordSearch.getEndTime());
    }
    wrapper.orderByDesc(IssueRecord::getReportTime);
    IPage<IssueRecord> iPage = PageUtils.toMpPage(pageVo,IssueRecord.class);
    IPage<IssueRecord> operationLogs = issueRecordMapper.selectPage(iPage, wrapper);
    PageDTO<IssueRecordDTO> pageInfo = new PageDTO<>();
    pageInfo.setTotal(operationLogs.getTotal());
    pageInfo.setPageNum((int) operationLogs.getCurrent());
    pageInfo.setPageSize((int) operationLogs.getSize());
    pageInfo.setPages((int) operationLogs.getPages());
    pageInfo
        .setList(operationLogs.getRecords().stream().map(IssueRecord::toIssueRecordDto).collect(Collectors.toList()));
    return pageInfo;
  }

  /**
   * 增加运营告警
   */
  @Transactional
  public void addSubmissionAlarmIssue(MiniMonitorAlarmSubmissionAddVO alarmVo, String reportName, String issueNo) {
    IssueRecord issueRecord = new IssueRecord();
    issueRecord.setIssueNo(issueNo);
    issueRecord.setAlarmType(AlarmEventErrorCodeEnum.OPERATION_ALARM.getAlarmEventType());
    issueRecord.setVehicleName(alarmVo.getVehicleName());
    issueRecord.setAlarmTime(new Date());
    VehicleBasicDTO vehicleBasicDto = vehicleManager.getBasicByName(alarmVo.getVehicleName());
    if (vehicleBasicDto != null) {
      issueRecord.setVehicleBusinessType(vehicleBasicDto.getBusinessType());
      issueRecord.setCityName(vehicleBasicDto.getCityName());
      issueRecord.setStationName(vehicleBasicDto.getStationName());
      issueRecord.setOwnerUseCase(vehicleBasicDto.getOwnerUseCase());
    }
    issueRecord.setSource(IssueSourceEnum.OPERATION_ALARM.getValue());
    issueRecord.setState(IssueStateEnum.WAITING.getIssueState());
    issueRecord.setReportUser(reportName);
    issueRecordMapper.insert(issueRecord);
    issueAlarmManager.addOperateAlarmRecord(alarmVo.getTitle(),alarmVo.getMessage(),
            alarmVo.getVehicleName(),alarmVo.getAttachment(), issueNo);
    singleVehicleManager.pushSingleVehicleTakeOverAndIssue(alarmVo.getVehicleName());
    pushIssueStateUpdateEvent(issueRecord, new Date());
  }

  /**
   * 增加异常提醒
   */
  @Transactional
  public void addAttentionAlarmIssue(MiniMonitorAlarmAttentionAddVO alarmVo, String reportName, String issueNo) {
    IssueRecord issueRecord = new IssueRecord();
    issueRecord.setIssueNo(issueNo);
    issueRecord.setAlarmType(alarmVo.getAlarmEventType());
    issueRecord.setAlarmTime(alarmVo.getStartTimestamp());
    issueRecord.setVehicleName(alarmVo.getVehicleName());
    VehicleBasicDTO vehicleBasicDto = vehicleManager.getBasicByName(alarmVo.getVehicleName());
    if (vehicleBasicDto != null) {
      issueRecord.setVehicleBusinessType(vehicleBasicDto.getBusinessType());
      issueRecord.setCityName(vehicleBasicDto.getCityName());
      issueRecord.setStationName(vehicleBasicDto.getStationName());
      issueRecord.setOwnerUseCase(vehicleBasicDto.getOwnerUseCase());
    }
    issueRecord.setSource(IssueSourceEnum.EXCEPTION_REMIND.getValue());
    issueRecord.setState(IssueStateEnum.WAITING.getIssueState());
    issueRecord.setReportUser(reportName);
    issueRecordMapper.insert(issueRecord);
    issueAlarmManager.addVehicleAlarmRecord(alarmVo.getAlarmEventType(), alarmVo.getStartTimestamp(), alarmVo.getVehicleName(), issueNo, true);
    singleVehicleManager.pushSingleVehicleTakeOverAndIssue(alarmVo.getVehicleName());
    pushIssueStateUpdateEvent(issueRecord, alarmVo.getStartTimestamp());
  }

  /**
   * 增加技术支持工单
   */
  @Transactional
  public void addOperateAlarmIssue(IssueVO issueAddVo, String reportName, String issueNo) {
    IssueRecord issueRecord = new IssueRecord();
    issueRecord.setIssueNo(issueNo);
    IssueAlarmVO issueAlarm = issueAddVo.getAlarmList().get(0);
    issueRecord.setAlarmType(issueAlarm.getAlarmEventType());
    issueRecord.setAlarmTime(issueAlarm.getReportTime());
    issueRecord.setVehicleName(issueAddVo.getVehicleName());
    VehicleBasicDTO vehicleBasicDto = vehicleManager.getBasicByName(issueAddVo.getVehicleName());
    if (vehicleBasicDto != null) {
      issueRecord.setVehicleBusinessType(vehicleBasicDto.getBusinessType());
      issueRecord.setCityName(vehicleBasicDto.getCityName());
      issueRecord.setStationName(vehicleBasicDto.getStationName());
      issueRecord.setOwnerUseCase(vehicleBasicDto.getOwnerUseCase());
    }
    issueRecord.setResult(IssueOperateResultEnum.SUPPORT_PROCESS.getValue());
    issueRecord.setSource(IssueSourceEnum.SUPPORT_ALARM.getValue());
    issueRecord.setState(IssueStateEnum.PROCESS.getIssueState());
    issueRecord.setReportUser(reportName);
    issueRecord.setOperateUser(reportName);
    issueRecord.setStartTime(new Date());
    issueRecordMapper.insert(issueRecord);
    issueAddVo.getAlarmList().forEach(alarmEvent -> issueAlarmManager.addVehicleAlarmRecord(
            alarmEvent.getAlarmEventType(), alarmEvent.getReportTime(), issueAddVo.getVehicleName(), issueNo, true));
    if (!CollectionUtils.isEmpty(issueAddVo.getIssueHistoryList())) {
      issueAddVo.getIssueHistoryList().stream().forEach(t -> {
        IssueOperateHistory operateHistory = new IssueOperateHistory();
        operateHistory.setTitle(t.getOperateTitle());
        operateHistory.setMessage(t.getOperateMsg());
        operateHistory.setTimestamp(t.getOperateTime());
        operateHistory.setVehicleAlarmId(t.getAlarmId());
        operateHistory.setUserName(reportName);
        operateHistory.setIssueNo(issueNo);
        issueOperateHistoryManager.insert(operateHistory);
      });
    }
    singleVehicleManager.pushSingleVehicleTakeOverAndIssue(issueAddVo.getVehicleName());
    pushIssueStateUpdateEvent(issueRecord, new Date());
  }
  
  /**
   * 获取工单详情
   */
  public IssueDetailDTO getIssueDetailFromDb(String issueNo) {
    IssueRecord issueRecord = issueRecordMapper.getByIssueNo(issueNo);
    return Optional.ofNullable(issueRecord).map(t -> buildIssueDetailFormDb(t)).orElse(new IssueDetailDTO());
  }

  /**
   * 获取工单列表
   */
  public List<IssueDetailDTO> listIssue(IssueRecordListRequestVO issueRequestVo) {
    LambdaQueryWrapper<IssueRecord> wrapper = new LambdaQueryWrapper<>();
    if (issueRequestVo.getVehicleName() != null) {
      wrapper.eq(IssueRecord::getVehicleName, issueRequestVo.getVehicleName());
    }
    if (issueRequestVo.getAlarmType() != null) {
      wrapper.eq(IssueRecord::getAlarmType, issueRequestVo.getAlarmType());
    }
    if (issueRequestVo.getBusinessType() != null) {
      wrapper.eq(IssueRecord::getVehicleBusinessType, issueRequestVo.getBusinessType());
    }
    if (issueRequestVo.getStateList() != null) {
      wrapper.in(IssueRecord::getState, issueRequestVo.getStateList());
      if (issueRequestVo.getStateList().contains(IssueStateEnum.FINISH.getIssueState())) {
        wrapper.orderByDesc(IssueRecord::getEndTime);
      }
    }

    if (issueRequestVo.getStartTime() != null && issueRequestVo.getEndTime() != null) {
      wrapper.ge(IssueRecord::getStartTime, issueRequestVo.getStartTime());
      wrapper.le(IssueRecord::getStartTime, issueRequestVo.getEndTime());
    }
    if (issueRequestVo.getIsReported() != null) {
      if (issueRequestVo.getIsReported()) {
        wrapper.isNotNull(IssueRecord::getJiraNo);
      } else {
        wrapper.isNull(IssueRecord::getJiraNo);
        wrapper.orderByDesc(IssueRecord::getEndTime);
      }
    }
    List<IssueRecord> issueRecord = issueRecordMapper.selectList(wrapper);
    if (issueRecord == null) {
      return Lists.newArrayList();
    }
    return issueRecord.stream().map(t -> buildIssueDetailFormDb(t)).collect(Collectors.toList());
  }

  /**
   * 处理完成工单
   */
  public List<IssueRecord> listFinishedIssue(IssueRecordSearch issueRecordSearch) {
    LambdaQueryWrapper<IssueRecord> wrapper = new LambdaQueryWrapper<>();
    if (issueRecordSearch.getVehicleName() != null) {
      wrapper.eq(IssueRecord:: getVehicleName, issueRecordSearch.getVehicleName());
    }
    if (issueRecordSearch.getCityName() != null) {
      wrapper.eq(IssueRecord:: getCityName, issueRecordSearch.getCityName());
    }
    if (issueRecordSearch.getStationName() != null) {
      wrapper.eq(IssueRecord:: getStationName, issueRecordSearch.getStationName());
    }
    if (issueRecordSearch.getStartTime() != null && issueRecordSearch.getEndTime() != null) {
      wrapper.ge(IssueRecord:: getEndTime, issueRecordSearch.getStartTime());
      wrapper.le(IssueRecord:: getEndTime, issueRecordSearch.getEndTime());
    }
    wrapper.eq(IssueRecord:: getState, IssueStateEnum.FINISH.getIssueState());
    wrapper.orderByDesc(IssueRecord:: getEndTime);
    List<IssueRecord> issueRecord = issueRecordMapper.selectList(wrapper);
    return issueRecord;
  }

  /**
   * <p>
   * This method helps to save the issue cache.
   * </p>
   *
   * @param monitorIssueVo The view object for issue cache update request.
   * @return The basic response.
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  @Transactional
  public void update(IssueVO monitorIssueVo, String userName) {
    String issueNo = monitorIssueVo.getIssueNo();
    IssueRecord issueRecord = issueRecordMapper.getByIssueNo(monitorIssueVo.getIssueNo());
    if (StringUtils.equals(issueRecord.getState(), IssueStateEnum.WAITING.getIssueState())) {
      issueRecord.setOperateUser(userName);
      issueRecord.setState(IssueStateEnum.PROCESS.getIssueState());
      IssueAlarmRecord alarmRecord = issueAlarmManager.getAlarmByIssueNoAndAlarmType(issueNo, issueRecord.getAlarmType(), true);      
      pushIssueStateUpdateEvent(issueRecord, Optional.ofNullable(alarmRecord).map(alarm -> alarm.getTimestamp()).orElse(issueRecord.getStartTime()));
      if (issueRecord.getStartTime() == null) {
        issueRecord.setStartTime(new Date());
      }
    }
    if (StringUtils.equalsAny(monitorIssueVo.getOperateResult(), IssueOperateResultEnum.OPERATION_RESUME.getValue(), IssueOperateResultEnum.OPERATION_ABORT.getValue())) {
      issueRecord.setState(IssueStateEnum.FINISH.getIssueState());
      issueRecord.setEndTime(new Date());
      issueRecord.setResult(monitorIssueVo.getOperateResult());
      pushIssueStateUpdateEvent(issueRecord, new Date());
    }
    LambdaQueryWrapper<IssueRecord> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(IssueRecord::getIssueNo, issueNo);
    issueRecordMapper.update(issueRecord, wrapper);
    appendIssueOperateList(monitorIssueVo.getIssueHistoryList(), issueNo);
    appendIssueAlarmList(monitorIssueVo.getAlarmList(), monitorIssueVo.getVehicleName(), issueNo);
  }

  public Integer subscribeIssueVehicleAlarm(String vehicleName, String issueNo) {
    String topicName = RedisTopicEnum.VEHICLE_ISSUE_ALARM_RECORD.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new MessageListener<String>() {
      @Override
      public void onMessage(CharSequence channel, String msg) {
        IssueProcessDataDTO dto = JsonUtils.readValue(msg, IssueProcessDataDTO.class);
        if (StringUtils.equals(dto.getType(), IssueProcessDataEnum.ALARM.getType())) {
          VehicleAlarmDTO vehicleAlarmDto = JsonUtils.readValue(dto.getData(), VehicleAlarmDTO.class);
        if (vehicleAlarmDto == null) {
          return;
        }
        vehicleAlarmDto.getAlarmEventList().stream().forEach(alarm -> {
            issueAlarmManager.addVehicleAlarmRecord(alarm.getType(), alarm.getReportTime(), vehicleName, issueNo, false);
        });
      } else if (StringUtils.equals(dto.getType(), IssueProcessDataEnum.OPERATION.getType())) {
        IssueOperateHistory operateHistory = JsonUtils.readValue(dto.getData(), IssueOperateHistory.class);
        if (operateHistory == null) {
          return;
        }
        issueOperateHistoryManager.insert(operateHistory);
       }
      }
    };
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("Issue add topic={},listener count={}", topicName, rTopic.countListeners());
    return listenerId;
  }

  public void unSubscribeIssueVehicleAlarm(String vehicleName) {
    String topicName = RedisTopicEnum.VEHICLE_ISSUE_ALARM_RECORD.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    rTopic.removeAllListeners();
  }

  public void pushIssueStateUpdateEvent(IssueRecord issueRecord, Date startTime) {
    String topicName = RedisTopicEnum.ISSUE_EVENT_PREFIX.getValue();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    IssueChangeDTO issueChangeDto = new IssueChangeDTO();
    issueChangeDto.setIssueState(issueRecord.getState());
    if (!StringUtils.isEmpty(issueRecord.getResult())) {
      issueChangeDto.setOperateResultName(IssueOperateResultEnum.of(issueRecord.getResult()).getTitle());
    }
    issueChangeDto.setStartTime(startTime);
    issueChangeDto.setAlarmType(issueRecord.getAlarmType());
    issueChangeDto.setIssueNo(issueRecord.getIssueNo());
    issueChangeDto.setVehicleName(issueRecord.getVehicleName());
    if (rTopic.countSubscribers() > 0) {
      long result = rTopic.publish(JsonUtils.writeValueAsString(issueChangeDto));
      log.info("Send issue state change event, result is {}", result);
    }
  }

  private void appendIssueAlarmList(List<IssueAlarmVO> alarmList, String vehicleName, String issueNo) {
    if (CollectionUtils.isEmpty(alarmList)) {
      return;
    }
    List<Integer> alarmId = new ArrayList<>();
    alarmList.stream().forEach(alarm -> {
      if (alarm.getId() == null) {
        issueAlarmManager.addVehicleAlarmRecord(alarm.getAlarmEventType(), alarm.getReportTime(), vehicleName, issueNo, true);
      } else {
        alarmId.add(alarm.getId());
      }
    });
    if (alarmId.isEmpty()) {
      return;
    }
    IssueAlarmRecord vehicleAlarmRecord = new IssueAlarmRecord();
    vehicleAlarmRecord.setSelected(true);
    issueAlarmManager.updateSelectedVehicleAlarmRecord(vehicleAlarmRecord, alarmId);
  }

  private void appendIssueOperateList(List<IssueOperateHistoryVO> issueHistoryList, String issueNo) {
    if (CollectionUtils.isEmpty(issueHistoryList)) {
      return;
    }
    for (IssueOperateHistoryVO issueHistory : issueHistoryList) {
      IssueOperateHistory operateHistory = new IssueOperateHistory();
      operateHistory.setUserName(issueHistory.getOperateUser());
      operateHistory.setTimestamp(issueHistory.getOperateTime());
      operateHistory.setTitle(issueHistory.getOperateTitle());
      operateHistory.setMessage(issueHistory.getOperateMsg());
      operateHistory.setVehicleAlarmId(issueHistory.getAlarmId());
      operateHistory.setIssueNo(issueNo);
      issueOperateHistoryManager.insert(operateHistory);
    }
  }

   /**
   * <p>
   * Build the issue from db.
   * </p>
   *
   * @param issueRecord The issue db.
   * @return The corresponding issue.
   */
  private IssueDetailDTO buildIssueDetailFormDb(IssueRecord issueRecord) {
    IssueDetailDTO monitroIssueDetailDto = new IssueDetailDTO();
    monitroIssueDetailDto.setIssueNo(issueRecord.getIssueNo());
    monitroIssueDetailDto.setVehicleName(issueRecord.getVehicleName());
    monitroIssueDetailDto.setStationName(issueRecord.getStationName());
    monitroIssueDetailDto.setJiraNo(issueRecord.getJiraNo());
    monitroIssueDetailDto.setIssueState(issueRecord.getState());
    monitroIssueDetailDto
        .setIssueStateName(IssueStateEnum.of(issueRecord.getState()).getIssueStateName());
    if (StringUtils.isNotBlank(issueRecord.getResult())) {
      monitroIssueDetailDto.setOperateResult(issueRecord.getResult());
      monitroIssueDetailDto
              .setOperateResultName(IssueOperateResultEnum.of(issueRecord.getResult()).getTitle());
    }
    monitroIssueDetailDto.setReportTime(issueRecord.getReportTime());
    monitroIssueDetailDto.setReportUserName(issueRecord.getReportUser());
    monitroIssueDetailDto.setAcceptTime(issueRecord.getStartTime());
    monitroIssueDetailDto.setFinishTime(issueRecord.getEndTime());
    monitroIssueDetailDto.setOperateUserName(issueRecord.getOperateUser());
    monitroIssueDetailDto.setAlarmType(issueRecord.getAlarmType());
    monitroIssueDetailDto.setAlarmTime(issueRecord.getAlarmTime());
    return monitroIssueDetailDto;
  }
}
