/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.errorcode;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.dto.technical.ErrorCodeTranslateInfoDTO;
import com.jdx.rover.metadata.jsf.service.technical.MetadataErrorCodeTranslateInfoService;
import com.jdx.rover.monitor.repository.redis.metadata.ErrorCodeTranslateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 错误码翻译
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MetadataErrorCodeTranslateApiManager {

  private final MetadataErrorCodeTranslateInfoService metadataErrorCodeTranslateInfoJsfService;

  private final ErrorCodeTranslateRepository errorCodeTranslateRepository;

  /**
   * 根据错误码获取映射列表
   *
   * @return list
   */
  public List<ErrorCodeTranslateInfoDTO> getErrorCodeTranslateInfo(String errorCode) {
    List<ErrorCodeTranslateInfoDTO> result = new ArrayList<>();
    try {
      HttpResult<List<com.jdx.rover.metadata.domain.dto.technical.ErrorCodeTranslateInfoDTO>> httpResult =
              metadataErrorCodeTranslateInfoJsfService.getErrorCodeTranslateInfoByErrorCode(errorCode);
      if (!HttpResult.isSuccess(httpResult)) {
        return result;
      }
      if (CollectionUtils.isNotEmpty(httpResult.getData())) {
        result = httpResult.getData().stream().map(data -> {
          ErrorCodeTranslateInfoDTO translateInfo = new ErrorCodeTranslateInfoDTO();
          translateInfo.setErrorTranslate(data.getErrorTranslate());
          translateInfo.setErrorMessage(data.getErrorMessage());
          translateInfo.setErrorCode(data.getErrorCode());
          translateInfo.setId(data.getId());
          return translateInfo;
        }).sorted(new Comparator<ErrorCodeTranslateInfoDTO>() {
          @Override
          public int compare(ErrorCodeTranslateInfoDTO o1, ErrorCodeTranslateInfoDTO o2) {
            return StringUtils.compare(o2.getErrorMessage(), o1.getErrorMessage());
          }
        }).collect(Collectors.toList());
      }
      errorCodeTranslateRepository.setErrorCode(errorCode, result);
    } catch (Exception e) {
      log.warn("元数据根据错误码获取映射列表调用失败,{},{}", e.toString());
    }
    log.info("元数据根据错误码获取映射列表调用完成,结果={}", result);
    return result;
  }

}
