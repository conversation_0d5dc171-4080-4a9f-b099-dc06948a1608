/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.drive;

import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.proto.ProtoUtils;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import jdx.rover.remote.drive.proto.DriveHeader;
import jdx.rover.remote.drive.server.proto.DriveServerVehicle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 远程驾驶连接服务
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ButtonCommandManager {
    /**
     * mqtt发送的jsf服务
     */
    private final MqttSendJsfService mqttSendJsfService;

    /**
     * 发送连接mqtt消息
     */
    public HttpResult<?> sendCommand(DriveRemoteCommandVO vo) {
        DriveServerVehicle.ServerToVehicle.Builder infoBuilder = buildServerToVehicle(vo);
        infoBuilder.setMessageType(DriveServerVehicle.ServerToVehicle.MessageType.BUTTON_COMMAND);
        infoBuilder.setButtonCommand(DriveServerVehicle.ServerToVehicle.ButtonCommand.valueOf(vo.getRemoteCommandType()));
        DriveServerVehicle.ServerToVehicle info = infoBuilder.build();

        MqttMessageVO<byte[]> mqttMessageVO = new MqttMessageVO<>();
        mqttMessageVO.setTopic(MqttTopicEnum.SERVER_VEHICLE.getTopic() + vo.getCockpitNumber() + "/" + vo.getVehicleName());
        mqttMessageVO.setMessage(info.toByteArray());
        mqttSendJsfService.sendBytes(mqttMessageVO);
        log.info("服务端发送车端按钮命令mqtt数据topic={},data={}", mqttMessageVO.getTopic(), ProtoUtils.protoToJson(info));
        return HttpResult.success();
    }

    /**
     * 构建mqtt消息
     */
    private static DriveServerVehicle.ServerToVehicle.Builder buildServerToVehicle(DriveRemoteCommandVO vo) {
        DriveHeader.RequestHeader.Builder requestHeaderBuilder = DriveHeader.RequestHeader.newBuilder();
        requestHeaderBuilder.setRequestTime(System.currentTimeMillis());
        requestHeaderBuilder.setRequestId(RequestIdUtils.getRequestId());
        requestHeaderBuilder.setClientName(vo.getCockpitNumber());
        requestHeaderBuilder.setNeedResponse(true);
        requestHeaderBuilder.setRetry(false);

        DriveServerVehicle.ServerToVehicle.Builder infoBuilder = DriveServerVehicle.ServerToVehicle.newBuilder();
        infoBuilder.setVehicleName(vo.getVehicleName());
        infoBuilder.setCockpitName(vo.getCockpitNumber());
        infoBuilder.setRequestHeader(requestHeaderBuilder);
        return infoBuilder;
    }
}
