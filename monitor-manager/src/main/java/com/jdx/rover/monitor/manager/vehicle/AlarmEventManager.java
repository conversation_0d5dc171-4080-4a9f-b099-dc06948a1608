package com.jdx.rover.monitor.manager.vehicle;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.VehicleAlarmDTO;
import com.jdx.rover.monitor.api.domain.enums.AlarmSourceEnum;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmCategoryEnum;
import com.jdx.rover.monitor.dto.issue.IssueProcessDataDTO;
import com.jdx.rover.monitor.dto.vehicle.AlarmEventDTO;
import com.jdx.rover.monitor.dto.vehicle.AlarmEventRealtimeDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehicleAlarmListDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePncTaskAndTrafficLightDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.enums.IssueProcessDataEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 告警事件服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AlarmEventManager {
  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private VehicleAlarmRepository vehicleAlarmRepository;

  private static final String CPU_TO_HIGH = "ErrorCode: ERROR_GUARDIAN_SYSTEM_CPU_TOO_HIGH";

  private static final String CPU_TEMPATURE_TO_HIGH = "ErrorCode: ERROR_GUARDIAN_SYSTEM_CPU_TEMPATURE_TOO_HIGH";

  private static final String HZ_NOT_MATCH = "ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH";


  /**
   * 通过车辆名称获取对象
   *
   * @return
   */
  public WsResult<List<AlarmEventRealtimeDTO>> listAlarmEventWs(String username) {
    List<AlarmEventRealtimeDTO> list = listAlarmEvent(username);
    WsResult<List<AlarmEventRealtimeDTO>> wsResult = WsResult.success(WebsocketEventTypeEnum.ALL_ALARM_EVENT.getValue(), list);
    return wsResult;
  }

  /**
   * 通过车辆名称获取对象
   *
   * @return
   */
  public List<AlarmEventRealtimeDTO> listAlarmEvent(String username) {
    Set<String> vehicleNameSet = userVehicleNameRepository.get(username);

    Map<String, VehicleAlarmDO> vehicleAlarmDTOMap = vehicleAlarmRepository.listMap(new ArrayList<>(vehicleNameSet));
    vehicleAlarmDTOMap.entrySet().removeIf(entry -> entry.getValue() == null);

    List<AlarmEventRealtimeDTO> list = new ArrayList<>();
    Set<String> alarmVehicleNameSet = vehicleAlarmDTOMap.keySet();
    if (CollectionUtils.isEmpty(alarmVehicleNameSet)) {
      return list;
    }

    List<VehicleRealtimeInfoDTO> realtimeList = vehicleRealtimeRepository.list(new ArrayList<>(alarmVehicleNameSet));
    for (VehicleRealtimeInfoDTO realtime : realtimeList) {
      if (SystemStateManager.isOfflineOrLost(realtime.getSystemState())) {
        continue;
      }
      VehicleAlarmDO value = vehicleAlarmDTOMap.get(realtime.getVehicleName());
      if (value == null) {
        continue;
      }
      List<VehicleAlarmDO.VehicleAlarmEventDO> alarmEventList = value.getAlarmEventList();
      if (CollectionUtils.isEmpty(alarmEventList)) {
        continue;
      }
      VehicleAlarmDO.VehicleAlarmEventDO vehicleAlarmEventDTO = alarmEventList.get(0);
      AlarmEventRealtimeDTO alarmEventRealtimeDTO = buildAlarmEvent(value.getVehicleName(), vehicleAlarmEventDTO.getType(), vehicleAlarmEventDTO.getReportTime());
      list.add(alarmEventRealtimeDTO);
    }
    return list;
  }

  /**
   * 保存或删除告警事件
   *
   * @param lastAlarm
   *
   */
  public List<VehicleAlarmDO.VehicleAlarmEventDO> saveOrRemoveAlarmEvent(List<VehicleAlarmDO.VehicleAlarmEventDO> lastAlarm, String vehicleName, List<VehicleAlarmEventDTO> alarmEventList, AlarmSourceEnum sourceEnum) {
    List<VehicleAlarmDO.VehicleAlarmEventDO> manualAlarmList =
            lastAlarm.stream().filter(alarm -> !StringUtils.equals(alarm.getSource(),
                    sourceEnum.getSource())).collect(Collectors.toList());
    List<VehicleAlarmDO.VehicleAlarmEventDO> currentAlarmList = new ArrayList<>();
    if (CollectionUtils.isEmpty(alarmEventList)
            && CollectionUtils.isEmpty(manualAlarmList)) {
      vehicleAlarmRepository.remove(vehicleName);
      log.info("告警事件消失,删除缓存![%s]", vehicleName);
    } else {
      if (CollectionUtils.isNotEmpty(alarmEventList)) {
        currentAlarmList.addAll(alarmEventList.stream().map(event -> buildAlarmInfo(event, sourceEnum)).collect(Collectors.toList()));
      }
      if (CollectionUtils.isNotEmpty(manualAlarmList)) {
        currentAlarmList.addAll(manualAlarmList);
      }
      currentAlarmList = currentAlarmList.stream().sorted(Comparator.comparing(tmp -> {
                VehicleAlarmCategoryEnum categoryEnum = VehicleAlarmCategoryEnum.of(tmp.getType());
                return categoryEnum.getPriority();
              })).collect(Collectors.toList());
      VehicleAlarmDO vehicleAlarmDto = new VehicleAlarmDO();
      vehicleAlarmDto.setVehicleName(vehicleName);
      vehicleAlarmDto.setAlarmEventList(currentAlarmList);
      vehicleAlarmDto.setRecordTime(new Date());
      vehicleAlarmRepository.add(vehicleName, vehicleAlarmDto);
    }
    return currentAlarmList;
  }

  /**
   * 过滤非任务下的CPU异常告警
   *
   * @param event 车辆异常事件
   * @param vehicleName 车辆
   */
  private static boolean filterIgnoreAlarm(VehicleAlarmEventDTO event, String vehicleName) {
    if (StringUtils.isBlank(event.getErrorMessage())) {
      return true;
    }
    if (event.getErrorMessage().startsWith(CPU_TO_HIGH) || event.getErrorMessage().startsWith(HZ_NOT_MATCH)) {
      String redisKey = RedisKeyEnum.TRAFFIC_LIGHT_SET_VEHICLE.getValue() + vehicleName;
      SingleVehiclePncTaskAndTrafficLightDTO dto = RedissonUtils.getObject(redisKey);
      if (Objects.isNull(dto) || !StringUtils.equals("ACTIVE", dto.getPncTaskType())) {
        log.info("过滤车辆{}非跑行任务中的异常{}", vehicleName, JsonUtils.writeValueAsString(event));
        return false;
      }
    }
    return true;
  }

  /**
   * 推送单车页数据
   *
   * @param vehicleName
   */
  public long pushChangeAlarmEvent(String vehicleName, String systemState) {
    AlarmEventRealtimeDTO alarmEventRealtimeDTO = new AlarmEventRealtimeDTO();
    alarmEventRealtimeDTO.setVehicleName(vehicleName);
    boolean offlineOrLost = SystemStateManager.isOfflineOrLost(systemState);
    if (!offlineOrLost) {
      VehicleAlarmDO vehicleAlarmDTO = vehicleAlarmRepository.getVehicleAlarmDTO(vehicleName);
      if (vehicleAlarmDTO != null && CollectionUtils.isNotEmpty(vehicleAlarmDTO.getAlarmEventList())) {
        VehicleAlarmDO.VehicleAlarmEventDO vehicleAlarmEventDTO = vehicleAlarmDTO.getAlarmEventList().get(0);
        alarmEventRealtimeDTO.setAlarmEvent(vehicleAlarmEventDTO.getType());
        alarmEventRealtimeDTO.setReportTime(vehicleAlarmEventDTO.getReportTime());
      }
    }
    long publishCnt = pushChangeAlarmEventData(alarmEventRealtimeDTO);
    return publishCnt;
  }

  /**
   * 推送单车页数据
   *
   * @param alarmEventRealtimeDTO
   */
  public long pushChangeAlarmEventData(AlarmEventRealtimeDTO alarmEventRealtimeDTO) {
    String topicName = RedisTopicEnum.ALARM_EVENT_PREFIX.getValue();
    RTopic topic = RedissonUtils.getRTopic(topicName);
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.CHANGE_ALARM_EVENT.getValue(), Lists.newArrayList(alarmEventRealtimeDTO));
    String jsonStr = JsonUtils.writeValueAsString(wsResult);
    long publishCnt = topic.publish(jsonStr);
    log.info("发送[{}]条小铃铛车辆告警,告警消息={}", publishCnt, jsonStr);
    return publishCnt;
  }

  /**
   * 推送工单页数据
   * @param vehicleName
   * @param vehicleAlarmDto
   */
  public void pushIssueAlarmEventData(String vehicleName, VehicleAlarmDTO vehicleAlarmDto) {
    String topicName = RedisTopicEnum.VEHICLE_ISSUE_ALARM_RECORD.getValue() + vehicleName;
    RTopic topic = RedissonUtils.getRTopic(topicName);
    if (topic.countSubscribers() > 0) {
      IssueProcessDataDTO dto = new IssueProcessDataDTO();
      dto.setType(IssueProcessDataEnum.ALARM.getType());
      dto.setData(JsonUtils.writeValueAsString(vehicleAlarmDto));
      long publishCnt = topic.publish(JsonUtils.writeValueAsString(dto));
      log.info("发送工单车辆告警[{}],告警消息={}", publishCnt, vehicleName, dto);
    }
  }

  /**
   * 通过车辆名称获取对象
   *
   * @param vehicleName 车辆名称
   * @return
   */
  public AlarmEventRealtimeDTO buildAlarmEvent(String vehicleName, String alarmEvent, Date reportTIme) {
    AlarmEventRealtimeDTO dto = new AlarmEventRealtimeDTO();
    dto.setVehicleName(vehicleName);
    dto.setAlarmEvent(alarmEvent);
    dto.setReportTime(reportTIme);
    return dto;
  }

  /**
   * 缓存告警发送对外告警
   */
  private VehicleAlarmDO.VehicleAlarmEventDO buildAlarmInfo(VehicleAlarmEventDTO alarmEvent, AlarmSourceEnum sourceEnum) {
    VehicleAlarmDO.VehicleAlarmEventDO alarmEventDo = new VehicleAlarmDO.VehicleAlarmEventDO();
    alarmEventDo.setType(alarmEvent.getType());
    alarmEventDo.setReportTime(alarmEvent.getReportTime());
    alarmEventDo.setErrorCode(alarmEvent.getErrorCode());
    alarmEventDo.setErrorLevel(alarmEvent.getErrorLevel());
    alarmEventDo.setErrorMessage(alarmEvent.getErrorMessage());
    alarmEventDo.setSource(sourceEnum.getSource());
    return alarmEventDo;
  }


  /**
   * 发送单车页车辆告警数据到Redis订阅主题。
   * @param vehicleName 车辆名称。
   * @param currentAlarm 当前车辆的告警信息列表。
   */
  public void pushSingleVehicleAlarmData(String vehicleName, List<AlarmInfoDTO> currentAlarm) {
    String topicName = RedisTopicEnum.SINGLE_VEHICLE_PREFIX.getValue() + vehicleName;
    RTopic topic = RedissonUtils.getRTopic(topicName);
    if (topic.countSubscribers() > 0) {
      SingleVehicleAlarmListDTO dto = new SingleVehicleAlarmListDTO();
      dto.setVehicleName(vehicleName);
      dto.setReportTime(new Date());
      if (CollectionUtils.isNotEmpty(currentAlarm)) {
        dto.setAlarmEventList(currentAlarm.stream().map(alarm -> {
          AlarmEventDTO eventDto = new AlarmEventDTO();
          eventDto.setAlarmEvent(alarm.getAlarmType());
          eventDto.setReportTime(alarm.getStartTime());
          return eventDto;
        }).collect(Collectors.toList()));
      }
      WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_ALARM.getValue(), dto);
      long publishCnt = topic.publish(JsonUtils.writeValueAsString(wsResult));
      log.info("发送{}单车页车辆告警[{}],告警消息={}", publishCnt, vehicleName, dto);
    }
  }
}
