package com.jdx.rover.monitor.manager.ticket;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.ticket.domain.dto.data.GetCockpitIssueInfoDTO;
import com.jdx.rover.ticket.domain.dto.data.GetTeamIssueDataDTO;
import com.jdx.rover.ticket.domain.dto.data.GetUserIssueInfoDTO;
import com.jdx.rover.ticket.domain.vo.data.GetCockpitIssueInfoVO;
import com.jdx.rover.ticket.domain.vo.data.GetTeamIssueDataVO;
import com.jdx.rover.ticket.domain.vo.data.GetUserIssueInfoVO;
import com.jdx.rover.ticket.jsf.service.TicketDataJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class IssueDataApiManager {

    private final TicketDataJsfService ticketDataJsfService;

    /**
     * 获取驾舱工单信息
     *
     * @param getCockpitIssueInfoVO getCockpitIssueInfoVO
     * @return List<GetCockpitIssueInfoDTO>
     */
    public List<GetCockpitIssueInfoDTO> getCockpitIssueInfo(GetCockpitIssueInfoVO getCockpitIssueInfoVO) {
        try {
            log.info("IssueDataApiClient call getCockpitIssueInfo request:[{}].", getCockpitIssueInfoVO);
            HttpResult<List<GetCockpitIssueInfoDTO>> httpResult = ticketDataJsfService.getCockpitIssueInfo(getCockpitIssueInfoVO);
            log.info("IssueDataApiClient call getCockpitIssueInfo response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("IssueDataApiClient call getCockpitIssueInfo exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取用户工单信息
     *
     * @param getUserIssueInfoVO getUserIssueInfoVO
     * @return List<GetUserIssueInfoDTO>
     */
    public List<GetUserIssueInfoDTO> getUserIssueInfo(GetUserIssueInfoVO getUserIssueInfoVO) {
        try {
            log.info("IssueDataApiClient call getUserIssueInfo request:[{}].", getUserIssueInfoVO);
            HttpResult<List<GetUserIssueInfoDTO>> httpResult = ticketDataJsfService.getUserIssueInfo(getUserIssueInfoVO);
            log.info("IssueDataApiClient call getUserIssueInfo response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("IssueDataApiClient call getUserIssueInfo exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取团队工单数据
     *
     * @param getTeamIssueDataVO getTeamIssueDataVO
     * @return List<GetTeamIssueDataDTO>
     */
    public List<GetTeamIssueDataDTO> getTeamIssueData(GetTeamIssueDataVO getTeamIssueDataVO) {
        try {
            log.info("IssueDataApiClient call getTeamIssueData request:[{}].", getTeamIssueDataVO);
            HttpResult<List<GetTeamIssueDataDTO>> httpResult = ticketDataJsfService.getTeamIssueData(getTeamIssueDataVO);
            log.info("IssueDataApiClient call getTeamIssueData response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("IssueQueryApiClient call getTeamIssueData exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }
}