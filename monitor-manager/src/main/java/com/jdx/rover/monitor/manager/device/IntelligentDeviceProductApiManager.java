/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.device;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.device.jsfapi.domain.dto.product.ProductModelListDTO;
import com.jdx.rover.device.jsfapi.domain.vo.product.ProductModelQueryVO;
import com.jdx.rover.device.jsfapi.service.server.product.IntelligentDeviceServerProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备产品服务管理
 * <AUTHOR>
 */
@Service
@Slf4j
public class IntelligentDeviceProductApiManager {

  /**
   * 设备服务接口
   */
  @Autowired
  public IntelligentDeviceServerProductService productService;

  /**
   * 获取设备详情
   */
  public List<ProductModelListDTO> getProductModuleList(String productKey) {
    List<ProductModelListDTO> result = null;
    try {
      ProductModelQueryVO productModelQueryVo = new ProductModelQueryVO();
      productModelQueryVo.setProductKey(productKey);
      HttpResult<List<ProductModelListDTO>> httpResult = productService.queryModelList(productModelQueryVo);;
      if (HttpResult.isSuccess(httpResult)) {
        result = httpResult.getData();
      }
      log.info("元数据通过产品获取型号列表调用完成,结果={}", result);
    } catch (Exception e) {
      log.warn("元数据通过产品{}获取型号列表调用失败", productKey, e);
    }
    return result;
  }


}
