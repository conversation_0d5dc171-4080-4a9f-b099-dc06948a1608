package com.jdx.rover.monitor.manager.jdme.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 京ME开放平台机器人配置
 */
@Data
public class JdmeConfigRobot {
    /**
     * 机器人ID
     */
    private String id = "00_ee824b500481492c";

    private String pin = "app.w8jfe2tb";
    /**
     * 运行环境
     */
    private String env = "PROD";
    /**
     * API的域名
     */
    private String host = "http://openme.jd.local";
    /**
     * 应用唯一的KEY标识
     */
    private String appKey = "zhinengche";
    /**
     * 应用的密钥
     */
    private String appSecret = "124A2644A08669F8";
    /**
     * 调用协同产品需要用到的参数，组编号
     */
    private String openTeamId = "f65e9fb91c8cca695105735ca51ce2e9";
    /**
     * 互动卡片模板编号
     */
    private String cardTemplateId = "templateMsgCard";
    /**
     * 群组编号
     */
    private String fixedGroupId;
    /**
     * 咨询人工PIN
     */
    private String manualPin;
    /**
     * 新群管理员
     */
    private String newGroupOwner;
    /**
     * 新群成员列表
     */
    private List<JdmeConfigRobotUser> newGroupMembers;
    /**
     * 固定的通知用户
     */
    private List<JdmeConfigRobotUser> atUsers;
}
