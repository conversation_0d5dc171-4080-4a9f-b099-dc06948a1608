
/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 车辆路口状态Manager
 *
 * <AUTHOR>
 */
@Slf4j
public class VehicleStateManager {
    /**
     * 是否停靠近路口或者在路口中
     *
     * @return
     */
    public static boolean isTakeOver(String vehicleState) {
        if (Objects.isNull(vehicleState)) {
            return false;
        }
        VehicleStateEnum vehicleStateEnum = VehicleStateEnum.valueOf(vehicleState);
        switch (vehicleStateEnum) {
            case LOCAL_CONTROL:
            case XBOX_CTRL:
            case REMOTE_CONTROL:
            case REMOTE_SUPER_CTRL:
            case REMOTE_MOBILE_CTRL:
            case REMOTE_JOYSTICK_CTRL:
            case REMOTE_JOYSTICK_SUPER_CTRL:
                return true;
        }
        return false;
    }
}
