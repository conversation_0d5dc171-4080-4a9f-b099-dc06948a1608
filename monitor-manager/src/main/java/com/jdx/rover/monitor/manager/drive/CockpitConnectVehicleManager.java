/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.drive;

import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.common.utils.proto.ProtoUtils;
import com.jdx.rover.monitor.entity.VehicleRemoteOperationStatusEnum;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.drive.command.DriveRemoteCommandTypeEnum;
import com.jdx.rover.monitor.enums.drive.error.DriveErrorEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.enums.vehicle.operate.AllowOperateEnum;
import com.jdx.rover.monitor.manager.vehicle.TakeOverManager;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import jdx.rover.remote.drive.control.proto.DriveControlDto;
import jdx.rover.remote.drive.control.proto.DriveControlDto.ControlConnect.ConnectOperation;
import jdx.rover.remote.drive.proto.DriveHeader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 远程驾驶连接服务
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class CockpitConnectVehicleManager {
    /**
     * 发送mqtt消息
     */
    private final MqttSendJsfService mqttSendJsfService;
    /**
     * 车辆状态
     */
    private final VehicleStatusRepository vehicleStatusRepository;
    /**
     * PC或小程序字符串
     */
    private static final String PC_AND_WEI_XIN_APP = "PC或小程序";
    /**
     * 监控车辆接管状态
     */
    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    /**
     * 发送连接mqtt消息
     */
    public HttpResult<Void> sendCommand(DriveRemoteCommandVO connectVO) {
        VehicleTakeOverEntity vehicleTakeOverEntity = vehicleTakeOverRepository.get(connectVO.getVehicleName());
        HttpResult<Void> validateResult = validateCommand(connectVO, vehicleTakeOverEntity);
        if (validateResult != null) {
            return validateResult;
        }
        if (Objects.equals(connectVO.getRemoteCommandType(), DriveRemoteCommandTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.name())) {
            // 虚假接管,保存接管状态
            connectSuccess(connectVO.getCockpitUserName(), connectVO.getCockpitNumber(), connectVO.getVehicleName());
            sendConnectCockpitMqtt(connectVO.getVehicleName(), connectVO.getCockpitNumber(), connectVO.getVehicleName(), ConnectOperation.CONNECT);
        } else {
            sendConnectCockpitMqtt(connectVO.getVehicleName(), connectVO.getCockpitNumber(), connectVO.getVehicleName(), ConnectOperation.DISCONNECT);
            sendConnectCockpitMqtt(connectVO.getCockpitNumber(), connectVO.getCockpitNumber(), connectVO.getVehicleName(), ConnectOperation.DISCONNECT);
            // 虚假释放
            disconnectSuccess(connectVO.getVehicleName());
        }
        return HttpResult.success();
    }

    /**
     * 连接成功
     */
    public void connectSuccess(String userName, String cockpitNumber, String vehicleName) {
        VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
        vehicleTakeOverEntity.setOperationStatus(VehicleRemoteOperationStatusEnum.TAKEOVER.getOperationStatus());
        vehicleTakeOverEntity.setUserName(userName);
        vehicleTakeOverEntity.setCommandSource(RemoteCommandSourceEnum.REMOTE_JOYSTICK.getCommandSource());
        vehicleTakeOverEntity.setCockpitNumber(cockpitNumber);
        vehicleTakeOverEntity.setOperateTime(new Date());
        vehicleTakeOverRepository.save(vehicleName, vehicleTakeOverEntity);

        Map<String, Object> paramMap = new ParamMap<VehicleStatusDO>()
                .addProperty(VehicleStatusDO::getCockpitNumber, cockpitNumber)
                .addProperty(VehicleStatusDO::getCockpitUserName, userName)
                .toMap();
        vehicleStatusRepository.putAllMapObject(vehicleName, paramMap);
    }

    /**
     * 断开连接成功
     */
    public void disconnectSuccess(String vehicleName) {
        vehicleStatusRepository.fastRemoveMapKey(vehicleName, VehicleStatusDO::getCockpitNumber, VehicleStatusDO::getCockpitUserName);
        vehicleTakeOverRepository.remove(vehicleName);
    }

    /**
     * 校验命令是否可以执行
     */
    private static HttpResult<Void> validateCommand(DriveRemoteCommandVO connectVO, VehicleTakeOverEntity vehicleTakeOverEntity) {
        String allowTakeOver = TakeOverManager.getAllowTakeOver(connectVO.getCockpitNumber(), vehicleTakeOverEntity);
        String commandType = connectVO.getRemoteCommandType();
        if (Objects.equals(commandType, DriveRemoteCommandTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.name())) {
            if (!Objects.equals(allowTakeOver, AllowOperateEnum.ALLOW.name())) {
                String msg = String.format(DriveErrorEnum.FORBID_ENTER_TAKE_OVER.getTitle(), vehicleTakeOverEntity.getUserName()
                        , Optional.ofNullable(vehicleTakeOverEntity.getCockpitNumber()).orElse(PC_AND_WEI_XIN_APP)
                        , connectVO.getVehicleName(), connectVO.getCockpitNumber());
                log.info(msg);
                return HttpResult.error(DriveErrorEnum.FORBID_ENTER_TAKE_OVER.getValue(), msg);
            }
        } else if (Objects.equals(commandType, DriveRemoteCommandTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.name())) {
            if (Objects.equals(allowTakeOver, AllowOperateEnum.FORBID.name())) {
                String msg = String.format(DriveErrorEnum.FORBID_EXIT_TAKE_OVER.getTitle(), vehicleTakeOverEntity.getUserName()
                        , Optional.ofNullable(vehicleTakeOverEntity.getCockpitNumber()).orElse(PC_AND_WEI_XIN_APP)
                        , connectVO.getVehicleName(), connectVO.getCockpitNumber());
                log.info(msg);
                return HttpResult.error(DriveErrorEnum.FORBID_EXIT_TAKE_OVER.getValue(), msg);
            }
        }
        return null;
    }

    /**
     * 发送连接mqtt消息
     */
    public void sendConnectCockpitMqtt(String clientName, String cockpitNumber, String vehicleName, ConnectOperation connectOperation) {
        DriveHeader.RequestHeader.Builder requestHeaderBuilder = DriveHeader.RequestHeader.newBuilder();
        requestHeaderBuilder.setRequestTime(System.currentTimeMillis());
        requestHeaderBuilder.setRequestId(RequestIdUtils.getRequestId());
        requestHeaderBuilder.setClientName(clientName);
        requestHeaderBuilder.setNeedResponse(true);

        requestHeaderBuilder.setRetry(false);
        DriveControlDto.ControlConnect.Builder infoBuilder = DriveControlDto.ControlConnect.newBuilder();
        infoBuilder.setVehicleName(vehicleName);
        infoBuilder.setCockpitName(cockpitNumber);
        infoBuilder.setConnectOperation(connectOperation);

        infoBuilder.setRequestHeader(requestHeaderBuilder);
        DriveControlDto.ControlConnect info = infoBuilder.build();

        MqttMessageVO<byte[]> mqttMessageVO = new MqttMessageVO<>();
        mqttMessageVO.setTopic(MqttTopicEnum.CONTROL_CONNECT_SERVER.getTopic() + clientName);
        mqttMessageVO.setMessage(info.toByteArray());
        mqttSendJsfService.sendBytes(mqttMessageVO);
        log.info("发送请求连接数据topic={},data={}", mqttMessageVO.getTopic(), ProtoUtils.protoToJson(info));
    }
}
