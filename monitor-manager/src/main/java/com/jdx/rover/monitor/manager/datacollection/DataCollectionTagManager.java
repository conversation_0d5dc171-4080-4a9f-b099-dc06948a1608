/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.manager.datacollection;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionTag;
import com.jdx.rover.monitor.repository.mapper.datacollection.DataCollectionTagMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据采集标签表 Manager 类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class DataCollectionTagManager extends BaseManager<DataCollectionTagMapper, DataCollectionTag> {

    /**
     * 获取所有标签列表
     *
     * @return List<DataCollectionTag>
     */
    @Cacheable(value = LocalCacheConstant.DATA_COLLECTION_TAG_LIST, unless = "#result?.size() == 0",
            key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).DATA_COLLECTION_TAG_LIST")
    public List<DataCollectionTag> listAllTags() {
        return this.list();
    }

    @LocalCacheEvict(value = LocalCacheConstant.DATA_COLLECTION_TAG_LIST,
            key = "T(com.jdx.rover.monitor.constant.LocalCacheConstant).DATA_COLLECTION_TAG_LIST")
    public Integer saveTag(DataCollectionTag dataCollectionTag) {
        this.save(dataCollectionTag);
        return dataCollectionTag.getId();
    }

    /**
     * 根据标签名称获取标签
     *
     * @param tagName 标签名称
     * @return DataCollectionTag
     */
    public DataCollectionTag getByTagName(String tagName) {
        LambdaQueryWrapper<DataCollectionTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataCollectionTag::getTagName, tagName);
        return this.getOne(queryWrapper);
    }
}