/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.delay;

import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;

import java.util.concurrent.TimeUnit;

/**
 * 延迟任务生产
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17
 */
public class DelayJobProducer {

    public static final String DELAY_QUEUE_NAME = "monitor-delay-queue";

    /**
     * 提交延迟任务
     */
    public static void submitJob(DelayJob delayJob, Long delay, TimeUnit timeUnit) {
        RBlockingQueue<DelayJob> blockingQueue = RedissonUtils.getRedissonClient().getBlockingQueue(DELAY_QUEUE_NAME);
        RDelayedQueue<DelayJob> delayedQueue = RedissonUtils.getRedissonClient().getDelayedQueue(blockingQueue);
        delayedQueue.offer(delayJob, delay, timeUnit);
    }

    /**
     * 取消延迟任务
     */
    public static boolean cancelJob(<PERSON>ay<PERSON>ob delayJob) {
        RBlockingQueue<DelayJob> blockingQueue = RedissonUtils.getRedissonClient().getBlockingQueue(DELAY_QUEUE_NAME);
        RDelayedQueue<DelayJob> delayedQueue = RedissonUtils.getRedissonClient().getDelayedQueue(blockingQueue);
        return delayedQueue.remove(delayJob);
    }
}
