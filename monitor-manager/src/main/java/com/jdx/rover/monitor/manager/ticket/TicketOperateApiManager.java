/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.ticket;

import com.jdx.rover.ticket.domain.vo.operate.AccidentOperateVO;
import com.jdx.rover.ticket.jsf.service.TicketOperateJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/16 10:34
 * @description 工单操作
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TicketOperateApiManager {

    /**
     * TicketOperateJsfService
     */
    private final TicketOperateJsfService ticketOperateJsfService;

    /**
     * 事故解除报备提醒
     *
     * @param eventId eventId
     */
    public void processAccident(String eventId) {
        try {
            AccidentOperateVO accidentOperateVO = new AccidentOperateVO();
            accidentOperateVO.setEventId(eventId);
            ticketOperateJsfService.processAccident(accidentOperateVO);
        } catch (Exception e) {
            log.error("TicketOperateApiManager processAccident exception.", e);
        }
    }
}
