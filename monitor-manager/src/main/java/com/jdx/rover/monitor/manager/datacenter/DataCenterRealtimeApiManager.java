package com.jdx.rover.monitor.manager.datacenter;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.warehouse.vehicle.VehicleRealtimeJsfDTO;
import com.jdx.rover.datacenter.domain.vo.warehouse.vehicle.SingleVehicleJsfVo;
import com.jdx.rover.datacenter.jsf.service.warehouse.VehicleRealtimeJsfService;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@NoArgsConstructor
public class DataCenterRealtimeApiManager {

    @Autowired
    private VehicleRealtimeJsfService vehicleRealtimeJsfService;

    /**
     * 查看实时信息
     * @param requestDto requestDto
     */
    public List<VehicleRealtimeJsfDTO> queryRealtimeInfo(SingleVehicleJsfVo requestDto) {
        List<VehicleRealtimeJsfDTO> result = null;
        try {
            HttpResult<List<VehicleRealtimeJsfDTO>> httpResult = vehicleRealtimeJsfService.queryHistoryRealtimeInfo(requestDto);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        }catch (Exception e) {
            log.error("调用warehouse查询实时信息失败,{}", e.toString());
        }
        log.info("调用warehouse查询实时信息成功,result:{}", JsonUtils.writeValueAsString(result));
        return result;
    }
}
