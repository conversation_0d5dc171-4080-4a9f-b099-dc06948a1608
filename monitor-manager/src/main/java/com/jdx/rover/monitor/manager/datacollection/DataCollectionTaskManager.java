/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.manager.datacollection;

import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionTask;
import com.jdx.rover.monitor.repository.mapper.datacollection.DataCollectionTaskMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;


/**
 * 数采任务manager
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Service
@RequiredArgsConstructor
public class DataCollectionTaskManager extends BaseManager<DataCollectionTaskMapper, DataCollectionTask> {

    /**
     * 根据车辆名称获取未完成任务
     * @param vehicleName 车辆名称
     * @return 未完成任务
     */
    public DataCollectionTask getUnfinishedTaskByVehicleName(String vehicleName) {
        return lambdaQuery()
            .eq(DataCollectionTask::getVehicleName, vehicleName)
            .isNull(DataCollectionTask::getEndTime)
            .one();
    }

    /**
     * 更新创建用户
     * @param id 任务id
     * @param createUser 创建用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCreateUser(Integer id, String createUser) {
        lambdaUpdate()
            .set(DataCollectionTask::getCreateUser, createUser)
            .eq(DataCollectionTask::getId, id)
            .update();
    }


    /**
     * 根据车辆名称及时间查询数据采集任务
     * @param vehicleName 车辆名称
     * @param reportTime 报告时间
     * @return 未结束的数据采集任务对象
     */
    public DataCollectionTask getCoverSceneTask(String vehicleName, Date reportTime) {
        return lambdaQuery()
                .eq(DataCollectionTask::getVehicleName, vehicleName)
                .le(DataCollectionTask::getStartTime, reportTime)
                .orderByDesc(DataCollectionTask::getStartTime) // 按时间倒排
                .last("LIMIT 1") // 取最后一条（即最新记录）
                .one();
    }

}