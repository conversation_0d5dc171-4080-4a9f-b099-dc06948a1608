/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.enums.schedule.OrderDeliveryStateUpdateEnum;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleOrderForMonitor;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * This is a vehicle realtime state service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@LiteflowComponent(id = "updateScheduleOrder", name = "更新调度订单")
public class ScheduleOrderUpdateComponent extends NodeComponent {

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  /**
   * <p>
   * Handle realtime schedule message.
   * </p>
   */
  @Override
  public void process() throws Exception {
    ScheduleOrderForMonitor scheduleTaskOrder = getSlot().getRequestData();
    List<ScheduleOrderForMonitor.MonitorOrderInfo> scheduleOrder = scheduleTaskOrder.getScheduleOrder();
    MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.getFromRedis(scheduleTaskOrder.getVehicleName());
    if (scheduleEntity == null || CollectionUtil.isEmpty(scheduleOrder) ||
            !StringUtils.equals(scheduleTaskOrder.getScheduleNo(), scheduleEntity.getScheduleNo())) {
      return;
    }
    Map<String, OrderDeliveryStateUpdateEnum> orderUpdateMap = new HashMap<>();
    scheduleOrder.stream().forEach(order -> {
      String taskType = order.getScheduleTaskType();
      String deliveryStatus = order.getDeliveryStatus();
      String loadMethod = order.getLoadMethod();
      if (StringUtils.equalsAny(TaskType.DELIVERY.getTaskType(), taskType)
              && order.getPackageIndex() != null && order.getPackageIndex() > 1) {
        return;
      }
      OrderDeliveryStateUpdateEnum updateEnum =
              OrderDeliveryStateUpdateEnum.valueOf(taskType, deliveryStatus, loadMethod);
      if (updateEnum == OrderDeliveryStateUpdateEnum.NOTASK_COMMON) {
        return;
      }
      if (updateEnum == OrderDeliveryStateUpdateEnum.DELIVERY_HALFWAY_TODROPOFF_SUCCESS || updateEnum == OrderDeliveryStateUpdateEnum.DROPOFF_TODROPOFF_SUCCESS) {
        if (!order.getIsAdd()) {
          return;
        }
      }
      updateEnum.getFunction().apply(scheduleEntity);
      orderUpdateMap.put(order.getOrderId(), updateEnum);
    });
    vehicleScheduleRepository.set(scheduleEntity);
    getSlot().setChainReqData(LiteFlowTaskEnum.UPDATESCHEDULESTOPORDER.getType(), orderUpdateMap);

  }

}

