/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.manager.shadow;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.shadow.api.domain.dto.ShadowVehicleRouteInfoDTO;
import com.jdx.rover.shadow.api.domain.vo.ShadowVehicleRouteVO;
import com.jdx.rover.shadow.api.jsf.ShadowVehicleRouteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 影子系统车辆路线manager
 * </p>
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShadowVehicleRouteManager {

    /**
     * 用于处理影子系统车辆路线相关业务逻辑的服务接口实例
     */
    private final ShadowVehicleRouteService vehicleRouteService;

    /**
     * 获取车辆历史轨迹
     * @param shadowVehicleRouteVo
     * @return
     */
    public ShadowVehicleRouteInfoDTO getVehicleHistoryRoute(ShadowVehicleRouteVO shadowVehicleRouteVo){
        try {
            HttpResult<ShadowVehicleRouteInfoDTO> httpResult = vehicleRouteService.getVehicleRoute(shadowVehicleRouteVo);
            log.info("获取车辆历史轨迹结果:{}", JsonUtils.writeValueAsString(httpResult));
            if (HttpResult.isSuccess(httpResult) && Objects.nonNull(httpResult.getData())) {
                return httpResult.getData();
            }
        } catch (Exception e) {
            log.error("获取车辆历史轨迹系统异常",e);
        }
        return null;
    }

}