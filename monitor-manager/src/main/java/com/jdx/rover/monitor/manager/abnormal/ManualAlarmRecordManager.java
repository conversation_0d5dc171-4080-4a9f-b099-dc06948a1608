/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager.abnormal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.ManualAlarmRecord;
import com.jdx.rover.monitor.repository.mapper.ManualAlarmRecordMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 人工告警记录
 *
 * <AUTHOR>
 */
@Service
public class ManualAlarmRecordManager extends BaseManager<ManualAlarmRecordMapper, ManualAlarmRecord> {

  /**
   * 查询车辆列表数据
   *
   * @param
   */
  /**
   * 查询车辆今天提报记录,最多显示100条
   *
   * @param
   */
  public List<ManualAlarmRecord> listTodayManualAlarmRecord(String vehicleName) {
    LambdaQueryWrapper<ManualAlarmRecord> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(ManualAlarmRecord::getVehicleName, vehicleName);

    LocalDate nowLocalDate = LocalDate.now();
    Date nowDate = Date.from(nowLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    wrapper.ge(ManualAlarmRecord::getReportTime, nowDate);

    wrapper.orderByDesc(ManualAlarmRecord::getReportTime);
    wrapper.last("limit 100");

    List<ManualAlarmRecord> alarmRecordList = this.list(wrapper);
    return alarmRecordList;
  }
}