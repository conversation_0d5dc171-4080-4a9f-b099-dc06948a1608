package com.jdx.rover.monitor.manager.vehicle;

import com.google.common.collect.Sets;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleCategoryEnum;
import com.jdx.rover.monitor.enums.vehicle.sort.VehicleSortTypeEnum;
import com.jdx.rover.monitor.repository.redis.UserAttentionRepository;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSelectTypeRepository;
import com.jdx.rover.monitor.repository.redis.sort.BusinessTypeScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.redis.sort.PowerScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.redis.sort.StationScoreSortedSetRepository;
import com.jdx.rover.monitor.repository.redis.sort.VehicleNameScoreSortedSetRepository;
import com.jdx.rover.monitor.vo.vehicle.MultiVehicleRealtimeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 多车页manager
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MultiVehicleManager {
  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;
  @Autowired
  private UserAttentionRepository userAttentionRepository;
  @Autowired
  private VehicleSelectTypeRepository vehicleSelectTypeRepository;
  @Autowired
  private BusinessTypeScoreSortedSetRepository businessTypeScoreSortedSetRepository;
  @Autowired
  private PowerScoreSortedSetRepository powerScoreSortedSetRepository;
  @Autowired
  private VehicleNameScoreSortedSetRepository vehicleNameScoreSortedSetRepository;
  @Autowired
  private StationScoreSortedSetRepository stationScoreSortedSetRepository;

  /**
   * 读取
   * 不能用retainAll,缓存取出来数据不能修改,否则数据不对
   *
   * @param vo
   * @return
   */
  public Set<String> getByCondition(MultiVehicleRealtimeVO vo, Set<String> userVehicleSet) {
    // 第一步 用户名下所有车辆set1
    if (CollectionUtils.isEmpty(userVehicleSet)) {
      return userVehicleSet;
    }

    // 是否有筛选下拉框
    boolean isSelect = CollectionUtils.isNotEmpty(vo.getVehicleUseCaseList()) || StringUtils.isNotBlank(vo.getVehicleBusinessType());

    // 第二步 根据筛选类型,获取所有车辆set2
    Set<String> result = userVehicleSet;
    if (isSelect) {
      Set<String> selectTypeSet = multiGet(vo.getVehicleBusinessType(), vo.getVehicleUseCaseList());
      // 只有车号排序,不分组
      result = Sets.intersection(userVehicleSet, selectTypeSet);
      if (CollectionUtils.isEmpty(userVehicleSet)) {
        log.info("用户名下车辆为空!username={},selectTypeSet={}", vo.getUsername(), selectTypeSet);
      }
    }
    if (isSimpleVehicleNameSort(vo)) {
      return result;
    }

    // 第三步 根据排序类型(车号,业务,电量) 获取分数排序set3
    Set<String> scoredSortedSet = Sets.newLinkedHashSet(this.getScoredSortedSetValue(vo.getSortType()));
    if (CollectionUtils.isEmpty(scoredSortedSet)) {
      log.info("用户名下车辆为空!username={},scoredSortedSet={}", vo.getUsername(), scoredSortedSet);
    }

    // 3个set交集,为排序筛选结果=set3交集set1交集set2
    result = Sets.intersection(scoredSortedSet, result);
    if (CollectionUtils.isEmpty(scoredSortedSet)) {
      log.info("用户名下车辆为空1!username={},userVehicleSet={}", vo.getUsername(), userVehicleSet);
    }
    return result;
  }

  /**
   * 通过排序类型,获取排序缓存ScoredSortedSet的value
   *
   * @param sortType
   * @return
   */
  public Collection<String> getScoredSortedSetValue(String sortType) {
    Collection<String> result;
    if (Objects.equals(VehicleSortTypeEnum.BUSINESS_TYPE.getValue(), sortType)) {
      result = businessTypeScoreSortedSetRepository.get();
    } else if (Objects.equals(VehicleSortTypeEnum.POWER.getValue(), sortType)) {
      result = powerScoreSortedSetRepository.get();
    } else if (Objects.equals(VehicleSortTypeEnum.STATION.getValue(), sortType)) {
      result = stationScoreSortedSetRepository.get();
    } else {
      result = vehicleNameScoreSortedSetRepository.get();
    }
    return result;
  }

  /**
   * 通过排序类型,获取排序缓存ScoredSortedSet的value
   *
   * @param sortType
   * @return
   */
  public List<Double> getScoredSortedSetScore(String sortType, List<String> vehicleNameList) {
    List<Double> scoreList = new ArrayList<>();
    if (CollectionUtils.isEmpty(vehicleNameList)) {
      return scoreList;
    }
    Map<String, Double> map;
    if (Objects.equals(VehicleSortTypeEnum.BUSINESS_TYPE.getValue(), sortType)) {
      map = businessTypeScoreSortedSetRepository.getValueAndScoreMap();
    } else if (Objects.equals(VehicleSortTypeEnum.POWER.getValue(), sortType)) {
      map = powerScoreSortedSetRepository.getValueAndScoreMap();
    } else if (Objects.equals(VehicleSortTypeEnum.STATION.getValue(), sortType)) {
      map = stationScoreSortedSetRepository.getValueAndScoreMap();
    } else {
      map = vehicleNameScoreSortedSetRepository.getValueAndScoreMap();
    }
    for (String vehicleName : vehicleNameList) {
      Double score = map.get(vehicleName);
      if (score == null) {
        log.info("getScoredSortedSetScore vehicleName={},sortType={},分数不存在", vehicleName, score);
      }
      scoreList.add(score);
    }
    return scoreList;
  }

  /**
   * 是否简单的车号排序,不分组
   *
   * @param vo
   * @return
   */
  private boolean isSimpleVehicleNameSort(MultiVehicleRealtimeVO vo) {
    if (StringUtils.equalsAny(vo.getCurrentCategory(), VehicleCategoryEnum.OFFLINE.getValue(),
            VehicleCategoryEnum.INTERESTED.getValue(), VehicleCategoryEnum.ALL.getValue())) {
      return true;
    }
    if (StringUtils.equals(vo.getCurrentCategory(), VehicleCategoryEnum.NORMAL.getValue()) &&
        StringUtils.equals(vo.getSortType(), VehicleSortTypeEnum.VEHICLE_NAME.getValue())) {
      return true;
    }
    return false;
  }

  /**
   * 下拉多选
   *
   * @param vehicleBusinessType
   * @param useCaseList
   * @return
   */
  private Set<String> multiGet(String vehicleBusinessType, List<String> useCaseList) {
    // 为空的时候,默认查询所有的
    if (CollectionUtils.isEmpty(useCaseList)) {
      return vehicleSelectTypeRepository.get(vehicleBusinessType, null);
    }
    Set<String> result = new HashSet<>();
    for (String useCase : useCaseList) {
      Set<String> tmp = vehicleSelectTypeRepository.get(vehicleBusinessType, useCase);
      result.addAll(tmp);
    }
    return result;
  }
}
