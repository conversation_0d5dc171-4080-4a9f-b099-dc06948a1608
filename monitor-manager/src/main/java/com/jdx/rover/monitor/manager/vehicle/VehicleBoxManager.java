package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.box.BoxGridInfoDto;
import com.jdx.rover.metadata.jsf.service.box.MetadataBoxBasicService;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMiniMonitorJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/16 18:33
 * @description 车辆货箱Manager
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleBoxManager {

    /**
     * 主数据货箱客户端
     */
    private final MetadataBoxBasicService metadataBoxBasicJsfService;

    /**
     * 调度远程指令
     */
    private final ScheduleMiniMonitorJsfService scheduleMiniMonitorJsfService;

    /**
     * 获取车辆箱体信息
     *
     * @param boxId 箱体ID
     * @return 车辆箱体基本信息
     */
    public BoxGridInfoDto getBoxById(Integer boxId) {
        if (null == boxId) {
            return null;
        }
        try {
            HttpResult<BoxGridInfoDto> boxGridInfoById = metadataBoxBasicJsfService.getBoxGridInfoById(boxId);
            BoxGridInfoDto data = boxGridInfoById.getData();
            log.info("元数据获取车辆箱体结果,{},{}", boxId, data);
            return data;
        } catch (Exception e) {
            log.warn("元数据获取箱体调用失败,{},{}", boxId, e.toString());
        }
        return null;
    }

    /**
     * 开箱
     *
     * @param vehicleName 车辆名称
     * @param gridNoList 格口编号列表
     */
    public void openBox(String vehicleName, List<String> gridNoList) {
        try {
            HttpResult httpResult = scheduleMiniMonitorJsfService.openBoxAll(vehicleName, gridNoList);
            log.info("调度开箱结果,{},{},{}", vehicleName, gridNoList, httpResult);
        } catch (Exception e) {
            log.warn("调度开箱失败,{},{},{}", vehicleName, gridNoList, e.toString());
        }
    }
}
