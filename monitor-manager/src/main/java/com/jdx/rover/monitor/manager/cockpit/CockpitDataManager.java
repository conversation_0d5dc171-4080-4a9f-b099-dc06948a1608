package com.jdx.rover.monitor.manager.cockpit;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitListDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitPageDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamListDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamPageDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitTeamUserPageDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitUserListDTO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitPageVO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamPageVO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitTeamUserPageVO;
import com.jdx.rover.metadata.jsf.service.cockpit.MetadataCockpitDataService;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: CockpitDataManager
 * @author: wangguotai
 * @create: 2024-06-20 09:22
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class CockpitDataManager {

    private final MetadataCockpitDataService metadataCockpitDataJsfService;

    /**
     * 获取座席团队列表
     *
     * @param username username
     * @return List<CockpitTeamListDTO>
     */
    public List<CockpitTeamListDTO> getCockpitTeamList(String username) {
        try {
            log.info("MetadataCockpitDataApiClient call getCockpitTeamList request:[{}].", username);
            HttpResult<List<CockpitTeamListDTO>> httpResult = metadataCockpitDataJsfService.getCockpitTeamList(username);
            log.info("MetadataCockpitDataApiClient call getCockpitTeamList response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataCockpitDataApiClient call getCockpitTeamList exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取座席列表
     *
     * @param username username
     * @return List<CockpitListDTO>
     */
    public List<CockpitListDTO> getCockpitList(String username) {
        try {
            log.info("MetadataCockpitDataApiClient call getCockpitList request:[{}].", username);
            HttpResult<List<CockpitListDTO>> httpResult = metadataCockpitDataJsfService.getCockpitList(username);
            log.info("MetadataCockpitDataApiClient call getCockpitList response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataCockpitDataApiClient call getCockpitList exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取座席用户列表
     *
     * @param username username
     * @return List<CockpitUserListDTO>
     */
    public List<CockpitUserListDTO> getCockpitUserList(String username) {
        try {
            log.info("MetadataCockpitDataApiClient call getCockpitUserList request:[{}].", username);
            HttpResult<List<CockpitUserListDTO>> httpResult = metadataCockpitDataJsfService.getCockpitUserList(username);
            log.info("MetadataCockpitDataApiClient call getCockpitUserList response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataCockpitDataApiClient call getCockpitUserList exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 分页获取驾舱
     *
     * @param cockpitPageVO cockpitPageVO
     * @return PageDTO<CockpitPageDTO>
     */
    public PageDTO<CockpitPageDTO> getCockpitPage(CockpitPageVO cockpitPageVO) {
        try {
            log.info("MetadataCockpitDataApiClient call getCockpitPage request:[{}].", cockpitPageVO);
            HttpResult<PageDTO<CockpitPageDTO>> httpResult = metadataCockpitDataJsfService.getCockpitPage(cockpitPageVO);
            log.info("MetadataCockpitDataApiClient call getCockpitPage response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataCockpitDataApiClient call getCockpitPage exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取座席团队分页
     *
     * @param cockpitTeamPageVO cockpitTeamPageVO
     * @return PageDTO<CockpitTeamPageDTO>
     */
    public PageDTO<CockpitTeamPageDTO> getCockpitTeamPage(CockpitTeamPageVO cockpitTeamPageVO) {
        try {
            log.info("MetadataCockpitDataApiClient call getCockpitTeamPage request:[{}].", cockpitTeamPageVO);
            HttpResult<PageDTO<CockpitTeamPageDTO>> httpResult = metadataCockpitDataJsfService.getCockpitTeamPage(cockpitTeamPageVO);
            log.info("MetadataCockpitDataApiClient call getCockpitTeamPage response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataCockpitDataApiClient call getCockpitTeamPage exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 分页获取驾驶团队成员信息
     *
     * @param cockpitTeamUserPageVO cockpitTeamUserPageVO
     * @return PageDTO<CockpitTeamUserPageDTO>
     */
    public PageDTO<CockpitTeamUserPageDTO> getCockpitTeamUserPage(CockpitTeamUserPageVO cockpitTeamUserPageVO) {
        try {
            log.info("MetadataCockpitDataApiClient call getCockpitTeamUserPage request:[{}].", cockpitTeamUserPageVO);
            HttpResult<PageDTO<CockpitTeamUserPageDTO>> httpResult = metadataCockpitDataJsfService.getCockpitTeamUserPage(cockpitTeamUserPageVO);
            log.info("MetadataCockpitDataApiClient call getCockpitTeamUserPage response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataCockpitDataApiClient call getCockpitTeamUserPage exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }
}