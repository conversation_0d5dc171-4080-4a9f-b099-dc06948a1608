package com.jdx.rover.monitor.manager.utils.vehicle;

import com.jdx.rover.metadata.api.domain.enums.SupplierEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleStopSourceEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 提供方工具类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/05
 **/
public class SupplierUtils {

    private SupplierUtils() {

    }

    /**
     * 校验车辆提供方
     *
     * @return 是否京东车
     */
    public static boolean isJdVehicle(String supplier) {
        return StringUtils.isBlank(supplier) || StringUtils.equals(supplier, SupplierEnum.JD.getValue());

    }

    /**
     * 校验京东停靠点
     *
     * @return 是否京东打点
     */
    public static boolean isJdStop(String stopSource) {
        return StringUtils.isBlank(stopSource) || StringUtils.equals(stopSource, VehicleStopSourceEnum.JD.getValue());

    }
}