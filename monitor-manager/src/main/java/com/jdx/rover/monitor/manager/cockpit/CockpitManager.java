/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.manager.cockpit;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitBasicInfoDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitInfoDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleStationInfoDTO;
import com.jdx.rover.metadata.domain.vo.cockpit.CockpitBindVehicleVO;
import com.jdx.rover.metadata.jsf.service.cockpit.MetadataCockpitInfoService;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 驾驶舱管理接口
 *
 * <AUTHOR>
 * @date 2023/05/20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CockpitManager {

    /**
     * 主数据座舱客户端
     */
    private final MetadataCockpitInfoService metadataCockpitInfoJsfService;

    /**
     * 通过驾驶舱编号,获取驾驶舱信息
     *
     * @param cockpitNumber
     * @return
     */
    public CockpitInfoDTO getCockpitInfo(String cockpitNumber) {
        CockpitInfoDTO result = null;
        try {
            HttpResult<CockpitInfoDTO> httpResult = metadataCockpitInfoJsfService.getCockpitInfo(cockpitNumber);
            if (HttpResult.isSuccess(httpResult)) {
                result = httpResult.getData();
            }
        } catch (Exception e) {
            log.warn("根据驾驶舱团队编号获取驾驶舱信息调用失败,{},{}", cockpitNumber, e.toString());
        }
        return result;
    }

    /**
     * 驾驶舱与车辆进行绑定
     *
     * @param cockpitNumber 驾驶舱 编号
     * @param vehicleList   车辆列表
     */
    public void cockpitBindVehicle(String cockpitNumber, String userName, List<String> vehicleList) {
        CockpitBindVehicleVO cockpitBindVehicleVo = new CockpitBindVehicleVO();
        cockpitBindVehicleVo.setCockpitNumber(cockpitNumber);
        cockpitBindVehicleVo.setVehicleNameList(vehicleList);
        cockpitBindVehicleVo.setOperator(userName);
        HttpResult<Object> result = metadataCockpitInfoJsfService.cockpitBindVehicle(cockpitBindVehicleVo);
        if (!HttpResult.isSuccess(result)) {
            throw new AppException(result.getMessage());
        }
    }

    /**
     * 根据驾舱编号获取绑定的车辆列表
     *
     * @param cockpitNumber 驾舱编号
     * @return List<VehicleStationInfoDTO> 车辆列表
     */
    public List<VehicleStationInfoDTO> getVehicleList(String cockpitNumber) {
        HttpResult<CockpitInfoDTO> cockpitInfo = metadataCockpitInfoJsfService.getCockpitInfo(cockpitNumber);
        if (HttpResult.isSuccess(cockpitInfo)) {
            return cockpitInfo.getData().getVehicleInfoList();
        }
        return null;
    }

    /**
     * 根据座席编号获取座席基础信息
     *
     * @param cockpitNumber cockpitNumber
     * @return CockpitBasicInfoDTO
     */
    public CockpitBasicInfoDTO getCockpitBasicInfo(String cockpitNumber) {
        try {
            log.info("MetadataCockpitClient call getCockpitBasicInfo request:[{}].", cockpitNumber);
            HttpResult<CockpitBasicInfoDTO> httpResult = metadataCockpitInfoJsfService.getCockpitBasicInfo(cockpitNumber);
            log.info("MetadataCockpitClient call getCockpitBasicInfo response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("MetadataCockpitClient call getCockpitBasicInfo exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }
}