package com.jdx.rover.monitor.manager.ticket;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.ticket.domain.dto.query.AlarmIssueDTO;
import com.jdx.rover.ticket.domain.dto.query.CockpitIssuesDTO;
import com.jdx.rover.ticket.domain.dto.query.IssueEventDTO;
import com.jdx.rover.ticket.domain.dto.query.VehicleCurrentIssueDTO;
import com.jdx.rover.ticket.domain.dto.query.VehicleIssuesDTO;
import com.jdx.rover.ticket.domain.vo.query.QueryAlarmIssueVO;
import com.jdx.rover.ticket.domain.vo.query.QueryIssueVO;
import com.jdx.rover.ticket.domain.vo.query.QueryVehicleVO;
import com.jdx.rover.ticket.domain.dto.query.IssueCheckDTO;
import com.jdx.rover.ticket.domain.vo.query.QueryCockpitVO;
import com.jdx.rover.ticket.jsf.service.TicketQueryJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: IssueQuerydomainManager
 * @author: wangguotai
 * @create: 2024-06-11 18:30
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class IssueQueryApiManager {

    private final TicketQueryJsfService ticketQueryJsfService;

    /**
     * 检查当前座席是否有工单
     *
     * @param queryCockpitVO queryCockpitVO
     * @return IssueCheckDTO
     */
    public IssueCheckDTO cockpitHasIssue(QueryCockpitVO queryCockpitVO) {
        try {
            log.info("ticketQueryJsfService call cockpitHasIssue request:[{}].", queryCockpitVO);
            HttpResult<IssueCheckDTO> httpResult = ticketQueryJsfService.cockpitHasIssue(queryCockpitVO);
            log.info("ticketQueryJsfService call cockpitHasIssue response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("ticketQueryJsfService call cockpitHasIssue exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取座席工单
     *
     * @param queryCockpitVO queryCockpitVO
     * @return CockpitIssuesDTO
     */
    public CockpitIssuesDTO queryCockpit(QueryCockpitVO queryCockpitVO) {
        try {
            log.info("ticketQueryJsfService call queryCockpit request:[{}].", queryCockpitVO);
            HttpResult<CockpitIssuesDTO> httpResult = ticketQueryJsfService.queryCockpit(queryCockpitVO);
            log.info("ticketQueryJsfService call queryCockpit response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("ticketQueryJsfService call queryCockpit exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 获取座席工单
     *
     * @param queryVehicleVo queryCockpitVO
     * @return CockpitIssuesDTO
     */
    public VehicleIssuesDTO queryIssueByVehicle(QueryVehicleVO queryVehicleVo) {
        try {
            log.info("ticketQueryJsfService call queryIssueByVehicle request:[{}].", queryVehicleVo);
            HttpResult<VehicleIssuesDTO> httpResult = ticketQueryJsfService.queryVehicle(queryVehicleVo);
            log.info("ticketQueryJsfService call queryIssueByVehicle response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("ticketQueryJsfService call queryIssueByVehicle exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     *工单关联的事件
     *
     * @param queryIssueVo queryIssueVo
     * @return IssueEventDTO
     */
    public IssueEventDTO queryIssueEventByIssue(QueryIssueVO queryIssueVo) {
        try {
            log.info("ticketQueryJsfService call queryIssueEventByIssue request:[{}].", queryIssueVo);
            HttpResult<IssueEventDTO> httpResult = ticketQueryJsfService.queryIssueEvent(queryIssueVo);
            log.info("ticketQueryJsfService call queryIssueEventByIssue response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("ticketQueryJsfService call queryIssueEventByIssue exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 查询车辆当前工单
     *
     * @param queryVehicleVO queryVehicleVO
     * @return VehicleCurrentIssueDTO
     */
    public VehicleCurrentIssueDTO queryVehicleCurrentIssue(QueryVehicleVO queryVehicleVO) {
        try {
            log.info("ticketQueryJsfService call queryVehicleCurrentIssue request:[{}].", queryVehicleVO);
            HttpResult<VehicleCurrentIssueDTO> httpResult = ticketQueryJsfService.queryVehicleCurrentIssue(queryVehicleVO);
            log.info("ticketQueryJsfService call queryVehicleCurrentIssue response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("ticketQueryJsfService call queryVehicleCurrentIssue exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 根据告警获取工单信息
     * @param queryAlarmIssueVO
     * @return
     */
    public List<AlarmIssueDTO> getIssueInfoByAlarm(QueryAlarmIssueVO queryAlarmIssueVO) {
        try {
            log.info("ticketQueryJsfService call getIssueInfoByAlarm request:[{}].", queryAlarmIssueVO);
            HttpResult<List<AlarmIssueDTO>> httpResult = ticketQueryJsfService.getIssueInfoByAlarm(queryAlarmIssueVO);
            log.info("ticketQueryJsfService call getIssueInfoByAlarm response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("ticketQueryJsfService call getIssueInfoByAlarm exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }
}