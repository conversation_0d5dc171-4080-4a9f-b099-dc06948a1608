package com.jdx.rover.monitor.manager.jdme.config;

import lombok.Data;

/**
 * 京ME开放平台接口API配置
 */
@Data
public class JdmeConfigApi {
    /**
     * 获取AppAccessToken<br>
     * appAccessToken 的最大有效期是 30 天。如果在有效期小于 10 分钟的情况下调用，会返回一个新的 appAccessToken，此时两个 appAccessToken 都是有效的。（不需要每次请求，推荐缓存，缓存时间参考返回的expireIn字段
     */
    private String appAccessToken = "/open-api/auth/v1/app_access_token";
    /**
     * 获取TeamAccessToken<br>
     * teamAccessToken 的最大有效期是 2 小时。如果在有效期小于 10 分钟的情况下调用，会返回一个新的 teamAccessToken，此时两个 teamAccessToken都是有效的。（不需要每次请求，推荐缓存，缓存时间参考返回的expireIn字段）
     */
    private String teamAccessToken = "/open-api/auth/v1/team_access_token";
    /**
     * 创建群<br>
     * 单个应用每天创建上限 不超过1000个
     */
    private String createGroup = "/open-api/suite/v1/timline/createGroup";
    /**
     * 添加群成员
     */
    private String addGroupMember= "/open-api/suite/v1/timline/addGroupMember";
    /**
     * 通过机器人发送消息
     */
    private String sendRobotMsg = "/open-api/suite/v1/timline/sendRobotMsg";
    /**
     * 发送互动卡片消息
     */
    private String sendJueMsg = "/open-api/suite/v1/timline/sendJUEMsg";
}
