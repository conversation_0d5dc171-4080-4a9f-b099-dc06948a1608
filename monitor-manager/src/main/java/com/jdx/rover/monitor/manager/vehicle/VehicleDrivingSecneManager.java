/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.map.api.domain.dto.GetLaneAndRoadTypeDTO;
import com.jdx.rover.monitor.manager.map.MapInfoManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.jodah.expiringmap.ExpirationPolicy;
import net.jodah.expiringmap.ExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 车辆行驶场景manager
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleDrivingSecneManager {

  /**
   * 地图服务
   */
  private final MapInfoManager mapInfoManager;

  /**
   * 按照车号存储
   */
  private static final ExpiringMap<String, GetLaneAndRoadTypeDTO> roadSceneMap = ExpiringMap.builder().maxSize(500)
          // 设置过期时间，如果key不设置过期时间，key永久有效。
          .expiration(5, TimeUnit.SECONDS)
          // 设置 Map的过期策略
          .expirationPolicy(ExpirationPolicy.CREATED)
          .build();

  /**
   * 获取行驶道路场景
   * @param vehicleName
   */
  public GetLaneAndRoadTypeDTO getVehicleDrivingRoadScene(String vehicleName, Double latitude, Double longitude) {
    GetLaneAndRoadTypeDTO roadScene = roadSceneMap.get(vehicleName);
    if (Objects.nonNull(roadScene)) {
      return roadScene;
    }
    GetLaneAndRoadTypeDTO getLaneAndRoadTypeResult = mapInfoManager.getLaneAndRoadTypeByPosition(latitude, longitude);
    if (StringUtils.isNotBlank(getLaneAndRoadTypeResult.getRoadType())) {
      roadSceneMap.put(vehicleName, getLaneAndRoadTypeResult);
    }
    return getLaneAndRoadTypeResult;
  }

}
