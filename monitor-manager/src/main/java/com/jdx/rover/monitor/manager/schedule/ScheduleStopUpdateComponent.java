/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.manager.schedule;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.monitor.dto.MonitorStationVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSchedulePncRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleStopOrderRepository;
import com.jdx.rover.schedule.api.domain.enums.StopTravelStatus;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTaskGoal;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.jsf.service.vehicle.ServerVehicleService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * This is a vehicle realtime state service.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@LiteflowComponent(id = "updateScheduleStop", name = "更新调度停靠点")
public class ScheduleStopUpdateComponent extends NodeComponent {

  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private VehicleSchedulePncRouteRepository pncRouteRepository;

  @Autowired
  private ServerVehicleService serverVehicleJsfService;

  @Autowired
  private VehicleManager vehicleManager;

  @Autowired
  private VehicleScheduleStopOrderRepository vehicleScheduleStopOrderRepository;

  @Override
  public boolean isAccess() {
    ScheduleTask scheduleTask = getSlot().getRequestData();
    if (CollectionUtils.isEmpty(scheduleTask.getScheduleTaskGoals()) || scheduleTask.getCurrentGoalIndex() == 0) {
      return false;
    }
    VehicleBasicDTO vehiclebasicDto = vehicleManager.getBasicByName(scheduleTask.getVehicleName());
    return vehiclebasicDto != null && StringUtils.equals(VehicleBusinessTypeEnum.DISPATCH.getValue(), vehiclebasicDto.getBusinessType());
  }

  /**
   * <p>
   * Handle realtime schedule message.
   * </p>
   */
  @Override
  public void process() throws Exception {
    ScheduleTask scheduleTask = getSlot().getRequestData();
    MonitorScheduleEntity scheduleEntity = getSlot().getChainReqData(LiteFlowTaskEnum.UPDATESCHEDULE.getType());
    try {
      if (scheduleTask.getCurrentGoalIndex() == 1) {
        // 初始规划导航信息
        initSchedulePncRouting(scheduleTask.getVehicleName(), scheduleEntity);
        // 推送前端调度变化通知
        pushVehicleScheduleUpdateEvent(scheduleTask.getVehicleName());
      }
      // 更新停靠点停靠状态
      updateScheduleStopList(scheduleTask, scheduleEntity);
      getSlot().setResponseData(scheduleEntity);
    }catch (Exception e) {
      log.error("Handle schedule exception ",e);
    }
  }

  /**
   * <p>
   * Init supervisor schedule stop entity when schedule created.
   * </p>
   *
   */
  private void updateScheduleStopList(ScheduleTask scheduleTask, MonitorScheduleEntity scheduleEntity) {
    LinkedList<MonitorScheduleStopEntity> stopEntities = scheduleEntity.getStop();
    List<ScheduleTaskGoal> taskGoalList = scheduleTask.getScheduleTaskGoals();
    MonitorScheduleStopEntity startStopEntity = null;
    LinkedList<MonitorScheduleStopEntity> updateStopEntities = new LinkedList<>();
    Date startTime = Optional.ofNullable(scheduleTask.getEstDepartureTime()).orElse(new Date());
    if (stopEntities.isEmpty()) {
      startStopEntity = new MonitorScheduleStopEntity();
      startStopEntity.setId(0);
      startStopEntity.setGoalId(0);
      startStopEntity.setName("起点");
      startStopEntity.setStopAction("START");
      startStopEntity.setTravelStatus(StopTravelStatus.DEPART.getTravelStatus());
      startStopEntity.setArrivedTime(startTime);
      startStopEntity.setStartTime(startTime);
      startStopEntity.setDepartTime(startTime);
    } else {
      startStopEntity = stopEntities.getFirst();
    }
    BigDecimal finishedMileag = BigDecimal.valueOf(0);
    for (ScheduleTaskGoal taskGoal : taskGoalList) {
      updateStopEntities.add(buildScheduleStopEntityFromScheduleGoal(taskGoal));
      if (StringUtils.equalsAny(taskGoal.getTravelStatus(), StopTravelStatus.DEPART.getTravelStatus(), StopTravelStatus.STAY.getTravelStatus())
              && taskGoal.getRoutingMileage() != null) {
        finishedMileag = finishedMileag.add(BigDecimal.valueOf(taskGoal.getRoutingMileage()));
      }
    }
    if (Double.compare(finishedMileag.doubleValue(), scheduleEntity.getFinishedMileage()) > 0) {
      scheduleEntity.setFinishedMileage(finishedMileag.doubleValue());
    }
    updateStopEntities.addFirst(startStopEntity);
    scheduleEntity.setStop(updateStopEntities);
  }

  private void initSchedulePncRouting(String vehicleName, MonitorScheduleEntity scheduleEntity) {
    Integer errorNavId = 999;
    try {
      HttpResult<VehiclePncInfoDTO> result = serverVehicleJsfService.getVehiclePnc(vehicleName);
      if (HttpResult.isSuccess(result) && result.getData() != null) {
        VehiclePncInfoDTO pncInfoDto = result.getData();
        if (pncInfoDto.getNavigationStop() != null) {
          List<MonitorRoutingPointEntity> routingPointEntityList = new LinkedList<>();
          pncInfoDto.getNavigationStop().stream().forEach(stop -> {
            stop.getRouting().stream().map(point ->
                    TransformUtility.toGCJ02Point(point.getLat(), point.getLon())).filter(mapPoint -> mapPoint != null)
                    .forEach(mapPointBO -> {
                      MonitorRoutingPointEntity entity = new MonitorRoutingPointEntity();
                      entity.setLon(mapPointBO.getLongitude());
                      entity.setLat(mapPointBO.getLatitude());
                      routingPointEntityList.add(entity);
                    });
          });
          pncRouteRepository.push(vehicleName, routingPointEntityList);
          errorNavId = pncInfoDto.getNaviRefreshId();
        }
        scheduleEntity.setNavRefreshId(errorNavId);
      }
    } catch (Exception e) {
      log.error("Schedule get vehicle pnc routing exception", e);
    }
  }

  private MonitorScheduleStopEntity buildScheduleStopEntityFromScheduleGoal(ScheduleTaskGoal scheduleGoal) {
    MonitorScheduleStopEntity entity = new MonitorScheduleStopEntity();
    entity.setId(scheduleGoal.getStopId());
    entity.setGoalId(scheduleGoal.getGoalId());
    entity.setStopAction(scheduleGoal.getStopAction());
    entity.setName(scheduleGoal.getStopName());
    entity.setLat(scheduleGoal.getLatitude());
    entity.setLon(scheduleGoal.getLongitude());
    entity.setGlobalMileage(scheduleGoal.getRoutingMileage());
    entity.setTravelStatus(scheduleGoal.getTravelStatus());
    entity.setStartTime(scheduleGoal.getStartDatetime());
    entity.setDepartTime(scheduleGoal.getDepartureDatetime());
    entity.setEstDepartTime(scheduleGoal.getEstimatedDepartureDatetime());
    entity.setArrivedTime(scheduleGoal.getArrivalDatetime());
    entity.setWaitingTime(scheduleGoal.getWaitingTime());
    return entity;
  }

  public void pushVehicleScheduleUpdateEvent(String vehicleName) {
    MonitorStationVehicleMapInfoDTO vehicleMapInfoDto = new MonitorStationVehicleMapInfoDTO();
    vehicleMapInfoDto.setName(vehicleName);
    VehicleRealtimeInfoDTO realtimeInfoDto = vehicleRealtimeRepository.get(vehicleName);
    if (realtimeInfoDto != null) {
      vehicleMapInfoDto.setLat(realtimeInfoDto.getLat());
      vehicleMapInfoDto.setLon(realtimeInfoDto.getLon());
    }
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_UPDATE.getValue(), vehicleMapInfoDto);
    String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    log.info("Send vehicle schedule update notification {}", topicName);
    rTopic.publish(JsonUtils.writeValueAsString(wsResult));
  }

}

