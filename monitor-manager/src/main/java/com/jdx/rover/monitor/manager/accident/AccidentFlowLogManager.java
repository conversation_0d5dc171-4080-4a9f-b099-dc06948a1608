package com.jdx.rover.monitor.manager.accident;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.repository.mapper.AccidentFlowLogMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 事故流程记录manager
 */
@Service
public class AccidentFlowLogManager extends BaseManager<AccidentFlowLogMapper, AccidentFlowLog> {
    /**
     * 根据事故编号查询流程记录
     * @param accidentNo
     * @return
     */
    public List<AccidentFlowLog> listByAccidentNo(String accidentNo) {
        LambdaQueryWrapper<AccidentFlowLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AccidentFlowLog::getAccidentNo, accidentNo);
        queryWrapper.orderByAsc(AccidentFlowLog::getCreateTime);
        return list(queryWrapper);
    }

}
