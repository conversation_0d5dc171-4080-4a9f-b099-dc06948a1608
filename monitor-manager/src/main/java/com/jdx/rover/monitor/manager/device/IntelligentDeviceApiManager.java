/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.device;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.device.jsfapi.domain.dto.device.DeviceDetailDTO;
import com.jdx.rover.device.jsfapi.domain.vo.device.DeviceDetailGetByNameServerVO;
import com.jdx.rover.device.jsfapi.service.server.device.IntelligentDeviceServerDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 设备服务管理
 * <AUTHOR>
 */
@Service
@Slf4j
public class IntelligentDeviceApiManager {

  /**
   * 设备服务接口
   */
  @Autowired
  public IntelligentDeviceServerDeviceService deviceService;

  /**
   * 获取设备详情
   */
  public DeviceDetailDTO getDeviceDetail(String productKey, String deviceName) {
    DeviceDetailDTO result = null;
    try {
      DeviceDetailGetByNameServerVO requestVo = new DeviceDetailGetByNameServerVO();
      requestVo.setProductKey(productKey);
      requestVo.setDeviceName(deviceName);
      HttpResult<DeviceDetailDTO> httpResult = deviceService.getDeviceDetailByNameServer(requestVo);;
      if (HttpResult.isSuccess(httpResult)) {
        result = httpResult.getData();
      }
      log.info("元数据通过设备号获取基本信息调用完成,结果={}", result);
    } catch (Exception e) {
      log.warn("元数据通过设备号{}-{}获取基本信息调用失败", productKey,deviceName, e);
    }
    return result;
  }


}
