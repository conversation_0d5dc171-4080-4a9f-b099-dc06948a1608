package com.jdx.rover.monitor.manager.accident;

import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentRecordLog;
import com.jdx.rover.monitor.repository.mapper.AccidentMapper;
import com.jdx.rover.monitor.repository.mapper.AccidentRecordLogMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 事故记录manager
 */
@Service
public class AccidentRecordLogManager extends BaseManager<AccidentRecordLogMapper, AccidentRecordLog> {

    public List<AccidentRecordLog> selectListByAccidentNo(String accidentNo) {
        return lambdaQuery().eq(AccidentRecordLog::getAccidentNo, accidentNo).orderByDesc(AccidentRecordLog::getId).list();
    }
}
