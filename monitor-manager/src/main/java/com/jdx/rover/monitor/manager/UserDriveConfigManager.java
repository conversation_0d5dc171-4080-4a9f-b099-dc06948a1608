/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.manager;

import com.jdx.rover.monitor.po.user.UserDriveConfig;
import com.jdx.rover.monitor.repository.mapper.UserDriveConfigMapper;
import com.jdx.rover.monitor.manager.base.BaseManager;
import org.springframework.stereotype.Service;

/**
 * 用户配置manager类
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Service
public class UserDriveConfigManager extends BaseManager<UserDriveConfigMapper, UserDriveConfig> {

    /**
     * 通过用户名获取用户配置
     */
    public UserDriveConfig getByUserName(String userName) {
        return this.lambdaQuery().eq(UserDriveConfig::getUserName, userName).one();
    }
}