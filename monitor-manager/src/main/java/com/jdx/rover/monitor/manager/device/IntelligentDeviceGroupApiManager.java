/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.device;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.device.jsfapi.domain.dto.group.GroupInfoDTO;
import com.jdx.rover.device.jsfapi.domain.vo.group.GroupInfoTreeGetVO;
import com.jdx.rover.device.jsfapi.service.server.group.IntelligentDeviceServerGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备组服务管理
 * <AUTHOR>
 */
@Service
@Slf4j
public class IntelligentDeviceGroupApiManager {

  /**
   * 设备服务接口
   */
  @Autowired
  public IntelligentDeviceServerGroupService deviceGroupService;
  
  /**
   * 获取设备组详情
   */
  public List<GroupInfoDTO> getGroupTree(String productKey) {
    List<GroupInfoDTO> result = null;
    try {
      GroupInfoTreeGetVO groupInfoTreeGetVo = new GroupInfoTreeGetVO();
      groupInfoTreeGetVo.setProductKey(productKey);
      HttpResult<List<GroupInfoDTO>> httpResult = deviceGroupService.getGroupInfoTree(groupInfoTreeGetVo);
      if (HttpResult.isSuccess(httpResult)) {
        result = httpResult.getData();
      }
    } catch (Exception e) {
      log.warn("元数据获取设备分组树调用失败", e);
    }
    return result;
  }


}
