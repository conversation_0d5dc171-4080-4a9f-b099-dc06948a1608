package com.jdx.rover.monitor.manager.mobile;

import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.mobile.VehiclePosition;
import com.jdx.rover.monitor.repository.mapper.mobile.VehiclePositionMapper;
import org.springframework.stereotype.Service;

/**
 * @description: VehiclePositionManager
 * @author: wangguotai
 * @create: 2024-05-24 18:12
 **/
@Service
public class VehiclePositionManager extends BaseManager<VehiclePositionMapper, VehiclePosition> {
}