package com.jdx.rover.monitor.manager.vehicle;

import cn.hutool.core.util.StrUtil;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.schedule.api.domain.vo.schedule.MiniMonitorRemoteLoginAddVo;
import com.jdx.rover.schedule.common.response.BaseResponse;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMiniMonitorJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/16 22:16
 * @description 远程登录车端Manager
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleLoginManager {

    /**
     * 调度远程指令
     */
    private final ScheduleMiniMonitorJsfService scheduleMiniMonitorJsfService;

    /**
     * 远程登录车端
     *
     * @param vehicleName vehicleName
     * @param contact contact
     */
    public void remoteLogin(String vehicleName, String contact) {
        MiniMonitorRemoteLoginAddVo miniMonitorRemoteLoginAddVo = new MiniMonitorRemoteLoginAddVo();
        miniMonitorRemoteLoginAddVo.setVehicleName(vehicleName);
        miniMonitorRemoteLoginAddVo.setContact(contact);
        try {
            HttpResult<BaseResponse> baseResponseHttpResult = scheduleMiniMonitorJsfService.remoteLogin(miniMonitorRemoteLoginAddVo);
            BaseResponse baseResponse = baseResponseHttpResult.getData();
            log.info("调度远程登录结果,{},{}", vehicleName + "-" + contact, baseResponse);
            if (!StrUtil.equals(baseResponse.getCode(), "0")) {
                throw new AppException(baseResponse.getCode(), baseResponse.getMessage());
            }
        } catch (Exception e) {
            log.warn("调度远程登录调用失败,{},{}", vehicleName + "-" + contact, e.toString());
            if (e instanceof AppException ae) {
                throw ae;
            }
        }
    }
}
