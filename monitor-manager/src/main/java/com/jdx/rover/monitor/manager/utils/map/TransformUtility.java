/***************************************************************************
*
* Copyright (c) 2020 JD.com, Inc. All Rights Reserved
*
**************************************************************************/

package com.jdx.rover.monitor.manager.utils.map;

import com.jdx.rover.monitor.bo.map.MapPointBO;

import java.util.Objects;

/**
* <p>
* This is a utility class that provides coordinate transform include GCJ-02 and WGS-84
* </p>
*
* <p>
* <strong>Thread Safety: </strong> This class has no state, and thus it is thread safe.
* </p>
*
* <AUTHOR>
* @version 1.0
*/
public class TransformUtility {

 /**
  * <p>
  * transform A.
  * </p>
  */
 private static final double A = 6378245.0;

 /**
  * <p>
  * transform PI.
  * </p>
  */
 private static final double PI = 3.1415926535897932384626;

 /**
  * <p>
  * transform EE.
  * </p>
  */
 private static final double EE = 0.00669342162296594323;

 /**
  * <p>
  * Max latitude in china.
  * </p>
  */
 private static final double MAX_LATITUDE = 55.8271;

 /**
  * <p>
  * Min latitude in china.
  * </p>
  */
 private static final double MIN_LATITUDE = 0.8293;

 /**
  * <p>
  * Max latitude in china.
  * </p>
  */
 private static final double MAX_LONGTITUDE = 137.8347;

 /**
  * <p>
  * Min latitude in china.
  * </p>
  */
 private static final double MIN_LONGTITUDE = 72.004;

  /**
   * <p>
   * 地球转向角.
   * </p>
   */
  private static final double EARTH_RADIUS = 6371;

 /**
  * <p>
  * Empty private constructor.
  * </p>
  */
 private TransformUtility() {
   // Empty
 }

 /**
  * <p>
  * GCJ-02 to WGS-84
  * </p>
  *
  * @param latitude  the latitude in GCJ-02
  * @param longitude the longitude in GCJ-02
  *
  * @return MapPointBO return WGS-84 latitude and longitude.
  */
 public static MapPointBO toWGS84Point(double latitude, double longitude) {
   MapPointBO mapPoint = calDev(latitude, longitude);
   double retLat = latitude - mapPoint.getLatitude();
   double retLon = longitude - mapPoint.getLongitude();
   mapPoint = calDev(retLat, retLon);
   retLat = latitude - mapPoint.getLatitude();
   retLon = longitude - mapPoint.getLongitude();
   return mapPoint;
 }

 /**
  * <p>
  * WGS-84 to GCJ-02.
  * </p>
  *
  * @param latitude  the latitude in WGS-84
  * @param longitude the the longitude in WGS-84
  *
  * @return MapPointBO return GCJ02 latitude and longitude.
  */
 public static MapPointBO toGCJ02Point(Double latitude, Double longitude) {
   if (Objects.isNull(latitude) || Objects.isNull(longitude)) {
       return null;
   }
   MapPointBO mapPoint = calDev(latitude, longitude);
   if (mapPoint == null) {
     return null;
   }
   double retLat = latitude + mapPoint.getLatitude();
   double retLon = longitude + mapPoint.getLongitude();
   mapPoint.setLatitude(retLat);
   mapPoint.setLongitude(retLon);
   return mapPoint;
 }

  /**
   * 计算两经纬度坐标距离，返回单位是千米
   * @param lat1
   * @param lon1
   * @param lat2
   * @param lon2
   * @return
   */
  public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    double phi1 = Math.toRadians(lat1);
    double phi2 = Math.toRadians(lat2);
    double deltaPhi = Math.toRadians(lat2 - lat1);
    double deltaLambda = Math.toRadians(lon2 - lon1);
    double a = Math.sin(deltaPhi / 2) * Math.sin(deltaPhi / 2) +
            Math.cos(phi1) * Math.cos(phi2) *
                    Math.sin(deltaLambda / 2) * Math.sin(deltaLambda / 2);
    double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    double distance = EARTH_RADIUS * c;
    return distance;
  }

 /**
  * <p>
  * Calibration function
  * </p>
  *
  * @param wgLat the latitude to be Calibration
  * @param wgLon the longitude to be Calibration
  *
  * @return MapPointBO return Calibration latitude and longitude.
  */
 private static MapPointBO calDev(double wgLat, double wgLon) {
   MapPointBO mapPoint = new MapPointBO();
   if (isOutOfChina(wgLat, wgLon)) {
     return null;
   }
   double dLat = calLat(wgLon - 105.0, wgLat - 35.0);
   double dLon = calLon(wgLon - 105.0, wgLat - 35.0);
   double radLat = wgLat / 180.0 * PI;
   double magic = Math.sin(radLat);
   magic = 1 - EE * magic * magic;
   double sqrtMagic = Math.sqrt(magic);
   if (magic == 0 || Math.cos(radLat) == 0) {
     return null;
   }
   dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
   dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
   mapPoint.setLatitude(dLat);
   mapPoint.setLongitude(dLon);
   return mapPoint;
 }

 /**
  * <p>
  * check localation in china
  * </p>
  *
  * @param wgLat the latitude to be check
  * @param wgLon the longitude to be check
  *
  * @return boolean return in china status.
  */
 public static boolean isOutOfChina(double lat, double lon) {
   if (lon < MIN_LONGTITUDE || lon > MAX_LONGTITUDE) {
     return true;
   }
   if (lat < MIN_LATITUDE || lat > MAX_LATITUDE) {
     return true;
   }
   return false;
 }

 /**
  * <p>
  * Calibration latitude
  * </p>
  *
  * @param x the x to be Calibration
  * @param y the y to be Calibration
  *
  * @return double return Calibration latitude
  */
 private static double calLat(double x, double y) {
   // reference to https://www.cnblogs.com/blogger-Li/p/11616835.html
   double ret =
       -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
   ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
   ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
   ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
   return ret;
 }

 /**
  * <p>
  * Calibration longitude
  * </p>
  *
  * @param x the x to be Calibration
  * @param y the y to be Calibration
  *
  * @return double return Calibration longitude
  */
 private static double calLon(double x, double y) {
   // reference to https://www.cnblogs.com/blogger-Li/p/11616835.html
   double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
   ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
   ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
   ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
   return ret;
 }

    /**
     * <p>
     *  判断位置是否在北京市
     * </p>
     */
    public static boolean isInBeiJing(double lat, double lon) {
        double MIN_LON = 116.4628;
        double MAX_LON = 116.6007;
        double MIN_LAT = 39.7252;
        double MAX_LAT = 39.8286;
        if (lon < MIN_LON || lon > MAX_LON) {
            return false;
        }
        if (lat < MIN_LAT || lat > MAX_LAT) {
            return false;
        }
        return true;
    }

}