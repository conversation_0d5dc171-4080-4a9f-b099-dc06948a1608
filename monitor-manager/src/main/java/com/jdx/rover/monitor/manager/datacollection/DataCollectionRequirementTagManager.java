/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.manager.datacollection;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionRequirementTag;
import com.jdx.rover.monitor.repository.mapper.datacollection.DataCollectionRequirementTagMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 数据采集需求关联标签表 Manager 类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class DataCollectionRequirementTagManager extends BaseManager<DataCollectionRequirementTagMapper, DataCollectionRequirementTag> {

    /**
     * 根据需求ID查询关联的标签列表
     *
     * @param requirementId 需求ID
     * @return List<DataCollectionRequirementTag>
     */
    public List<DataCollectionRequirementTag> listByRequirementId(Integer requirementId, boolean getEnabled) {
        LambdaQueryWrapper<DataCollectionRequirementTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataCollectionRequirementTag::getRequirementId, requirementId);
        if (getEnabled) {
            queryWrapper.eq(DataCollectionRequirementTag::getEnabled, true);
        }
        return this.list(queryWrapper);
    }

    /**
     * 批量保存需求标签关联关系
     *
     * @param requirementTagList 需求标签关联列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchRequirementTags(List<DataCollectionRequirementTag> requirementTagList) {
        this.saveBatch(requirementTagList);
    }

    /**
     * 获取多个需求ID对应的标签列表
     * @param requirementIdColl 需求ID集合
     * @param enable 启用状态
     * @return 标签列表
     */
    public List<DataCollectionRequirementTag> listByRequirementIdCollAndEnable(Collection<Integer> requirementIdColl, Boolean enable) {
        if (CollectionUtil.isEmpty(requirementIdColl)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
            .in(DataCollectionRequirementTag::getRequirementId, requirementIdColl)
            .eq(Objects.nonNull(enable), DataCollectionRequirementTag::getEnabled, enable)
            .list();
    }

    /**
     * 根据标签ID列表和启用状态查询数据采集需求标签
     * @param tagId 标签ID列表
     * @param enable 启用状态枚举
     * @return 符合条件的数据采集需求标签列表
     */
    public List<DataCollectionRequirementTag> listByTagId(List<Integer> tagId, Boolean enable) {
        return lambdaQuery()
                .in(DataCollectionRequirementTag::getTagId, tagId)
                .eq(Objects.nonNull(enable), DataCollectionRequirementTag::getEnabled, enable)
                .orderByDesc(DataCollectionRequirementTag::getCreateTime).last(" LIMIT 200")
                .list();
    }

    /**
     * 删除需求关联标签
     *
     * @param requirementId requirementId
     */
    public void deleteEnabledTagsByRequirementId(Integer requirementId) {
        lambdaUpdate()
            .eq(DataCollectionRequirementTag::getRequirementId, requirementId)
            .remove();
    }
}