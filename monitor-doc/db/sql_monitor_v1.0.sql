--
-- Table structure for table `monitor_user_operation_log`
--

DROP TABLE IF EXISTS `monitor_user_operation_log`;
CREATE TABLE `monitor_user_operation_log` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `vehicle_name` VARCHAR(50) NOT NULL,
  `city_id` INT UNSIGNED NOT NULL,
  `city_name` VARCHAR(50) NOT NULL,
  `station_id` INT UNSIGNED NOT NULL,
  `station_name` VARCHAR(50) NOT NULL,
  `operation_type` VARCHAR(200) NOT NULL,
  `operation_message` VARCHAR(200) NOT NULL,
  `operate_timestamp` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `user_name` VARCHAR(100) NOT NULL,
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  INDEX `user_reported_vehicle_name_idx` (`operate_timestamp`,`vehicle_name`),
  INDEX `user_reported_operate_time_idx` (`operate_timestamp`,`city_name`,`station_name`,`vehicle_name`));

--
-- Table structure for table `guardian_vehicle_alarm_info`
--
DROP TABLE IF EXISTS `guardian_vehicle_alarm_info`;
CREATE TABLE `guardian_vehicle_alarm_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `alarm_event` varchar(100) DEFAULT NULL,
  `topic` varchar(64) NOT NULL,
  `description` varchar(1024) NOT NULL,
  `component_id` int unsigned NOT NULL,
  `component_name` varchar(64) NOT NULL,
  `report_timestamp` datetime(3) DEFAULT NULL,
  `report_user_name` varchar(50) DEFAULT NULL,
  `vehicle_version` varchar(64) DEFAULT NULL,
  `vehicle_name` varchar(50) NOT NULL,
  `city_name` VARCHAR(50) NOT NULL,
  `station_name` VARCHAR(50) NOT NULL,
  `system_state` VARCHAR(50) DEFAULT NULL,
  `use_case` VARCHAR(50) DEFAULT NULL,
  `operate_timestamp` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `exception_info_id` int unsigned DEFAULT NULL,
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  UNIQUE KEY `KEYid_UNIQUE` (`id`),
  INDEX `idx_vehicle_alarm_vehicle` (`vehicle_name`),
  INDEX `idx_vehicle_alarm_operate_time` (`operate_timestamp`, `city_name`, `station_name`, `vehicle_name`),
  INDEX `idx_vehicle_alarm_time_vehicle` (`operate_timestamp`, `vehicle_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `guardian_vehicle_exception_info`
--
DROP TABLE IF EXISTS `guardian_vehicle_exception_info`;
CREATE TABLE `guardian_vehicle_exception_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `vehicle_name` varchar(50) NOT NULL,
  `city_name` VARCHAR(50) NOT NULL,
  `station_name` VARCHAR(50) NOT NULL,
  `error_code` varchar(45) DEFAULT NULL,
  `error_level` varchar(45) DEFAULT NULL,
  `error_message` varchar(1024) DEFAULT NULL,
  `operate_timestamp` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `created_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  UNIQUE KEY `KEYid_UNIQUE` (`id`),
  INDEX `idx_vehicle_exception_vehicle` (`vehicle_name`),
  INDEX `idx_vehicle_exception_operate_time` (`operate_timestamp`, `city_name`, `station_name`, `vehicle_name`),
  INDEX `idx_vehicle_exception_time_vehicle` (`operate_timestamp`, `vehicle_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `issue_record`
--
DROP TABLE IF EXISTS `issue_record`;
CREATE TABLE `issue_record` (
  `id` int NOT NULL AUTO_INCREMENT,
  `issue_no` varchar(45) NOT NULL,
  `vehicle_name` varchar(45) NOT NULL,
  `vehicle_business_type` varchar(45) NOT NULL,
  `alarm_type` varchar(45) NOT NULL,
  `city_name` varchar(50) NOT NULL,
  `station_name` varchar(50) NOT NULL,
  `source` varchar(45) NOT NULL,
  `state` varchar(45) NOT NULL,
  `report_user` varchar(50) NOT NULL,
  `operate_user` varchar(50) DEFAULT NULL,
  `report_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `start_time` datetime(3) DEFAULT NULL,
  `end_time` datetime(3) DEFAULT NULL,
  `jira_no` varchar(45) DEFAULT NULL,
  `result` varchar(45) DEFAULT NULL,
  `owner_use_case` varchar(45) DEFAULT NULL,
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  INDEX `issue_no_idx` (`issue_no`),
  INDEX `vehicle_name_idx` (`vehicle_name`),
  INDEX `issue_record_city_station_idx` (`start_time`, `vehicle_name`),
  INDEX `issue_record_time_idx` (`start_time`, `city_name`, `station_name`, `vehicle_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `issue_alarm_attachment`
--
DROP TABLE IF EXISTS `issue_alarm_attachment`;
CREATE TABLE `issue_alarm_attachment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `alarm_id` int unsigned NOT NULL,
  `timestamp` datetime(3) DEFAULT NULL,
  `url` varchar(200) DEFAULT NULL,
  `issue_no` varchar(45) DEFAULT NULL,
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  INDEX `issue_no_idx` (`issue_no`),
  INDEX `alarm_id_idx` (`alarm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `issue_operate_history`
--
DROP TABLE IF EXISTS `issue_operate_history`;
CREATE TABLE `issue_operate_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `issue_no` varchar(45) NOT NULL,
  `vehicle_alarm_id` int DEFAULT NULL,
  `timestamp` datetime(3) NOT NULL,
  `user_name` varchar(45) DEFAULT NULL,
  `title` varchar(100) DEFAULT NULL,
  `message` varchar(500) DEFAULT NULL,
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  KEY `issue_no_idx` (`issue_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `issue_vehicle_alarm_record`
--
DROP TABLE IF EXISTS `issue_alarm_record`;
CREATE TABLE `issue_alarm_record` (
  `id` int NOT NULL AUTO_INCREMENT,
  `vehicle_name` varchar(45) NOT NULL,
  `alarm_type` varchar(50) DEFAULT NULL,
  `alarm_code` varchar(200) DEFAULT NULL,
  `alarm_no` varchar(50) DEFAULT NULL,
  `timestamp` datetime(3) DEFAULT NULL,
  `issue_no` varchar(45) DEFAULT NULL,
  `source` varchar(45) DEFAULT NULL,
  `selected` tinyint(1) DEFAULT '0',
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  INDEX `issue_no_idx` (`issue_no`),
  INDEX `vehicle_name_idx` (`vehicle_name`),
  INDEX `vehicle_issue_alarm_idx` (`issue_no`,`vehicle_name`, `alarm_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `jdx_monitor`.`guardian_vehicle_alarm_info` (`id`, `alarm_event`, `description`, `component_id`, `component_name`, `report_timestamp`, `report_user_name`, `vehicle_version`, `vehicle_name`, `city_name`, `station_name`, `use_case`,`operate_timestamp`, `created_time`, `modified_time`, `yn`) VALUES ('1', 'LOW_BATTERY', '电量低', '26126', '安卓', '2022-02-18 10:59:59', 'test', 'QA_20220218_daily', 'JD0001', '北京', '亦庄智配站', 'OPEN', '2022-02-18 10:59:59', '2022-02-18 10:59:59', '2022-02-18 10:59:59', '0');
INSERT INTO `jdx_monitor`.`issue_record` (`id`, `issue_no`, `vehicle_name`, `vehicle_business_type`, `alarm_type`, `city_name`, `station_name`, `source`, `state`, `report_user`, `operate_user`, `start_time`, `result`, `owner_use_case`, `created_time`, `modified_time`, `yn`) VALUES ('1', 'GD2022021700001', 'JD40006', 'DISPATCH', 'LOW_BATTERY', '苏州', '常熟东南营业部', 'EXCEPTION_REMIND', 'FINISH', '', 'test', '2022-02-18 10:59:59.000', 'OPERATION_ABORT', 'OPEN', '2022-02-18 10:59:59.000', '2022-02-18 10:59:59.000', '0');

--
-- Table structure for table `assist_jira_record`
--
DROP TABLE IF EXISTS `assist_jira_record`;
CREATE TABLE `assist_jira_record` (
  `id` int NOT NULL AUTO_INCREMENT,
  `is_reported` tinyint unsigned NOT NULL,
  `vehicle_name` varchar(64) NOT NULL,
  `issue_type_id` int unsigned NOT NULL,
  `topic` varchar(512) NOT NULL,
  `description` varchar(1024) NOT NULL,
  `alarm_event` varchar(64) DEFAULT NULL,
  `system_state` varchar(64) DEFAULT NULL,
  `priority_id` int unsigned NOT NULL,
  `defect_type_id` int unsigned NOT NULL,
  `defect_type_value` varchar(64) NOT NULL,
  `severity_id` int unsigned NOT NULL,
  `severity_value` varchar(64) NOT NULL,
  `discovery_phase_id` int unsigned NOT NULL,
  `discovery_phase_value` varchar(64) NOT NULL,
  `discover_situation_id` int unsigned NOT NULL,
  `discover_situation_value` varchar(64) NOT NULL,
  `component_id` int unsigned NOT NULL,
  `component_name` varchar(64) NOT NULL,
  `use_case` varchar(64) DEFAULT NULL,
  `version_id` int unsigned NOT NULL,
  `version_value` varchar(64) NOT NULL,
  `city_name` varchar(64) DEFAULT NULL,
  `station_name` varchar(64) DEFAULT NULL,
  `operate_time` datetime(3) NOT NULL,
  `report_user` varchar(64) NULL,
  `exception_id` int DEFAULT NULL,
  `is_selected` tinyint(1) DEFAULT '0',
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  UNIQUE KEY `KEYid_UNIQUE` (`id`),
  KEY `idx_assist_jira` (`vehicle_name`),
  KEY `assist_jira_record_time_idx` (`operate_time`, `city_name`, `station_name`, `vehicle_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `assist_jira_debug_time`;
CREATE TABLE `assist_jira_debug_time` (
  `id` int NOT NULL AUTO_INCREMENT,
  `jira_record_id` int NOT NULL,
  `debug_time` datetime(3) NOT NULL,
  `is_selected` tinyint(1) DEFAULT '0',
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  KEY `idx_assist_jira` (`jira_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `assist_jira_attachment`;
CREATE TABLE `assist_jira_attachment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `jira_record_id` int unsigned NOT NULL,
  `is_enabled` tinyint(1) NOT NULL,
  `attachment_name` varchar(100) NOT NULL,
  `attachment_key` varchar(100) NOT NULL,
  `bucket_name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `assist_jira_attachment_assist_jira_id_idx` (`jira_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `guardian_vehicle_abnormal`
--
DROP TABLE IF EXISTS `guardian_vehicle_abnormal`;
CREATE TABLE `guardian_vehicle_abnormal` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `vehicle_name` varchar(50) NOT NULL COMMENT '车辆名称',
  `module_name` varchar(63) DEFAULT NULL COMMENT '模块名称',
  `error_code` varchar(45) DEFAULT NULL COMMENT '错误码',
  `error_level` varchar(10) DEFAULT NULL COMMENT '错误级别',
  `error_message` varchar(1024) DEFAULT NULL COMMENT '错误消息',
  `start_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `yn` tinyint unsigned DEFAULT '1' COMMENT '是否有效(0:无效 1:有效)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `KEYid_UNIQUE` (`id`),
  INDEX `idx_abnormal_vehicle_name` (`vehicle_name`),
  INDEX `idx_abnormal_start_time` (`start_time`, `vehicle_name`, `error_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='guardian车辆异常';

--
-- Table structure for table `vehicle_abnormal_boot`
--
DROP TABLE IF EXISTS `vehicle_abnormal_boot`;
CREATE TABLE `vehicle_abnormal_boot` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `vehicle_name` varchar(50) NOT NULL COMMENT '车辆名称',
  `boot_uuid` varchar(45) DEFAULT NULL COMMENT '启动标识',
  `boot_count` int unsigned NOT NULL COMMENT '启动次数',
  `node_name` varchar(50) DEFAULT NULL COMMENT '启动节点',
  `module_name` varchar(50) DEFAULT NULL COMMENT '启动模块',
  `boot_status` varchar(25) DEFAULT NULL COMMENT '启动状态',
  `error_code` varchar(25) DEFAULT NULL COMMENT '启动错误码',
  `error_message` varchar(1024) DEFAULT NULL COMMENT '启动错误信息',
  `duration` int unsigned DEFAULT NULL COMMENT '启动时长',
  `users` varchar(200) DEFAULT NULL COMMENT '关注用户',
  `yn` tinyint unsigned DEFAULT '1' COMMENT '是否有效(0:无效 1:有效)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_abnormal_boot_vehicle_name` (`vehicle_name`，`boot_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='guardian车辆异常启动模块';

ALTER TABLE `rover_monitor`.`guardian_vehicle_alarm_info`
ADD COLUMN `boot_uuid` varchar(45) DEFAULT NULL COMMENT '启动标识' AFTER `exception_info_id`;

ALTER TABLE `rover_monitor`.`guardian_vehicle_alarm_info`
ADD COLUMN `boot_id` int unsigned DEFAULT NULL COMMENT '软启动标识' AFTER `boot_uuid`;

ALTER TABLE `rover_monitor`.`guardian_vehicle_alarm_info`
ADD COLUMN `end_timestamp` datetime(3) DEFAULT NULL COMMENT '告警结束时间' AFTER `operate_timestamp`;

ALTER TABLE `rover_monitor`.`vehicle_abnormal_boot`
ADD COLUMN `boot_id` int unsigned DEFAULT NULL COMMENT '软启动标识' AFTER `boot_uuid`;

ALTER TABLE `rover_monitor`.`vehicle_abnormal_boot`
ADD UNIQUE INDEX `boot_uuid_id` (`boot_uuid`, `boot_id`);