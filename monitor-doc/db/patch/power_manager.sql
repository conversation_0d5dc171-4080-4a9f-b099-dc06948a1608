ALTER TABLE `rover_monitor`.`monitor_user_operation_log`
ADD COLUMN `request_id` VARCHAR(64) NULL COMMENT '请求ID' AFTER `user_name`,
ADD COLUMN `state` VARCHAR(64) NULL COMMENT '状态' AFTER `request_id`,
ADD UNIQUE INDEX `requestid_vehiclename_unique` (`request_id` DESC, `vehicle_name` ASC);

ALTER TABLE `rover_monitor`.`monitor_user_operation_log`
ADD COLUMN `fail_message` VARCHAR(255) NULL COMMENT '失败消息' AFTER `state`;

ALTER TABLE `rover_monitor`.`issue_record`
ADD COLUMN `alarm_time` datetime(3) DEFAULT NULL COMMENT '告警发生时间' AFTER `alarm_type`;
