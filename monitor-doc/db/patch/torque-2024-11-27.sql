DROP TABLE IF EXISTS `user_drive_config`;
CREATE TABLE `user_drive_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_name` varchar(100) NOT NULL COMMENT '用户名称',
  `speed_limit` float DEFAULT NULL COMMENT '速度限制',
  `yn` tinyint unsigned DEFAULT '1' COMMENT '是否有效(0:无效 1:有效)',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `KEYid_UNIQUE` (`id`),
  INDEX `idx_drive_config_user_name` (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置';