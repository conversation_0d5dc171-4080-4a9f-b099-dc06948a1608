CREATE TABLE `pda_realtime_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `product_key` varchar(63) NOT NULL COMMENT '产品标识',
  `device_name` varchar(63) NOT NULL COMMENT '设备编号',
  `product_model_no` varchar(63) DEFAULT NULL COMMENT '设备型号',
  `product_model_name` varchar(200) DEFAULT NULL COMMENT '设备型号名称',
  `realtime_status` varchar(15) DEFAULT 'OFFLINE' COMMENT '在线状态',
  `group_one` varchar(63) DEFAULT NULL COMMENT '一级分组',
  `group_two` varchar(63) DEFAULT NULL COMMENT '二级分组',
  `group_three` varchar(63) DEFAULT NULL COMMENT '三级分组',
  `group_name` varchar(63) DEFAULT NULL COMMENT '分组名称',
  `network_type` varchar(63) DEFAULT NULL COMMENT '网络类型',
  `position_status` tinyint DEFAULT NULL COMMENT '定位状态',
  `burial_status` tinyint DEFAULT NULL COMMENT '埋点状态',
  `created_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `yn` tinyint DEFAULT '0' NOT NULL COMMENT '是否有效(0:否 1:是)',
  `modified_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近修改时间',
  PRIMARY KEY (`id`),
  KEY `group_idx`(`group_one`, `group_two`, `group_three`),
  KEY `product_device_idx`(`product_key`, `device_name`),
  KEY `model_device_idx`(`product_model_no`, `realtime_status`),
  KEY `device_time_idx`(`modified_time`, `realtime_status`),
  KEY `pda_device_idx`(`device_name`),
  KEY `device_realtime_state_idx`(`realtime_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备状态表';

ALTER TABLE `rover_monitor`.`pda_realtime_info` ADD INDEX `group_realtime_status_idx`(`group_one`, `group_two`, `group_three`, `realtime_status`)

ALTER TABLE `rover_monitor`.`pda_realtime_info` ADD INDEX `pda_modified_time_idx`(`modified_time`)

