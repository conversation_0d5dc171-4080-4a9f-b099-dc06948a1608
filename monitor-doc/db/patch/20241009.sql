-- 增加京ME推送动态参数配置
-- 事故消息推送配置表
drop table if exists `accident_jdme_config`;
create table `accident_jdme_config` (
    id int(11) not null AUTO_INCREMENT comment '主键',
    fixed_group_id varchar(64) not null comment '固定的领导群号',
    manual_pin varchar(64) not null comment '转人工处理的人工ERP',
    new_group_owner varchar(64) not null comment '一事一群管理员',
    deleted datetime(3) DEFAULT '1970-01-01 08:00:01.000' comment '删除时间',
    create_time datetime(3) not null DEFAULT CURRENT_TIMESTAMP(3) comment '创建时间',
    create_user varchar(64) comment '创建用户',
    modify_time datetime(3) not null DEFAULT CURRENT_TIMESTAMP(3) comment '修改时间',
    modify_user varchar(64) comment '修改用户',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='事故消息推送配置表';

--事故消息推送配置用户关联表
drop table if exists `accident_jdme_config_user`;
create table `accident_jdme_config_user` (
    id int(11) not null AUTO_INCREMENT comment '主键',
    config_id int(11) not null comment '配置编号：accident_jdme_config.id',
    `type` varchar(10) not null comment '关联用户类型：member-成员用户；at-需要艾特的用户',
    pin varchar(64) not null comment '用户ERP',
    nickname varchar(64) comment '用户昵称',
    deleted datetime(3) DEFAULT '1970-01-01 08:00:01.000' comment '删除时间',
    create_time datetime(3) not null DEFAULT CURRENT_TIMESTAMP(3) comment '创建时间',
    create_user varchar(64) comment '创建用户',
    modify_time datetime(3) not null DEFAULT CURRENT_TIMESTAMP(3) comment '修改时间',
    modify_user varchar(64) comment '修改用户',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='事故消息推送配置用户关联表';

insert into accident_jdme_config (id, fixed_group_id, manual_pin, new_group_owner) values ('100001', '10211022273', 'caohe', 'caohe');
insert into accident_jdme_config_user (config_id, `type`, pin, nickname) values ('100001', 'member', 'liuyafei11', '刘亚飞');
insert into accident_jdme_config_user (config_id, `type`, pin, nickname) values ('100001', 'member', 'shaojinyu', '邵金玉');
insert into accident_jdme_config_user (config_id, `type`, pin, nickname) values ('100001', 'member', 'huangxuejun6', '黄学军');
insert into accident_jdme_config_user (config_id, `type`, pin, nickname) values ('100001', 'member', 'liuwenwen52', '刘文文');