
CREATE TABLE `robot_realtime_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `product_key` varchar(63) NOT NULL COMMENT '产品标识',
  `device_name` varchar(63) NOT NULL COMMENT '设备编号',
  `remark_name` varchar(63) DEFAULT NULL COMMENT '设备备注',
  `product_model_no` varchar(63) DEFAULT NULL COMMENT '设备型号',
  `product_model_name` varchar(200) DEFAULT NULL COMMENT '设备型号名称',
  `group_one` varchar(63) DEFAULT NULL COMMENT '父分组号',
  `group_two` varchar(63) DEFAULT NULL COMMENT '分组号',
  `group_three` varchar(63) DEFAULT NULL COMMENT '分组号',
  `group_name` varchar(63) DEFAULT NULL COMMENT '分组名',
  `group_level_name` varchar(125) DEFAULT NULL COMMENT '全分组名',
  `realtime_status` varchar(15) DEFAULT 'OFFLINE' COMMENT '在线状态',
  `created_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `yn` tinyint DEFAULT '0' NOT NULL COMMENT '是否有效(0:否 1:是)',
  `modified_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近修改时间',
  PRIMARY KEY (`id`),
  KEY `group_idx`(`product_key`, `group_one`, `group_two`, `device_name`),
  KEY `product_device_idx`(`product_key`, `device_name`),
  KEY `model_device_idx`(`product_key`, `group_one`, `group_two`, `realtime_status`),
  KEY `model_page_device_idx`(`product_key`, `group_one`, `group_two`, `realtime_status`, `modified_time`),
  KEY `device_time_idx`(`modified_time`, `realtime_status`),
  KEY `device_realtime_state_idx`(`realtime_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备状态表';

DROP TABLE IF EXISTS `robot_abnormal_info`;
CREATE TABLE `robot_abnormal_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `product_key` varchar(63) NOT NULL COMMENT '产品标识',
  `device_name` varchar(63) NOT NULL COMMENT '设备编号',
  `boot_id` bigint(30) unsigned DEFAULT NULL COMMENT '启动标识',
  `performance_module` varchar(63) DEFAULT NULL COMMENT '性能模块',
  `abnormal_module` varchar(63) DEFAULT NULL COMMENT '异常模块',
  `error_code` varchar(63) DEFAULT NULL COMMENT '错误码',
  `error_number` int unsigned DEFAULT NULL COMMENT '错误编号',
  `error_level` varchar(63) DEFAULT NULL COMMENT '错误级别',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `start_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '开始时间',
  `end_time` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `created_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `yn` tinyint DEFAULT '0' NOT NULL COMMENT '是否有效(0:否 1:是)',
  `modified_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近修改时间',
  PRIMARY KEY (`id`),
  KEY `product_device_idx`(`product_key`, `device_name`, `error_code`, `end_time`),
  KEY `device_boot_idx`(`product_key`, `device_name`,`boot_id`, `start_time`, `end_time`),
  KEY `device_time_idx`(`product_key`, `device_name`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备异常表';

ALTER TABLE `rover_monitor`.`robot_realtime_info`
ADD COLUMN `work_mode` varchar(63) DEFAULT NULL COMMENT '工作模式' AFTER `realtime_status`;

ALTER TABLE `rover_monitor`.`robot_realtime_info`
ADD COLUMN `enable` tinyint DEFAULT '0' NOT NULL COMMENT '启停用' AFTER `work_mode`;

ALTER TABLE `rover_monitor`.`robot_abnormal_info`
ADD COLUMN `remark_name` varchar(63) DEFAULT NULL COMMENT '车牌号' AFTER `device_name`,
ADD COLUMN `work_mode` varchar(63) DEFAULT NULL COMMENT '工作模式' AFTER `end_time`,
ADD COLUMN `group_no` varchar(63) DEFAULT NULL COMMENT '分组编号' AFTER `work_mode`,
ADD COLUMN `station_name` varchar(63) DEFAULT NULL COMMENT '站点名称' AFTER `group_no`,
ADD COLUMN `task_no` varchar(63) DEFAULT NULL COMMENT '任务号' AFTER `station_name`,
ADD COLUMN `point` varchar(63) DEFAULT NULL COMMENT '位置' AFTER `task_no`,
ADD COLUMN `process_mode` varchar(63) DEFAULT NULL COMMENT '处理方式' AFTER `point`,
ADD COLUMN `follow_user` varchar(63) DEFAULT NULL COMMENT '跟进用户' AFTER `process_mode`,
ADD KEY `device_page_idx`(`start_time`,`product_key`, `device_name`, `process_mode`),
ADD KEY `device_page_idx_name`(`start_time`,`product_key`, `remark_name`, `process_mode`);

