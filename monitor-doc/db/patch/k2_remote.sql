ALTER TABLE `rover_monitor`.`guardian_vehicle_alarm_info`
ADD COLUMN `alarm_number` VARCHAR(64) DEFAULT NULL COMMENT '告警编号' AFTER `alarm_event`;

ALTER TABLE `rover_monitor`.`guardian_vehicle_alarm_info`
ADD COLUMN `alarm_source` VARCHAR(32) DEFAULT NULL COMMENT '告警来源' AFTER `alarm_number`;

ALTER TABLE `rover_monitor`.`guardian_vehicle_alarm_info`
ADD COLUMN `issue_number` VARCHAR(64) DEFAULT NULL COMMENT '工单编号' AFTER `alarm_source`;

DROP TABLE IF EXISTS `work_record`;
CREATE TABLE `work_record` (
    `id` int(11) AUTO_INCREMENT NOT NULL COMMENT 'ID',
    `user_name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '用户名',
    `cockpit_number` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '座席编号',
    `cockpit_status` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '座席状态',
    `cockpit_mode` varchar(50) NOT NULL COMMENT '座席模式',
    `start_time` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '开始时间',
    `end_time` datetime(3) NULL COMMENT '结束时间',
    `cockpit_type` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '座席类型',
    `cockpit_team_number` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '座席团队编号',
    `yn` tinyint DEFAULT '0' NOT NULL COMMENT '是否有效(0:否 1:是)',
    `created_time` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    `modified_time` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) NOT NULL COMMENT '最近修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_cockpit_number` (`cockpit_number`)
) ENGINE = InnoDB AUTO_INCREMENT =1 CHARSET = utf8mb4 COLLATE utf8mb4_bin COMMENT = '工作记录';

DROP TABLE IF EXISTS `manual_alarm_record`;
CREATE TABLE `manual_alarm_record` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `vehicle_name` VARCHAR(32) NOT NULL,
  `cockpit_number` VARCHAR(64) DEFAULT NULL,
  `source` VARCHAR(32) NOT NULL,
  `report_user` VARCHAR(64) NOT NULL,
  `report_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `description` VARCHAR(500) DEFAULT NULL,
  `created_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `modified_time` DATETIME(3) DEFAULT NULL,
  `yn` tinyint(1) unsigned,
  PRIMARY KEY (`id`),
  INDEX `manual_alarm_record_vehicle_name_idx` (`report_time`,`vehicle_name`),
  INDEX `manual_alarm_record_operate_user_idx` (`report_time`,`source`,`report_user`),
  INDEX `manual_alarm_record_operate_cockpit_idx` (`report_time`,`source`, `cockpit_number`));


ALTER TABLE `rover_monitor`.`guardian_vehicle_alarm_info` ADD INDEX `idx_guardian_vehicle_alarm_number` (`alarm_number`);


