drop table if exists `accident_jdme_push`;
create table `accident_jdme_push` (
    id int(11) not null AUTO_INCREMENT comment '主键',
    shadow_event_id int(11) comment '影子系统事件编号',
    accident_no varchar(64) comment '事故编号',
    accident_group_id varchar(64) comment '事故群号',
    title varchar(128) comment '标题',
    vehicle_name varchar(64) comment '车牌号',
    level varchar(64) comment '事故等级',
    severity varchar(64) comment '严重程度',
    bug_id bigint comment '缺陷ID',
    bug_code varchar(64) comment '缺陷编号',
    debug_time text comment '发生时间',
    accident_address varchar(255) comment '发生位置',
    status varchar(16) comment '推送状态（INIT、SUCCESS、FAIL）',
    video_url varchar(512) comment '事故视频地址',
    event_play_url varchar(512) comment 'PC端整车回放地址',
    follower varchar(64) comment '问题跟进人ERP',
    deleted datetime(3) DEFAULT '1970-01-01 08:00:01.000' comment '删除时间',
    create_time datetime(3) not null DEFAULT CURRENT_TIMESTAMP(3) comment '创建时间',
    create_user varchar(64) comment '创建用户',
    modify_time datetime(3) not null DEFAULT CURRENT_TIMESTAMP(3) comment '修改时间',
    modify_user varchar(64) comment '修改用户',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='事故消息推送表';
ALTER TABLE `accident_jdme_push`
	ADD INDEX `accident_group_id` (`accident_group_id`),
	ADD INDEX `shadow_event_id` (`shadow_event_id`);