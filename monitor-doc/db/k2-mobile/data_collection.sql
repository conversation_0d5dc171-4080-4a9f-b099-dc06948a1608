/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

CREATE TABLE `data_collection_requirement` (
   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
   `requirement_number` varchar(63) NOT NULL COMMENT '需求编号',
   `status` varchar(63) NOT NULL DEFAULT "ONGOING" COMMENT '需求状态',
   `progress` double NOT NULL DEFAULT 0 COMMENT '任务进度',
   `description` varchar(255) NOT NULL COMMENT '需求说明',
   `required_detail` varchar(255) COMMENT '必要事项',
   `forbidden_detail` varchar(255) COMMENT '禁忌事项',
   `create_user` varchar(63) COMMENT '创建用户',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `modify_user` varchar(63) COMMENT '修改用户',
   `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `deleted` datetime DEFAULT '1970-01-01 08:00:01' COMMENT '删除时间',
   PRIMARY KEY (`id`),
   INDEX `idx_task_number_nor`(`requirement_number`)
) ENGINE = InnoDb DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据采集需求表';

CREATE TABLE `data_collection_tag` (
   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
   `tag_name` varchar(63) NOT NULL COMMENT '标签名称',
   `create_user` varchar(63) COMMENT '创建用户',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `modify_user` varchar(63) COMMENT '修改用户',
   `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `deleted` datetime DEFAULT '1970-01-01 08:00:01' COMMENT '删除时间',
   PRIMARY KEY (`id`)
) ENGINE = InnoDb DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据采集标签表';

CREATE TABLE `data_collection_requirement_tag` (
   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
   `requirement_id` int(11) NOT NULL COMMENT '需求id',
   `tag_id` int(11) NOT NULL COMMENT '标签id',
   `tag_name` varchar(63) NOT NULL COMMENT '标签名称',
   `parent_tag_id` int(11) NOT NULL COMMENT '父级标签id',
   `fe_id` int(11) NULL COMMENT '前端标签id',
   `fe_parent_id` int(11) NULL COMMENT '前端父标签id',
   `tag_type` varchar(63) DEFAULT "DETAIL" COMMENT '标签类型',
   `count` int(11) NOT NULL DEFAULT 1 COMMENT '标签需求数量',
   `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
   `create_user` varchar(63) COMMENT '创建用户',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `modify_user` varchar(63) COMMENT '修改用户',
   `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `deleted` datetime DEFAULT '1970-01-01 08:00:01' COMMENT '删除时间',
   PRIMARY KEY (`id`),
   INDEX `idx_requirement_tag_nor`(`requirement_id`, `tag_id`)
) ENGINE = InnoDb DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据采集需求关联标签表';

CREATE TABLE `data_collection_task` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `start_time` datetime COMMENT '任务开始时间',
    `end_time` datetime COMMENT '任务结束时间',
    `vehicle_name` varchar(31) COMMENT '任务车号',
    `status` varchar(63) COMMENT '任务状态',
    `address` varchar(63) COMMENT '采集地点',
    `drive_path` varchar(1024) COMMENT '采集路径',
    `linked_scene` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关联场景',
    `create_user` varchar(63) COMMENT '创建用户',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_user` varchar(63) COMMENT '修改用户',
    `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` datetime DEFAULT '1970-01-01 08:00:01' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    INDEX `idx_data_collection_task_time`(`start_time`, `vehicle_name`),
    INDEX `idx_data_collection_task_vehicle`(`vehicle_name`)
) ENGINE = InnoDb DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据采集任务';

CREATE TABLE `data_collection_scene` (
     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
     `vehicle_name` varchar(31) COMMENT '车号',
     `scene_number` varchar(31) COMMENT '场景编号',
     `start_time` datetime COMMENT '场景开始时间',
     `end_time` datetime COMMENT '场景结束时间',
     `report_time` datetime NOT NULL COMMENT '场景上报时间',
     `status` varchar(63) COMMENT '场景状态',
     `audio_file_key` varchar(63) COMMENT '采集语音',
     `audio_recognition_result` varchar(1023) COMMENT '语音转换中文',
     `audio_recognition_key_words` varchar(1023) COMMENT '语音关键字',
     `drive_path` varchar(1023) COMMENT '行驶路径',
     `create_user` varchar(63) COMMENT '创建用户',
     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modify_user` varchar(63) COMMENT '修改用户',
     `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `deleted` datetime DEFAULT '1970-01-01 08:00:01' COMMENT '删除时间',
     PRIMARY KEY (`id`),
     INDEX `idx_data_collection_scene_time`(`start_time`, `vehicle_name`),
     INDEX `idx_data_collection_scene_vehicle`(`vehicle_name`),
     INDEX `idx_data_collection_scene_report_time`(`report_time`, `vehicle_name`)
) ENGINE = InnoDb DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据采集场景';

CREATE TABLE `data_collection_scene_tag` (
     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
     `scene_id` int(11) NOT NULL COMMENT '场景标识',
     `tag_id` int(11) NOT NULL COMMENT '标签标识',
     `tag_name` varchar(63) NOT NULL COMMENT '标签名称',
     `create_user` varchar(63) COMMENT '创建用户',
     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modify_user` varchar(63) COMMENT '修改用户',
     `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `deleted` datetime DEFAULT '1970-01-01 08:00:01' COMMENT '删除时间',
     PRIMARY KEY (`id`),
     INDEX `idx_data_collection_scene_tag_id`(`scene_id`, `tag_id`)
) ENGINE = InnoDb DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据采集场景标签关联表';

CREATE TABLE `data_collection_scene_requirement` (
     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
     `scene_id` int(11) NOT NULL COMMENT '场景标识',
     `requirement_id` int(11) NOT NULL COMMENT '需求标识',
     `linked` tinyint(1) NULL DEFAULT 1 COMMENT '是否关联',
     `over_lap` int(11) NULL DEFAULT 1 COMMENT '关联重合度',
     `create_user` varchar(63) COMMENT '创建用户',
     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modify_user` varchar(63) COMMENT '修改用户',
     `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `deleted` datetime DEFAULT '1970-01-01 08:00:01' COMMENT '删除时间',
     `manual` tinyint(1) NULL DEFAULT 0 COMMENT '是否手动添加',
     PRIMARY KEY (`id`),
     INDEX `idx_data_collection_scene_requirement`(`scene_id`, `requirement_id`)
) ENGINE = InnoDb DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据采集场景需求关联表';