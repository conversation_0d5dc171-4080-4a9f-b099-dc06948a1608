DROP TABLE IF EXISTS vehicle_position;
CREATE TABLE vehicle_position(
    id SERIAL NOT NULL,
    vehicle_name VARCHAR(63) NOT null,
    point public.geometry(point) NOT null,
    deleted TIMESTAMP NOT NULL DEFAULT '1970-01-01 08:00:01',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user VARCHAR(63) DEFAULT null,
    modify_user VARCHAR(63) DEFAULT null,
    PRIMARY KEY (id)
);
COMMENT ON TABLE vehicle_position IS '车辆实时位置表';
COMMENT ON COLUMN vehicle_position.id IS '主键ID';
COMMENT ON COLUMN vehicle_position.vehicle_name IS '车辆名称';
COMMENT ON COLUMN vehicle_position.point IS '车辆位置';
COMMENT ON COLUMN vehicle_position.deleted IS '是否删除';
COMMENT ON COLUMN vehicle_position.create_time IS '创建时间';
COMMENT ON COLUMN vehicle_position.modify_time IS '最近修改时间';
COMMENT ON COLUMN vehicle_position.create_user IS '创建人';
COMMENT ON COLUMN vehicle_position.modify_user IS '最近修改人';
CREATE UNIQUE INDEX IF NOT EXISTS vehicle_name_uk ON vehicle_position (vehicle_name);