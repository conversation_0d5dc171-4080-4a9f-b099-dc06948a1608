/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

CREATE TABLE `map_collection_task` (
   `id` INT (11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
   `task_name` VARCHAR (63) NOT NULL COMMENT '任务名称',
   `task_status` VARCHAR (63) NOT NULL DEFAULT 'UNDER_EXPLORATION' COMMENT '任务状态',
   `task_creator` VARCHAR (63) NOT NULL COMMENT '任务创建人',
   `city_id` INT (11) NOT NULL COMMENT '城市ID',
   `station_id` INT (11) NOT NULL COMMENT '站点ID',
   `task_route` JSON DEFAULT NULL COMMENT '勘查线路',
   `total_mileage` DOUBLE DEFAULT NULL COMMENT '勘查线路总里程',
   `route_color` VARCHAR (63) NULL DEFAULT 'GREEN' COMMENT '线路颜色',
   `four_lane` JSON DEFAULT NULL COMMENT '四车道',
   `road_names` JSON DEFAULT NULL COMMENT '线路名称列表',
   `vehicle_name` VARCHAR (63) DEFAULT NULL COMMENT '采集车辆名称',
   `task_submit_time` DATETIME DEFAULT NULL COMMENT '任务提交时间',
   `create_user` VARCHAR (63) COMMENT '创建用户',
   `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `modify_user` VARCHAR (63) COMMENT '修改用户',
   `modify_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `deleted` DATETIME DEFAULT '1970-01-01 08:00:01.000' COMMENT '删除时间',
   PRIMARY KEY (`id`),
   KEY `idx_vehicle_status_nor` (`vehicle_name`, `task_status`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COMMENT = '勘查任务表';

CREATE TABLE `map_collection_mark` (
   `id` INT (11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
   `latitude` DECIMAL (10, 7) NOT NULL COMMENT '纬度（厘米）',
   `longitude` DECIMAL (10, 7) NOT NULL COMMENT '经度（厘米）',
   `mark_type` VARCHAR (63) NOT NULL COMMENT '标记类型',
   `address_name` VARCHAR (255) NOT NULL COMMENT '标记地址名称',
   `remark` VARCHAR (255) DEFAULT NULL COMMENT '备注',
   `attachment_list` JSON DEFAULT NULL COMMENT '附件',
   `create_user` VARCHAR (63) COMMENT '创建用户',
   `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `modify_user` VARCHAR (63) COMMENT '修改用户',
   `modify_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `deleted` DATETIME DEFAULT '1970-01-01 08:00:01.000' COMMENT '删除时间',
   PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COMMENT = '勘查标记表';

ALTER TABLE map_collection_task ADD COLUMN route_plan_type VARCHAR(63) DEFAULT 'MOTOR_VEHICLE' COMMENT '线路方案' AFTER road_names;
ALTER TABLE map_collection_task ADD COLUMN precise_type VARCHAR(63) DEFAULT 'STANDARD_PRECISION' COMMENT '精度方案，默认标精' AFTER road_names;
ALTER TABLE map_collection_task ALTER COLUMN `route_color` SET DEFAULT 'YELLOW';

ALTER TABLE map_collection_task ADD COLUMN task_route_type VARCHAR(63) DEFAULT 'SETOUT' COMMENT '线路类型' AFTER task_creator;