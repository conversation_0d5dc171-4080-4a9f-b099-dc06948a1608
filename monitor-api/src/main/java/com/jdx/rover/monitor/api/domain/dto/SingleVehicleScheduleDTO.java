/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 监控单车页调度信息
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleScheduleDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 时间
   */
  private Date recordTime;

  /**
   * 调度单号
   */
  private String scheduleNo;

  /**
   * 调度状态
   */
  private String scheduleState;

  /**
   * 调度任务
   */
  private String taskType;

  /**
   * 当前停靠点索引
   */
  private Integer currentGoalIndex;

  /**
   * 规划总里程
   */
  private Double globalMileage;

  /**
   * 完成里程
   */
  private Double finishedMileage = 0.0;

  /**
   * 开始时间
   */
  private Date startDateTime;

  /**
   * 预计开始时间
   */
  private Date estDepartureTime;

  /**
   * 停靠点列表
   */
  private List<SingleVehicleScheduleStopDTO> stopList;
}
