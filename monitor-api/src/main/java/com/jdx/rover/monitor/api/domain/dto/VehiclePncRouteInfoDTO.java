/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is a view object class for vehicle pnc info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehiclePncRouteInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 记录时间
   */
  private Date recordTime;

  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 调度单号
   */
  private String scheduleNo;

  /**
   * 规划总里程
   */
  private Double globalMileage;

  /**
   * 路径重新规划标志
   */
  private Integer naviRefreshId;

  /**
   * 规划停靠点
   */
  private List<VehiclePncRouteStopInfoDTO> navigationStop;

}
