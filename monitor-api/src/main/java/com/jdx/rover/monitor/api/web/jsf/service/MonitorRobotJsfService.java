/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.web.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.*;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceAbnormalSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceBasicSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDevicePageSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotProductSearchVO;

import java.util.List;

/**
 * <p>
 * 监控机器人服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorRobotJsfService {

    HttpResult<List<RobotTreeGroupBasicInfoDTO>> getDeviceGroupTree(RobotProductSearchVO productSearchVo);

    HttpResult<RobotDevicePageSearchDTO> pageSearchRobotDevice(RobotDevicePageSearchVO devicePageSearchVo);

    HttpResult<RobotDeviceBasicDetailInfoDTO> getDeviceBasic(RobotDeviceBasicSearchVO deviceBasicSearchVo);

    HttpResult<RobotDeviceRealtimeStateDTO> getDeviceRealtimeStatus(RobotDeviceBasicSearchVO deviceBasicSearchVo);

    HttpResult<List<RobotDeviceAbnormalInfoDTO>> getDeviceAbnormalInfo(RobotDeviceAbnormalSearchVO deviceAbnormalSearchVo);

}
