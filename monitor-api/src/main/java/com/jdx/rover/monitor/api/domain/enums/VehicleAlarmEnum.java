/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆告警枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleAlarmEnum {
    VEHICLE_STOP_FATAL("VEHICLE_STOP_FATAL", "异常停车", "VEHICLE_ALARM"),
    LOW_PRESSURE("LOW_PRESSURE", "胎压异常", "VEHICLE_ALARM"),
    LOW_BATTERY("LOW_BATTERY", "电量低", "VEHICLE_ALARM"),
    VEHICLE_CRASH("VEHICLE_CRASH", "碰撞", "VEHICLE_ALARM"),
    SENSOR_ERROR("SENSOR_ERROR", "传感器异常", "VEHICLE_ALARM"),
    VEHICLE_STOP_TIMEOUT("VEHICLE_STOP_TIMEOUT", "遇阻停车", "VEHICLE_ALARM"),
    VEHICLE_STOP_GUARDIAN("VEHICLE_STOP_GUARDIAN", "安全接管停车", "VEHICLE_ALARM"),
    VEHICLE_STOP_TRAFFICLIGHT_FAIL("VEHICLE_STOP_TRAFFICLIGHT_FAIL", "感知看灯失败", "VEHICLE_ALARM"),
    VEHICLE_STOP_INTERSECTION_STUCK("VEHICLE_STOP_INTERSECTION_STUCK", "路口遇阻", "VEHICLE_ALARM"),
    VEHICLE_STOP_TRAFFICLIGHT_ADJUST("VEHICLE_STOP_TRAFFICLIGHT_ADJUST", "路口看灯调整失败", "VEHICLE_ALARM"),
    VEHICLE_STOP_TRAFFICLIGHT_TAKEUP("VEHICLE_STOP_TRAFFICLIGHT_TAKEUP", "看灯点被占", "VEHICLE_ALARM"),
    VEHICLE_STOP_STOP_TAKEUP("VEHICLE_STOP_STOP_TAKEUP", "停靠点被占", "VEHICLE_ALARM"),
    VEHICLE_STOP_STOP_ADJUST("VEHICLE_STOP_STOP_ADJUST", "停靠点调整失败", "VEHICLE_ALARM"),
    VEHICLE_STOP_BUTTON("VEHICLE_STOP_BUTTON", "按钮停车", "VEHICLE_ALARM"),
    PASS_NO_SIGNAL_INTERSECTION("PASS_NO_SIGNAL_INTERSECTION", "无保护左转", "VEHICLE_ALARM"),
    BOOT_FAIL("BOOT_FAIL", "开机失败", "VEHICLE_ALARM"),
    BOOT_TIMEOUT("BOOT_TIMEOUT", "开机过久", "VEHICLE_ALARM"),
    BOOT_ABNORMAL("BOOT_ABNORMAL", "异常重启", "VEHICLE_ALARM"),
    BOOT_ABNORMAL_FAIL("BOOT_ABNORMAL_FAIL", "异常重启失败", "VEHICLE_ALARM"),
    GATE_STUCK("GATE_STUCK", "过门遇阻", "VEHICLE_ALARM"),
    GUARDIAN_LOST("GUARDIAN_LOST", "失联", "VEHICLE_ALARM"),
    INTERSECTION_OVER_LINE("INTERSECTION_OVER_LINE", "路口压线", "VEHICLE_ALARM"),
    TIRE_PUNCTURE("TIRE_PUNCTURE", "爆胎/扎胎", "VEHICLE_ALARM"),
    PARK_HARD("PARK_HARD", "停靠困难", "VEHICLE_ALARM"),
    PRESSURE_SHARP_DECREASE("PRESSURE_SHARP_DECREASE", "胎压骤减", "VEHICLE_ALARM"),
    CPU_HIGH_TEMPERATURE("CPU_HIGH_TEMPERATURE", "CPU温度过高", "VEHICLE_ALARM"),
    LOCALIZATION_ERROR("LOCALIZATION_ERROR", "定位异常", "VEHICLE_ALARM"),
    ROUTING_PLAN_FAIL("ROUTING_PLAN_FAIL", "Routing规划失败", "VEHICLE_ALARM"),
    RADAR_WARNING("RADAR_WARNING", "雷达预警", "VEHICLE_ALARM"),
    RADAR_FAILURE("RADAR_FAILURE", "雷达故障", "VEHICLE_ALARM"),
    AUDIBLE_AND_VISUAL_ALARM_FAILURE("AUDIBLE_AND_VISUAL_ALARM_FAILURE", "声光电故障", "VEHICLE_ALARM"),
    EDGE_SENSOR_FAILURE("EDGE_SENSOR_FAILURE", "触边故障", "VEHICLE_ALARM"),
    BATTERY_FAILURE("BATTERY_FAILURE", "电池故障", "VEHICLE_ALARM"),
    BRAKE_FAILURE("BRAKE_FAILURE", "刹车故障", "VEHICLE_ALARM"),
    PARKING_BRAKE_FAILURE("PARKING_BRAKE_FAILURE", "驻车故障", "VEHICLE_ALARM"),
    STEERING_EPS_FAILURE("STEERING_EPS_FAILURE", "转向EPS故障", "VEHICLE_ALARM"),
    ELECTRONIC_CONTROL_MOTOR_FAILURE("ELECTRONIC_CONTROL_MOTOR_FAILURE", "电控、电机故障", "VEHICLE_ALARM"),
    SIM_CARD_ABNORMAL("SIM_CARD_ABNORMAL", "SIM卡异常", "VEHICLE_ALARM"),
    CONNECTIVITY_FAILURE("CONNECTIVITY_FAILURE", "连通性失败", "VEHICLE_ALARM"),
    DOMAIN_CONTROLLER_ALARM("DOMAIN_CONTROLLER_ALARM", "域控告警", "VEHICLE_ALARM"),
    TIRE_PRESSURE_SENSOR_OFFLINE("TIRE_PRESSURE_SENSOR_OFFLINE", "胎压传感器离线", "VEHICLE_ALARM"),

    // 区域预警
    ATTENTION_REGION_WARN("ATTENTION_REGION_WARN", "关注预警", "CLOUD_ALARM"),
    PARKING_REGION_WARN("PARKING_REGION_WARN", "停车预警", "CLOUD_ALARM"),

    // 人工触发告警
    MANUAL_REPORT("MANUAL_REPORT", "人工告警", "MANUAL_ALARM"),
    BUG_REPORT("BUG_REPORT", "问题提报", "MANUAL_ALARM"),
    ;

    private String value;

    private String name;

    private String source;

    public static VehicleAlarmEnum of(final String value) {
        for (VehicleAlarmEnum itemEnum : VehicleAlarmEnum.values()) {
            if (itemEnum.value.equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }
}
