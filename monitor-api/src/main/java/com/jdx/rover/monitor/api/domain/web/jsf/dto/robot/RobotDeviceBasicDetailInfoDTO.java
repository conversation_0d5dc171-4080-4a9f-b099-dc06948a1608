/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto.robot;

import lombok.Data;

/**
 * <p>
 * 多合一设备设备基础信息
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotDeviceBasicDetailInfoDTO {

    /**
     * <p>
     * 产品标识
     * </p>
     */
    private String productKey;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备别名
     */
    private String remarkName;

    /**
     * <p>
     * 设备型号名
     * </p>
     */
    private String productModelName;

    /**
     * <p>
     * 分组信息
     * </p>
     */
    private RobotDeviceGroupInfoDTO groupInfo;

    /**
     * <p>
     * 安卓版本
     * </p>
     */
    private String androidVersion;

    /**
     * <p>
     * 设备版本
     * </p>
     */
    private String roverVersion;

    /**
     * <p>
     * 视频版本
     * </p>
     */
    private String videoVersion;

    /**
     * <p>
     * 地图版本
     * </p>
     */
    private String mapVersion;

    /**
     * <p>
     * 地图编号
     * </p>
     */
    private String mapId;

}
