/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 座席模式枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AcceptIssueStatusEnum {

  ACCEPT("ACCEPT", "接单"),
  STOP("STOP", "停止接单"),
  ;
  private String value;

  private String name;

  public static AcceptIssueStatusEnum of(final String value) {
    for (AcceptIssueStatusEnum itemEnum : AcceptIssueStatusEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
