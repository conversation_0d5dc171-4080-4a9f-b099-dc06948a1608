package com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet;

import lombok.Data;

/**
 * 事故分析传输对象
 */
@Data
public class AccidentAnalysisDTO {

    /**
     * 今日分析-高风险事故数
     */
    private Integer todayHighAccidents;

    /**
     * 今日分析-中风险事故数
     */
    private Integer todayMediumAccidents;

    /**
     * 今日分析-行驶里程
     */
    private String todayVehicleMileage;

    /**
     * 今日分析-安全组未跟进事故数
     */
    private Integer todayNoFollowUpAccidents;

    /**
     * 事故跟进-超过30天未解决事故数
     */
    private Integer accidentsUnresolvedMoreThan30Days;

    /**
     * 事故跟进-超过90天未解决事故数
     */
    private Integer accidentsUnresolvedMoreThan90Days;

    /**
     * 事故跟进-挂起事故数
     */
    private Integer suspendedAccidents;

    /**
     * 本周事故统计-本周时间段
     */
    private String weekDatePeriod;

    /**
     * 本周事故统计-事故总数
     */
    private Integer weekTotalAccidents;

    /**
     * 本周事故统计-高风险事故数
     */
    private Integer weekHighAccidents;

    /**
     * 本周事故统计-中风险事故数
     */
    private Integer weekMediumAccidents;

    /**
     * 本周事故统计-行驶里程
     */
    private String weekVehicleMileage;

    /**
     * 本周事故统计-中高风险事故率
     */
    private String weekAccidentRate;

    /**
     * 本月事故统计-本月时间段
     */
    private String mouthDatePeriod;

    /**
     * 本月事故统计-事故总数
     */
    private Integer mouthTotalAccidents;

    /**
     * 本月事故统计-高风险事故数
     */
    private Integer mouthHighAccidents;

    /**
     * 本月事故统计-中风险事故数
     */
    private Integer mouthMediumAccidents;

    /**
     * 本月事故统计-行驶里程
     */
    private String mouthVehicleMileage;

    /**
     * 本月事故统计-中高风险事故率
     */
    private String mouthAccidentRate;
}
