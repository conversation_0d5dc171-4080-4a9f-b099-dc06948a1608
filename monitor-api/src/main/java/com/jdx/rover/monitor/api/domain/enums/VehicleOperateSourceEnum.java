/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 指令操作来源
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleOperateSourceEnum {
  MONITOR("MONITOR", "监控"),
  MINI_MONITOR("MINI_MONITOR", "小程序"),
  REMOTE_JOYSTICK("REMOTE_JOYSTICK", "平行驾驶")
  ;

  /**
   * 指令操作方
   */
  private String source;

  /**
   * 指令操作方名称
   */
  private String sourceName;

  public static VehicleOperateSourceEnum of(String commandSource) {
    for (VehicleOperateSourceEnum em : VehicleOperateSourceEnum.values()) {
      if (Objects.equals(commandSource, em.getSource())) {
        return em;
      }
    }
    return null;
  }
}
