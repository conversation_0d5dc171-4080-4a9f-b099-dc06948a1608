/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 指令操作状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum OperateStateEnum {
  SUCCESS("SUCCESS", "成功"),
  FAIL("FAIL", "失败"),
  EXCEPTION("EXCEPTION", "异常"),

  ;

  /**
   * 指令操作方
   */
  private String state;

  /**
   * 指令操作方名称
   */
  private String stateName;

  public static OperateStateEnum of(String state) {
    for (OperateStateEnum em : OperateStateEnum.values()) {
      if (Objects.equals(state, em.getState())) {
        return em;
      }
    }
    return null;
  }

}
