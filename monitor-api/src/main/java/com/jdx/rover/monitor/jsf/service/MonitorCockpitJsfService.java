package com.jdx.rover.monitor.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.CockpitRealStatusDTO;

import java.util.List;

/**
 * <p>
 * 云驾座席服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorCockpitJsfService {

  /**
   * <p>
   * 获取全部座席实时状态
   * </p>
   *
   */
  HttpResult<List<CockpitRealStatusDTO>> getAllCockpitRealStatus();
}
