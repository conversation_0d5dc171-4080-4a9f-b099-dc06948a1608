/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 影子系统单车页实时模型
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleRealDTO {

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 时间
   */
  private Date recordTime;

  /**
   * 配送状态
   */
  private String scheduleState;

  /**
   * 配送任务号
   */
  private String scheduleNo;

  /**
   * 任务类型
   */
  private String taskType;

  /**
   * 系统状态
   */
  private String systemState;

  /**
   * 车辆模式
   */
  private String vehicleState;

  /**
   * 剩余电量
   */
  private Double power;

  /**
   * 速度
   */
  private Double speed;

  /**
   * 纬度
   */
  private Double latitude;

  /**
   * 经度
   */
  private Double longitude;

  /**
   * 零偏状态
   */
  private Double steerZero;

  /**
   * 定位置信度
   */
  private Double sceneSignal;

  /**
   * 当前站点完成里程
   */
  private Double currentStopFinishedMileage;

  /**
   * 当前调度完成里程
   */
  private Double currentScheduleFinishedMileage;

  /**
   * 调度信息
   */
  private SingleVehicleDrivableDirectionDTO vehicleDrivableDirection;

  /**
   * 告警事件
   */
  private List<SingleVehicleAlarmDTO> alarmEventList;

}
