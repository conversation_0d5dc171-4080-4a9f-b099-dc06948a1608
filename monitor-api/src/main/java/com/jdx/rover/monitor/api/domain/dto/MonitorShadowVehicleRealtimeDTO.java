/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is vehicle realtime for shadow.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorShadowVehicleRealtimeDTO {

  /**
   * 类型
   */
  private String type;

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 上报时间
   */
  private Date reportTime;

  /**
   * 调度状态
   */
  private String scheduleState;

  /**
   * 任务类型
   */
  private String taskType;

  /**
   * 系统状态
   */
  private String systemState;

  /**
   * 车辆模式
   */
  private String vehicleState;

  /**
   * 电量
   */
  private Double power;

  /**
   * 纬度
   */
  @Deprecated
  private Double lat;

  /**
   * 经度
   */
  @Deprecated
  private Double lon;

  /**
   * 速度
   */
  private Double speed;

  /**
   * 纬度
   */
  private Double latitude;

  /**
   * 经度
   */
  private Double longitude;

  /**
   * 朝向
   */
  private Double heading;

  /**
   * 总里程
   */
  private Double totalMileage;

  /**
   * 已完成里程
   */
  private Double currentStopFinishedMileage;

  /**
   * 调度已完成里程
   */
  private Double currentScheduleFinishedMileage;

  /**
   * 四路使能
   */
  private VehicleDrivableDirectionDTO vehicleDrivableDirection;

  /**
   * 车辆告警
   */
  private List<String> alarmEventList;

  /**
   * 车辆异常信息
   */
  private List<VehicleExceptionInfoDTO> exceptionInfo;

  /**
   * 车辆接管状态
   */
  private String takeOver;

  /**
   * 零偏状态
   */
  private Double steerZero;

  /**
   * 车辆调度
   */
  private VehicleScheduleDTO schedule;

  /**
   * 车辆版本
   */
  private VehicleVersionDTO version;

}
