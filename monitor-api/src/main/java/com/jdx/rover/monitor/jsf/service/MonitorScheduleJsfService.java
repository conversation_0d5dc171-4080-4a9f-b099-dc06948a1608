package com.jdx.rover.monitor.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.VehicleMileageDTO;
import com.jdx.rover.monitor.api.domain.vo.VehicleMileageVO;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public interface MonitorScheduleJsfService {

    /**
     * <p>
     * 影子系统获取车辆实时数据.
     * </p>
     *
     */
    HttpResult<List<VehicleMileageDTO>> getVehicleMileage(@NotNull(message = "非法请求") VehicleMileageVO vehicleMileageVO);
}
