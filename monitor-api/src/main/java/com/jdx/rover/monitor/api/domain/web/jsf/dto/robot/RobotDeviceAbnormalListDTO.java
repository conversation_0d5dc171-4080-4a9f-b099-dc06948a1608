/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto.robot;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 设备告警列表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotDeviceAbnormalListDTO {
    /**
     * <p>
     * 告警类型
     * </p>
     */
    private String alarmCode;

    /**
     * <p>
     * 告警类型名
     * </p>
     */
    private String alarmCodeName;

    /**
     * 异常级别
     */
    private String alarmLevel;

    /**
     * 异常级别名
     */
    private String alarmLevelName;

    /**
     * <p>
     * 触发时间
     * </p>
     */
    private Date reportTime;

}
