/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class VehicleVersionDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车辆版本
   */
  private String roverVersion;

  /**
   * 视频版本
   */
  private String videoVersion;

  /**
   * 安卓版本
   */
  private String androidVersion;

  /**
   * 地图版本
   */
  private String mapVersion;

  /**
   * 车辆版本
   */
  private String roverCloudVersion;

  /**
   * 视频版本
   */
  private String videoCloudVersion;

  /**
   * 安卓版本
   */
  private String androidCloudVersion;

}
