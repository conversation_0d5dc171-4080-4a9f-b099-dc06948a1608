package com.jdx.rover.monitor.api.domain.web.jsf.vo.user;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 保存用户配置VO
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Data
public class SaveUserDriveConfigJsfVO implements Serializable {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 座舱编号
     */
    private String cockpitNumber;

    /**
     * 用户名称
     */
    @NotBlank
    private String userName;

    /**
     * 速度限制
     */
    @NotNull
    private Integer speedLimit;
}
