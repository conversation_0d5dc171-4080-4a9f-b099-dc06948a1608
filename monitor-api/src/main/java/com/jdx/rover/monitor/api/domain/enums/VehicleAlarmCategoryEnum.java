/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆告警分类.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleAlarmCategoryEnum {
    // 失联
    GUARDIAN_LOST("GUARDIAN_LOST", "失联", "GUARDIAN_LOST", 101),

    // 碰撞
    VEHICLE_CRASH("VEHICLE_CRASH", "碰撞", "VEHICLE_CRASH", 201),

    // 硬件异常
    DOMAIN_CONTROLLER_ALARM("DOMAIN_CONTROLLER_ALARM", "域控告警", "HARDWARE_ERROR", 301),
    RADAR_WARNING("RADAR_WARNING", "雷达预警", "HARDWARE_ERROR", 302),
    RADAR_FAILURE("RADAR_FAILURE", "雷达故障", "HARDWARE_ERROR", 303),
    AUDIBLE_AND_VISUAL_ALARM_FAILURE("AUDIBLE_AND_VISUAL_ALARM_FAILURE", "声光电故障", "HARDWARE_ERROR", 304),
    EDGE_SENSOR_FAILURE("EDGE_SENSOR_FAILURE", "触边故障", "HARDWARE_ERROR", 305),
    BATTERY_FAILURE("BATTERY_FAILURE", "电池故障", "HARDWARE_ERROR", 306),
    BRAKE_FAILURE("BRAKE_FAILURE", "刹车故障", "HARDWARE_ERROR", 307),
    PARKING_BRAKE_FAILURE("PARKING_BRAKE_FAILURE", "驻车故障", "HARDWARE_ERROR", 308),
    STEERING_EPS_FAILURE("STEERING_EPS_FAILURE", "转向EPS故障", "HARDWARE_ERROR", 309),
    ELECTRONIC_CONTROL_MOTOR_FAILURE("ELECTRONIC_CONTROL_MOTOR_FAILURE", "电控、电机故障", "HARDWARE_ERROR", 310),

    // 网络信号
    SIM_CARD_ABNORMAL("SIM_CARD_ABNORMAL", "SIM卡异常", "NETWORK_SIGNAL", 401),
    CONNECTIVITY_FAILURE("CONNECTIVITY_FAILURE", "连通性失败", "NETWORK_SIGNAL", 402),

    // 人工告警
    MANUAL_REPORT("MANUAL_REPORT", "人工告警", "MANUAL", 501),

    // 传感器异常
    LOCALIZATION_ERROR("LOCALIZATION_ERROR", "定位异常", "SENSOR_ERROR", 601),
    SENSOR_ERROR("SENSOR_ERROR", "传感器异常", "SENSOR_ERROR", 602),

    // 开机异常
    BOOT_ABNORMAL_FAIL("BOOT_ABNORMAL_FAIL", "异常重启失败", "BOOT", 701),
    BOOT_ABNORMAL("BOOT_ABNORMAL", "异常重启", "BOOT", 702),
    BOOT_FAIL("BOOT_FAIL", "开机失败", "BOOT", 703),
    BOOT_TIMEOUT("BOOT_TIMEOUT", "开机过久", "BOOT", 704),

    // 路口异常
    PASS_NO_SIGNAL_INTERSECTION("PASS_NO_SIGNAL_INTERSECTION", "无保护左转", "INTERSECTION_ABNORMAL", 801),
    VEHICLE_STOP_TRAFFICLIGHT_TAKEUP("VEHICLE_STOP_TRAFFICLIGHT_TAKEUP", "看灯点被占", "INTERSECTION_ABNORMAL", 802),
    VEHICLE_STOP_TRAFFICLIGHT_FAIL("VEHICLE_STOP_TRAFFICLIGHT_FAIL", "感知看灯失败", "INTERSECTION_ABNORMAL", 803),
    VEHICLE_STOP_TRAFFICLIGHT_ADJUST("VEHICLE_STOP_TRAFFICLIGHT_ADJUST", "路口看灯调整失败", "INTERSECTION_ABNORMAL", 804),
    VEHICLE_STOP_INTERSECTION_STUCK("VEHICLE_STOP_INTERSECTION_STUCK", "路口遇阻", "INTERSECTION_ABNORMAL", 805),

    // 停车
    VEHICLE_STOP_BUTTON("VEHICLE_STOP_BUTTON", "按钮停车", "STOP", 901),
    GATE_STUCK("GATE_STUCK", "过门遇阻", "STOP", 902),
    INTERSECTION_OVER_LINE("INTERSECTION_OVER_LINE", "路口压线", "STOP", 903),
    VEHICLE_STOP_STOP_TAKEUP("VEHICLE_STOP_STOP_TAKEUP", "停靠点被占", "STOP", 904),
    VEHICLE_STOP_STOP_ADJUST("VEHICLE_STOP_STOP_ADJUST", "停靠点调整失败", "STOP", 905),
    VEHICLE_STOP_FATAL("VEHICLE_STOP_FATAL", "异常停车", "STOP", 906),
    CPU_HIGH_TEMPERATURE("CPU_HIGH_TEMPERATURE", "CPU温度过高", "STOP", 907),
    ROUTING_PLAN_FAIL("ROUTING_PLAN_FAIL", "Routing规划失败", "STOP", 908),
    VEHICLE_STOP_GUARDIAN("VEHICLE_STOP_GUARDIAN", "安全接管停车", "STOP", 909),
    PARK_HARD("PARK_HARD", "停靠困难", "STOP", 910),
    VEHICLE_STOP_TIMEOUT("VEHICLE_STOP_TIMEOUT", "遇阻停车", "STOP", 911),

    // 区域预警
    ATTENTION_REGION_WARN("ATTENTION_REGION_WARN", "关注预警", "REGION", 1001),
    PARKING_REGION_WARN("PARKING_REGION_WARN", "停车预警", "REGION", 1002),

    // 电量
    LOW_BATTERY("LOW_BATTERY", "电量低", "LOW_BATTERY", 1101),

    // 胎压
    TIRE_PRESSURE_SENSOR_OFFLINE("TIRE_PRESSURE_SENSOR_OFFLINE", "胎压传感器离线", "LOW_PRESSURE", 1201),
    TIRE_PUNCTURE("TIRE_PUNCTURE", "爆胎/扎胎", "LOW_PRESSURE", 1202),
    PRESSURE_SHARP_DECREASE("PRESSURE_SHARP_DECREASE", "胎压骤减", "LOW_PRESSURE", 1203),
    LOW_PRESSURE("LOW_PRESSURE", "胎压异常", "LOW_PRESSURE", 1204),
    ;

    /**
     * 类型
     */
    private String value;

    /**
     * 名称
     */
    private String name;

    /**
     * 分类
     */
    private String category;

    /**
     * 优先级
     */
    private final Integer priority;

    public static VehicleAlarmCategoryEnum of(final String value) {
        for (VehicleAlarmCategoryEnum itemEnum : VehicleAlarmCategoryEnum.values()) {
            if (itemEnum.value.equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }
}
