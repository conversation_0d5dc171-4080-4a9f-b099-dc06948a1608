package com.jdx.rover.monitor.api.web.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.AccidentAnalysisDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.AccidentBoardDetailDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.MonthlyAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.RecentAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.WeeklyAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.AccidentBoardDetailVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.MonthlyAccidentTrendsVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.RecentAccidentTrendsVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.WeeklyAccidentTrendsVO;

import java.util.List;

/**
 * 事故看板相关接口
 */
public interface MonitorAccidentBoardJsfService {

    /**
     * 获取事故分析
     * @return
     */
    HttpResult<AccidentAnalysisDTO> getAccidentAnalysis();

    /**
     * 近一段时间事故趋势
     * @param recentAccidentTrendsVO
     * @return
     */
    HttpResult<List<RecentAccidentTrendsDTO>> recentAccidentTrends(RecentAccidentTrendsVO recentAccidentTrendsVO);

    /**
     * 周度事故统计趋势图
     * @param weeklyAccidentTrendsVO
     * @return
     */
    HttpResult<List<WeeklyAccidentTrendsDTO>> weeklyAccidentTrends(WeeklyAccidentTrendsVO weeklyAccidentTrendsVO);

    /**
     * 月度事故统计趋势图
     * @param monthlyAccidentTrendsVO
     * @return
     */
    HttpResult<List<MonthlyAccidentTrendsDTO>> monthlyAccidentTrends(MonthlyAccidentTrendsVO monthlyAccidentTrendsVO);

    /**
     * 获取事故详情
     * @param accidentBoardDetailVO
     * @return
     */
    HttpResult<List<AccidentBoardDetailDTO>> getAccidentDetail(AccidentBoardDetailVO accidentBoardDetailVO);
}
