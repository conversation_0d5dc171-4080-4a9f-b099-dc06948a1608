/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

/**
 * <p>
 * This is vehicle take over info for shadow.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorShadowVehicleTakeOverDTO {

  /**
   * 工单状态
   */
  private String issueState;

  /**
   * 车号
   */
  private String takeOverUser;

  /**
   * 调度状态
   */
  private String vehicleState;

  /**
   * 调度状态
   */
  private String scheduleState;

}
