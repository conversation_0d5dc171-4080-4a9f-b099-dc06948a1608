/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆事件枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleEventEnum {
    VEHICLE_STOP_FATAL("VEHICLE_STOP_FATAL", "异常停车"),
    LOW_PRESSURE("LOW_PRESSURE", "胎压异常"),
    VEHICLE_CRASH("VEHICLE_CRASH", "碰撞"),
    SENSOR_ERROR("SENSOR_ERROR", "传感器异常"),
    VEHICLE_STOP_TIMEOUT("VEHICLE_STOP_TIMEOUT", "遇阻停车"),
    VEHICLE_STOP_GUARDIAN("VEHICLE_STOP_GUARDIAN", "安全接管停车"),
    VEHICLE_STOP_TRAFFICLIGHT_FAIL("VEHICLE_STOP_TRAFFICLIGHT_FAIL", "感知看灯失败"),
    VEHICLE_STOP_INTERSECTION_STUCK("VEHICLE_STOP_INTERSECTION_STUCK", "路口遇阻"),
    VEHICLE_STOP_TRAFFICLIGHT_ADJUST("VEHICLE_STOP_TRAFFICLIGHT_ADJUST", "路口看灯调整失败"),
    VEHICLE_STOP_TRAFFICLIGHT_TAKEUP("VEHICLE_STOP_TRAFFICLIGHT_TAKEUP", "看灯点被占"),
    VEHICLE_STOP_STOP_TAKEUP("VEHICLE_STOP_STOP_TAKEUP", "停靠点被占"),
    VEHICLE_STOP_STOP_ADJUST("VEHICLE_STOP_STOP_ADJUST", "停靠点调整失败"),
    VEHICLE_STOP_BUTTON("VEHICLE_STOP_BUTTON", "按钮停车"),
    PASS_NO_SIGNAL_INTERSECTION("PASS_NO_SIGNAL_INTERSECTION", "无保护左转"),
    BOOT_FAIL("BOOT_FAIL", "开机失败"),
    BOOT_TIMEOUT("BOOT_TIMEOUT", "开机过久"),
    BOOT_ABNORMAL("BOOT_ABNORMAL", "异常重启"),
    BOOT_ABNORMAL_FAIL("BOOT_ABNORMAL_FAIL", "异常重启失败"),
    GATE_STUCK("GATE_STUCK", "过门遇阻"),
    GUARDIAN_LOST("GUARDIAN_LOST", "失联"),
    INTERSECTION_OVER_LINE("INTERSECTION_OVER_LINE", "路口压线"),
    TIRE_PUNCTURE("TIRE_PUNCTURE", "爆胎/扎胎"),
    PARK_HARD("PARK_HARD", "停靠困难"),
    PRESSURE_SHARP_DECREASE("PRESSURE_SHARP_DECREASE", "胎压骤减"),
    CPU_HIGH_TEMPERATURE("CPU_HIGH_TEMPERATURE", "CPU温度过高"),
    LOCALIZATION_ERROR("LOCALIZATION_ERROR", "定位异常"),
    ROUTING_PLAN_FAIL("ROUTING_PLAN_FAIL", "Routing规划失败"),
    RADAR_WARNING("RADAR_WARNING", "雷达预警"),
    RADAR_FAILURE("RADAR_FAILURE", "雷达故障"),
    AUDIBLE_AND_VISUAL_ALARM_FAILURE("AUDIBLE_AND_VISUAL_ALARM_FAILURE", "声光电故障"),
    EDGE_SENSOR_FAILURE("EDGE_SENSOR_FAILURE", "触边故障"),
    BATTERY_FAILURE("BATTERY_FAILURE", "电池故障"),
    BRAKE_FAILURE("BRAKE_FAILURE", "刹车故障"),
    PARKING_BRAKE_FAILURE("PARKING_BRAKE_FAILURE", "驻车故障"),
    STEERING_EPS_FAILURE("STEERING_EPS_FAILURE", "转向EPS故障"),
    ELECTRONIC_CONTROL_MOTOR_FAILURE("ELECTRONIC_CONTROL_MOTOR_FAILURE", "电控、电机故障"),
    SIM_CARD_ABNORMAL("SIM_CARD_ABNORMAL", "SIM卡异常"),
    CONNECTIVITY_FAILURE("CONNECTIVITY_FAILURE", "连通性失败"),
    DOMAIN_CONTROLLER_ALARM("DOMAIN_CONTROLLER_ALARM", "域控告警"),
    TIRE_PRESSURE_SENSOR_OFFLINE("TIRE_PRESSURE_SENSOR_OFFLINE", "胎压传感器离线"),
    // 区域预警
    ATTENTION_REGION_WARN("ATTENTION_REGION_WARN", "关注预警"),
    PARKING_REGION_WARN("PARKING_REGION_WARN", "停车预警"),
    // 人工触发告警
    MANUAL_REPORT("MANUAL_REPORT", "人工告警"),
    BUG_REPORT("BUG_REPORT", "问题提报"),
    // 远程指令
    REMOTE_REQUEST_EMERGENCY_STOP("REMOTE_REQUEST_EMERGENCY_STOP", "急停"),
    REMOTE_REQUEST_TEMPORARY_STOP("REMOTE_REQUEST_TEMPORARY_STOP", "临时接管"),
    REMOTE_REQUEST_RECOVERY("REMOTE_REQUEST_RECOVERY", "急停恢复"),
    REMOTE_REQUEST_AS_ARRIVED("REMOTE_REQUEST_AS_ARRIVED", "视同到达"),
    REMOTE_REQUEST_RELIEVE_BUTTON_STOP("REMOTE_REQUEST_RELIEVE_BUTTON_STOP", "解除拍停"),

    REMOTE_REQUEST_REMOTE_CONTROL("REMOTE_REQUEST_REMOTE_CONTROL", "遥控指令"),

    REMOTE_REQUEST_PASS_LIGHT("REMOTE_REQUEST_PASS_LIGHT", "一键通过红绿灯"),
    REMOTE_REQUEST_CANCEL_LIGHT("REMOTE_REQUEST_CANCEL_LIGHT", "取消通过红绿灯"),

    REMOTE_REQUEST_REMOTE_RESTART("REMOTE_REQUEST_REMOTE_RESTART", "软重启"),
    REMOTE_CONTROL_POWER_REBOOT("REMOTE_CONTROL_POWER_REBOOT", "断电重启"),

    REMOTE_DRIVE_CONNECT_COCKPIT("REMOTE_DRIVE_CONNECT_COCKPIT", "平行驾驶接管"),
    REMOTE_DRIVE_DISCONNECT_COCKPIT("REMOTE_DRIVE_DISCONNECT_COCKPIT", "退出平行驾驶接管"),
    ;

    private String value;

    private String name;

    public static VehicleEventEnum of(final String value) {
        for (VehicleEventEnum itemEnum : VehicleEventEnum.values()) {
            if (itemEnum.value.equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }
}
