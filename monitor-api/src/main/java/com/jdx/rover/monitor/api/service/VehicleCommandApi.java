/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.api.service;

import com.jdx.rover.common.utils.result.HttpResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 * 车辆指令
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface VehicleCommandApi {

  /**
   * <p>
   *  获取用户接管的车辆列表
   * </p>
   *
   */
  @GetMapping("/monitor/web/command/getTakeOverVehicle")
  HttpResult getTakeOverVehicle(@RequestParam String userName);

}
