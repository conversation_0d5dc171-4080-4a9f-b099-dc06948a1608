/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.web.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.MonitorChangeVideoModeVO;

/**
 * <p>
 * 监控视频服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/12
 */
public interface MonitorVideoJsfService {

    HttpResult<Void> changeVideoMode(MonitorChangeVideoModeVO changeVideoModeVo);

}
