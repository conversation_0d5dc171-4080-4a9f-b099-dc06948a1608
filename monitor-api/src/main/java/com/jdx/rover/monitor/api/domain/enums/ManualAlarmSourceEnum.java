/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 人工告警来源
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ManualAlarmSourceEnum {
  MINI_MONITOR("MINI_MONITOR", "运营端小程序"),
  PATROL_MONITOR("PATROL_MONITOR", "监控巡查"),
  PATROL_REMOTE_CONTROL("PATROL_REMOTE_CONTROL", "平行驾驶巡查"),
  WORKBENCH_MONITOR("WORKBENCH_MONITOR", "小哥工作台"),
  ;

  /**
   * 来源
   */
  private String source;

  /**
   * 来源名
   */
  private String sourceName;

  public static ManualAlarmSourceEnum of(String source) {
    for (ManualAlarmSourceEnum em : ManualAlarmSourceEnum.values()) {
      if (Objects.equals(source, em.getSource())) {
        return em;
      }
    }
    return null;
  }

}
