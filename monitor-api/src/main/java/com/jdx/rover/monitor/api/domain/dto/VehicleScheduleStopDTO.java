/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class VehicleScheduleStopDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 识别ID
   */
  private Integer goalId;

  /**
   * 停靠点名
   */
  private String name;

  /**
   * 纬度
   */
  private Double latitude;

  /**
   * 经度
   */
  private Double longitude;

  /**
   * 执行动作
   */
  private String stopAction;

  /**
   * 行驶状态
   */
  private String travelStatus;

  /**
   * 到达时间
   */
  private Date arrivedTime;

  /**
   * 开始时间
   */
  private Date startTime;

  /**
   * 离开时间
   */
  private Date departTime;

  /**
   * 预计离开时间
   */
  private Date estDepartTime;

  /**
   * 等待时间
   */
  private Integer waitingTime;

  /**
   * 总订单数
   */
  private Integer totalOrderNum;

  /**
   * 总里程
   */
  private Double globalMileage;

}
