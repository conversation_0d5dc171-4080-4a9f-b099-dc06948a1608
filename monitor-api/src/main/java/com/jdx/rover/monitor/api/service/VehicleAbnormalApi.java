/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.api.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.vo.VehicleAbnormalVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * This is api interface for abnormal info.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface VehicleAbnormalApi {

  /**
   * <p>
   * Post vehicle abnormal request.
   * </p>
   *
   */
  @PostMapping("/monitor/web/abnormal/list")
  HttpResult getVehicleRealtimeInfo(@RequestBody VehicleAbnormalVO vehicleAbnormalVo);

}
