/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆操作类型枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleOperateTypeEnum {

  REMOTE_REQUEST_EMERGENCY_STOP("REMOTE_REQUEST_EMERGENCY_STOP", "急停"),
  REMOTE_REQUEST_TEMPORARY_STOP("REMOTE_REQUEST_TEMPORARY_STOP", "临时接管"),
  REMOTE_REQUEST_RECOVERY("REMOTE_REQUEST_RECOVERY", "急停恢复"),
  REMOTE_REQUEST_AS_ARRIVED("REMOTE_REQUEST_AS_ARRIVED", "视同到达"),
  REMOTE_REQUEST_RELIEVE_BUTTON_STOP("REMOTE_REQUEST_RELIEVE_BUTTON_STOP","解除拍停"),

  REMOTE_REQUEST_REMOTE_CONTROL("REMOTE_REQUEST_REMOTE_CONTROL", "遥控指令"),

  REMOTE_REQUEST_PASS_LIGHT("REMOTE_REQUEST_PASS_LIGHT",  "一键通过红绿灯"),
  REMOTE_REQUEST_CANCEL_LIGHT("REMOTE_REQUEST_CANCEL_LIGHT", "取消通过红绿灯"),

  REMOTE_REQUEST_REMOTE_RESTART("REMOTE_REQUEST_REMOTE_RESTART", "软重启"),
  REMOTE_CONTROL_POWER_REBOOT("REMOTE_CONTROL_POWER_REBOOT","断电重启"),

  REMOTE_REQUEST_WEB_TERMINAL("REMOTE_REQUEST_WEB_TERMINAL", "访问远程工具"),


  REMOTE_DRIVE_CONNECT_COCKPIT("REMOTE_DRIVE_CONNECT_COCKPIT", "平行驾驶接管"),
  REMOTE_DRIVE_DISCONNECT_COCKPIT("REMOTE_DRIVE_DISCONNECT_COCKPIT", "退出平行驾驶接管"),
  ;

  private String value;

  private String name;

  public static VehicleOperateTypeEnum of(final String value) {
    for (VehicleOperateTypeEnum itemEnum : VehicleOperateTypeEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }

}
