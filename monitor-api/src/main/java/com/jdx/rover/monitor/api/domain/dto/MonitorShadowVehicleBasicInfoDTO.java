/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

/**
 * <p>
 * This is a vehicle basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorShadowVehicleBasicInfoDTO {

  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the bussiness type of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String businessType;

  /**
   * <p>
   * Represents the version of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleVersion;

  /**
   * <p>
   * Represents the version of android. The default value is null. It's
   * changeable.
   * </p>
   */
  private String androidVersion;

  /**
   * <p>
   * Represents the version of video. The default value is null. It's
   * changeable.
   * </p>
   */
  private String videoVersion;

  /**
   * <p>
   * Represents the version of map. The default value is null. It's
   * changeable.
   * </p>
   */
  private String mapVersion;

  /**
   * <p>
   * Represents the cityName of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the name of station. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationName;

    /**
   * <p>
   * Represents the user of station. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationUser;

  /**
   * <p>
   * Represents the phone of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationUserPhone;

  /**
   * <p>
   * Represents the use case of station. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationUseCase;

}
