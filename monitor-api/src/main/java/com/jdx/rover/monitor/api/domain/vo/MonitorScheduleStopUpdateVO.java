/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.api.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * This is a view object for schedule stop.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleStopUpdateVO {

  /**
   * <p>
   * Represents the vehicle name.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the schedule no.
   * </p>
   */
  @NotBlank
  private String scheduleNo;

  /**
   * <p>
   * Represents the schedule stop id.
   * </p>
   */
  private Integer stopId;

  /**
   * <p>
   * Represents the schedule goal id.
   * </p>
   */
  private Integer goalId;

  /**
   * <p>
   * Represents the schedule goal waiting time.
   * </p>
   */
  private Integer waitingTime;

}
