/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * 监控单车页调度信息
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleOrderDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 时间
   */
  private Date recordTime;

  /**
   * 调度单号
   */
  private String scheduleNo;

  /**
   * 订单号
   */
  private String orderId;

  /**
   * 原始单号
   */
  private String originalId;

  /**
   * 配送状态
   */
  private String deliveryStatus;

  /**
   * 联系方式
   */
  private String contact;

  /**
   * 联系人
   */
  private String name;
  /**
   * 包裹数
   */
  private Integer packageIndex;

}
