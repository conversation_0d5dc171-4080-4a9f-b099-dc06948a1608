/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.web.jsf.vo;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;

import java.util.List;

/**
 * 分页查询PDA设备请求
 *
 * <AUTHOR>
 */
@Data
public class PdaPageSearchVO extends PageVO {
  /**
   * 设备编号
   */
  private String deviceName;

  /**
   * 设备状态
   */
  private String realtimeStatus;

  /**
   * 一级分组
   */
  private String groupOne;

  /**
   * 二级分组
   */
  private String groupTwo;

  /**
   * 三级分组
   */
  private String groupThree;

  /**
   * 产品型号
   */
  private List<String> productModelNo;

  /**
   * 网络状态
   */
  private String networkType;

  /**
   * 定位状态
   */
  private Integer positionStatus;

  /**
   * 埋点状态
   */
  private Integer burialStatus;

}
