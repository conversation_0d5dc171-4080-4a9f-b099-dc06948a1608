package com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 事故看板明细
 */
@Data
public class AccidentBoardDetailDTO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 事故等级
     */
    private String accidentLevelName;

    /**
     * 事故模块
     */
    private String accidentModuleName;

    /**
     * 事故标签
     */
    private String accidentTagName;

    /**
     * 问题描述
     */
    private String bugDescription;

    /**
     * 关键问题分析
     */
    private String safetyGroupDescription;

    /**
     * 事故发生时间
     */
    private Date accidentTime;

    /**
     * 缺陷生成时间
     */
    private Date bugCreateTime;

    /**
     * 缺陷状态
     */
    private String bugStatusName;

    /**
     * 缺陷链接
     */
    private String bugUrl;
}
