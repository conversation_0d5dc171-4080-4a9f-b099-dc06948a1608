/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 座席实时状态
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class CockpitRealStatusDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 座席编号
     */
    private String cockpitNumber;

    /**
     * 座席用户名称
     */
    private String cockpitUserName;

    /**
     * 座席状态
     */
    private String cockpitStatus;

    /**
     * 接单状态
     */
    private String acceptIssueStatus;

    /**
     * 座席模式
     */
    private String cockpitMode;

    /**
     * 记录时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;
}