/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto;

import com.jdx.rover.common.domain.page.PageDTO;
import lombok.Data;

/**
 * <p>
 * pda分页查询结果
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class PdaPageSearchDTO extends PageDTO<PdaRealtimeBasicInfoDTO> {

  /**
   * 在线设备数量
   */
  private Long onlineCount = 0L;

  /**
   * 离线设备数量
   */
  private Long offlineCount = 0L;

  /**
   * 设备总数量
   */
  private Long totalCount = 0L;

  /**
   * 异常设备数量
   */
  private Long abnormalCount = 0L;


}
