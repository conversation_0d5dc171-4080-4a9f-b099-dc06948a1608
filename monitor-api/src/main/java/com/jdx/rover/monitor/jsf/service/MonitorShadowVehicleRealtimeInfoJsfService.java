package com.jdx.rover.monitor.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleAlarmDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleBasicInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleRealtimeDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleTakeOverDTO;
import jakarta.validation.constraints.NotEmpty;

/**
 * <p>
 * This is api interface for vehicle realtime info.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorShadowVehicleRealtimeInfoJsfService {

  /**
   * <p>
   * 影子系统获取车辆实时数据.
   * </p>
   *
   */
  HttpResult<MonitorShadowVehicleRealtimeDTO> getVehicleRealtimeInfo(@NotEmpty(message = "车号不能为空") String vehicleName);

  /**
   * <p>
   * 影子系统获取车辆基础数据.
   * </p>
   *
   */
  HttpResult<MonitorShadowVehicleBasicInfoDTO> getVehicleBasicInfo(@NotEmpty(message = "车号不能为空") String vehicleName);

  /**
   * <p>
   * 影子系统获取车辆接管状态.
   * </p>
   *
   */
  HttpResult<MonitorShadowVehicleTakeOverDTO> getVehicleTakeOverInfo(@NotEmpty(message = "车号不能为空") String vehicleName);

  /**
   * <p>
   * 影子系统获取车辆告警数据.
   * </p>
   *
   */
  HttpResult<MonitorShadowVehicleAlarmDTO> getVehicleRealtimeAlarmInfo(@NotEmpty(message = "车号不能为空") String vehicleName);
}
