package com.jdx.rover.monitor.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.vo.MonitorScheduleStopUpdateVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <p>
 * This is api interface for schedule command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorVehicleScheduleStopJsfService {

  /**
   * <p>
   * Post update schedule stop request.
   * </p>
   *
   */
  HttpResult<Void> updateScheduleStop(@NotNull(message = "非法请求") @Valid MonitorScheduleStopUpdateVO monitorScheduleStopUpdateVo);

}
