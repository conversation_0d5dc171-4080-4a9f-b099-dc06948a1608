/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 车辆告警事件
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleAlarmDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 时间
   */
  private Date recordTime;

  /**
   * 时间
   */
  private String alarmSource;

  /**
   * 实时列表
   */
  private List<AlarmInfoDTO> alarmEventList;

}
