/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto.robot;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 机器人设备分组树
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotTreeGroupBasicInfoDTO {

  /**
   * 分组编号
   */
  private String groupNo;

  /**
   * 分组名称
   */
  private String groupName;

  /**
   * 子分组
   */
  private List<RobotTreeGroupBasicInfoDTO> child;

  /**
   * 设备总数
   */
  private Integer totalCount = 0;

  /**
   * 设备在线数
   */
  private Integer onlineCount = 0;

  /**
   * 设备列表
   */
  private List<RobotTreeDeviceRealtimeInfoDTO> deviceList;

}
