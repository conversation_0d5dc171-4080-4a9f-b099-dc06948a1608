/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 座席状态枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum CockpitStatusEnum {

  WORK("WORK", "工作"),
  REST("REST", "休息"),
  OFFLINE("OFFLINE", "离线"),
  ;

  /**
   * 状态值
   */
  private String value;

  /**
   * 状态名
   */
  private String name;

  public static CockpitStatusEnum of(final String value) {
    for (CockpitStatusEnum itemEnum : CockpitStatusEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
