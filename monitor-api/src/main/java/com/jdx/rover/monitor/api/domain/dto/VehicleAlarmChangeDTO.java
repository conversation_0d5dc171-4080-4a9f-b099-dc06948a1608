/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 车辆告警变化信息
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleAlarmChangeDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 时间
   */
  private Date recordTime;

  /**
   * <p>
   * 纬度
   * </p>
   */
  private Double latitude;

  /**
   * <p>
   * 经度
   * </p>
   */
  private Double longitude;

  /**
   * 新增告警列表
   */
  private List<AlarmInfoDTO> addAlarm;

  /**
   * 消失列表
   */
  private List<AlarmInfoDTO> disappearAlarm;

  /**
   * 更新列表
   */
  private List<AlarmInfoDTO> updateAlarm;

  /**
   * 实时列表
   */
  private List<AlarmInfoDTO> currentAlarm;

}
