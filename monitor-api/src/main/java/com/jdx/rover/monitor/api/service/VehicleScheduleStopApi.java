/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.api.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.vo.MonitorScheduleStopUpdateVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * This is api interface for schedule command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface VehicleScheduleStopApi {

  /**
   * <p>
   * Post update schedule stop request.
   * </p>
   *
   */
  @PostMapping("/monitor/schedule/schedule_stop/update")
  HttpResult updateScheduleStop(@RequestBody MonitorScheduleStopUpdateVO monitorScheduleStopUpdateVo);

}
