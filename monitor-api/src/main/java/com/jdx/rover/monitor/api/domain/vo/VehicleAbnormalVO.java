/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a view object for schedule stop.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleAbnormalVO {

  /**
   * <p>
   * Represents the vehicle name.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the start time.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  /**
   * <p>
   * Represents the end time.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

}
