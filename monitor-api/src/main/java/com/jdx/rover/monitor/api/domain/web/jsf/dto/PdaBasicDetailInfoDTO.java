/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.web.jsf.dto;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * pda基础信息详情
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class PdaBasicDetailInfoDTO {

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备别名
     */
    private String remarkName;

    /**
     * 产品标识
     */
    private String productKey;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 设备型号编号
     */
    private String productModelNo;

    /**
     * 设备型号名称
     */
    private String productModelName;

    /**
     * 分组编号
     */
    private String groupNo;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分组级别名称
     */
    private String groupLevelName;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 激活时间
     */
    private Date activeTime;
}
