/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

/**
 * <p>
 * This is security log event.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorSecurityLogDTO {
  /**
   * <p>
   * Represents the client ip.
   * </p>
   */
  private String clientIp;

  /**
   * <p>
   * Represents the method name.
   * </p>
   */
  private String interfaceName;

  /**
   * <p>
   * Represents the receiver phone.
   * </p>
   */
  private String phone;

  /**
   * <p>
   * Represents the user.
   * </p>
   */
  private String user;

  /**
   * <p>
   * Represents the receiver user.
   * </p>
   */
  private String receiverUser;

  /**
   * <p>
   * Represents the receiver user.
   * </p>
   */
  private String receiverAddress;

  /**
   * <p>
   * Represents the order no.
   * </p>
   */
  private String orderNo;

}
