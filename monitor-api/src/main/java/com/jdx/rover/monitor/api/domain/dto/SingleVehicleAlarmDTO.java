/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 影子系统单车页实时模型
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleAlarmDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String alarmEvent;

  /**
   * 上报时间
   */
  private Date reportTime;

}
