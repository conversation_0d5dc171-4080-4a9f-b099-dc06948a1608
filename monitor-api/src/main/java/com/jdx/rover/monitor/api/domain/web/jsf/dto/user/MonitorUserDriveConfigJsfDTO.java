package com.jdx.rover.monitor.api.domain.web.jsf.dto.user;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户配置DTO
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Data
public class MonitorUserDriveConfigJsfDTO implements Serializable {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 速度限制
     */
    private Integer speedLimit;
}
