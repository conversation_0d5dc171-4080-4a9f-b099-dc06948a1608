/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto;

import lombok.Data;

/**
 * <p>
 * pda 实时状态表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class PdaRealtimeBasicInfoDTO {

  /**
   * <p>
   * 设备编号
   * </p>
   */
  private String deviceName;

  /**
   * <p>
   * 设备型号
   * </p>
   */
  private String productModelNo;

  /**
   * <p>
   * 设备型号名
   * </p>
   */
  private String productModelName;

  /**
   * <p>
   * 实时状态(在线/离线/异常)
   * </p>
   */
  private String realtimeStatus;

  /**
   * <p>
   * 实时状态名(在线/离线/异常)
   * </p>
   */
  private String realtimeStatusName;

  /**
   * <p>
   * 分组名称
   * </p>
   */
  private String groupName;

  /**
   * <p>
   * 网络类型（Wi-Fi在线/蜂窝在线）
   * </p>
   */
  private Integer networkType;

  /**
   * <p>
   * 网络类型名（Wi-Fi在线/蜂窝在线）
   * </p>
   */
  private String networkTypeName;

  /**
   * <p>
   * 定位状态（开/关）
   * </p>
   */
  private Integer positionStatus;

  /**
   * <p>
   * 埋点状态（开/关）
   * </p>
   */
  private Integer burialStatus;

  /**
   * <p>
   * 电池电量
   * </p>
   */
  private Integer deviceRestElectric;

}
