/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a vehicle alarm information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorShadowVehicleAlarmDTO {

  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the alarm of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<SingleVehicleAlarmDTO> alarm;

}
