/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.api.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleAlarmDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleBasicInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleRealtimeDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleTakeOverDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <p>
 * This is api interface for vehicle realtime info.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ShadowVehicleRealtimeInfoApi {

  /**
   * <p>
   * 影子系统获取车辆实时数据.
   * </p>
   *
   */
  @GetMapping("/monitor/web/shadow/vehicle/realtime/{vehicleName}")
  HttpResult<MonitorShadowVehicleRealtimeDTO> getVehicleRealtimeInfo(@PathVariable("vehicleName") String vehicleName);

  /**
   * <p>
   * 影子系统获取车辆基础数据.
   * </p>
   *
   */
  @GetMapping("/monitor/web/shadow/vehicle/basic/{vehicleName}")
  HttpResult<MonitorShadowVehicleBasicInfoDTO> getVehicleBasicInfo(@PathVariable("vehicleName") String vehicleName);

  /**
   * <p>
   * 影子系统获取车辆接管状态.
   * </p>
   *
   */
  @GetMapping("/monitor/web/shadow/vehicle/take/{vehicleName}")
  HttpResult<MonitorShadowVehicleTakeOverDTO> getVehicleTakeOverInfo(@PathVariable("vehicleName") String vehicleName);

  /**
   * <p>
   * 影子系统获取车辆告警数据.
   * </p>
   *
   */
  @GetMapping("/monitor/web/shadow/vehicle/alarm/{vehicleName}")
  HttpResult<MonitorShadowVehicleAlarmDTO> getVehicleRealtimeAlarmInfo(@PathVariable("vehicleName") String vehicleName);

  /**
   * <p>
   * 影子系统获取车辆规划路径.
   * </p>
   *
   */
  @GetMapping("/monitor/web/shadow/vehicle/planning/route/{vehicleName}")
  HttpResult getVehiclePncRoutingInfo(@PathVariable("vehicleName") String vehicleName);

}
