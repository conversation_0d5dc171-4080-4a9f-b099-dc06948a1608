/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class VehicleExceptionInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 上报时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date reportTime;

  /**
   * 错误代码
   */
  private String errorCode;

  /**
   * 错误级别
   */
  private String errorLevel;

  /**
   * 错误消息
   */
  private String errorMessage;

  /**
   * 翻译消息
   */
  private String translateMessage;

}
