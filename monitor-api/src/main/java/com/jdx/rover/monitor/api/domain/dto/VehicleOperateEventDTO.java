/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import com.jd.fastjson.annotation.JSONField;
import com.jdx.rover.monitor.api.domain.enums.OperateStateEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 车辆事件详细信息
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleOperateEventDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 发送时间
   */
  private Date recordTime;

  /**
   * 用户名
   */
  private String userName;

  /**
   * 座席编号
   */
  private String cockpitNumber;

  /**
   * 操作类型
   */
  private String operateType;

  /**
   * 操作时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date operateTime;

  /**
   * 操作状态
   */
  private String operateState = OperateStateEnum.SUCCESS.getState();

  /**
   * 操作端
   */
  private String operateSource;

  /**
   * 操作详细内容
   */
  private String operateMessage;

}
