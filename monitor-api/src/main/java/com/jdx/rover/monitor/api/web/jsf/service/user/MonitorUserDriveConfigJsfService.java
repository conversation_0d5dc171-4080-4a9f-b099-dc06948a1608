/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.api.web.jsf.service.user;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.user.MonitorUserDriveConfigJsfDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.user.GetUserDriveConfigJsfVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.user.SaveUserDriveConfigJsfVO;


/**
 * 用户配置jsf服务
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
public interface MonitorUserDriveConfigJsfService {
    /**
     * 保存用户配置
     */
    HttpResult<MonitorUserDriveConfigJsfDTO> saveUserDriveConfig(SaveUserDriveConfigJsfVO saveUserDriveConfigJsfVO);

    /**
     * 获取用户配置
     */
    HttpResult<MonitorUserDriveConfigJsfDTO> getUserDriveConfig(GetUserDriveConfigJsfVO getUserDriveConfigJsfVO);
}
