package com.jdx.rover.monitor.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.VehicleTakeOverDTO;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * <p>
 * 车辆指令
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorVehicleCommandJsfService {

  /**
   * <p>
   *  获取用户接管的车辆列表
   * </p>
   *
   */
  HttpResult<List<VehicleTakeOverDTO>> getTakeOverVehicle(@NotEmpty(message = "用户名不能为空") String userName);
}
