/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto.robot;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机器人设备看板实时状态
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotDeviceRealtimePageInfoDTO {

  /**
   * <p>
   * 车架号
   * </p>
   */
  private String deviceName;

  /**
   * <p>
   * 车牌号
   * </p>
   */
  private String remarkName;

  /**
   * <p>
   * 分组全名
   * </p>
   */
  private String groupLevelName;

  /**
   * <p>
   * 设备型号
   * </p>
   */
  private String productModelName;

  /**
   * <p>
   * 实时状态(在线/离线/异常)
   * </p>
   */
  private String realtimeStatus;

  /**
   * <p>
   * 是否充电
   * </p>
   */
  private Boolean chargingState;

  /**
   * <p>
   * 分组名称
   * </p>
   */
  private String groupName;

  /**
   * <p>
   * 电量
   * </p>
   */
  private Double power;

  /**
   * <p>
   * 任务类型
   * </p>
   */
  private String taskType;

  /**
   * <p>
   * 任务波次时间
   * </p>
   */
  private Date taskEtcTime;

  /**
   * <p>
   * 急停状态
   * </p>
   */
  private Boolean emergencyState;

  /**
   * <p>
   * 告警异常
   * </p>
   */
  private List<RobotDeviceAbnormalListDTO> alarmEvent;

  /**
   * <p>
   * 远程呼叫
   * </p>
   */
  private Boolean remoteCall;

  /**
   * <p>
   * 远程呼叫跟进用户
   * </p>
   */
  private String followUser;

  /**
   * <p>
   * 工作模式
   * </p>
   */
  private String workModeName;

}
