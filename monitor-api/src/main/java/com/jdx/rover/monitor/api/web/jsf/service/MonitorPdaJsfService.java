/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.web.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.*;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.PdaBasicSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.PdaPageSearchVO;

import java.util.List;

/**
 * <p>
 * 监控Pda服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorPdaJsfService {

    HttpResult<List<PdaModuleDeviceStatisticDTO>> getModuleDeviceStatisticList();

    HttpResult<List<PdaGroupBasicInfoDTO>> getDeviceGroupTree();

    HttpResult<PdaPageSearchDTO> pageSearchPdaDevice(PdaPageSearchVO pdaPageSearchVo);

    HttpResult<PdaBasicDetailInfoDTO> getDeviceBasic(PdaBasicSearchVO pdaBasicSearchVo);

    HttpResult<PdaRealtimeStatusInfoDTO> getRealtimeStatus(PdaBasicSearchVO pdaBasicSearchVo);
}
