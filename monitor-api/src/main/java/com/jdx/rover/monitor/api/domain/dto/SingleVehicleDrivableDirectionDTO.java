package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 四路使能
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleDrivableDirectionDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 前
   */
  private Boolean enableFront;

  /**
   * 后
   */
  private Boolean enableBack;

  /**
   * 左
   */
  private Boolean enableLeft;

  /**
   * 右
   */
  private Boolean enableRight;
}
