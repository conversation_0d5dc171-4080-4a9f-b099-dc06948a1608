/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 缺陷提报
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class BugReportDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 缺陷编号
   */
  private String bugCode;

  /**
   * 发送时间
   */
  private Date recordTime;

  /**
   * 工单编号
   */
  private String issueNumber;

  /**
   * 事件编号
   */
  private String eventNumber;

}
