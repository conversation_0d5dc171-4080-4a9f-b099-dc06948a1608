package com.jdx.rover.monitor.jsf.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.VehicleExceptionInfoDTO;
import com.jdx.rover.monitor.api.domain.vo.VehicleAbnormalVO;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * <p>
 * This is api interface for abnormal info.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorVehicleAbnormalJsfService {

  /**
   * <p>
   * Post vehicle abnormal request.
   * </p>
   *
   */
  HttpResult<List<VehicleExceptionInfoDTO>> getVehicleRealtimeInfo(@NotNull(message = "非法请求") VehicleAbnormalVO vehicleAbnormalVo);

}
