/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 规划停靠点
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehiclePncRouteStopInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 停靠点ID
   */
  private Integer stopId;

  /**
   * 规划路径
   */
  private List<VehicleRoutePositionDTO> routing;
}
