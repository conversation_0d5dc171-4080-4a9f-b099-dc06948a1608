/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class VehicleScheduleDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 调度号
   */
  private String scheduleNo;

  /**
   * 调度开始时间
   */
  private Date startDateTime;

  /**
   * 调度预计开始时间
   */
  private Date estDepartureTime;

  /**
   * 调度总订单数
   */
  private String totalOrderNum;

  /**
   * 取消订单数
   */
  private String canceledOrderNum;

  /**
   * 完成订单数
   */
  private String finishedOrderNum;

  /**
   * 停靠点
   */
  private List<VehicleScheduleStopDTO> stop;

}
