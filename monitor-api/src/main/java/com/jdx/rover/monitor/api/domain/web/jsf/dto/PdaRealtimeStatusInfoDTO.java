/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * pda实时信息响应
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class PdaRealtimeStatusInfoDTO {

    /**
     * 设备编号
     */
    private String deviceName;

    /**
     * IMEI，IMEI1和IMEI2放一个字符串中，使用逗号隔开
     */
    private String imei;

    /**
     * MEID
     */
    private String meid;

    /**
     * MDM版本号
     */
    private String mdmVersionCode;


    /**
     * 系统版本名
     */
    private String systemVersionName;

    /**
     * WIFI-MAC地址
     */
    private String wifiMac;

    /**
     * BT-MAC地址
     */
    private String btMac;

    /**
     * 屏幕分辨率
     */
    private String screenResolution;

    /**
     * 网络类型 （1.无线 2.移动）
     */
    private Integer networkType;

    /**
     * 网络类型名（无线 移动）
     */
    private String networkTypeName;

    /**
     * WI-FI SSID
     */
    private String wifiSsid;

    /**
     * WI-FI BSSID
     */
    private String wifiBssid;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * IPV6地址
     */
    private String ipAddressV6;

    /**
     * 坐标位置，经纬度放一个字符串里，用逗号隔开
     */
    private String coordinatePosition;

    /**
     * 位置更新时间
     */
    private Date positionUpdateTime;

    /**
     * 电池容量
     */
    private Integer batteryCapacity;

    /**
     * 设备电池剩余电量
     */
    private Integer deviceRestElectric;

    /**
     * 白名单编码
     */
    private String appWhitelistCode;

    /**
     * 白名单开关
     */
    private Integer appWhitelistSwitchStatus;

    /**
     * 埋点开关状态  （1.开 0、关）
     */
    private Integer burialStatus;

    /**
     * 埋点策略编码
     */
    private String buriedPointStrategyCode;

    /**
     * 设备锁定/解锁状态  （1.开 0、关）
     */
    private Integer lockStatus;

    /**
     * 定位开关状态  （1.开 0、关）
     */
    private Integer positionStatus;

    /**
     * 高精定位开关状态（1.开 0、关）
     */
    private Integer highPositionStatus;

    /**
     * 高性能WIFI开关状态（1.开 0、关）
     */
    private Integer highPerformanceWifiStatus;

    /**
     * 在线状态
     */
    private String realtimeStatusName;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 设备实时状态更新时间
     */
    private Date recordTime;

}
