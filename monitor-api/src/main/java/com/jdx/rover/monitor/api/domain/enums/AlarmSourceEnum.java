/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 告警来源
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AlarmSourceEnum {
  VEHICLE_ALARM("VEHICLE_ALARM", "车辆报警事件"),
  CLOUD_ALARM("CLOUD_ALARM", "云端预警事件"),
  MANUAL_ALARM("MANUAL_ALARM", "人工告警事件"),

  ;

  /**
   * 来源
   */
  private String source;

  /**
   * 来源名
   */
  private String sourceName;

  public static AlarmSourceEnum of(String source) {
    for (AlarmSourceEnum em : AlarmSourceEnum.values()) {
      if (Objects.equals(source, em.getSource())) {
        return em;
      }
    }
    return null;
  }

}
