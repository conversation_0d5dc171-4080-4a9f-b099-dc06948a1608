/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * pda设备分组树
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class PdaGroupBasicInfoDTO {

  /**
   * 分组编号
   */
  private String groupNo;

  /**
   * 分组名称
   */
  private String groupName;

  /**
   * 子分组
   */
  private List<PdaGroupBasicInfoDTO> child;

  /**
   * 设备总数
   */
  private Integer totalCount = 0;

  /**
   * 设备在线数
   */
  private Integer onlineCount = 0;

}
