/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 座席模式枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum CockpitModeEnum {

  LISTENING_MODE("LISTENING_MODE", "听单模式"),
  WATCH_CAR_MODE("WATCH_CAR_MODE", "盯车模式"),
  PATROL_MODE("PATROL_MODE", "巡查模式"),
  FREE_MODE("FREE_MODE", "自由模式"),
  MAP_COLLECTION_MODE("MAP_COLLECTION_MODE", "采图模式"),
  ;
  private String value;

  private String name;

  public static CockpitModeEnum of(final String value) {
    for (CockpitModeEnum itemEnum : CockpitModeEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
