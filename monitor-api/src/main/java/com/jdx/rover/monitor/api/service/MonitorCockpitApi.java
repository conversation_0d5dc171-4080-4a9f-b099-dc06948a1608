/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.CockpitRealStatusDTO;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <p>
 * 云驾座席服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorCockpitApi {

  /**
   * <p>
   * 获取全部座席实时状态
   * </p>
   *
   */
  @GetMapping("/monitor/web/cockpit/getAllCockpitRealStatus")
  HttpResult<List<CockpitRealStatusDTO>> getAllCockpitRealStatus();

}
