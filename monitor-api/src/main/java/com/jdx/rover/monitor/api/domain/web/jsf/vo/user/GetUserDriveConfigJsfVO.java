package com.jdx.rover.monitor.api.domain.web.jsf.vo.user;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取用户配置VO
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Data
public class GetUserDriveConfigJsfVO implements Serializable {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 用户名称
     */
    @NotBlank
    private String userName;
}
