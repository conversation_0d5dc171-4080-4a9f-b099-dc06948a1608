/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.dto;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 告警详情信息
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AlarmInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 告警类型
   */
  private String alarmType;

  /**
   * 告警编号
   */
  private String alarmNumber;

  /**
   * 告警来源
   */
  private String alarmSource;

  /**
   * 开始时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date startTime;

  /**
   * 消失时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date endTime;

  /**
   * 错误代码
   */
  private String errorCode;

  /**
   * 错误级别
   */
  private String errorLevel;

  /**
   * 错误消息
   */
  private String errorMessage;

}
