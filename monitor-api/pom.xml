<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2021 www.jd.com All rights reserved.
  ~ 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jdx.rover</groupId>
    <artifactId>rover-monitor-api</artifactId>
    <version>1.1.32-BETA-SNAPSHOT</version>

    <properties>
        <jdk.version>1.8</jdk.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
        <rover-common.version>1.0.0</rover-common.version>
        <feign-core.version>2.2.1.RELEASE</feign-core.version>
        <rover-jsf-provider.version>1.1.0-SNAPSHOT</rover-jsf-provider.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.33</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-common</artifactId>
            <version>${rover-common.version}</version>
        </dependency>
        <!--feign core-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <version>${feign-core.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>3.1.0</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-jsf-provider</artifactId>
            <version>${rover-jsf-provider.version}</version>
        </dependency>
    </dependencies>
</project>